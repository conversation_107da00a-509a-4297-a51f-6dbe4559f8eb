using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Policies;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Common.Tests.Fixtures.Domain;

public class PackageFlightTestFixture
{
    private readonly ICurrencyConversionPolicy _currencyConversionPolicy;

    public PackageFlightTestFixture()
    {
        _currencyConversionPolicy = Substitute.For<ICurrencyConversionPolicy>();
    }

    public PackageFlightTestFixture WithAnyCurrencyConversion()
    {
        _currencyConversionPolicy
            .Convert(Arg.Any<decimal>(), Arg.Any<Currency>())
            .Returns(callInfo => callInfo.Arg<decimal>());

        return this;
    }

    public PackageFlightTestFixture WithCurrencyConversion(string fromCurrency, decimal currencyRate)
    {
        _currencyConversionPolicy
            .Convert(Arg.Any<decimal>(), Arg.Is<Currency>(currency => currency == fromCurrency))
            .Returns(callInfo => callInfo.Arg<decimal>() * currencyRate);

        return this;
    }

    public Flight CreateFlight(
        string? flightId = null,
        Dictionary<PackageOccupancy, decimal>? prices = null,
        DateTime? updatedAt = null)
    {
        return new Flight
        {
            Id = flightId ?? "flightId1",
            Prices = prices ?? new Dictionary<PackageOccupancy, decimal> { { DefaultTestValues.PackageOccupancy, 100M } },
            UpdatedAt = updatedAt ?? new DateTime(2025, 1, 1)
        };
    }

    public FlightOffer CreateFlightOffer(
        string? id = null,
        DateTime? departureDate = null,
        DateTime? returnArrivalDate = null, 
        Dictionary<FlightOccupancy, decimal>? prices = null)
    {
        return new FlightOffer
        {
            Id = id ?? "id1",
            DepartureDate = departureDate ?? new DateTime(2025, 3, 24),
            ArrivalDate = new DateTime(2025, 3, 25),
            ReturnDepartureDate = new DateTime(2025, 3, 31),
            ReturnArrivalDate = returnArrivalDate ?? new DateTime(2025, 3, 31),
            DepartureAirport = new Airport("LHR"),
            ArrivalAirport = new Airport("MAD"),
            ProviderCode = 12,
            Stops = 0,
            AirlineCodes = ["BA"],
            FlightIds = ["flightId1"],
            FlightNumbers = new FlightNumbers(["BA123"]),
            ReturnFlightNumbers = new FlightNumbers(["BA456"]),
            Prices = new Dictionary<string, FlightPrices>
            {
                ["flightId1"] = new()
                {
                    Prices = prices ?? new Dictionary<FlightOccupancy, decimal>
                    {
                        [new FlightOccupancy(2, 0, 0, 0)] = 100M
                    },
                    Currency = "PLN",
                    UpdatedAt = new DateTime(2025, 1, 1)
                }
            },
            BaggageIncluded = true,
        };
    }

    public FlightQuote CreateFlightQuote(
        string? flightId = null,
        DateTime? updateTime = null,
        Currency? currency = null,
        Dictionary<PackageOccupancy, decimal>? prices = null)
    {
        return new FlightQuote
        {
            FlightId = flightId ?? "flightId1",
            UpdateTime = updateTime ?? new DateTime(2025, 1, 2),
            Currency = currency ?? DefaultTestValues.Currency,
            Prices = prices ?? new Dictionary<PackageOccupancy, decimal> { { DefaultTestValues.PackageOccupancy, 100M } }
        };
    }

    public PackageFlight CreatePackageFlight(
        PackageFlightId? id = null,
        string? definitionId = null,
        Currency? currency = null,
        PackageOccupancy[]? occupancies = null,
        FlightOffer[]? flightOffers = null)
    {
        return PackageFlight.Create(
            id: id ?? new PackageFlightId(
                checkIn: DateOnly.FromDateTime(new DateTime(2025, 3, 24)),
                stayLength: 7,
                marketId: "uk",
                arrivalAirport: new Airport("MAD"),
                departureAirport: new Airport("LHR")),
            definitionId: definitionId ?? "uk-es",
            currency: currency ?? DefaultTestValues.Currency,
            occupancies: occupancies ?? [DefaultTestValues.PackageOccupancy],
            flightOffers: flightOffers ?? [CreateFlightOffer()],
            configure: pf => pf.ApplyPolicies(_currencyConversionPolicy));
    }
}
