using Esky.Packages.Domain.Model.Markets;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Common.Tests.Fixtures.Domain;

public class MarketTestFixture
{
    public static Market CreateMarket(
        string? id = null,
        Currency? currency = null,
        string? partnerCode = null,
        Airport[]? departureAirports = null,
        ProviderConfigurationId[]? providerConfigurationIds = null,
        bool? enableInboundOutboundFlightDepartureHours = null,
        bool? emitPriceHistoryEvents = null,
        TimeZoneInfo? timeZone = null)
    {
        return Market.Create(
            id: id ?? "uk",
            currency: currency ?? DefaultTestValues.Currency,
            partnerCode: partnerCode ?? "partner1",
            departureAirports: departureAirports ?? [DefaultTestValues.DepartureAirport],
            providerConfigurationIds: providerConfigurationIds ?? [new ProviderConfigurationId("1")],
            enableInboundOutboundFlightDepartureHours: enableInboundOutboundFlightDepartureHours ?? false,
            emitPriceHistoryEvents: emitPriceHistoryEvents ?? true,
            timeZone: timeZone ?? TimeZoneInfo.FindSystemTimeZoneById("Europe/London"));
    }
}
