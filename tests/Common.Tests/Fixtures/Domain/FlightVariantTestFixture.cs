using Esky.Packages.Domain.Model.LiveVariants;
using Esky.Packages.Domain.Policies;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Common.Tests.Fixtures.Domain;

public class FlightVariantTestFixture
{
    private readonly ICurrencyConversionPolicy _currencyConversionPolicy;

    public FlightVariantTestFixture()
    {
        _currencyConversionPolicy = Substitute.For<ICurrencyConversionPolicy>();
    }

    public FlightVariantTestFixture WithAnyCurrencyConversion()
    {
        _currencyConversionPolicy
            .Convert(Arg.Any<decimal>(), Arg.Any<Currency>())
            .Returns(callInfo => callInfo.Arg<decimal>());

        return this;
    }

    public FlightVariantTestFixture WithCurrencyConversion(Currency fromCurrency, decimal currencyRate)
    {
        _currencyConversionPolicy
            .Convert(Arg.Any<decimal>(), Arg.Is<Currency>(currency => currency.Equals(fromCurrency)))
            .Returns(callInfo => callInfo.Arg<decimal>() * currencyRate);

        return this;
    }

    public FlightLiveVariant CreateAlternativeFlight(
        string? key = null,
        string? departureAirport = null,
        string? arrivalAirport = null,
        DateTime? departureDate = null,
        DateTime? arrivalDate = null,
        DateTime? returnDepartureDate = null,
        DateTime? returnArrivalDate = null,
        int? stops = null,
        int? providerCode = null,
        string[]? airlineCodes = null,
        string[]? flightIds = null,
        string[]? legLocators = null,
        Currency? currency = null,
        Money[]? prices = null,
        bool? registeredBaggageIncluded = null)
    {
        return FlightLiveVariant.Create(
            key: key ?? "key1",
            departureAirport: departureAirport ?? DefaultTestValues.DepartureAirport,
            arrivalAirport: arrivalAirport ?? DefaultTestValues.ArrivalAirport,
            departureDate: departureDate ?? new DateTime(2025, 3, 1, 17, 0, 0),
            arrivalDate: arrivalDate ?? new DateTime(2025, 3, 1, 19, 0, 0),
            returnDepartureDate: returnDepartureDate ?? new DateTime(2025, 3, 8, 7, 0, 0),
            returnArrivalDate: returnArrivalDate ?? new DateTime(2025, 3, 8, 14, 0, 0),
            flightNumbers: new FlightNumbers(["123"]),
            returnFlightNumbers: new FlightNumbers(["456"]),
            stops: stops ?? 0,
            providerCode: providerCode ?? 1,
            airlineCodes: airlineCodes ?? ["BA"],
            flightIds: flightIds ?? ["flightId1"],
            legLocators: legLocators ?? ["legLocator"],
            currency: currency ?? DefaultTestValues.Currency,
            prices: prices ?? [new Money(1000M, DefaultTestValues.Currency)],
            registeredBaggageIncluded ?? false,
            configure: af => af.ApplyPolicies(_currencyConversionPolicy)
        );
    }
}
