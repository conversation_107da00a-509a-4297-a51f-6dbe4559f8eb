using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Policies;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Common.Tests.Fixtures.Domain;

public class PackageHotelOfferTestFixture
{
    private readonly ICurrencyConversionPolicy _currencyConversionPolicy;

    public PackageHotelOfferTestFixture()
    {
        _currencyConversionPolicy = Substitute.For<ICurrencyConversionPolicy>();
    }

    public PackageHotelOfferTestFixture WithAnyCurrencyConversion()
    {
        _currencyConversionPolicy
            .Convert(Arg.Any<decimal>(), Arg.Any<Currency>())
            .Returns(callInfo => callInfo.Arg<decimal>());

        return this;
    }

    public PackageHotelOfferTestFixture WithCurrencyConversion(string fromCurrency, decimal currencyRate)
    {
        _currencyConversionPolicy
            .Convert(Arg.Any<decimal>(), Arg.Is<Currency>(currency => currency == fromCurrency))
            .Returns(callInfo => callInfo.Arg<decimal>() * currencyRate);

        return this;
    }

    public PackageHotelOfferStayKey CreatePackageHotelOfferStayKey(
        int? metaCode = null,
        DateOnly? checkIn = null,
        int? stayLength = null)
    {
        return new PackageHotelOfferStayKey(
            MetaCode: metaCode ?? 123,
            CheckIn: checkIn ?? DateOnly.FromDateTime(new DateTime(2025, 3, 24)),
            StayLength: stayLength ?? 7);
    }

    public HotelOffer CreateHotelOffer(
        int? metaCode = null,
        DateOnly? checkIn = null,
        int? stayLength = null,
        ProviderConfigurationId? providerConfigurationId = null,
        Occupancy? occupancy = null,
        Dictionary<MealPlan, Dictionary<Refundability, RoomOffer[]>>? roomOffersByMealPlanByRefundability = null,
        DateTime? updatedAt = null)
    {
        return new HotelOffer
        {
            MetaCode = metaCode ?? 123,
            CheckIn = checkIn ?? DateOnly.FromDateTime(new DateTime(2025, 3, 24)),
            StayLength = stayLength ?? 7,
            ProviderConfigurationId = providerConfigurationId ?? new ProviderConfigurationId("OTS|123"),
            Occupancy = occupancy ?? DefaultTestValues.Occupancy,
            RoomOffersByMealPlanByRefundability = roomOffersByMealPlanByRefundability ?? new Dictionary<MealPlan, Dictionary<Refundability, RoomOffer[]>>
            {
                { MealPlan.Breakfast, new Dictionary<Refundability, RoomOffer[]> { { Refundability.Refundable, [
                    new RoomOffer
                    {
                        Availability = 1,
                        Price = new Money(100M, DefaultTestValues.Currency),
                        RoomIds = new RoomIds(["123"])
                    }
                    ]
                } } }
            },
            UpdatedAt = updatedAt ?? new DateTime(2025, 1, 2)
        };
    }

    public PackageHotelOffer CreatePackageHotelOffer(
        PackageHotelOfferId? id = null,
        string? definitionId = null,
        Currency? currency = null,
        PackageOccupancy[]? occupancies = null,
        ProviderConfigurationId[]? providerConfigurationIds = null,
        Dictionary<Airport, Airport[]>? airports = null,
        HotelOffer[]? hotelOffers = null)
    {
        var packageHotelOffer = PackageHotelOffer.Create(
            id: id ?? new PackageHotelOfferId(
                checkIn: DateOnly.FromDateTime(new DateTime(2025, 3, 24)),
                stayLength: 7,
                marketId: "uk",
                metaCode: 123),
            definitionId: definitionId ?? "uk-es",
            currency: currency ?? DefaultTestValues.Currency,
            occupancies: occupancies ?? [DefaultTestValues.PackageOccupancy],
            providerConfigurationIds: providerConfigurationIds ?? [new ProviderConfigurationId("OTS|123")],
            airports: airports ?? new Dictionary<Airport, Airport[]> { { new Airport("MAD"), [new Airport("LHR"), new Airport("LTN")] } },
            hotelOffers: hotelOffers ?? [CreateHotelOffer()],
            configure: offer => offer.ApplyPolicies(_currencyConversionPolicy)
        );

        return packageHotelOffer;
    }
}
