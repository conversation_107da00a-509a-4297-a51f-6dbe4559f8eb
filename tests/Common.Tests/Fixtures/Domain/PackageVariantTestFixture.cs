using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageVariants;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Common.Tests.Fixtures.Domain;

public class PackageVariantTestFixture
{
    public static PackageVariant CreatePackageVariant(
        DateOnly? checkIn = null,
        int? stayLength = null,
        string? marketId = null,
        int? metaCode = null,
        Airport? departureAirport = null,
        Airport? arrivalAirport = null,
        PackageOccupancy? occupancy = null,
        MealPlan? mealPlan = null,
        DateOnly? departureDate = null,
        DateOnly? returnDepartureDate = null,
        DateOnly? returnArrivalDate = null,
        Refundability? refundability = null,
        RoomIds? roomIds = null,
        FlightNumbers? flightNumbers = null,
        FlightNumbers? returnFlightNumbers = null,
        bool? baggageIncluded = null,
        string? flightOfferId = null,
        int? flightVariantPrice = null,
        int? hotelOfferVariantPrice = null)
    {
        return PackageVariant.Create(
            checkIn: checkIn ?? new DateOnly(2024, 3, 15),
            stayLength: stayLength ?? DefaultTestValues.StayLength,
            marketId: marketId ?? "uk",
            metaCode: metaCode ?? 12345,
            departureAirport: departureAirport ?? DefaultTestValues.DepartureAirport,
            arrivalAirport: arrivalAirport ?? DefaultTestValues.ArrivalAirport,
            occupancy: occupancy ?? DefaultTestValues.PackageOccupancy,
            mealPlan: mealPlan ?? DefaultTestValues.MealPlan,
            departureDate: departureDate ?? new DateOnly(2024, 3, 15),
            returnDepartureDate: returnDepartureDate ?? new DateOnly(2024, 3, 22),
            returnArrivalDate: returnArrivalDate ?? new DateOnly(2024, 3, 22),
            refundability: refundability ?? DefaultTestValues.Refundability,
            roomIds: roomIds ?? RoomIds.Empty,
            flightNumbers: flightNumbers ?? new FlightNumbers(["EZ123"]),
            returnFlightNumbers: returnFlightNumbers ?? new FlightNumbers(["EZ456"]),
            baggageIncluded: baggageIncluded ?? true,
            flightOfferId: flightOfferId ?? "flight123",
            flightVariantPrice: flightVariantPrice ?? 500,
            hotelOfferVariantPrice: hotelOfferVariantPrice ?? 800);
    }
}
