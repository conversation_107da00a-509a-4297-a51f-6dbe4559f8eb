<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<RootNamespace>Esky.Packages.Common.Tests</RootNamespace>
		<IsTestProject>true</IsTestProject>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="coverlet.collector" />
		<PackageReference Include="Microsoft.Extensions.TimeProvider.Testing" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" />
		<PackageReference Include="NSubstitute" />
		<PackageReference Include="Shouldly" />
		<PackageReference Include="xunit" />
		<PackageReference Include="xunit.runner.visualstudio" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\src\Domain\Domain.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Using Include="Xunit" />
		<Using Include="NSubstitute" />
		<Using Include="Shouldly" />
	</ItemGroup>

</Project>
