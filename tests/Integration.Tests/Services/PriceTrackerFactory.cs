using Esky.Packages.Integration.Tests.Infrastructure;

namespace Esky.Packages.Integration.Tests.Services;

public class PriceTrackerFactory(ConnectionStrings connectionStrings) 
    : BaseServiceFactory<PriceTracker.Program>()
{
    protected override Dictionary<string, string> GetConfigurationValues()
    {
        return new Dictionary<string, string>
        {
            ["Mongo:ConnectionString"] = connectionStrings.MongoDbConnectionString,
            ["FlightPricesConsumer:BootstrapServers"] = connectionStrings.KafkaBootstrapServers,
            ["HotelOfferPricesConsumer:BootstrapServers"] = connectionStrings.KafkaBootstrapServers
        };
    }
}