using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Esky.Packages.Integration.Tests.Infrastructure;
using Esky.Packages.Infrastructure.HttpClients.Constants;
using Esky.Packages.Integration.Tests.TestData;
using Esky.Packages.Application.Abstractions.Gateways.DataAnalyticsGateway;
using Esky.Packages.Infrastructure.Observability;
using Microsoft.Extensions.Time.Testing;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Esky.Packages.Integration.Tests.Services;

public abstract partial class BaseServiceFactory<TProgram> 
    : WebApplicationFactory<TProgram> where TProgram : class
{
    private const string ExampleApiUrl = "http://example.com";

    private static readonly Dictionary<string, string> ApiUrls = new()
        {
            { "Apis:FlightCache:Url", ExampleApiUrl },
            { "Apis:FlightLive:Url", ExampleApiUrl },
            { "Apis:HotelStatic:Url", ExampleApiUrl },
            { "Apis:HotelCache:Url", ExampleApiUrl },
            { "Apis:HotelTransaction:Url", ExampleApiUrl },
            { "Apis:CurrencyConverter:Url", ExampleApiUrl },
            { "Apis:OfferAccuracy:Url", ExampleApiUrl },
    };

    private DateTimeOffset FakeTime { get; set; } = new(2025, 8, 2, 11, 0, 0, TimeSpan.Zero);

    private readonly FakeTimeProvider _fakeTimeProvider = new();

    public void ChangeCurrentDayToTomorrow()
    {
        FakeTime = FakeTime.AddDays(1);
        _fakeTimeProvider.SetUtcNow(FakeTime);
    }

    protected virtual void ConfigureServices(IServiceCollection services)
    {
        // Override this method in derived classes to configure services
    }

    protected virtual Dictionary<string, string> GetConfigurationValues()
    {
        // Override this method in derived classes to provide specific configuration values
        return [];
    }

    protected void MockCurrencyConverterGateway(IServiceCollection services)
    {
        const string Path = "TestData.Gateways.CurrencyService.Response.json";
        var testData = TestDataReader.ReadResource(Path);

        services.AddHttpClient(ApiHttpClientConsts.CurrencyConverterClient)
            .ConfigurePrimaryHttpMessageHandler(() => new MockHttpMessageHandler(testData));
    }

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        _fakeTimeProvider.SetUtcNow(FakeTime);

        builder.ConfigureServices(services =>
        {
            MockCurrencyConverterGateway(services);
            ConfigureServices(services);

            services.RemoveAll<TimeProvider>();
            services.AddSingleton<TimeProvider>(_fakeTimeProvider);
            services.AddSingleton(Substitute.For<ICommunicationLogger>());
            services.AddSingleton(Substitute.For<IDataAnalyticsGateway>());
        });

        var configurationValues = GetConfigurationValues();
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(ApiUrls)
            .AddInMemoryCollection(configurationValues)
            .Build();

        builder
            .UseConfiguration(configuration)
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddInMemoryCollection(ApiUrls);
                config.AddInMemoryCollection(configurationValues);
            });
    }
} 