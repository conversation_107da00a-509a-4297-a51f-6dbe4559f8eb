using Esky.Packages.Integration.Tests.Infrastructure;

namespace Esky.Packages.Integration.Tests.Services;

public class RepartitionerFactory(ConnectionStrings connectionStrings) 
    : BaseServiceFactory<Repartitioner.Program>()
{
    protected override Dictionary<string, string> GetConfigurationValues()
    {
        return new Dictionary<string, string>
        {
            ["Mongo:ConnectionString"] = connectionStrings.MongoDbConnectionString,
            ["MongoSearch:ConnectionString"] = connectionStrings.MongoDbConnectionString,
            ["BloomFilterNotificationConsumer:BootstrapServers"] = connectionStrings.KafkaBootstrapServers,
            ["FlightQuoteConsumer:BootstrapServers"] = connectionStrings.KafkaBootstrapServers,
            ["HotelOfferQuoteConsumer:BootstrapServers"] = connectionStrings.KafkaBootstrapServers,
            ["RepartitionedFlightQuoteProducer:BootstrapServers"] = connectionStrings.KafkaBootstrapServers,
            ["RepartitionedHotelOfferQuoteProducer:BootstrapServers"] = connectionStrings.KafkaBootstrapServers,
        };
    }
}