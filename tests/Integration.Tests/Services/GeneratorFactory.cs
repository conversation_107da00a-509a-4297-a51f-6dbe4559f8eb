using Esky.Packages.Integration.Tests.Infrastructure;
using Esky.Packages.Infrastructure.HttpClients.Constants;
using Esky.Packages.Integration.Tests.TestData;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.Packages.Integration.Tests.Services;

public class GeneratorFactory(ConnectionStrings connectionStrings) 
    : BaseServiceFactory<Generator.Program>()
{
    private int _flightCacheResponseIndex = 0;
    private int _hotelCacheResponseIndex = 0;

    private readonly string[] _flightCacheResponsePaths = 
    {
        "TestData.Gateways.FlightCache.FlightOffers.Response1.json",
        "TestData.Gateways.FlightCache.FlightOffers.Response2.json",
        "TestData.Gateways.FlightCache.FlightOffers.Response3.json",
        "TestData.Gateways.FlightCache.FlightOffers.Response4.json",
        "TestData.Gateways.FlightCache.FlightOffers.Response5.json"
    };
    private readonly string[] _hotelCacheResponsePaths = 
    {
        "TestData.Gateways.HotelCache.Response1.json"
    };
    
    protected override Dictionary<string, string> GetConfigurationValues()
    {
        return new Dictionary<string, string>
        {
            ["Mongo:ConnectionString"] = connectionStrings.MongoDbConnectionString,
            ["RabbitMq:Host"] = connectionStrings.RabbitMqConnectionString,
            ["BloomFilterNotificationProducer:BootstrapServers"] = connectionStrings.KafkaBootstrapServers,
            ["HotelGateway:ConnectionString"] = connectionStrings.SqlServerConnectionString
        };
    }

    protected override void ConfigureServices(IServiceCollection services)
    {
        MockFlightCacheGateway(services);
        MockHotelCacheGateway(services);
        MockHotelStaticGateway(services);
    }

    private void MockFlightCacheGateway(IServiceCollection services)
    {
        services.AddHttpClient(ApiHttpClientConsts.FlightCacheClient)
            .ConfigurePrimaryHttpMessageHandler(() => new MockHttpMessageHandler(() => GetNextFlightCacheResponse()));
    }

    private void MockHotelCacheGateway(IServiceCollection services)
    {
        services.AddHttpClient(ApiHttpClientConsts.HotelCacheClient)
            .ConfigurePrimaryHttpMessageHandler(() => new MockHttpMessageHandler(() => GetNextHotelCacheResponse()));
    }

    private static void MockHotelStaticGateway(IServiceCollection services)
    {
        const string Path = "TestData.Gateways.HotelStatic.Response.json";
        var response = TestDataReader.ReadResource(Path);

        services.AddHttpClient(ApiHttpClientConsts.HotelStaticClient)
            .ConfigurePrimaryHttpMessageHandler(() => new MockHttpMessageHandler(response));
    }

    private string GetNextFlightCacheResponse()
    {
        var response = TestDataReader.ReadResource(_flightCacheResponsePaths[_flightCacheResponseIndex]);
        _flightCacheResponseIndex = (_flightCacheResponseIndex + 1) % _flightCacheResponsePaths.Length;

        return response;
    }

    private string GetNextHotelCacheResponse()
    {
        var response = TestDataReader.ReadResource(_hotelCacheResponsePaths[_hotelCacheResponseIndex]);
        _hotelCacheResponseIndex = (_hotelCacheResponseIndex + 1) % _hotelCacheResponsePaths.Length;

        return response;
    }
}