using Esky.Packages.Integration.Tests.Infrastructure;

namespace Esky.Packages.Integration.Tests.Services;

public class ServicesBootstrapper(InfrastructureBootstrapper infrastructure) : IAsyncDisposable
{
    private readonly InfrastructureBootstrapper _infrastructure = infrastructure;
    
    private HttpClient? _apiClient;
    private HttpClient? _generatorClient;
    private HttpClient? _flightVariantorClient;
    private HttpClient? _flightOplogReaderClient;
    private HttpClient? _repartitionerClient;
    private HttpClient? _repricerClient;
    private HttpClient? _priceTrackerClient;
    
    private ApiFactory? _apiFactory;

    public HttpClient ApiClient => _apiClient ?? throw new InvalidOperationException("ApiClient not initialized");
    public HttpClient GeneratorClient => _generatorClient ?? throw new InvalidOperationException("GeneratorClient not initialized");
    public HttpClient FlightVariantorClient => _flightVariantorClient ?? throw new InvalidOperationException("FlightVariantorClient not initialized");
    public HttpClient FlightOplogReaderClient => _flightOplogReaderClient ?? throw new InvalidOperationException("FlightOplogReaderClient not initialized");
    public HttpClient RepartitionerClient => _repartitionerClient ?? throw new InvalidOperationException("RepartitionerClient not initialized");
    public HttpClient RepricerClient => _repricerClient ?? throw new InvalidOperationException("RepricerClient not initialized");
    public HttpClient PriceTrackerClient => _priceTrackerClient ?? throw new InvalidOperationException("PriceTrackerClient not initialized");
    
    public ApiFactory ApiFactory => _apiFactory ?? throw new InvalidOperationException("ApiFactory not initialized");

    public Task InitializeAsync()
    {
        var connectionStrings = _infrastructure.GetConnectionStrings();

        _apiFactory = new ApiFactory(connectionStrings);
        var generatorFactory = new GeneratorFactory(connectionStrings);
        var flightVariantorFactory = new FlightVariantorFactory(connectionStrings);
        var flightOplogReaderFactory = new FlightOplogReaderFactory(connectionStrings);
        var repartitionerFactory = new RepartitionerFactory(connectionStrings);
        var repricerFactory = new RepricerFactory(connectionStrings);
        var priceTrackerFactory = new PriceTrackerFactory(connectionStrings);

        _apiClient = _apiFactory.CreateClient();
        _generatorClient = generatorFactory.CreateClient();
        _flightVariantorClient = flightVariantorFactory.CreateClient();
        _flightOplogReaderClient = flightOplogReaderFactory.CreateClient();
        _repartitionerClient = repartitionerFactory.CreateClient();
        _repricerClient = repricerFactory.CreateClient();
        _priceTrackerClient = priceTrackerFactory.CreateClient();

        return Task.CompletedTask;
    }

    public ValueTask DisposeAsync()
    {
        _apiClient?.Dispose();
        _generatorClient?.Dispose();
        _flightVariantorClient?.Dispose();
        _flightOplogReaderClient?.Dispose();
        _repartitionerClient?.Dispose();
        _repricerClient?.Dispose();
        _apiFactory?.Dispose();

        return ValueTask.CompletedTask;
    }
} 