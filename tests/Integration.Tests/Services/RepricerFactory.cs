using Esky.Packages.Integration.Tests.Infrastructure;

namespace Esky.Packages.Integration.Tests.Services;

public class RepricerFactory(ConnectionStrings connectionStrings) 
    : BaseServiceFactory<Repricer.Program>()
{
    protected override Dictionary<string, string> GetConfigurationValues()
    {
        return new Dictionary<string, string>
        {
            ["Mongo:ConnectionString"] = connectionStrings.MongoDbConnectionString,
            ["FlightQuoteConsumer:BootstrapServers"] = connectionStrings.KafkaBootstrapServers,
            ["HotelOfferQuoteConsumer:BootstrapServers"] = connectionStrings.KafkaBootstrapServers,
            ["BloomFilterNotificationProducer:BootstrapServers"] = connectionStrings.KafkaBootstrapServers
        };
    }
}