<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<RootNamespace>Esky.Packages.Integration.Tests</RootNamespace>
		<IsTestProject>true</IsTestProject>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="coverlet.collector" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" />
		<PackageReference Include="Microsoft.Data.SqlClient" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" />
		<PackageReference Include="MongoDB.Driver" />
		<PackageReference Include="NSubstitute" />
		<PackageReference Include="Shouldly" />
		<PackageReference Include="Testcontainers.Kafka" />
		<PackageReference Include="Testcontainers.MongoDb" />
		<PackageReference Include="Testcontainers.MsSql" />
		<PackageReference Include="Testcontainers.RabbitMq" />
		<PackageReference Include="xunit" />
		<PackageReference Include="xunit.runner.visualstudio" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\src\Api\Api.csproj" />
		<ProjectReference Include="..\..\src\FlightOplogReader\FlightOplogReader.csproj" />
		<ProjectReference Include="..\..\src\FlightVariantor\FlightVariantor.csproj" />
		<ProjectReference Include="..\..\src\Generator\Generator.csproj" />
		<ProjectReference Include="..\..\src\PriceTracker\PriceTracker.csproj" />
		<ProjectReference Include="..\..\src\Repartitioner\Repartitioner.csproj" />
		<ProjectReference Include="..\..\src\Repricer\Repricer.csproj" />
		<ProjectReference Include="..\Common.Tests\Common.Tests.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Using Include="Xunit" />
		<Using Include="NSubstitute" />
		<Using Include="Shouldly" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="TestData\**\*" />
	</ItemGroup>

</Project>
