using Esky.Packages.Integration.Tests.Infrastructure;
using System.Reflection;
using System.Text.Json;

namespace Esky.Packages.Integration.Tests.TestData;

public static class TestDataReader
{
    public static string ReadResource(string resourcePath)
    {
        var assembly = Assembly.GetExecutingAssembly();
        var resourceName = $"Esky.Packages.Integration.Tests.{resourcePath}";

        var availableResources = assembly.GetManifestResourceNames();
        if (!availableResources.Contains(resourceName))
        {
            throw new InvalidOperationException(
                $"Resource '{resourcePath}' not found in assembly 'Esky.Packages.Integration.Tests'. " +
                $"Available resources: {string.Join(", ", availableResources)}");
        }

        using var stream = assembly.GetManifestResourceStream(resourceName)
            ?? throw new InvalidOperationException($"Failed to open stream for resource '{resourcePath}'");

        using var reader = new StreamReader(stream);
        return reader.ReadToEnd();
    }

    public static T? ReadResource<T>(string resourcePath)
    {
        var stringContent = ReadResource(resourcePath);

        return JsonSerializer.Deserialize<T>(stringContent, InfrastructureBootstrapper.SerializerOptions);
    }
}
