using Esky.Packages.Contract.Calendars;
using Esky.Packages.Contract.Packages;
using Esky.Packages.Contract.PackageVariants;
using Esky.Packages.Contract.Search;
using Esky.Packages.Domain.Types.Extensions;
using Esky.Packages.Integration.Tests.Infrastructure;
using Esky.Packages.Integration.Tests.Requests;
using Esky.Packages.Integration.Tests.Services;
using Esky.Packages.Integration.Tests.TestData;

namespace Esky.Packages.Integration.Tests;

public class IntegrationTests : IAsyncLifetime
{
    private static readonly TimeSpan DefaultTimeout = TimeSpan.FromSeconds(60);
    private readonly InfrastructureBootstrapper _infrastructure;
    private readonly ServicesBootstrapper _services;

    public IntegrationTests()
    {
        if (TimeZoneInfo.Local.BaseUtcOffset != TimeSpan.Zero)
        {
            // On Windows: Set-TimeZone -Id "UTC"  and restore with: Set-TimeZone -Id "Central European Standard Time"
            throw new InvalidOperationException("Integration tests must run in UTC timezone.");
        }

        _infrastructure = new InfrastructureBootstrapper();
        _services = new ServicesBootstrapper(_infrastructure);
    }

    public async Task InitializeAsync()
    {
        await _infrastructure.InitializeAsync();
        await _services.InitializeAsync();
    }

    public async Task DisposeAsync()
    {
        await _services.DisposeAsync();
        await _infrastructure.DisposeAsync();
    }

    [Fact]
    public async Task TestEndToEnd()
    {
        _ = await MarketsRequests.AddMarket(_services.ApiClient);
        _ = await PackageDefinitionsRequests.AddPackageDefinition(_services.ApiClient);
        _ = await PackageGenerationsRequests.EnqueuePackageGenerations(_services.ApiClient);

        // Package after generation
        var package = await PackagesRequests.WaitForPackage(
            httpClient: _services.ApiClient,
            predicate: package => package is not null &&
                       package.FlightOffers.Length > 0 &&
                       package.HotelOffers.Length > 0,
            timeout: DefaultTimeout);
        var expectedPackage = TestDataReader.ReadResource<PackageDto>("TestData.Results.Package.json");
        package.ShouldBeEquivalentTo(expectedPackage);

        // Calendars
        var calendarsResponse = await CalendarsRequests.Calendars(_services.ApiClient);
        var expectedCalendarsResponse = TestDataReader.ReadResource<CalendarsDto>("TestData.Results.Calendars.json")!;
        CompareCalendars(calendarsResponse, expectedCalendarsResponse);

        // Search
        var searchResponse = await SearchRequests.Search(_services.ApiClient);
        var expectedSearchResponse = TestDataReader.ReadResource<SearchDto>("TestData.Results.Search.json");
        searchResponse.ShouldBeEquivalentTo(expectedSearchResponse);

        // Package variant price history
        _services.ApiFactory.ChangeCurrentDayToTomorrow();
        _ = await SearchRequests.Search(_services.ApiClient); // Search with new date should create new price history point
        var packagePriceHistory = await PackagesRequests.WaitForGetPackagePriceHistory(_services.ApiClient, searchResponse!.Variants.First().PackageVariantId, DefaultTimeout);
        var expectedPackagePriceHistoryResponse = TestDataReader.ReadResource<PackageVariantsPriceHistoryResponseDto>("TestData.Results.PackagePriceHistory.json");
        packagePriceHistory.ShouldBeEquivalentTo(expectedPackagePriceHistoryResponse);

        // Package live variants
        var packageLiveVariants = await PackagesRequests.GetPackageLiveVariants(_services.ApiClient);
        var expectedPackageLiveVariants = TestDataReader.ReadResource<PackageLiveVariantsDto>("TestData.Results.PackageLiveVariants.json");
        packageLiveVariants.ShouldBeEquivalentTo(expectedPackageLiveVariants);

        // Package after hotel and flight price changes
        _infrastructure.Kafka.PublishEvent("flightQuotes", KafkaEvents.FlightsQuotes.PriceChanges());
        _infrastructure.Kafka.PublishEvent("hotelOfferCacheQuotes", KafkaEvents.HotelOfferQuote.PriceChanges());

        package = await PackagesRequests.WaitForPackage(
            httpClient: _services.ApiClient,
            predicate: package => package is not null &&
                                  package.FlightOffers.First().Prices.First(p => p.Occupancy is { Adults: 2, ChildrenAges.Length: 0 }).Price.Price is 3m &&
                       package.HotelOffers.First().Prices.First().Value is 3,
            timeout: DefaultTimeout);
        expectedPackage = TestDataReader.ReadResource<PackageDto>("TestData.Results.Package_PriceChanges.json");
        package.ShouldBeEquivalentTo(expectedPackage);

        // Package after hotel and flight unavailability should not exists anymore
        _infrastructure.Kafka.PublishEvent("flightQuotes", KafkaEvents.FlightsQuotes.SoldOuts());
        _infrastructure.Kafka.PublishEvent("hotelOfferCacheQuotes", KafkaEvents.HotelOfferQuote.SoldOuts());

        package = await PackagesRequests.WaitForPackage(
            httpClient: _services.ApiClient,
            predicate: package => package is null,
            timeout: DefaultTimeout);
        package.ShouldBeNull();
    }

    private static void CompareCalendars(CalendarsDto calendarsResponse, CalendarsDto expectedCalendarsResponse)
    {
        // Shoudly issues: use DeepEquals for nested dictionary comparison by values, not references
        calendarsResponse.Calendars.Count.ShouldBe(expectedCalendarsResponse!.Calendars.Count);

        for (int i = 0; i < calendarsResponse.Calendars.Count; i++)
        {
            var actual = calendarsResponse.Calendars[i];
            var expected = expectedCalendarsResponse.Calendars[i];

            actual.Year.ShouldBe(expected.Year);
            actual.Month.ShouldBe(expected.Month);

            actual.Days.DeepEquals(expected.Days).ShouldBeTrue();
        }
    }
}