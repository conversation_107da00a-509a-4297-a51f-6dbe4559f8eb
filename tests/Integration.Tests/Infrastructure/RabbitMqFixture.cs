using Testcontainers.RabbitMq;

namespace Esky.Packages.Integration.Tests.Infrastructure;

public class RabbitMqFixture : IAsyncDisposable
{
    public RabbitMqContainer RabbitMqContainer { get; private set; }

    public RabbitMqFixture()
    {
        RabbitMqContainer = new RabbitMqBuilder()
            .WithImage("rabbitmq:management")
            .WithPortBinding(5672, true)   // AMQP port
            .WithPortBinding(15672, true)  // Management UI port
            .WithEnvironment("RABBITMQ_DEFAULT_USER", "rabbitmq")
            .WithEnvironment("RABBITMQ_DEFAULT_PASS", "rabbitmq")
            .Build();
    }

    public async Task InitializeAsync()
    {
        await RabbitMqContainer.StartAsync();
    }

    public string GetConnectionString()
    {
        return RabbitMqContainer.GetConnectionString();
    }

    public async ValueTask DisposeAsync()
    {
        await RabbitMqContainer.DisposeAsync();
    }
}
