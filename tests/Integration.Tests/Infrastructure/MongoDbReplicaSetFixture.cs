using DotNet.Testcontainers.Containers;
using Testcontainers.MongoDb;

namespace Esky.Packages.Integration.Tests.Infrastructure;

public class MongoDbReplicaSetFixture : IAsyncDisposable
{
    private const string _databaseName = "esky-packages";

    public MongoDbContainer Database { get; private set; }

    public MongoDbReplicaSetFixture()
    {
        const string mongoInitScript =
             $$"""
             mongosh <<EOF
             rs.initiate();
             while(!rs.isMaster().ismaster) sleep(500);
             use {{_databaseName}};
             db.createCollection("packages", { changeStreamPreAndPostImages: { enabled: true } });
             db.createCollection("packageFlights", { changeStreamPreAndPostImages: { enabled: true } });
             db.createCollection("packageHotelOffers", { changeStreamPreAndPostImages: { enabled: true } });
             """;

        Database = new MongoDbBuilder()
            .WithImage("mongo:latest")
            .WithPortBinding(27017, true)
            .WithUsername(null)
            .WithPassword(null)
            .WithCommand("--replSet", "rs0", "--bind_ip_all")
            .WithStartupCallback(async (container, cancellationToken) =>
            {
                ExecResult result;
                do
                {
                    Console.WriteLine("[Mongo] initialization script executing");
                    result = await container.ExecAsync(["/bin/sh", "-c", mongoInitScript], cancellationToken);
                    Console.WriteLine($"[Mongo] initialization script exit code: {result.ExitCode}");
                } while (result.ExitCode != 0);
            })
            .Build();
    }

    public async Task InitializeAsync()
    {
        await Database.StartAsync();
    }

    public string GetConnectionString()
    {
        return new UriBuilder(Database.GetConnectionString()) 
        { 
            Path = _databaseName 
        }.ToString();
    }

    public async ValueTask DisposeAsync()
    {
        await Database.DisposeAsync();
    }
}
