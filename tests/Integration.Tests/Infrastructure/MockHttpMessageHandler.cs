using System.Net;
using System.Text;

namespace Esky.Packages.Integration.Tests.Infrastructure;

public class MockHttpMessageHandler : HttpMessageHandler
{
    private readonly Func<string>? _getNextResponse;
    private readonly string? _staticResponse;

    private MockHttpMessageHandler()
    {
    }

    public MockHttpMessageHandler(string staticResponse)
    {
        _staticResponse = staticResponse;
    }

    public MockHttpMessageHandler(Func<string> getNextResponse)
    {
        _getNextResponse = getNextResponse;
    }

    protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        var responseContent = _getNextResponse?.Invoke() ?? _staticResponse ?? string.Empty;

        var response = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
        };

        return Task.FromResult(response);
    }
}