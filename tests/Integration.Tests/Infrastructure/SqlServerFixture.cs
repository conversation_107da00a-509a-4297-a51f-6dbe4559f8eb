using Testcontainers.MsSql;
using Microsoft.Data.SqlClient;
using System.Text.Json;
using Esky.Packages.Integration.Tests.TestData;
using DotNet.Testcontainers.Builders;

namespace Esky.Packages.Integration.Tests.Infrastructure;

public class SqlServerFixture : IAsyncDisposable
{
    public MsSqlContainer SqlContainer { get; private set; }

    public SqlServerFixture()
    {
        SqlContainer = new MsSqlBuilder()
            .WithPassword("Strong_password_123!")
            .Build();
    }

    public async Task InitializeAsync()
    {
        await SqlContainer.StartAsync();
        await InitializeDatabase();
    }

    private async Task InitializeDatabase()
    {
        using var internalConnection = new SqlConnection(GetConnectionStringInternal());
        await internalConnection.OpenAsync();

        using (var command = internalConnection.CreateCommand())
        {
            command.CommandText = @"
                IF NOT EXISTS (SELECT * FROM sys.databases WHERE name = 'HotelDb')
                BEGIN
                    CREATE DATABASE HotelDb;
                END";
            await command.ExecuteNonQueryAsync();
        }

        await internalConnection.CloseAsync();

        var hotelDbConnectionString = GetConnectionString();
        using var hotelDbConnection = new SqlConnection(hotelDbConnectionString);
        await hotelDbConnection.OpenAsync();

        await CreateTables(hotelDbConnection);
        
        await InsertHotelsData(hotelDbConnection);
        await InsertCountriesData(hotelDbConnection);
        await InsertRegionsData(hotelDbConnection);
        await InsertTagsData(hotelDbConnection);
        
        await CreateViews(hotelDbConnection);
    }
    
    private async Task CreateTables(SqlConnection connection)
    {
        // Create Hotels table
        using (var command = connection.CreateCommand())
        {
            command.CommandText = @"
                IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Hotels')
                BEGIN
                    CREATE TABLE Hotels (
                        MetaCode INT PRIMARY KEY,
                        Name NVARCHAR(255) NOT NULL,
                        CityCode NVARCHAR(5) NOT NULL,
                        CountryCode NVARCHAR(5) NOT NULL,
                        CheckInOut NVARCHAR(100) DEFAULT '14:00/11:00'
                    );
                END";
            await command.ExecuteNonQueryAsync();
        }
        
        // Create Countries table
        using (var command = connection.CreateCommand())
        {
            command.CommandText = @"
                IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Countries')
                BEGIN
                    CREATE TABLE Countries (
                        Code NVARCHAR(5) PRIMARY KEY,
                        Name NVARCHAR(255) NOT NULL,
                        ContinentCode NVARCHAR(5) NOT NULL
                    );
                END";
            await command.ExecuteNonQueryAsync();
        }
        
        // Create MetaHotelRegion table
        using (var command = connection.CreateCommand())
        {
            command.CommandText = @"
                IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'MetaHotelRegion')
                BEGIN
                    CREATE TABLE MetaHotelRegion (
                        MetaCode INT NOT NULL,
                        RegionCode NVARCHAR(50) NOT NULL,
                        PRIMARY KEY (MetaCode, RegionCode)
                    );
                END";
            await command.ExecuteNonQueryAsync();
        }
        
        // Create MetaHotelTags table
        using (var command = connection.CreateCommand())
        {
            command.CommandText = @"
                IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'MetaHotelTags')
                BEGIN
                    CREATE TABLE MetaHotelTags (
                        Id INT IDENTITY(1,1) PRIMARY KEY,
                        TagName NVARCHAR(100) NOT NULL
                    );
                END";
            await command.ExecuteNonQueryAsync();
        }
        
        // Create MetaHotelTagsMetaHotelsMetadata linking table
        using (var command = connection.CreateCommand())
        {
            command.CommandText = @"
                IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'MetaHotelTagsMetaHotelsMetadata')
                BEGIN
                    CREATE TABLE MetaHotelTagsMetaHotelsMetadata (
                        MetaCode INT NOT NULL,
                        TagId INT NOT NULL,
                        PRIMARY KEY (MetaCode, TagId)
                    );
                END";
            await command.ExecuteNonQueryAsync();
        }
    }
    
    private async Task CreateViews(SqlConnection connection)
    {
        // Create ActiveHotelsMetadataView (the view referenced in SqlHotelSelectorQueryTranslator)
        using (var command = connection.CreateCommand())
        {
            command.CommandText = @"
                IF NOT EXISTS (SELECT * FROM sys.views WHERE name = 'ActiveHotelsMetadataView')
                BEGIN
                    EXEC('
                        CREATE VIEW dbo.ActiveHotelsMetadataView AS
                        SELECT 
                            h.MetaCode, 
                            h.Name, 
                            h.CityCode, 
                            h.CountryCode, 
                            h.CheckInOut
                        FROM 
                            dbo.Hotels h
                    ')
                END";
            await command.ExecuteNonQueryAsync();
        }
    }
    
    private async Task InsertHotelsData(SqlConnection connection)
    {
        var hotelData = JsonSerializer.Deserialize<List<Hotel>>(
            TestDataReader.ReadResource("TestData.Gateways.HotelGateway.HotelSearch.json"),
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

        if (hotelData != null)
        {
            foreach (var hotel in hotelData)
            {
                using var command = connection.CreateCommand();
                command.CommandText = @"
                    IF NOT EXISTS (SELECT 1 FROM Hotels WHERE MetaCode = @MetaCode)
                    BEGIN
                        INSERT INTO Hotels (MetaCode, Name, CityCode, CountryCode, CheckInOut)
                        VALUES (@MetaCode, @Name, @CityCode, @CountryCode, @CheckInOut)
                    END";

                command.Parameters.AddWithValue("@MetaCode", hotel.MetaCode);
                command.Parameters.AddWithValue("@Name", hotel.Name);
                command.Parameters.AddWithValue("@CityCode", hotel.CityCode);
                command.Parameters.AddWithValue("@CountryCode", hotel.CountryCode);
                command.Parameters.AddWithValue("@CheckInOut", "14:00/11:00"); // Default check-in/check-out

                await command.ExecuteNonQueryAsync();
            }
        }
    }
    
    private async Task InsertCountriesData(SqlConnection connection)
    {
        var countries = new List<(string Code, string Name, string ContinentCode)>
        {
            ("ES", "Spain", "EU"),
            ("PT", "Portugal", "EU"),
            ("IT", "Italy", "EU"),
            ("GR", "Greece", "EU"),
            ("TR", "Turkey", "AS"),
            ("EG", "Egypt", "AF")
        };
        
        foreach (var country in countries)
        {
            using var command = connection.CreateCommand();
            command.CommandText = @"
                IF NOT EXISTS (SELECT 1 FROM Countries WHERE Code = @Code)
                BEGIN
                    INSERT INTO Countries (Code, Name, ContinentCode)
                    VALUES (@Code, @Name, @ContinentCode)
                END";

            command.Parameters.AddWithValue("@Code", country.Code);
            command.Parameters.AddWithValue("@Name", country.Name);
            command.Parameters.AddWithValue("@ContinentCode", country.ContinentCode);

            await command.ExecuteNonQueryAsync();
        }
    }
    
    private async Task InsertRegionsData(SqlConnection connection)
    {
        var regions = new List<(int MetaCode, string RegionCode)>
        {
            (127416, "BALEARIC"),  // Mallorca hotel
            (626617, "BALEARIC"),  // Mallorca hotel
            (846838, "CATALONIA"), // Barcelona hotel
            (3713392, "CANARY"),   // Tenerife hotel
            (4654964, "CANARY")    // Tenerife South hotel
        };
        
        foreach (var region in regions)
        {
            using var command = connection.CreateCommand();
            command.CommandText = @"
                IF NOT EXISTS (SELECT 1 FROM MetaHotelRegion WHERE MetaCode = @MetaCode AND RegionCode = @RegionCode)
                BEGIN
                    INSERT INTO MetaHotelRegion (MetaCode, RegionCode)
                    VALUES (@MetaCode, @RegionCode)
                END";

            command.Parameters.AddWithValue("@MetaCode", region.MetaCode);
            command.Parameters.AddWithValue("@RegionCode", region.RegionCode);

            await command.ExecuteNonQueryAsync();
        }
    }
    
    private async Task InsertTagsData(SqlConnection connection)
    {
        var tags = new[] { "Beach", "Family", "Luxury", "AllInclusive", "Popular" };
        
        foreach (var tag in tags)
        {
            using var command = connection.CreateCommand();
            command.CommandText = @"
                IF NOT EXISTS (SELECT 1 FROM MetaHotelTags WHERE TagName = @TagName)
                BEGIN
                    INSERT INTO MetaHotelTags (TagName)
                    VALUES (@TagName)
                END";

            command.Parameters.AddWithValue("@TagName", tag);
            await command.ExecuteNonQueryAsync();
        }
        
        var hotelTags = new List<(int MetaCode, string[] Tags)>
        {
            (127416, new[] { "Beach", "Family", "Popular" }),
            (626617, new[] { "Beach", "Luxury" }),
            (846838, new[] { "City", "Luxury", "Popular" }),
            (3713392, new[] { "Beach", "AllInclusive", "Popular" }),
            (4654964, new[] { "Beach", "Family" })
        };
        
        foreach (var hotelTag in hotelTags)
        {
            foreach (var tag in hotelTag.Tags)
            {
                int tagId;
                using (var getTagIdCmd = connection.CreateCommand())
                {
                    getTagIdCmd.CommandText = "SELECT Id FROM MetaHotelTags WHERE TagName = @TagName";
                    getTagIdCmd.Parameters.AddWithValue("@TagName", tag);
                    var result = await getTagIdCmd.ExecuteScalarAsync();
                    
                    if (result == null || result == DBNull.Value)
                        continue;
                    
                    tagId = (int)result;
                }
                
                using var linkCmd = connection.CreateCommand();
                linkCmd.CommandText = @"
                    IF NOT EXISTS (SELECT 1 FROM MetaHotelTagsMetaHotelsMetadata WHERE MetaCode = @MetaCode AND TagId = @TagId)
                    BEGIN
                        INSERT INTO MetaHotelTagsMetaHotelsMetadata (MetaCode, TagId)
                        VALUES (@MetaCode, @TagId)
                    END";
                
                linkCmd.Parameters.AddWithValue("@MetaCode", hotelTag.MetaCode);
                linkCmd.Parameters.AddWithValue("@TagId", tagId);
                
                await linkCmd.ExecuteNonQueryAsync();
            }
        }
    }

    public string GetConnectionString()
    {
        var connectionString = SqlContainer.GetConnectionString();
        
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(connectionString);
        
        builder.InitialCatalog = "HotelDb";
        builder.UserID = "sa";
        builder.Password = "Strong_password_123!";
        builder.TrustServerCertificate = true;
        builder.Encrypt = false;
        
        return builder.ConnectionString;
    }

    private string GetConnectionStringInternal()
    {
        var connectionString = SqlContainer.GetConnectionString();
        
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(connectionString);
        
        builder.UserID = "sa";
        builder.Password = "Strong_password_123!";
        builder.TrustServerCertificate = true;
        builder.Encrypt = false;
        
        return builder.ConnectionString;
    }

    public async ValueTask DisposeAsync()
    {
        await SqlContainer.DisposeAsync();
    }

    private class Hotel
    {
        public int MetaCode { get; set; }
        public string Name { get; set; } = string.Empty;
        public string CityCode { get; set; } = string.Empty;
        public string CountryCode { get; set; } = string.Empty;
    }
} 