using Esky.Packages.Domain.Types;
using Esky.Packages.Infrastructure.Database.Serializers;
using MongoDB.Bson.IO;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Integration.Tests.Serializators;

public class FlightNumbersSerializerTests
{
    private readonly FlightNumbersSerializer _serializer;

    public FlightNumbersSerializerTests()
    {
        _serializer = new FlightNumbersSerializer();
    }

    [Fact]
    public void Serialize_ValidFlightNumbers_ShouldWriteAsString()
    {
        // Arrange
        var flightNumbers = new FlightNumbers(["AA123", "BA4567", "LH890"]);
        using var stream = new MemoryStream();
        using var writer = new BsonBinaryWriter(stream);

        // Write as part of a document field
        writer.WriteStartDocument();
        writer.WriteName("flightNumbers");
        var context = BsonSerializationContext.CreateRoot(writer);

        // Act
        _serializer.Serialize(context, default, flightNumbers);

        // Assert
        writer.WriteEndDocument();
        stream.Position = 0;

        using var reader = new BsonBinaryReader(stream);
        reader.ReadStartDocument();
        reader.ReadName();
        var result = reader.ReadString();
        Assert.Equal("AA123,BA4567,LH890", result);
    }

    [Fact]
    public void Deserialize_FromStringFormat_ShouldReturnFlightNumbers()
    {
        // Arrange
        const string input = "AA123,BA4567,LH890";
        using var stream = new MemoryStream();
        using var writer = new BsonBinaryWriter(stream);

        // Write as part of a document field
        writer.WriteStartDocument();
        writer.WriteName("flightNumbers");
        writer.WriteString(input);
        writer.WriteEndDocument();

        stream.Position = 0;
        using var reader = new BsonBinaryReader(stream);
        reader.ReadStartDocument();
        reader.ReadName();
        var context = BsonDeserializationContext.CreateRoot(reader);

        // Act
        var result = _serializer.Deserialize(context, default);

        // Assert
        Assert.Equal(new FlightNumbers(["AA123", "BA4567", "LH890"]), result);
    }

    [Fact]
    public void Deserialize_FromArrayFormat_ShouldReturnFlightNumbers()
    {
        // Arrange
        var flightNumbersArray = new[] { "AA123", "BA4567", "LH890" };
        using var stream = new MemoryStream();
        using var writer = new BsonBinaryWriter(stream);

        // Write as part of a document field
        writer.WriteStartDocument();
        writer.WriteName("flightNumbers");
        writer.WriteStartArray();
        foreach (var flightNumber in flightNumbersArray)
        {
            writer.WriteString(flightNumber);
        }
        writer.WriteEndArray();
        writer.WriteEndDocument();

        stream.Position = 0;
        using var reader = new BsonBinaryReader(stream);
        reader.ReadStartDocument();
        reader.ReadName();
        var context = BsonDeserializationContext.CreateRoot(reader);

        // Act
        var result = _serializer.Deserialize(context, default);

        // Assert
        Assert.Equal(new FlightNumbers(["AA123", "BA4567", "LH890"]), result);
    }

    [Fact]
    public void Deserialize_FromEmptyArray_ShouldReturnEmptyFlightNumbers()
    {
        // Arrange
        using var stream = new MemoryStream();
        using var writer = new BsonBinaryWriter(stream);

        // Write as part of a document field
        writer.WriteStartDocument();
        writer.WriteName("flightNumbers");
        writer.WriteStartArray();
        writer.WriteEndArray();
        writer.WriteEndDocument();

        stream.Position = 0;
        using var reader = new BsonBinaryReader(stream);
        reader.ReadStartDocument();
        reader.ReadName();
        var context = BsonDeserializationContext.CreateRoot(reader);

        // Act
        var result = _serializer.Deserialize(context, default);

        Assert.Equal("", result.ToString());
    }

    [Fact]
    public void Deserialize_FromInvalidFlightNumberInArray_ShouldThrowArgumentException()
    {
        // Arrange
        var flightNumbersArray = new[] { "AA123", "INVALID", "LH890" };
        using var stream = new MemoryStream();
        using var writer = new BsonBinaryWriter(stream);

        // Write as part of a document field
        writer.WriteStartDocument();
        writer.WriteName("flightNumbers");
        writer.WriteStartArray();
        foreach (var flightNumber in flightNumbersArray)
        {
            writer.WriteString(flightNumber);
        }
        writer.WriteEndArray();
        writer.WriteEndDocument();

        stream.Position = 0;
        using var reader = new BsonBinaryReader(stream);
        reader.ReadStartDocument();
        reader.ReadName();
        var context = BsonDeserializationContext.CreateRoot(reader);

        // Act & Assert
        Assert.Throws<ArgumentException>(() => _serializer.Deserialize(context, default));
    }

    [Fact]
    public void Deserialize_FromInvalidStringFormat_ShouldThrowArgumentException()
    {
        // Arrange
        const string input = "AA123,INVALID,LH890";
        using var stream = new MemoryStream();
        using var writer = new BsonBinaryWriter(stream);

        // Write as part of a document field
        writer.WriteStartDocument();
        writer.WriteName("flightNumbers");
        writer.WriteString(input);
        writer.WriteEndDocument();

        stream.Position = 0;
        using var reader = new BsonBinaryReader(stream);
        reader.ReadStartDocument();
        reader.ReadName();
        var context = BsonDeserializationContext.CreateRoot(reader);

        // Act & Assert
        Assert.Throws<ArgumentException>(() => _serializer.Deserialize(context, default));
    }

    [Theory]
    [InlineData("AA123")]
    [InlineData("AA123,BA4567")]
    [InlineData("AA123,BA4567,LH890,FR1234")]
    public void SerializeDeserialize_RoundTrip_ShouldPreserveData(string flightNumbersString)
    {
        // Arrange
        var originalFlightNumbers = FlightNumbers.Parse(flightNumbersString, null);

        // Serialize
        using var serializeStream = new MemoryStream();
        using var writer = new BsonBinaryWriter(serializeStream);

        writer.WriteStartDocument();
        writer.WriteName("flightNumbers");
        var serializeContext = BsonSerializationContext.CreateRoot(writer);
        _serializer.Serialize(serializeContext, default, originalFlightNumbers);
        writer.WriteEndDocument();

        // Deserialize
        serializeStream.Position = 0;
        using var reader = new BsonBinaryReader(serializeStream);
        reader.ReadStartDocument();
        reader.ReadName();
        var deserializeContext = BsonDeserializationContext.CreateRoot(reader);
        var deserializedFlightNumbers = _serializer.Deserialize(deserializeContext, default);

        // Assert
        Assert.Equal(originalFlightNumbers.ToString(), deserializedFlightNumbers.ToString());
        Assert.Equal(originalFlightNumbers, deserializedFlightNumbers);
    }
}