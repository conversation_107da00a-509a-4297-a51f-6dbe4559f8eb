using Esky.Packages.Domain.Types;
using Esky.Packages.Infrastructure.Serialization.Converters;
using System.Text.Json;

namespace Esky.Packages.Integration.Tests.Serializators;

public class FlightNumbersJsonConverterTests
{
    private readonly JsonSerializerOptions _options;

    public FlightNumbersJsonConverterTests()
    {
        _options = new JsonSerializerOptions();
        _options.Converters.Add(new FlightNumbersJsonConverter());
    }

    [Fact]
    public void Serialize_ValidFlightNumbers_ShouldWriteAsString()
    {
        // Arrange
        var flightNumbers = new FlightNumbers(["AA123", "BA4567", "LH890"]);

        // Act
        var json = JsonSerializer.Serialize(flightNumbers, _options);

        // Assert
        Assert.Equal("\"AA123,BA4567,LH890\"", json);
    }

    [Fact]
    public void Deserialize_FromStringFormat_ShouldReturnFlightNumbers()
    {
        // Arrange
        const string json = "\"AA123,BA4567,LH890\"";

        // Act
        var result = JsonSerializer.Deserialize<FlightNumbers>(json, _options);

        // Assert
        Assert.Equal("AA123,BA4567,LH890", result.ToString());
    }

    [Fact]
    public void Deserialize_FromArrayFormat_ShouldReturnFlightNumbers()
    {
        // Arrange
        const string json = "[\"AA123\", \"BA4567\", \"LH890\"]";

        // Act
        var result = JsonSerializer.Deserialize<FlightNumbers>(json, _options);

        // Assert
        Assert.Equal("AA123,BA4567,LH890", result.ToString());
    }

    [Fact]
    public void Deserialize_FromEmptyArray_ShouldReturnEmptyFlightNumbers()
    {
        // Arrange
        const string json = "[]";

        // Act
        var result = JsonSerializer.Deserialize<FlightNumbers>(json, _options);

        Assert.Equal("", result.ToString());
    }

    [Fact]
    public void Deserialize_FromInvalidFlightNumberInArray_ShouldThrowArgumentException()
    {
        // Arrange
        const string json = "[\"AA123\", \"INVALID\", \"LH890\"]";

        // Act & Assert
        Assert.Throws<ArgumentException>(() => JsonSerializer.Deserialize<FlightNumbers>(json, _options));
    }

    [Fact]
    public void Deserialize_FromInvalidStringFormat_ShouldThrowArgumentException()
    {
        // Arrange
        const string json = "\"AA123,INVALID,LH890\"";

        // Act & Assert
        Assert.Throws<ArgumentException>(() => JsonSerializer.Deserialize<FlightNumbers>(json, _options));
    }

    [Theory]
    [InlineData("AA123")]
    [InlineData("AA123,BA4567")]
    [InlineData("AA123,BA4567,LH890,FR1234")]
    public void SerializeDeserialize_RoundTrip_ShouldPreserveData(string flightNumbersString)
    {
        // Arrange
        var originalFlightNumbers = FlightNumbers.Parse(flightNumbersString, null);

        // Act - Serialize then deserialize
        var json = JsonSerializer.Serialize(originalFlightNumbers, _options);
        var deserializedFlightNumbers = JsonSerializer.Deserialize<FlightNumbers>(json, _options);

        // Assert
        Assert.Equal(originalFlightNumbers.ToString(), deserializedFlightNumbers.ToString());
        Assert.Equal(originalFlightNumbers, deserializedFlightNumbers);
    }

    [Fact]
    public void Deserialize_LegacyAndNewFormat_ShouldProduceSameResult()
    {
        // Arrange
        const string legacyJson = "[\"AA123\", \"BA4567\", \"LH890\"]";
        const string newJson = "\"AA123,BA4567,LH890\"";

        // Act
        var legacyResult = JsonSerializer.Deserialize<FlightNumbers>(legacyJson, _options);
        var newResult = JsonSerializer.Deserialize<FlightNumbers>(newJson, _options);

        // Assert
        Assert.Equal(legacyResult.ToString(), newResult.ToString());
        Assert.Equal(legacyResult, newResult);
    }
}