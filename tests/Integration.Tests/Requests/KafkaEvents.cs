using Esky.Packages.Repricer.Events.Flights;
using static Esky.Packages.Integration.Tests.Infrastructure.KafkaFixture;

namespace Esky.Packages.Integration.Tests.Requests;

public static class KafkaEvents
{
    public static class FlightsQuotes
    {
        public static List<KafkaEvent> PriceChanges()
        {
            return
            [
                new KafkaEvent("LGWTFN250808145.4||VY6021|VY3208", FlightQuoteEvent(price: 1m)),
                new KafkaEvent("TFNLGW25081158.391||UX9117|UX1013", FlightQuoteEvent(price: 2m))
            ];
        }

        public static List<KafkaEvent> SoldOuts()
        {
            return
            [
                new KafkaEvent("LGWREU250808250815137.350||BY4524||BY4525", FlightQuoteEvent(price: null))
            ];
        }

        private static FlightQuoteEvent FlightQuoteEvent(decimal? price)
        {
            return new FlightQuoteEvent
            {
                RefreshDate = DateTime.UtcNow,
                Currency = "PLN",
                PaxConfigurations = new Dictionary<string, PaxPrice?>
                {
                    {
                        "2.0.0.0", !price.HasValue ? null : new PaxPrice { TotalPrice = price.Value }
                    }
                }
            };
        }
    }

    public static class HotelOfferQuote
    {
        public static KafkaEvent PriceChanges()
        {
            return new KafkaEvent("250808:3:3713392:12|epaeac:A2", HotelOfferQuoteEvent(price: 3m));
        }

        public static KafkaEvent SoldOuts()
        {
            return new KafkaEvent("250808:3:3713392:12|epaeac:A2", HotelOfferQuoteEvent(price: null));
        }

        private static HotelOfferQuoteEvent HotelOfferQuoteEvent(decimal? price)
        {
            return new HotelOfferQuoteEvent
            {
                Id = "250808:3:3713392:12|epaeac:A2",
                CheckIn = new DateOnly(2025, 8, 8),
                MetaCode = 3713392,
                StayLength = 3,
                ProviderConfigurationId = "12|epaeac",
                Occupancy = "A2",
                RoomOffersByMealPlanByRefundability = !price.HasValue ? [] : new Dictionary<string, Dictionary<string, HotelOfferQuoteEvent.HotelOfferQuoteEventRoomOffer[]>>
                {
                    {
                        "None", new Dictionary<string, HotelOfferQuoteEvent.HotelOfferQuoteEventRoomOffer[]>
                        {
                            {
                                "Refundable", [
                                    new HotelOfferQuoteEvent.HotelOfferQuoteEventRoomOffer
                                    {
                                        MetaRoomIds = [ "123", "789" ],
                                        Availability = 1,
                                        Price = new HotelOfferQuoteEvent.HotelOfferQuoteEventMoney { Value = price.Value, Currency = "PLN" }
                                    }
                                ]
                            }
                        }
                    },
                    {
                        "Breakfast", new Dictionary<string, HotelOfferQuoteEvent.HotelOfferQuoteEventRoomOffer[]>
                        {
                            {
                                "Refundable", [
                                    new HotelOfferQuoteEvent.HotelOfferQuoteEventRoomOffer
                                    {
                                        MetaRoomIds = [ "456" ],
                                        Availability = 1,
                                        Price = new HotelOfferQuoteEvent.HotelOfferQuoteEventMoney { Value = price.Value, Currency = "PLN" }
                                    }
                                ]
                            }
                        }
                    }
                }
            };
        }
    }
}