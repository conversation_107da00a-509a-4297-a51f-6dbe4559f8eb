using Esky.Packages.Contract.Calendars;
using Esky.Packages.Contract.Common;
using Esky.Packages.Domain.Model.Markets;
using Esky.Packages.Integration.Tests.Infrastructure;
using System.Text;
using System.Text.Json;

namespace Esky.Packages.Integration.Tests.Requests;

public static class CalendarsRequests
{
    public static async Task<CalendarsDto> Calendars(HttpClient httpClient)
    {
        var calendarQuery = new GetCalendarQuery
        (
            MarketId: "pl",
            MetaCode: 3713392,
            DepartureDateFrom: new DateOnly(2025, 8, 8),
            DepartureDateTo: new DateOnly(2025, 8, 8),
            DepartureAirports: ["LHR", "LGW"],
            MealPlans: [MealPlanDto.None, MealPlanDto.Breakfast, MealPlanDto.HalfBoard, MealPlanDto.FullBoard, MealPlanDto.AllInclusive],
            Occupancies: [new OccupancyDto(Adults: 2, ChildrenAges: [])],
            Occupancy: null,
            InboundDepartures: null,
            OutboundDepartures: null
        );

        var response = await httpClient.PostAsync(
            "/api/calendars/",
            new StringContent(JsonSerializer.Serialize(calendarQuery, InfrastructureBootstrapper.SerializerOptions),
                Encoding.UTF8, "application/json"));

        var stringContent = await response.Content.ReadAsStringAsync();

        return JsonSerializer.Deserialize<CalendarsDto>(stringContent, InfrastructureBootstrapper.SerializerOptions)!;
    }
}
