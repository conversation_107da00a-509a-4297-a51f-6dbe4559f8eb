using Esky.Packages.Contract.Packages;
using Esky.Packages.Contract.PackageVariants;
using Esky.Packages.Integration.Tests.Infrastructure;
using System.Text;
using System.Text.Json;

namespace Esky.Packages.Integration.Tests.Requests;

public class PackagesRequests
{
    private const string PackageId = "250808:3:pl:3713392";

    public static async Task<PackageDto?> WaitForPackage(HttpClient httpClient, Func<PackageDto?, bool> predicate, TimeSpan timeout)
    {
        var startTime = DateTime.UtcNow;
        var endTime = startTime + timeout;

        while (DateTime.UtcNow < endTime)
        {
            var package = await GetPackageById(httpClient);
            if (predicate(package))
            {
                return package;
            }

            await Task.Delay(TimeSpan.FromSeconds(1));
        }

        throw new Exception("Timeout elapsed waiting for predicate.");
    }

    public static async Task<PackageDto?> GetPackageById(HttpClient httpClient)
    {
        var response = await httpClient.GetAsync($"/api/packages/{PackageId}");
        var stringContent = await response.Content.ReadAsStringAsync();

        return JsonSerializer.Deserialize<PackageDto>(stringContent, InfrastructureBootstrapper.SerializerOptions);
    }

    public static async Task<PackageLiveVariantsDto?> GetPackageLiveVariants(HttpClient httpClient)
    {
        var query = new GetPackageLiveVariantsQuery(
            Occupancies: [new Contract.Common.OccupancyDto(Adults: 2, ChildrenAges: [])],
            DepartureAirports: null,
            FlightOptionId: "LGWTFS250501226.198||U28037:0:0,TFSLGW250504226I.198||U28034:0:1",
            InboundDepartures: null,
            OutboundDepartures: null,
            PreferredDepartureAirport: null,
            PreferredMealPlans: null);

        return await GetPackageLiveVariants(httpClient, PackageId, query);
    }

    public static async Task<PackageLiveVariantsDto?> GetPackageLiveVariants(HttpClient httpClient, string packageId, GetPackageLiveVariantsQuery query)
    {
        var response = await httpClient.PostAsync(
            $"/api/packages/{packageId}/live-variants",
            new StringContent(JsonSerializer.Serialize(query, InfrastructureBootstrapper.SerializerOptions), Encoding.UTF8, "application/json"));

        response.EnsureSuccessStatusCode();

        var stringContent = await response.Content.ReadAsStringAsync();
        if (string.IsNullOrWhiteSpace(stringContent))
        {
            throw new Exception("Response content is empty");
        }

        return JsonSerializer.Deserialize<PackageLiveVariantsDto>(stringContent, InfrastructureBootstrapper.SerializerOptions);
    }

    public static async Task<PackageVariantsPriceHistoryResponseDto> WaitForGetPackagePriceHistory(HttpClient httpClient, string packageVariantId, TimeSpan timeout)
    {
        var packageVariantDateRanges = new[]
        {
            new PackageVariantDateRangeDto(
                PackageVariantId: packageVariantId,
                StartDate: new DateOnly(2025, 8, 1),
                EndDate: new DateOnly(2025, 8, 3))
        };

        var query = new GetPackageVariantsPriceHistoryQuery(packageVariantDateRanges);

        var startTime = DateTime.UtcNow;
        var endTime = startTime + timeout;

        while (DateTime.UtcNow < endTime)
        {
            var response = await httpClient.PostAsync(
                $"/api/packages/price-history",
                new StringContent(JsonSerializer.Serialize(query, InfrastructureBootstrapper.SerializerOptions), Encoding.UTF8, "application/json"));

            response.EnsureSuccessStatusCode();

            var stringContent = await response.Content.ReadAsStringAsync();
            if (string.IsNullOrWhiteSpace(stringContent))
            {
                throw new Exception("Response content is empty");
            }

            var result = JsonSerializer.Deserialize<PackageVariantsPriceHistoryResponseDto>(stringContent, InfrastructureBootstrapper.SerializerOptions);

            if (result == null || result.PackageVariants.Count == 0)
            {
                throw new Exception("Bad response. Should contain at least one variant");
            }

            if (result.PackageVariants.Any(x => x.HistoryPoints.Any()))
            {
                return result;
            }

            await Task.Delay(TimeSpan.FromSeconds(1));
        }

        throw new Exception("Timeout elapsed waiting for predicate.");
    }
}
