using Esky.Packages.Contract.Markets;
using Esky.Packages.Integration.Tests.Infrastructure;
using System.Text;
using System.Text.Json;

namespace Esky.Packages.Integration.Tests.Requests;

public static class MarketsRequests
{
    public static async Task<HttpResponseMessage> AddMarket(HttpClient httpClient)
    {
        var market = new MarketDto(
            Id: "pl",
            Currency: "PLN",
            PartnerCode: "ESKYPLPACKAGES",
            DepartureAirports: ["LHR", "LGW"],
            HotelOfferProviderConfigurationIds: ["12|epaeac", "36|epa", "119|plp", "119|hup", "119|skp", "122|epa", "123|epa", "124|esky-package"],
            EnableInboundOutboundFlightDepartureHours: true,
            EmitPriceHistoryEvents: true,
            TimeZone: "Europe/Warsaw");

        return await httpClient.PostAsync(
            "/api/markets/",
            new StringContent(JsonSerializer.Serialize(market, InfrastructureBootstrapper.SerializerOptions),
                Encoding.UTF8, "application/json"));
    }
}