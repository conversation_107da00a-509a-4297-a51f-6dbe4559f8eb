using Esky.Packages.Contract.Common;
using Esky.Packages.Contract.Search;
using Esky.Packages.Integration.Tests.Infrastructure;
using System.Text;
using System.Text.Json;

namespace Esky.Packages.Integration.Tests.Requests;

public static class SearchRequests
{
    public static async Task<SearchDto> Search(HttpClient httpClient)
    {
        var searchQuery = new GetSearchQueryDto(
            MarketId: "pl",
            MetaCodes: [3713392],
            StayLengths: [3],
            DepartureDateFrom: new DateOnly(2025, 8, 8),
            DepartureDateTo: new DateOnly(2025, 8, 8),
            DepartureAirports: ["LHR", "LGW"],
            MealPlans: [MealPlanDto.None, MealPlanDto.Breakfast, MealPlanDto.HalfBoard, MealPlanDto.FullBoard, MealPlanDto.AllInclusive],
            Occupancies: [new OccupancyDto(Adults: 2, ChildrenAges: [])],
            InboundDepartures: null,
            OutboundDepartures: null,
            MaxPrice: null,
            UseDynamicSearchFallback: false);

        var response = await httpClient.PostAsync(
            "/api/search/",
            new StringContent(JsonSerializer.Serialize(searchQuery, InfrastructureBootstrapper.SerializerOptions),
                Encoding.UTF8, "application/json"));

        var stringContent = await response.Content.ReadAsStringAsync();

        return JsonSerializer.Deserialize<SearchDto>(stringContent, InfrastructureBootstrapper.SerializerOptions)!;
    }
}
