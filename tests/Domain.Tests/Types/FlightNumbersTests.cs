using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Types;

public class FlightNumbersTests
{
    [Theory]
    [InlineData("AA123", "BA4567")]
    [InlineData("LH1", "FR9999")]
    public void Constructor_ValidNumbers_ShouldSucceed(params string[] numbers)
    {
        var flightNumbers = new FlightNumbers(numbers);
        Assert.Equal(numbers, flightNumbers.ToString().Split(','));
    }

    [Theory]
    [InlineData("LH")]
    [InlineData("")]
    [InlineData("A2")]
    [InlineData("A")]
    [InlineData("A123456")]
    public void Constructor_InvalidNumbers_ShouldThrow(params string[] numbers)
    {
        Assert.Throws<ArgumentException>(() => new FlightNumbers(numbers));
    }

    [Fact]
    public void ToString_ShouldReturnCommaSeparated()
    {
        var numbers = new[] { "AA123", "BA4567" };
        var flightNumbers = new FlightNumbers(numbers);
        Assert.Equal("AA123,BA4567", flightNumbers.ToString());
    }

    [Fact]
    public void Parse_ValidString_ShouldReturnFlightNumbers()
    {
        const string input = "AA123,BA4567,A123";
        var flightNumbers = FlightNumbers.Parse(input, null);
        Assert.Equal(["AA123", "BA4567", "A123" ], flightNumbers.ToString().Split(','));
    }

    [Fact]
    public void Parse_InvalidString_ShouldThrow()
    {
        const string input = "A1,BA4567";
        Assert.Throws<ArgumentException>(() => FlightNumbers.Parse(input, null));
    }

    [Fact]
    public void TryParse_ValidString_ShouldReturnTrue()
    {
        const string input = "AA123,BA4567,A123";
        var result = FlightNumbers.TryParse(input, null, out var flightNumbers);
        Assert.True(result);
        Assert.Equal(["AA123", "BA4567", "A123"], flightNumbers.ToString().Split(','));
    }

    [Fact]
    public void TryParse_InvalidString_ShouldReturnFalse()
    {
        const string input = "A1,BA4567";
        var result = FlightNumbers.TryParse(input, null, out _);
        Assert.False(result);
    }

    [Fact]
    public void Equality_ShouldWorkCorrectly()
    {
        var numbers1 = new[] { "AA123", "BA4567" };
        var numbers2 = new[] { "AA123", "BA4567" };
        var numbers3 = new[] { "BA4567", "AA123" };
        var flightNumbers1 = new FlightNumbers(numbers1);
        var flightNumbers2 = new FlightNumbers(numbers2);
        var flightNumbers3 = new FlightNumbers(numbers3);
        Assert.True(flightNumbers1.Equals(flightNumbers2));
        Assert.True(flightNumbers1 == flightNumbers2);
        Assert.False(flightNumbers1.Equals(flightNumbers3));
        Assert.False(flightNumbers1 == flightNumbers3);
        Assert.True(flightNumbers1 != flightNumbers3);
    }
}