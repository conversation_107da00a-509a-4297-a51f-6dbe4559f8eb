using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Types;

public class RoomIdsTests
{
    [Fact]
    public void Constructor_WithNullArray_ShouldCreateEmptyRoomIds()
    {
        // Arrange & Act
        var roomIds = new RoomIds(null!);

        // Assert
        roomIds.Value.ShouldBeEmpty();
    }

    [Fact]
    public void Constructor_WithEmptyArray_ShouldCreateEmptyRoomIds()
    {
        // Arrange & Act
        var roomIds = new RoomIds([]);

        // Assert
        roomIds.Value.ShouldBeEmpty();
    }

    [Fact]
    public void Constructor_WithRoomIds_ShouldSortThemAlphabetically()
    {
        // Arrange
        var input = new[] { "room3", "room1", "room2" };

        // Act
        var roomIds = new RoomIds(input);

        // Assert
        roomIds.Value.ShouldBe(["room1", "room2", "room3"]);
    }

    [Fact]
    public void Constructor_WithDuplicateRoomIds_ShouldKeepDuplicates()
    {
        // Arrange
        var input = new[] { "room1", "room2", "room1" };

        // Act
        var roomIds = new RoomIds(input);

        // Assert
        roomIds.Value.ShouldBe(["room1", "room1", "room2"]);
    }

    [Fact]
    public void Merge_WithMultipleRoomIds_ShouldCombineAndSortAllRoomIds()
    {
        // Arrange
        var roomIds1 = new RoomIds(["room3", "room1"]);
        var roomIds2 = new RoomIds(["room2", "room4"]);
        var roomIds3 = new RoomIds(["room5"]);

        // Act
        var merged = RoomIds.Merge([roomIds1, roomIds2, roomIds3]);

        // Assert
        merged.Value.ShouldBe(["room1", "room2", "room3", "room4", "room5"]);
    }

    [Fact]
    public void Merge_WithEmptyArray_ShouldReturnEmptyRoomIds()
    {
        // Act
        var merged = RoomIds.Merge([]);

        // Assert
        merged.Value.ShouldBeEmpty();
    }

    [Fact]
    public void Merge_WithSomeEmptyRoomIds_ShouldIgnoreEmptyOnes()
    {
        // Arrange
        var roomIds1 = new RoomIds(["room1"]);
        var emptyRoomIds = RoomIds.Empty;
        var roomIds2 = new RoomIds(["room2"]);

        // Act
        var merged = RoomIds.Merge([roomIds1, emptyRoomIds, roomIds2]);

        // Assert
        merged.Value.ShouldBe(["room1", "room2"]);
    }

    [Fact]
    public void ToString_WithMultipleRoomIds_ShouldReturnCommaSeparatedString()
    {
        // Arrange
        var roomIds = new RoomIds(["room2", "room1", "room3"]);

        // Act
        var result = roomIds.ToString();

        // Assert
        result.ShouldBe("room1,room2,room3");
    }

    [Fact]
    public void ToString_WithEmptyRoomIds_ShouldReturnEmptyString()
    {
        // Arrange
        var roomIds = RoomIds.Empty;

        // Act
        var result = roomIds.ToString();

        // Assert
        result.ShouldBeEmpty();
    }

    [Fact]
    public void Parse_WithValidCommaSeparatedString_ShouldReturnRoomIds()
    {
        // Arrange
        var input = "room3,room1,room2";

        // Act
        var roomIds = RoomIds.Parse(input, null);

        // Assert
        roomIds.Value.ShouldBe(["room1", "room2", "room3"]);
    }

    [Fact]
    public void Parse_WithStringContainingSpaces_ShouldTrimSpaces()
    {
        // Arrange
        var input = " room2 , room1 , room3 ";

        // Act
        var roomIds = RoomIds.Parse(input, null);

        // Assert
        roomIds.Value.ShouldBe(["room1", "room2", "room3"]);
    }

    [Fact]
    public void Parse_WithEmptyCommaSeparatedValues_ShouldIgnoreEmptyValues()
    {
        // Arrange
        var input = "room1,,room2,";

        // Act
        var roomIds = RoomIds.Parse(input, null);

        // Assert
        roomIds.Value.ShouldBe(["room1", "room2"]);
    }

    [Fact]
    public void TryParse_WithValidString_ShouldReturnTrueAndRoomIds()
    {
        // Arrange
        var input = "room2,room1";

        // Act
        var success = RoomIds.TryParse(input, null, out var result);

        // Assert
        success.ShouldBeTrue();
        result.Value.ShouldBe(["room1", "room2"]);
    }

    [Fact]
    public void OperatorEquals_ShouldWorkCorrectly()
    {
        // Arrange
        var roomIds1 = new RoomIds(["room1", "room2"]);
        var roomIds2 = new RoomIds(["room2", "room1"]);
        var roomIds3 = new RoomIds(["room1", "room3"]);

        // Act & Assert
        (roomIds1 == roomIds2).ShouldBeTrue();
        (roomIds1 == roomIds3).ShouldBeFalse();
    }

    [Fact]
    public void OperatorNotEquals_ShouldWorkCorrectly()
    {
        // Arrange
        var roomIds1 = new RoomIds(["room1", "room2"]);
        var roomIds2 = new RoomIds(["room2", "room1"]);
        var roomIds3 = new RoomIds(["room1", "room3"]);

        // Act & Assert
        (roomIds1 != roomIds2).ShouldBeFalse();
        (roomIds1 != roomIds3).ShouldBeTrue();
    }

    [Theory]
    [InlineData(new[] { "room:1" }, new[] { "room1" })]
    [InlineData(new[] { "room,2" }, new[] { "room2" })]
    [InlineData(new[] { "room:,3" }, new[] { "room3" })]
    [InlineData(new[] { "ro:om,1", "room:2,test" }, new[] { "room1", "room2test" })]
    [InlineData(new[] { ":::room1,,,", "room2:,:," }, new[] { "room1", "room2" })]
    public void Constructor_WithColonsAndCommas_ShouldRemoveThem(string[] input, string[] expected)
    {
        // Act
        var roomIds = new RoomIds(input);

        // Assert
        roomIds.Value.ShouldBe(expected);
    }
}