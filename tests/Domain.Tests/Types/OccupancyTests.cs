using Esky.Packages.Contract.Common;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Types;

public class OccupancyTests
{
    [Fact]
    public void ToString_ShouldReturnExpectedString()
    {
        // Arrange
        var occupancy = new Occupancy(2, new[] { 5, 3 });

        // Act
        var result = occupancy.ToString();

        // Assert
        Assert.Equal("A2C3C5", result);
    }

    [Fact]
    public void ToDto_ShouldReturnExpectedDto()
    {
        // Arrange
        var occupancy = new Occupancy(2, new[] { 5, 3 });

        // Act
        var dto = occupancy.ToDto();

        // Assert
        Assert.Equal(2, dto.Adults);
        Assert.Equal(new[] { 5, 3 }, dto.ChildrenAges);
    }

    [Fact]
    public void FromDto_ShouldReturnExpectedOccupancy()
    {
        // Arrange
        var dto = new OccupancyDto(2, new[] { 5, 3 });

        // Act
        var occupancy = Occupancy.FromDto(dto);

        // Assert
        Assert.Equal(2, occupancy.Adults);
        Assert.Equal(new[] { 5, 3 }, occupancy.ChildrenAges);
    }

    [Fact]
    public void FromDtoArray_ShouldReturnExpectedOccupancyArray()
    {
        // Arrange
        var dtos = new[]
        {
            new OccupancyDto(2, new[] { 5, 3 }),
            new OccupancyDto(1, new[] { 7 })
        };

        // Act
        var occupancies = Occupancy.FromDto(dtos);

        // Assert
        Assert.Equal(2, occupancies.Length);
        Assert.Equal(2, occupancies[0].Adults);
        Assert.Equal(new[] { 5, 3 }, occupancies[0].ChildrenAges);
        Assert.Equal(1, occupancies[1].Adults);
        Assert.Equal(new[] { 7 }, occupancies[1].ChildrenAges);
    }
    
    [Fact]
    public void Equals_ShouldReturnTrue_ForEqualOccupancies()
    {
        // Arrange
        var occupancy1 = new Occupancy(2, new[] { 5, 3 });
        var occupancy2 = new Occupancy(2, new[] { 5, 3 });

        // Act
        var result = occupancy1.Equals(occupancy2);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void Equals_ShouldReturnFalse_ForDifferentAdults()
    {
        // Arrange
        var occupancy1 = new Occupancy(2, new[] { 5, 3 });
        var occupancy2 = new Occupancy(3, new[] { 5, 3 });

        // Act
        var result = occupancy1.Equals(occupancy2);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void Equals_ShouldReturnFalse_ForDifferentChildrenAges()
    {
        // Arrange
        var occupancy1 = new Occupancy(2, new[] { 5, 3 });
        var occupancy2 = new Occupancy(2, new[] { 6, 3 });

        // Act
        var result = occupancy1.Equals(occupancy2);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void Equals_ShouldReturnTrue_ForEmptyChildrenAges()
    {
        // Arrange
        var occupancy1 = new Occupancy(2, Array.Empty<int>());
        var occupancy2 = new Occupancy(2, Array.Empty<int>());

        // Act
        var result = occupancy1.Equals(occupancy2);

        // Assert
        Assert.True(result);
    }
}