using Esky.Packages.Common.Tests;
using Esky.Packages.Common.Tests.Fixtures.Domain;
using Esky.Packages.Domain.Model.LiveVariants;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Model.PackageVariants;

public class HotelOfferLiveVariantsTests
{
    private readonly HotelOfferVariantTestFixture _fixture;

    public HotelOfferLiveVariantsTests()
    {
        _fixture = new HotelOfferVariantTestFixture()
            .WithAnyCurrencyConversion();
    }

    [Fact]
    public void CreateCheapestHotelOfferVariantsByRooms_ReturnsAllOffersForCheapestRoom()
    {
        // Arrange
        var roomOffers1 = new Dictionary<string, IEnumerable<HotelOfferLiveVariant>>
        {
            ["room1"] =
            [
                _fixture.CreateHotelOfferVariant(price: new Money(150M, DefaultTestValues.Currency), offerId: "offer1"),
                _fixture.CreateHotelOfferVariant(price: new Money(100M, DefaultTestValues.Currency), offerId: "offer2"),
            ],
            ["room2"] =
            [
                _fixture.CreateHotelOfferVariant(price: new Money(90M, DefaultTestValues.Currency), offerId: "offer3"),
                _fixture.CreateHotelOfferVariant(price: new Money(180M, DefaultTestValues.Currency), offerId: "offer4"),
            ]
        };
        var roomOffers2 = new Dictionary<string, IEnumerable<HotelOfferLiveVariant>>
        {
            ["room3"] =
            [
                _fixture.CreateHotelOfferVariant(price: new Money(200M, DefaultTestValues.Currency), offerId: "offer5"),
                _fixture.CreateHotelOfferVariant(price: new Money(100M, DefaultTestValues.Currency), offerId: "offer6"),
            ],
            ["room4"] =
            [
                _fixture.CreateHotelOfferVariant(price: new Money(900M, DefaultTestValues.Currency), offerId: "offer7"),
                _fixture.CreateHotelOfferVariant(price: new Money(80M, DefaultTestValues.Currency), offerId: "offer8"),
            ]
        };
        var hotelOfferVariantsGroupedByRooms = new[]
        {
            roomOffers1,
            roomOffers2
        };
        var onlyRefundable = false;
        MealPlan[]? preferredMealPlans = null;

        // Act
        var hotelOfferVariants = HotelOfferLiveVariants.CreateCheapestHotelOfferVariantsByRooms(
            hotelOfferVariantsGroupedByRooms,
            onlyRefundable,
            preferredMealPlans);

        // Assert
        hotelOfferVariants.HotelOffers.Count.ShouldBe(4);
        hotelOfferVariants.HotelOffers.ShouldContain(offer => offer.OfferId == "offer3");
        hotelOfferVariants.HotelOffers.ShouldContain(offer => offer.OfferId == "offer4");
        hotelOfferVariants.HotelOffers.ShouldContain(offer => offer.OfferId == "offer7");
        hotelOfferVariants.HotelOffers.ShouldContain(offer => offer.OfferId == "offer8");
        hotelOfferVariants.OnlyRefundable.ShouldBeFalse();
    }

    [Fact]
    public void CreateCheapestHotelOfferVariantsByRooms_WithOnlyRefundable_ReturnsCheapestRefundableOffers()
    {
        // Arrange
        var refundable = new Refundability(isRefundable: true);
        var nonRefundable = new Refundability(isRefundable: false);
        var onlyRefundable = true;
        MealPlan[]? preferredMealPlans = null;

        var roomOffers1 = new Dictionary<string, IEnumerable<HotelOfferLiveVariant>>
        {
            ["room1"] =
            [
                _fixture.CreateHotelOfferVariant(price: new Money(150M, DefaultTestValues.Currency), refundability: refundable, offerId: "offer1"),
                _fixture.CreateHotelOfferVariant(price: new Money(100M, DefaultTestValues.Currency), refundability: nonRefundable, offerId: "offer2"),
            ],
            ["room2"] =
            [
                _fixture.CreateHotelOfferVariant(price: new Money(90M, DefaultTestValues.Currency), refundability: refundable, offerId: "offer3"),
                _fixture.CreateHotelOfferVariant(price: new Money(95M, DefaultTestValues.Currency), refundability: refundable, offerId: "offer4"),
                _fixture.CreateHotelOfferVariant(price: new Money(180M, DefaultTestValues.Currency), refundability: nonRefundable, offerId: "offer5"),
            ]
        };
        var roomOffers2 = new Dictionary<string, IEnumerable<HotelOfferLiveVariant>>
        {
            ["room3"] =
            [
                _fixture.CreateHotelOfferVariant(price: new Money(200M, DefaultTestValues.Currency), refundability: refundable, offerId: "offer6"),
                _fixture.CreateHotelOfferVariant(price: new Money(100M, DefaultTestValues.Currency), refundability: nonRefundable, offerId: "offer7"),
            ],
            ["room4"] =
            [
                _fixture.CreateHotelOfferVariant(price: new Money(900M, DefaultTestValues.Currency), refundability: refundable, offerId: "offer8"),
                _fixture.CreateHotelOfferVariant(price: new Money(80M, DefaultTestValues.Currency), refundability: nonRefundable, offerId: "offer9"),
            ]
        };
        var hotelOfferVariantsGroupedByRooms = new[]
        {
            roomOffers1,
            roomOffers2
        };

        // Act
        var hotelOfferVariants = HotelOfferLiveVariants.CreateCheapestHotelOfferVariantsByRooms(
            hotelOfferVariantsGroupedByRooms,
            onlyRefundable,
            preferredMealPlans);

        // Assert
        hotelOfferVariants.HotelOffers.Count.ShouldBe(3);
        hotelOfferVariants.HotelOffers.ShouldContain(offer => offer.OfferId == "offer3");
        hotelOfferVariants.HotelOffers.ShouldContain(offer => offer.OfferId == "offer4");
        hotelOfferVariants.HotelOffers.ShouldContain(offer => offer.OfferId == "offer6");
        hotelOfferVariants.OnlyRefundable.ShouldBeTrue();
    }

    [Fact]
    public void GetHotelOffersByMealPlan_GroupsOffersByMealPlanCorrectly()
    {
        // Arrange
        MealPlan[]? preferredMealPlans = null;

        var roomOffers = new Dictionary<string, IEnumerable<HotelOfferLiveVariant>>
        {
            ["room1"] =
            [
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.Breakfast, price: new Money(100M, DefaultTestValues.Currency), offerId: "offer1"),
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.AllInclusive, price: new Money(200M, DefaultTestValues.Currency), offerId: "offer2"),
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.Breakfast, price: new Money(150M, DefaultTestValues.Currency), offerId: "offer3")
            ]
        };
        var hotelOfferVariantsGroupedByRooms = new[]
        {
            roomOffers
        };
        var hotelOfferVariants = HotelOfferLiveVariants.CreateCheapestHotelOfferVariantsByRooms(
            hotelOfferVariantsGroupedByRooms,
            onlyRefundable: false,
            preferredMealPlans);

        // Act
        var groupedOffers = hotelOfferVariants.GetHotelOffersByMealPlan();

        // Assert
        groupedOffers.Count.ShouldBe(2);
        groupedOffers.ShouldContainKey(MealPlan.Breakfast);
        groupedOffers.ShouldContainKey(MealPlan.AllInclusive);

        groupedOffers[MealPlan.Breakfast].Count.ShouldBe(2);
        groupedOffers[MealPlan.AllInclusive].Count.ShouldBe(1);

        groupedOffers[MealPlan.Breakfast][0].Price.ShouldBe(100M);
        groupedOffers[MealPlan.Breakfast][1].Price.ShouldBe(150M);
    }

    [Fact]
    public void CreateCheapestHotelOfferVariantsByRooms_WithPreferredMealPlans_FiltersToMatchingOffers()
    {
        // Arrange
        var roomOffers = new Dictionary<string, IEnumerable<HotelOfferLiveVariant>>
        {
            ["room1"] =
            [
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.Breakfast, price: new Money(100M, DefaultTestValues.Currency), offerId: "offer1"),
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.AllInclusive, price: new Money(150M, DefaultTestValues.Currency), offerId: "offer2"),
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.HalfBoard, price: new Money(120M, DefaultTestValues.Currency), offerId: "offer3")
            ]
        };
        var hotelOfferVariantsGroupedByRooms = new[] { roomOffers };
        var preferredMealPlans = new[] { MealPlan.Breakfast, MealPlan.AllInclusive };

        // Act
        var result = HotelOfferLiveVariants.CreateCheapestHotelOfferVariantsByRooms(
            hotelOfferVariantsGroupedByRooms,
            onlyRefundable: false,
            preferredMealPlans);

        // Assert
        result.HotelOffers.Count.ShouldBe(2);
        result.HotelOffers.ShouldContain(offer => offer.OfferId == "offer1");
        result.HotelOffers.ShouldContain(offer => offer.OfferId == "offer2");
        result.HotelOffers.ShouldNotContain(offer => offer.OfferId == "offer3");
    }

    [Fact]
    public void CreateCheapestHotelOfferVariantsByRooms_WithPreferredMealPlans_WhenNoMatches_FallsBackToAllOffers()
    {
        // Arrange
        var roomOffers = new Dictionary<string, IEnumerable<HotelOfferLiveVariant>>
        {
            ["room1"] =
            [
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.Breakfast, price: new Money(100M, DefaultTestValues.Currency), offerId: "offer1"),
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.HalfBoard, price: new Money(120M, DefaultTestValues.Currency), offerId: "offer2")
            ]
        };
        var hotelOfferVariantsGroupedByRooms = new[] { roomOffers };
        var preferredMealPlans = new[] { MealPlan.AllInclusive, MealPlan.FullBoard };

        // Act
        var result = HotelOfferLiveVariants.CreateCheapestHotelOfferVariantsByRooms(
            hotelOfferVariantsGroupedByRooms,
            onlyRefundable: false,
            preferredMealPlans);

        // Assert - Should fallback to all offers since no preferred meal plans found
        result.HotelOffers.Count.ShouldBe(2);
        result.HotelOffers.ShouldContain(offer => offer.OfferId == "offer1");
        result.HotelOffers.ShouldContain(offer => offer.OfferId == "offer2");
    }

    [Fact]
    public void CreateCheapestHotelOfferVariantsByRooms_WithPreferredMealPlans_AcrossMultipleRooms_FiltersCorrectly()
    {
        // Arrange
        var roomOffers1 = new Dictionary<string, IEnumerable<HotelOfferLiveVariant>>
        {
            ["room1"] =
            [
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.Breakfast, price: new Money(100M, DefaultTestValues.Currency), offerId: "offer1"),
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.AllInclusive, price: new Money(150M, DefaultTestValues.Currency), offerId: "offer2")
            ],
            ["room2"] =
            [
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.HalfBoard, price: new Money(90M, DefaultTestValues.Currency), offerId: "offer3"),
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.Breakfast, price: new Money(110M, DefaultTestValues.Currency), offerId: "offer4")
            ]
        };
        var roomOffers2 = new Dictionary<string, IEnumerable<HotelOfferLiveVariant>>
        {
            ["room3"] =
            [
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.AllInclusive, price: new Money(200M, DefaultTestValues.Currency), offerId: "offer5"),
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.FullBoard, price: new Money(180M, DefaultTestValues.Currency), offerId: "offer6")
            ]
        };
        var hotelOfferVariantsGroupedByRooms = new[] { roomOffers1, roomOffers2 };
        var preferredMealPlans = new[] { MealPlan.Breakfast, MealPlan.AllInclusive };

        // Act
        var result = HotelOfferLiveVariants.CreateCheapestHotelOfferVariantsByRooms(
            hotelOfferVariantsGroupedByRooms,
            onlyRefundable: false,
            preferredMealPlans);

        // Assert - Should only include offers with preferred meal plans (Breakfast or AllInclusive)
        result.HotelOffers.Count.ShouldBe(3);
        result.HotelOffers.ShouldContain(offer => offer.OfferId == "offer1"); // Breakfast from room1 (cheapest room in first group)
        result.HotelOffers.ShouldContain(offer => offer.OfferId == "offer2"); // AllInclusive from room1 (cheapest room in first group)
        result.HotelOffers.ShouldContain(offer => offer.OfferId == "offer5"); // AllInclusive from room3 (cheapest room in second group)
        result.HotelOffers.ShouldNotContain(offer => offer.OfferId == "offer6"); // FullBoard is not a preferred meal plan
    }

    [Fact]
    public void CreateCheapestHotelOfferVariantsByRooms_WithPreferredMealPlansAndRefundableFilter_CombinesFiltersCorrectly()
    {
        // Arrange
        var refundable = new Refundability(isRefundable: true);
        var nonRefundable = new Refundability(isRefundable: false);
        
        var roomOffers = new Dictionary<string, IEnumerable<HotelOfferLiveVariant>>
        {
            ["room1"] =
            [
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.Breakfast, refundability: refundable, price: new Money(100M, DefaultTestValues.Currency), offerId: "offer1"),
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.Breakfast, refundability: nonRefundable, price: new Money(80M, DefaultTestValues.Currency), offerId: "offer2"),
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.AllInclusive, refundability: refundable, price: new Money(150M, DefaultTestValues.Currency), offerId: "offer3"),
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.HalfBoard, refundability: refundable, price: new Money(120M, DefaultTestValues.Currency), offerId: "offer4"),
                _fixture.CreateHotelOfferVariant(mealPlan: MealPlan.AllInclusive, refundability: nonRefundable, price: new Money(150M, DefaultTestValues.Currency), offerId: "offer5"),
            ]
        };
        var hotelOfferVariantsGroupedByRooms = new[] { roomOffers };
        var preferredMealPlans = new[] { MealPlan.Breakfast, MealPlan.AllInclusive };

        // Act
        var result = HotelOfferLiveVariants.CreateCheapestHotelOfferVariantsByRooms(
            hotelOfferVariantsGroupedByRooms,
            onlyRefundable: true,
            preferredMealPlans);

        // Assert - Should have refundable offers with preferred meal plans
        result.HotelOffers.Count.ShouldBe(2);
        result.HotelOffers.ShouldContain(offer => offer.OfferId == "offer1"); // Breakfast + Refundable
        result.HotelOffers.ShouldContain(offer => offer.OfferId == "offer3"); // AllInclusive + Refundable
        result.HotelOffers.ShouldNotContain(offer => offer.OfferId == "offer2"); // Breakfast but not refundable
        result.HotelOffers.ShouldNotContain(offer => offer.OfferId == "offer4"); // Refundable but not preferred meal plan
        result.HotelOffers.ShouldNotContain(offer => offer.OfferId == "offer5"); // AllInvlusive but not refundable
        result.OnlyRefundable.ShouldBeTrue();
    }
}
