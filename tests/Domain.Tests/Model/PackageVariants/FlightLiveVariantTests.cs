using Esky.Packages.Common.Tests;
using Esky.Packages.Common.Tests.Fixtures.Domain;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Model.PackageVariants;

public class FlightLiveVariantTests
{
    private readonly FlightVariantTestFixture _fixture;

    public FlightLiveVariantTests()
    {
        _fixture = new FlightVariantTestFixture()
            .WithAnyCurrencyConversion();
    }

    [Fact]
    public void UpdateLivePrice_WithSameCurrency_UpdatesPriceCorrectly()
    {
        // Arrange
        var alternativeFlight = _fixture.CreateAlternativeFlight(currency: Currency.GBP, prices: [new Money(100M, DefaultTestValues.Currency)]);
        var newPrice = new Money(120M, Currency.GBP);
        var legLocators = new[] { "newLegLocator" };

        // Act
        alternativeFlight.UpdateLivePrice(newPrice, legLocators);

        // Assert
        alternativeFlight.Price.ShouldBe(120M);
        alternativeFlight.LegLocators.ShouldBe(legLocators);
    }

    [Fact]
    public void UpdateLivePrice_WithDifferentCurrency_ConvertsAndUpdatesPriceCorrectly()
    {
        // Arrange
        var fixture = new FlightVariantTestFixture()
            .WithCurrencyConversion(Currency.USD, 0.77M);
        var alternativeFlight = fixture.CreateAlternativeFlight(currency: Currency.GBP, prices: [new Money(100M, DefaultTestValues.Currency)]);
        var newPrice = new Money(130M, Currency.USD);
        var legLocators = new[] { "newLegLocator" };

        // Act
        alternativeFlight.UpdateLivePrice(newPrice, legLocators);

        // Assert
        alternativeFlight.Price.ShouldBe(100M); // 130 * 0.77 = 100.1 ~ 100
        alternativeFlight.LegLocators.ShouldBe(legLocators);
    }

    [Fact]
    public void Compensate_ExceedsCompensation_ReturnsPrice()
    {
        // Arrange
        var alternativeFlight = _fixture.CreateAlternativeFlight(prices: [new Money(200M, DefaultTestValues.Currency)]);
        decimal lastCompensatedPrice = 100M;

        // Act
        var compensatedPrice = alternativeFlight.Compensate(lastCompensatedPrice);

        // Assert
        compensatedPrice.ShouldBe(200M);
    }

    [Fact]
    public void Compensate_WithinCompensation_ReturnsLastCompensatedPrice()
    {
        // Arrange
        var alternativeFlight = _fixture.CreateAlternativeFlight(prices: [new Money(105M, DefaultTestValues.Currency)]);
        decimal lastCompensatedPrice = 100M;

        // Act
        var compensatedPrice = alternativeFlight.Compensate(lastCompensatedPrice);

        // Assert
        compensatedPrice.ShouldBe(100M);
    }

    [Fact]
    public void Compensate_OnlyUpWithLowerPrice_KeepsOriginalPrice()
    {
        // Arrange
        var alternativeFlight = _fixture.CreateAlternativeFlight(prices: [new Money(150M, DefaultTestValues.Currency)]);
        decimal lastCompensatedPrice = 100M;

        // Act
        var compensatedPrice = alternativeFlight.Compensate(lastCompensatedPrice, onlyUp: true);

        // Assert
        compensatedPrice.ShouldBe(150M);
    }

    [Fact]
    public void MarkSelected_SetsIsSelectedToTrue()
    {
        // Arrange
        var alternativeFlight = _fixture.CreateAlternativeFlight();

        // Act
        alternativeFlight.MarkAsSelected();

        // Assert
        alternativeFlight.IsSelected.ShouldBeTrue();
    }
}
