using Esky.Packages.Common.Tests;
using Esky.Packages.Common.Tests.Fixtures.Domain;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Model.PackageVariants;

public class HotelOfferLiveVariantTests
{
    private readonly HotelOfferVariantTestFixture _fixture;

    public HotelOfferLiveVariantTests()
    {
        _fixture = new HotelOfferVariantTestFixture()
            .WithAnyCurrencyConversion();
    }

    [Fact]
    public void Compensate_ExceedsCompensation_ReturnsPrice()
    {
        // Arrange
        var hotelOfferVariant = _fixture.CreateHotelOfferVariant(price: new Money(200M, DefaultTestValues.Currency));
        decimal lastCompensatedPrice = 100M;

        // Act
        var compensatedPrice = hotelOfferVariant.Compensate(lastCompensatedPrice);

        // Assert
        compensatedPrice.ShouldBe(200M);
    }

    [Fact]
    public void Compensate_WithinCompensation_ReturnsLastCompensatedPrice()
    {
        // Arrange
        var hotelOfferVariant = _fixture.CreateHotelOfferVariant(price: new Money(105M, DefaultTestValues.Currency));
        decimal lastCompensatedPrice = 100M;

        // Act
        var compensatedPrice = hotelOfferVariant.Compensate(lastCompensatedPrice);

        // Assert
        compensatedPrice.ShouldBe(100M);
    }

    [Fact]
    public void Compensate_OnlyUpWithHigherPrice_ReturnsPrice()
    {
        // Arrange
        var hotelOfferVariant = _fixture.CreateHotelOfferVariant(price: new Money(150M, DefaultTestValues.Currency));
        decimal lastCompensatedPrice = 100M;

        // Act
        var compensatedPrice = hotelOfferVariant.Compensate(lastCompensatedPrice, onlyUp: true);

        // Assert
        compensatedPrice.ShouldBe(150M);
    }
}
