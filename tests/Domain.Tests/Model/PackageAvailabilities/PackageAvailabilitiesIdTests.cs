using Esky.Packages.Domain.Model.PackageAvailabilities;

namespace Esky.Packages.Domain.Tests.Model.PackageAvailabilities;

public class PackageAvailabilitiesIdTests
{
    [Fact]
    public void Constructor_WithValidStringId_ParsesCorrectly()
    {
        // Arrange
        var id = "def123:5";

        // Act
        var packageAvailabilitiesId = new PackageAvailabilitiesId(id);

        // Assert
        packageAvailabilitiesId.DefinitionId.ShouldBe("def123");
        packageAvailabilitiesId.Partition.ShouldBe(5);
    }

    [Fact]
    public void Constructor_WithInvalidStringId_ThrowsFormatException()
    {
        // Arrange
        var id = "invalid:id";

        // Act & Assert
        Should.Throw<FormatException>(() => new PackageAvailabilitiesId(id));
    }

    [Fact]
    public void Equals_WithSameValues_ReturnsTrue()
    {
        // Arrange
        var id1 = new PackageAvailabilitiesId("def123:5");
        var id2 = new PackageAvailabilitiesId("def123:5");

        // Act & Assert
        id1.Equals(id2).ShouldBeTrue();
    }

    [Fact]
    public void Equals_WithDifferentValues_ReturnsFalse()
    {
        // Arrange
        var id1 = new PackageAvailabilitiesId("def123:5");
        var id2 = new PackageAvailabilitiesId("def123:6");

        // Act & Assert
        id1.Equals(id2).ShouldBeFalse();
    }

    [Fact]
    public void ToString_ReturnsExpectedFormat()
    {
        // Arrange
        var packageAvailabilitiesId = new PackageAvailabilitiesId("def123", 5);

        // Act
        var result = packageAvailabilitiesId.ToString();

        // Assert
        result.ShouldBe("def123:5");
    }

    [Fact]
    public void TryParse_WithValidString_ReturnsTrue()
    {
        // Arrange
        var id = "def123:5";

        // Act
        var success = PackageAvailabilitiesId.TryParse(id, null, out var packageAvailabilitiesId);

        // Assert
        success.ShouldBeTrue();
        packageAvailabilitiesId.DefinitionId.ShouldBe("def123");
        packageAvailabilitiesId.Partition.ShouldBe(5);
    }

    [Fact]
    public void TryParse_WithInvalidString_ReturnsFalse()
    {
        // Arrange
        var id = "invalid:id";

        // Act
        var success = PackageAvailabilitiesId.TryParse(id, null, out var packageAvailabilitiesId);

        // Assert
        success.ShouldBeFalse();
        packageAvailabilitiesId.ShouldBe(default);
    }
}
