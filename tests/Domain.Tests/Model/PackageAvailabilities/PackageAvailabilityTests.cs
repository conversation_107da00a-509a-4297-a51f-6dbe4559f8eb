using Esky.Packages.Common.Tests;
using Esky.Packages.Common.Tests.Fixtures.Domain;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Model.PackageAvailabilities;

public class PackageAvailabilityTests
{
    [Fact]
    public void Merge_WithDifferentMetaCodes_ShouldThrow()
    {
        // Arrange
        var packageAvailability = PackageAvailabilityTestFixture.CreatePackageAvailability(metaCode: 123);
        var packageAvailabilityWithDifferentMetaCode = PackageAvailabilityTestFixture.CreatePackageAvailability(metaCode: 456);

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() => packageAvailability.Merge(packageAvailabilityWithDifferentMetaCode));
    }

    [Fact]
    public void Merge_WithDifferentPackage_UpdatesState()
    {
        // Arrange
        var packageAvailability = PackageAvailabilityTestFixture.CreatePackageAvailability();

        var packageAvailabilityWithUpdates = PackageAvailabilityTestFixture.CreatePackageAvailability(
            occupancies: [new PackageOccupancy(1, 0, 0, 0)],
            mealPlans: [new MealPlan("AllInclusive")],
            departureAirports: [new Airport("LGW")],
            stayLengths: [7, 14],
            lowestTotalPrice: 450m,
            lowestPricesPerStayLength: new Dictionary<int, decimal>
            {
                { 7, 450m },
                { 14, 850m }
            },
            minCheckIn: DateOnly.FromDateTime(new DateTime(2024, 12, 31)),
            maxCheckIn: DateOnly.FromDateTime(new DateTime(2025, 6, 2)));

        // Act
        packageAvailability.Merge(packageAvailabilityWithUpdates);

        // Assert
        packageAvailability.Occupancies.ShouldContain(DefaultTestValues.PackageOccupancy);
        packageAvailability.Occupancies.ShouldContain(new PackageOccupancy(1, 0, 0, 0));
        packageAvailability.MealPlans.ShouldContain(DefaultTestValues.MealPlan);
        packageAvailability.MealPlans.ShouldContain(new MealPlan("AllInclusive"));
        packageAvailability.DepartureAirports.ShouldContain(DefaultTestValues.DepartureAirport);
        packageAvailability.DepartureAirports.ShouldContain(new Airport("LGW"));
        packageAvailability.StayLengths.ShouldContain(DefaultTestValues.StayLength);
        packageAvailability.StayLengths.ShouldContain(14);
        packageAvailability.LowestTotalPrice.ShouldBe(450m);
        packageAvailability.LowestPricesPerStayLength.ShouldContainKey(DefaultTestValues.StayLength);
        packageAvailability.LowestPricesPerStayLength.ShouldContainKey(14);
        packageAvailability.MinCheckIn.ShouldBe(packageAvailabilityWithUpdates.MinCheckIn);
        packageAvailability.MaxCheckIn.ShouldBe(packageAvailabilityWithUpdates.MaxCheckIn);
    }
}
