using Esky.Packages.Domain.Model.PackageHotelOfferVariantPrices;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Model.PackageHotelOfferVariantPrices;

public class PackageHotelOfferVariantPriceKeyTests
{
    [Fact]
    public void Parse_ValidRefundableString_ReturnsExpected()
    {
        var key = PackageHotelOfferVariantPriceKey.Parse("R|", null);
        Assert.Equal(Refundability.Refundable, key.Refundability);
        Assert.Equal(RoomIds.Empty, key.RoomIds);
    }

    [Fact]
    public void Parse_ValidNonRefundableString_ReturnsExpected()
    {
        var key = PackageHotelOfferVariantPriceKey.Parse("N|", null);
        Assert.Equal(Refundability.NonRefundable, key.Refundability);
        Assert.Equal(RoomIds.Empty, key.RoomIds);
    }

    [Theory]
    [InlineData("")]
    [InlineData("Invalid")]
    [InlineData("R")]
    public void Parse_InvalidString_ThrowsFormatException(string input)
    {
        Assert.Throws<FormatException>(() => PackageHotelOfferVariantPriceKey.Parse(input, null));
    }

    [Fact]
    public void ToString_ReturnsExpectedFormat()
    {
        var key = new PackageHotelOfferVariantPriceKey
        {
            Refundability = Refundability.Refundable,
            RoomIds = RoomIds.Empty
        };

        var result = key.ToString();

        Assert.Equal("R|", result);
    }

    [Fact]
    public void TryParse_ValidString_ReturnsTrue()
    {
        var success = PackageHotelOfferVariantPriceKey.TryParse("R|", null, out var result);

        Assert.True(success);
        Assert.Equal(Refundability.Refundable, result.Refundability);
    }

    [Fact]
    public void TryParse_InvalidString_ReturnsFalse()
    {
        var success = PackageHotelOfferVariantPriceKey.TryParse("Invalid", null, out var result);

        Assert.False(success);
    }

    [Fact]
    public void Parse_ValidRefundableStringWithRoomId_ReturnsExpected()
    {
        var key = PackageHotelOfferVariantPriceKey.Parse("R|Standard Double Room", null);
        Assert.Equal(Refundability.Refundable, key.Refundability);
        Assert.Equal(new RoomIds(["Standard Double Room"]), key.RoomIds);
    }

    [Fact]
    public void Parse_ValidNonRefundableStringWithRoomId_ReturnsExpected()
    {
        var key = PackageHotelOfferVariantPriceKey.Parse("N|Standard Double Room", null);
        Assert.Equal(Refundability.NonRefundable, key.Refundability);
        Assert.Equal(new RoomIds(["Standard Double Room"]), key.RoomIds);
    }

    [Fact]
    public void Parse_ValidStringWithMultipleRoomIds_ReturnsExpected()
    {
        var key = PackageHotelOfferVariantPriceKey.Parse("R|Standard Double Room,Deluxe Suite", null);
        Assert.Equal(Refundability.Refundable, key.Refundability);
        Assert.Equal(new RoomIds(["Standard Double Room", "Deluxe Suite"]), key.RoomIds);
    }

    [Fact]
    public void ToString_WithRoomId_ReturnsExpectedFormat()
    {
        var key = new PackageHotelOfferVariantPriceKey
        {
            Refundability = Refundability.Refundable,
            RoomIds = new RoomIds(["Standard Double Room"])
        };

        var result = key.ToString();

        Assert.Equal("R|Standard Double Room", result);
    }

    [Fact]
    public void ToString_WithMultipleRoomIds_ReturnsExpectedFormat()
    {
        var key = new PackageHotelOfferVariantPriceKey
        {
            Refundability = Refundability.NonRefundable,
            RoomIds = new RoomIds(["Standard Double Room", "Deluxe Suite"])
        };

        var result = key.ToString();

        Assert.Equal("N|Deluxe Suite,Standard Double Room", result);
    }

    [Fact]
    public void TryParse_ValidStringWithRoomId_ReturnsTrue()
    {
        var success = PackageHotelOfferVariantPriceKey.TryParse("R|Standard Double Room", null, out var result);

        Assert.True(success);
        Assert.Equal(Refundability.Refundable, result.Refundability);
        Assert.Equal(new RoomIds(["Standard Double Room"]), result.RoomIds);
    }
}
