using Esky.Packages.Domain.Model.Markets;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Model.Markets;

public class MarketTests
{
    [Theory]
    [InlineData("pl", "Europe/Warsaw")]
    [InlineData("uk", "Europe/London")]
    [InlineData("hu", "Europe/Budapest")]
    [InlineData("sk", "Europe/Bratislava")]
    [InlineData("ro", "Europe/Bucharest")]
    [InlineData("bg", "Europe/Sofia")]
    [InlineData("hr", "Europe/Zagreb")]
    [InlineData("cz", "Europe/Prague")]
    [InlineData("rs", "Europe/Belgrade")]
    [InlineData("fi", "Europe/Helsinki")]
    [InlineData("nl", "Europe/Amsterdam")]
    [InlineData("fr", "Europe/Paris")]
    [InlineData("ie", "Europe/Dublin")]
    [InlineData("es", "Europe/Madrid")]
    public void Create_WithValidTimezone_ShouldSetTimezoneCorrectly(string marketId, string timezoneId)
    {
        // Arrange
        var timeZone = TimeZoneInfo.FindSystemTimeZoneById(timezoneId);
        var currency = new Currency("EUR");
        var partnerCode = "TEST";
        var departureAirports = new[] { new Airport("LHR") };
        var providerConfigurationIds = new[] { new ProviderConfigurationId("123|test") };

        // Act
        var market = Market.Create(
            marketId,
            currency,
            partnerCode,
            departureAirports,
            providerConfigurationIds,
            enableInboundOutboundFlightDepartureHours: true,
            emitPriceHistoryEvents: false,
            timeZone);

        // Assert
        market.Id.ShouldBe(marketId);
        market.TimeZone.Id.ShouldBe(timezoneId);
    }

    [Theory]
    [InlineData("pl", "Europe/Warsaw")]
    [InlineData("uk", "Europe/London")]
    [InlineData("hu", "Europe/Budapest")]
    [InlineData("cz", "Europe/Prague")]
    [InlineData("fi", "Europe/Helsinki")]
    public void GetMarketDateOnlyForUtc_WithSpecificDateTime_ShouldReturnCorrectLocalDate(string marketId, string timezoneId)
    {
        // Arrange
        var timeZone = TimeZoneInfo.FindSystemTimeZoneById(timezoneId);
        var currency = new Currency("EUR");
        var partnerCode = "TEST";
        var departureAirports = new[] { new Airport("LHR") };
        var providerConfigurationIds = new[] { new ProviderConfigurationId("123|test") };

        var market = Market.Create(
            marketId,
            currency,
            partnerCode,
            departureAirports,
            providerConfigurationIds,
            enableInboundOutboundFlightDepartureHours: true,
            emitPriceHistoryEvents: false,
            timeZone);

        var utcDateTime = new DateTime(2024, 6, 15, 12, 0, 0, DateTimeKind.Utc);

        // Act
        var result = market.GetMarketDateOnlyForUtc(utcDateTime);

        // Assert
        var expectedLocalTime = TimeZoneInfo.ConvertTimeFromUtc(utcDateTime, timeZone);
        var expectedDateOnly = DateOnly.FromDateTime(expectedLocalTime);
        
        result.ShouldBe(expectedDateOnly);
    }
} 