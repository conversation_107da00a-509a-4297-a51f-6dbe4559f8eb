using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlightVariants;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Model.PackageFlightVariants;

public class PackageFlightVariantIdTests
{
    [Fact]
    public void Constructor_WithValidStringId_ParsesCorrectly()
    {
        // Arrange
        var id = "250101:7:pl:LAX:WAW:A2:M:M";

        // Act
        var packageFlightVariantId = new PackageFlightVariantId(id);

        // Assert
        packageFlightVariantId.CheckIn.ShouldBe(DateOnly.ParseExact("250101", "yyMMdd"));
        packageFlightVariantId.StayLength.ShouldBe(7);
        packageFlightVariantId.MarketId.ShouldBe("pl");
        packageFlightVariantId.ArrivalAirport.ShouldBe(new Airport("LAX"));
        packageFlightVariantId.DepartureAirport.ShouldBe(new Airport("WAW"));
        packageFlightVariantId.Occupancy.ShouldBe(new PackageOccupancy(2, 0, 0, 0));
        packageFlightVariantId.InboundDeparture.ShouldBe(TimeOfDay.Morning);
        packageFlightVariantId.OutboundDeparture.ShouldBe(TimeOfDay.Morning);
    }

    [Fact]
    public void Constructor_WithInvalidStringId_ThrowsFormatException()
    {
        // Arrange
        var id = "invalid:id";

        // Act & Assert
        Should.Throw<FormatException>(() => new PackageFlightVariantId(id));
    }

    [Fact]
    public void Equals_WithSameValues_ReturnsTrue()
    {
        // Arrange
        var id1 = new PackageFlightVariantId("250101:7:pl:LAX:WAW:A2:M:M");
        var id2 = new PackageFlightVariantId("250101:7:pl:LAX:WAW:A2:M:M");

        // Act & Assert
        id1.Equals(id2).ShouldBeTrue();
    }

    [Fact]
    public void Equals_WithDifferentValues_ReturnsFalse()
    {
        // Arrange
        var id1 = new PackageFlightVariantId("250101:7:pl:LAX:WAW:A2:M:M");
        var id2 = new PackageFlightVariantId("250101:7:pl:LAX:KRK:A2:M:M");

        // Act & Assert
        id1.Equals(id2).ShouldBeFalse();
    }

    [Fact]
    public void ToString_ReturnsExpectedFormat()
    {
        // Arrange
        var packageFlightVariantId = new PackageFlightVariantId(
            DateOnly.ParseExact("250101", "yyMMdd"), 7, "pl", new Airport("LAX"), new Airport("WAW"), 
            new PackageOccupancy(2, 0, 0, 0), TimeOfDay.Morning, TimeOfDay.Morning);

        // Act
        var result = packageFlightVariantId.ToString();

        // Assert
        result.ShouldBe("250101:7:pl:LAX:WAW:A2:M:M");
    }

    [Fact]
    public void TryParse_WithValidString_ReturnsTrue()
    {
        // Arrange
        var id = "250101:7:pl:LAX:WAW:A2:M:M";

        // Act
        var success = PackageFlightVariantId.TryParse(id, null, out var packageFlightVariantId);

        // Assert
        success.ShouldBeTrue();
        packageFlightVariantId.CheckIn.ShouldBe(DateOnly.ParseExact("250101", "yyMMdd"));
        packageFlightVariantId.StayLength.ShouldBe(7);
        packageFlightVariantId.MarketId.ShouldBe("pl");
        packageFlightVariantId.DepartureAirport.ShouldBe(new Airport("WAW"));
        packageFlightVariantId.ArrivalAirport.ShouldBe(new Airport("LAX"));
        packageFlightVariantId.Occupancy.ShouldBe(new PackageOccupancy(2, 0, 0, 0));
        packageFlightVariantId.InboundDeparture.ShouldBe(TimeOfDay.Morning);
        packageFlightVariantId.OutboundDeparture.ShouldBe(TimeOfDay.Morning);
    }

    [Fact]
    public void TryParse_WithInvalidString_ReturnsFalse()
    {
        // Arrange
        var id = "invalid:id";

        // Act
        var success = PackageFlightVariantId.TryParse(id, null, out var packageFlightVariantId);

        // Assert
        success.ShouldBeFalse();
        packageFlightVariantId.ShouldBe(default);
    }
}