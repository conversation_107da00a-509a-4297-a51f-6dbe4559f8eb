using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Model.PackageFlights;

public class PackageFlightIdTests
{
    [Fact]
    public void Constructor_WithValidStringId_ParsesCorrectly()
    {
        // Arrange
        var id = "250101:7:pl:JFK:LAX";

        // Act
        var packageFlightId = new PackageFlightId(id);

        // Assert
        packageFlightId.CheckIn.ShouldBe(DateOnly.ParseExact("250101", "yyMMdd"));
        packageFlightId.StayLength.ShouldBe(7);
        packageFlightId.MarketId.ShouldBe("pl");
        packageFlightId.ArrivalAirport.ShouldBe(new Airport("JFK"));
        packageFlightId.DepartureAirport.ShouldBe(new Airport("LAX"));
    }

    [Fact]
    public void Constructor_WithInvalidStringId_ThrowsFormatException()
    {
        // Arrange
        var id = "invalid:id";

        // Act & Assert
        Should.Throw<FormatException>(() => new PackageFlightId(id));
    }

    [Fact]
    public void Equals_WithSameValues_ReturnsTrue()
    {
        // Arrange
        var id1 = new PackageFlightId("250101:7:pl:JFK:LAX");
        var id2 = new PackageFlightId("250101:7:pl:JFK:LAX");

        // Act & Assert
        id1.Equals(id2).ShouldBeTrue();
    }

    [Fact]
    public void Equals_WithDifferentValues_ReturnsFalse()
    {
        // Arrange
        var id1 = new PackageFlightId("250101:7:pl:JFK:LAX");
        var id2 = new PackageFlightId("250101:7:pl:JFK:SFO");

        // Act & Assert
        id1.Equals(id2).ShouldBeFalse();
    }

    [Fact]
    public void ToString_ReturnsExpectedFormat()
    {
        // Arrange
        var packageFlightId = new PackageFlightId(DateOnly.ParseExact("250101", "yyMMdd"), 7, "pl", new Airport("JFK"), new Airport("LAX"));

        // Act
        var result = packageFlightId.ToString();

        // Assert
        result.ShouldBe("250101:7:pl:JFK:LAX");
    }

    [Fact]
    public void TryParse_WithValidString_ReturnsTrue()
    {
        // Arrange
        var id = "250101:7:pl:JFK:LAX";

        // Act
        var success = PackageFlightId.TryParse(id, null, out var packageFlightId);

        // Assert
        success.ShouldBeTrue();
        packageFlightId.CheckIn.ShouldBe(DateOnly.ParseExact("250101", "yyMMdd"));
        packageFlightId.StayLength.ShouldBe(7);
        packageFlightId.MarketId.ShouldBe("pl");
        packageFlightId.ArrivalAirport.ShouldBe(new Airport("JFK"));
        packageFlightId.DepartureAirport.ShouldBe(new Airport("LAX"));
    }

    [Fact]
    public void TryParse_WithInvalidString_ReturnsFalse()
    {
        // Arrange
        var id = "invalid:id";

        // Act
        var success = PackageFlightId.TryParse(id, null, out var packageFlightId);

        // Assert
        success.ShouldBeFalse();
        packageFlightId.ShouldBe(default);
    }
}
