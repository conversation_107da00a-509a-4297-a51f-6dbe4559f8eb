using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Model.PackageFlights.Exceptions;

namespace Esky.Packages.Domain.Tests.Model.PackageFlights;

public class FlightCheckInCheckOutTests
{
    [Fact]
    public void WhenReturnDepartureDateBeforeArrivalDate_ThrowsException()
    {
        // Arrange
        var arrivalDate = new DateTime(2025, 3, 19, 10, 0, 0);
        var returnDepartureDate = new DateTime(2025, 3, 18, 10, 0, 0);

        // Act & Assert
        Should.Throw<ReturnDepartureDateBeforeArrivalException>(() =>
            new FlightCheckInCheckOut(arrivalDate, returnDepartureDate));
    }

    [Fact]
    public void WithValidDatesBeforeThreshold_CalculatesCheckInCheckOutAsPreviousDays()
    {
        // Arrange
        var arrivalDate = new DateTime(2025, 3, 12, 3, 0, 0); // 3:00 AM
        var returnDepartureDate = new DateTime(2025, 3, 19, 1, 0, 0); // 1:00 AM

        // Act
        var result = new FlightCheckInCheckOut(arrivalDate, returnDepartureDate);

        // Assert
        result.CheckIn.ShouldBe(new DateOnly(2025, 3, 11));
        result.CheckOut.ShouldBe(new DateOnly(2025, 3, 18));
        result.StayLength.ShouldBe(7);
    }

    [Fact]
    public void WithValidDatesAfterThreshold_CalculatesCorrectCheckInAsCurrentDays()
    {
        // Arrange
        var arrivalDate = new DateTime(2025, 3, 12, 5, 0, 0); // 5:00 AM
        var returnDepartureDate = new DateTime(2025, 3, 19, 10, 0, 0); // 10:00 AM

        // Act
        var result = new FlightCheckInCheckOut(arrivalDate, returnDepartureDate);

        // Assert
        result.CheckIn.ShouldBe(new DateOnly(2025, 3, 12));
        result.CheckOut.ShouldBe(new DateOnly(2025, 3, 19));
        result.StayLength.ShouldBe(7);
    }

    [Fact]
    public void WithValidDatesExactlyAtThreshold_CalculatesCheckInAndCheckOutAsCurrentDays()
    {
        // Arrange
        var arrivalDate = new DateTime(2025, 3, 12, 4, 0, 0); // 4:00 AM
        var returnDepartureDate = new DateTime(2025, 3, 19, 2, 0, 0); // 2:00 AM

        // Act
        var result = new FlightCheckInCheckOut(arrivalDate, returnDepartureDate);

        // Assert
        result.CheckIn.ShouldBe(new DateOnly(2025, 3, 12));
        result.CheckOut.ShouldBe(new DateOnly(2025, 3, 19));
        result.StayLength.ShouldBe(7);
    }
}