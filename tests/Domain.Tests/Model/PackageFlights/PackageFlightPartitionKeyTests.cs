using Esky.Packages.Domain.Model.PackageFlights;

namespace Esky.Packages.Domain.Tests.Model.PackageFlights;

public class PackageFlightPartitionKeyTests
{
    [Fact]
    public void Constructor_WithValidStringKey_ParsesCorrectly()
    {
        // Arrange
        var key = "JFKLAX";

        // Act
        var partitionKey = new PackageFlightPartitionKey(key);

        // Assert
        partitionKey.AirportPair.ShouldBe(["JFK", "LAX"]);
    }

    [Fact]
    public void Constructor_WithInvalidStringKey_ThrowsFormatException()
    {
        // Arrange
        var key = "invalid";

        // Act & Assert
        Should.Throw<FormatException>(() => new PackageFlightPartitionKey(key));
    }

    [Fact]
    public void Equals_WithSameValues_ReturnsTrue()
    {
        // Arrange
        var key1 = new PackageFlightPartitionKey("JFKLAX");
        var key2 = new PackageFlightPartitionKey("JFKLAX");

        // Act & Assert
        key1.Equals(key2).ShouldBeTrue();
    }

    [Fact]
    public void Equals_WithDifferentValues_ReturnsFalse()
    {
        // Arrange
        var key1 = new PackageFlightPartitionKey("JFKLAX");
        var key2 = new PackageFlightPartitionKey("JFKSFO");

        // Act & Assert
        key1.Equals(key2).ShouldBeFalse();
    }

    [Fact]
    public void ToString_ReturnsExpectedFormat()
    {
        // Arrange
        var partitionKey = new PackageFlightPartitionKey("JFK", "LAX");

        // Act
        var result = partitionKey.ToString();

        // Assert
        result.ShouldBe("JFKLAX");
    }

    [Fact]
    public void TryParse_WithValidString_ReturnsTrue()
    {
        // Arrange
        var key = "JFKLAX";

        // Act
        var success = PackageFlightPartitionKey.TryParse(key, null, out var partitionKey);

        // Assert
        success.ShouldBeTrue();
        partitionKey.AirportPair.ShouldBe(["JFK", "LAX"]);
    }

    [Fact]
    public void TryParse_WithInvalidString_ReturnsFalse()
    {
        // Arrange
        var key = "invalid";

        // Act
        var success = PackageFlightPartitionKey.TryParse(key, null, out var partitionKey);

        // Assert
        success.ShouldBeFalse();
        partitionKey.AirportPair.ShouldBe(default);
    }
}
