using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Model.Common;

public class PackageOccupancyTests
{
    [Theory]
    [InlineData(1, new[] { 5 }, 1, new[] { 0, 1, 0 })]
    [InlineData(1, new[] { 13 }, 1, new[] { 1, 0, 0 })]
    [InlineData(2, new[] { 1 }, 2, new[] { 0, 0, 1 })]
    [InlineData(2, new[] { 5 }, 2, new[] { 0, 1, 0 })]
    [InlineData(2, new[] { 13 }, 2, new[] { 1, 0, 0 })]
    [InlineData(2, new[] { 5, 5 }, 2, new[] { 0, 2, 0 })]
    [InlineData(2, new[] { 5, 13 }, 2, new[] { 1, 1, 0 })]
    [InlineData(2, new[] { 13, 13 }, 2, new[] { 2, 0, 0 })]
    public void MapWithChildren_ShouldReturnExpectedResults(int inputAdults, int[] childrenAges, int expectedAdults, 
        int[] expectedBuckets) 
    {
        // Arrange
        var occupancy = new Occupancy(inputAdults, childrenAges);
        var expected = new PackageOccupancy(expectedAdults, expectedBuckets[0], expectedBuckets[1], expectedBuckets[2]);

        // Act
        var result = PackageOccupancy.FromOccupancy(occupancy);

        // Assert
        result.ShouldBeEquivalentTo(expected);
    }
    
    [Theory]
    [InlineData(1, 1)]
    [InlineData(2, 2)]
    [InlineData(3, 3)]
    [InlineData(4, 4)]
    public void Map_ShouldReturnExpectedResults(int inputAdults, int expectedAdults) 
    {
        // Arrange
        var occupancy = new Occupancy(inputAdults, []);
        var expected = new PackageOccupancy(expectedAdults, 0, 0, 0);

        // Act
        var result = PackageOccupancy.FromOccupancy(occupancy);

        // Assert
        result.ShouldBeEquivalentTo(expected);
    }
}