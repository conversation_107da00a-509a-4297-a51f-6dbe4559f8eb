using Esky.Packages.Domain.Types.Extensions;

namespace Esky.Packages.Domain.Tests.Model.Common;

public class DateExtensionsTests
{
    [Fact]
    public void DifferenceInStayLength_ShouldReturnCorrectDifference()
    {
        // Arrange
        var checkIn = new DateOnly(2025, 3, 21);
        var checkOut = new DateOnly(2025, 3, 14);

        // Act
        var stayLength = checkIn.DifferenceInStayLength(checkOut);

        // Assert
        stayLength.ShouldBe(7);
    }

    [Fact]
    public void Minus_ShouldReturnCorrectTimeSpan()
    {
        // Arrange
        var checkIn = new DateOnly(2025, 3, 21);
        var checkOut = new DateOnly(2025, 3, 14);

        // Act
        var difference = checkIn.Minus(checkOut);

        // Assert
        difference.ShouldBe(TimeSpan.FromDays(7));
    }

    [Fact]
    public void ToDateOnly_ShouldReturnCorrectDateOnly()
    {
        // Arrange
        var dateTime = new DateTime(2025, 3, 21, 10, 30, 0);

        // Act
        var dateOnly = dateTime.ToDateOnly();

        // Assert
        dateOnly.ShouldBe(new DateOnly(2025, 3, 21));
    }

    [Fact]
    public void GetDateChunks_ShouldHandleSingleChunk()
    {
        // Arrange
        var minCheckIn = new DateOnly(2025, 3, 1);
        var maxCheckIn = new DateOnly(2025, 3, 5);
        var chunks = 1;

        // Act
        var result = DateExtensions.GetDateChunks(minCheckIn, maxCheckIn, chunks);

        // Assert
        result.ShouldBe(new[]
        {
            (DateOnly.MinValue, minCheckIn),
            (minCheckIn, maxCheckIn),
            (maxCheckIn, DateOnly.MaxValue)
        });
    }

    [Fact]
    public void GetDateChunks_ShouldDivideDateRangeIntoEqualChunks()
    {
        // Arrange
        var minCheckIn = new DateOnly(2025, 3, 1);
        var maxCheckIn = new DateOnly(2025, 3, 11);
        var chunks = 2;

        // Act
        var result = DateExtensions.GetDateChunks(minCheckIn, maxCheckIn, chunks);

        // Assert
        result.ShouldBe(new[]
        {
            (DateOnly.MinValue, minCheckIn),
            (minCheckIn, minCheckIn.AddDays(5)),
            (minCheckIn.AddDays(5), maxCheckIn),
            (maxCheckIn, DateOnly.MaxValue)
        });
    }

    [Fact]
    public void GetDateChunks_ShouldHandleMultipleChunks()
    {
        // Arrange
        var minCheckIn = new DateOnly(2025, 3, 1);
        var maxCheckIn = new DateOnly(2025, 3, 10);
        var chunks = 3;

        // Act
        var result = DateExtensions.GetDateChunks(minCheckIn, maxCheckIn, chunks);

        // Assert
        result.ShouldBe(new[]
        {
            (DateOnly.MinValue, minCheckIn),
            (minCheckIn, minCheckIn.AddDays(3)),
            (minCheckIn.AddDays(3), minCheckIn.AddDays(6)),
            (minCheckIn.AddDays(6), maxCheckIn),
            (maxCheckIn, DateOnly.MaxValue)
        });
    }
}
