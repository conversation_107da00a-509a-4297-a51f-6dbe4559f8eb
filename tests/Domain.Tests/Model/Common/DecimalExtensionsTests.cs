using Esky.Packages.Domain.Types.Extensions;

namespace Esky.Packages.Domain.Tests.Model.Common;

public class DecimalExtensionsTests
{
    [Theory]
    [InlineData(1, 1)]
    [InlineData(1.2, 1)]
    [InlineData(1.4, 1)]
    [InlineData(1.5, 2)]
    [InlineData(1.6, 2)]
    [InlineData(1.9, 2)]
    [InlineData(2, 2)]
    public void RoundAwayFromZero_ShouldReturnExpectedResults(decimal input, decimal expected)
    {
        // Act
        var result = input.RoundAwayFromZero();

        // Assert
        Assert.Equal(expected, result);
    }
}
