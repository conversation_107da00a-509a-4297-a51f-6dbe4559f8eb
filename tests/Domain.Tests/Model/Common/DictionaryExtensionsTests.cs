using Esky.Packages.Domain.Types.Extensions;

namespace Esky.Packages.Domain.Tests.Model.Common;

public class DictionaryExtensionsTests
{
    [Fact]
    public void DeepEquals_DictionariesWithSameContent_ReturnsTrue()
    {
        var dict1 = new Dictionary<string, int>
        {
            { "key1", 100 },
            { "key2", 200 }
        };

        var dict2 = new Dictionary<string, int>
        {
            { "key1", 100 },
            { "key2", 200 }
        };

        var result = dict1.DeepEquals(dict2);

        result.ShouldBeTrue();
    }

    [Fact]
    public void DeepEquals_DictionariesWithDifferentValues_ReturnsFalse()
    {
        var dict1 = new Dictionary<string, int>
        {
            { "key1", 100 },
            { "key2", 200 }
        };

        var dict2 = new Dictionary<string, int>
        {
            { "key1", 100 },
            { "key2", 300 }
        };

        var result = dict1.DeepEquals(dict2);

        result.ShouldBeFalse();
    }

    [Fact]
    public void DeepEquals_DictionariesWithDifferentKeys_ReturnsFalse()
    {
        var dict1 = new Dictionary<string, int>
        {
            { "key1", 100 },
            { "key2", 200 }
        };

        var dict2 = new Dictionary<string, int>
        {
            { "key1", 100 },
            { "key3", 200 }
        };

        var result = dict1.DeepEquals(dict2);

        result.ShouldBeFalse();
    }

    [Fact]
    public void DeepEquals_DictionariesWithDifferentCounts_ReturnsFalse()
    {
        var dict1 = new Dictionary<string, int>
        {
            { "key1", 100 }
        };

        var dict2 = new Dictionary<string, int>
        {
            { "key1", 100 },
            { "key2", 200 }
        };

        var result = dict1.DeepEquals(dict2);

        result.ShouldBeFalse();
    }

    [Fact]
    public void DeepEquals_NestedDictionariesWithSameContent_ReturnsTrue()
    {
        var dict1 = new Dictionary<string, Dictionary<string, int>>
        {
            { "level1Key1", new Dictionary<string, int> { { "level2Key1", 100 } } },
            { "level1Key2", new Dictionary<string, int> { { "level2Key2", 200 } } }
        };

        var dict2 = new Dictionary<string, Dictionary<string, int>>
        {
            { "level1Key1", new Dictionary<string, int> { { "level2Key1", 100 } } },
            { "level1Key2", new Dictionary<string, int> { { "level2Key2", 200 } } }
        };

        var result = dict1.DeepEquals(dict2);

        result.ShouldBeTrue();
    }

    [Fact]
    public void DeepEquals_NestedDictionariesWithDifferentValues_ReturnsFalse()
    {
        var dict1 = new Dictionary<string, Dictionary<string, int>>
        {
            { "level1Key1", new Dictionary<string, int> { { "level2Key1", 100 } } }
        };

        var dict2 = new Dictionary<string, Dictionary<string, int>>
        {
            { "level1Key1", new Dictionary<string, int> { { "level2Key1", 200 } } }
        };

        var result = dict1.DeepEquals(dict2);

        result.ShouldBeFalse();
    }

    [Fact]
    public void DeepEquals_NestedDictionariesWithDifferentKeys_ReturnsFalse()
    {
        var dict1 = new Dictionary<string, Dictionary<string, int>>
        {
            { "level1Key1", new Dictionary<string, int> { { "level2Key1", 100 } } }
        };

        var dict2 = new Dictionary<string, Dictionary<string, int>>
        {
            { "level1Key2", new Dictionary<string, int> { { "level2Key1", 100 } } }
        };

        var result = dict1.DeepEquals(dict2);

        result.ShouldBeFalse();
    }

    [Fact]
    public void DeepEquals_ThreeLevelDictionariesWithSameContent_ReturnsTrue()
    {
        var dict1 = new Dictionary<string, Dictionary<string, Dictionary<string, int>>>
        {
            {
                "level1Key1",
                new Dictionary<string, Dictionary<string, int>>
                {
                    { "level2Key1", new Dictionary<string, int> { { "level3Key1", 100 } } }
                }
            }
        };

        var dict2 = new Dictionary<string, Dictionary<string, Dictionary<string, int>>>
        {
            {
                "level1Key1",
                new Dictionary<string, Dictionary<string, int>>
                {
                    { "level2Key1", new Dictionary<string, int> { { "level3Key1", 100 } } }
                }
            }
        };

        var result = dict1.DeepEquals(dict2);

        result.ShouldBeTrue();
    }

    [Fact]
    public void DeepEquals_ThreeLevelDictionariesWithDifferentValues_ReturnsFalse()
    {
        var dict1 = new Dictionary<string, Dictionary<string, Dictionary<string, int>>>
        {
            {
                "level1Key1",
                new Dictionary<string, Dictionary<string, int>>
                {
                    { "level2Key1", new Dictionary<string, int> { { "level3Key1", 100 } } }
                }
            }
        };

        var dict2 = new Dictionary<string, Dictionary<string, Dictionary<string, int>>>
        {
            {
                "level1Key1",
                new Dictionary<string, Dictionary<string, int>>
                {
                    { "level2Key1", new Dictionary<string, int> { { "level3Key1", 200 } } }
                }
            }
        };

        var result = dict1.DeepEquals(dict2);

        result.ShouldBeFalse();
    }

    [Fact]
    public void DeepEquals_DictionariesWithCustomValueComparer_ReturnsTrue()
    {
        var dict1 = new Dictionary<string, string>
        {
            { "key1", "Value" }
        };

        var dict2 = new Dictionary<string, string>
        {
            { "key1", "value" }
        };

        var result = dict1.DeepEquals(dict2, (x, y) => string.Equals(x, y, StringComparison.OrdinalIgnoreCase));

        result.ShouldBeTrue();
    }

    [Fact]
    public void DeepEquals_DictionariesWithNullValues_ThrowsArgumentException()
    {
        var dict1 = new Dictionary<string, string>
        {
            { "key1", null! }
        };

        var dict2 = new Dictionary<string, string>
        {
            { "key1", null! }
        };

        Should.Throw<ArgumentException>(() => dict1.DeepEquals(dict2));
    }
}