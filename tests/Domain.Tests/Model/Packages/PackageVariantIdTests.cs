using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageVariants;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Model.Packages;

public class PackageVariantIdTests
{
    [Fact]
    public void Constructor_WithValidStringId_ParsesCorrectly()
    {
        // Arrange
        var id = "250101:7:pl:1234:A2:B:R:R001,R002:JFK:KRK:250102:250108:100,200:300,400:True";

        // Act
        var packageVariantId = new PackageVariantId(id);

        // Assert
        packageVariantId.CheckIn.ShouldBe(DateOnly.ParseExact("250101", "yyMMdd"));
        packageVariantId.StayLength.ShouldBe(7);
        packageVariantId.MarketId.ShouldBe("pl");
        packageVariantId.MetaCode.ShouldBe(1234);
        packageVariantId.Occupancy.ShouldBe(new PackageOccupancy(2, 0, 0, 0));
        packageVariantId.MealPlan.ShouldBe(MealPlan.Breakfast);
        packageVariantId.Refundability.ShouldBe(new Refundability(true));
        packageVariantId.RoomIds.ShouldBe(new RoomIds(["R001", "R002"]));
        packageVariantId.ArrivalAirport.ShouldBe(new Airport("JFK"));
        packageVariantId.DepartureAirport.ShouldBe(new Airport("KRK"));
        packageVariantId.DepartureDate.ShouldBe(DateOnly.ParseExact("250102", "yyMMdd"));
        packageVariantId.ReturnDepartureDate.ShouldBe(DateOnly.ParseExact("250108", "yyMMdd"));
        packageVariantId.FlightNumbers.ShouldBe(new FlightNumbers(["100", "200"]));
        packageVariantId.ReturnFlightNumbers.ShouldBe(new FlightNumbers(["300", "400"]));
        packageVariantId.BaggageIncluded.ShouldBe(true);
    }

    [Fact]
    public void Constructor_WithInvalidStringId_ThrowsFormatException()
    {
        // Arrange
        var id = "invalid:id";

        // Act & Assert
        Should.Throw<FormatException>(() => new PackageVariantId(id));
    }

    [Fact]
    public void Equals_WithSameValues_ReturnsTrue()
    {
        // Arrange
        var id1 = new PackageVariantId("250101:7:pl:1234:A2:B:R:R001,R002:JFK:KRK:250102:250108:100,200:300,400:True");
        var id2 = new PackageVariantId("250101:7:pl:1234:A2:B:R:R001,R002:JFK:KRK:250102:250108:100,200:300,400:True");

        // Act & Assert
        id1.Equals(id2).ShouldBeTrue();
    }

    [Fact]
    public void Equals_WithDifferentValues_ReturnsFalse()
    {
        // Arrange
        var id1 = new PackageVariantId("250101:7:pl:1234:A2:B:R:R001,R002:JFK:KRK:250102:250108:100,200:300,400:True");
        var id2 = new PackageVariantId("250101:7:pl:5678:A2:B:R:R001,R002:JFK:KRK:250102:250108:100,200:300,400:True");

        // Act & Assert
        id1.Equals(id2).ShouldBeFalse();
    }

    [Fact]
    public void ToString_ReturnsExpectedFormat()
    {
        // Arrange
        var packageVariantId = new PackageVariantId(
            DateOnly.ParseExact("250101", "yyMMdd"), 7, "pl", 1234,
            new PackageOccupancy(2, 0, 0, 0), MealPlan.Breakfast,
            new Refundability(true), new RoomIds(["R001", "R002"]), new Airport("JFK"), new Airport("KRK"),
            DateOnly.ParseExact("250102", "yyMMdd"), DateOnly.ParseExact("250108", "yyMMdd"),
            new FlightNumbers(["100", "200"]), new FlightNumbers(["300", "400"]), true);

        // Act
        var result = packageVariantId.ToString();

        // Assert
        result.ShouldBe("250101:7:pl:1234:A2:B:R:R001,R002:JFK:KRK:250102:250108:100,200:300,400:True");
    }

    [Fact]
    public void TryParse_WithValidString_ReturnsTrue()
    {
        // Arrange
        var id = "250101:7:pl:1234:A2:B:R:R001,R002:JFK:KRK:250102:250108:100,200:300,400:True";

        // Act
        var success = PackageVariantId.TryParse(id, null, out var packageVariantId);

        // Assert
        success.ShouldBeTrue();
        packageVariantId.CheckIn.ShouldBe(DateOnly.ParseExact("250101", "yyMMdd"));
        packageVariantId.StayLength.ShouldBe(7);
        packageVariantId.MarketId.ShouldBe("pl");
        packageVariantId.MetaCode.ShouldBe(1234);
        packageVariantId.Occupancy.ShouldBe(new PackageOccupancy(2, 0, 0, 0));
        packageVariantId.MealPlan.ShouldBe(MealPlan.Breakfast);
        packageVariantId.Refundability.ShouldBe(new Refundability(true));
        packageVariantId.RoomIds.ShouldBe(new RoomIds(["R001", "R002"]));
        packageVariantId.ArrivalAirport.ShouldBe(new Airport("JFK"));
        packageVariantId.DepartureAirport.ShouldBe(new Airport("KRK"));
        packageVariantId.DepartureDate.ShouldBe(DateOnly.ParseExact("250102", "yyMMdd"));
        packageVariantId.ReturnDepartureDate.ShouldBe(DateOnly.ParseExact("250108", "yyMMdd"));
        packageVariantId.FlightNumbers.ShouldBe(new FlightNumbers(["100", "200"]));
        packageVariantId.ReturnFlightNumbers.ShouldBe(new FlightNumbers(["300", "400"]));
        packageVariantId.BaggageIncluded.ShouldBe(true);
    }
}
