using Esky.Packages.Domain.Model.Packages;

namespace Esky.Packages.Domain.Tests.Model.Packages;

public class PackageIdTests
{
    [Fact]
    public void Constructor_WithValidStringId_ParsesCorrectly()
    {
        // Arrange
        var id = "250101:7:pl:1234";

        // Act
        var packageId = new PackageId(id);

        // Assert
        packageId.CheckIn.ShouldBe(DateOnly.ParseExact("250101", "yyMMdd"));
        packageId.StayLength.ShouldBe(7);
        packageId.MarketId.ShouldBe("pl");
        packageId.MetaCode.ShouldBe(1234);
    }

    [Fact]
    public void Constructor_WithInvalidStringId_ThrowsFormatException()
    {
        // Arrange
        var id = "invalid:id";

        // Act & Assert
        Should.Throw<FormatException>(() => new PackageId(id));
    }

    [Fact]
    public void Equals_WithSameValues_ReturnsTrue()
    {
        // Arrange
        var id1 = new PackageId("250101:7:pl:1234");
        var id2 = new PackageId("250101:7:pl:1234");

        // Act & Assert
        id1.Equals(id2).ShouldBeTrue();
    }

    [Fact]
    public void Equals_WithDifferentValues_ReturnsFalse()
    {
        // Arrange
        var id1 = new PackageId("250101:7:pl:1234");
        var id2 = new PackageId("250101:7:pl:5678");

        // Act & Assert
        id1.Equals(id2).ShouldBeFalse();
    }

    [Fact]
    public void ToString_ReturnsExpectedFormat()
    {
        // Arrange
        var packageId = new PackageId(DateOnly.ParseExact("250101", "yyMMdd"), 7, "pl", 1234);

        // Act
        var result = packageId.ToString();

        // Assert
        result.ShouldBe("250101:7:pl:1234");
    }

    [Fact]
    public void TryParse_WithValidString_ReturnsTrue()
    {
        // Arrange
        var id = "250101:7:pl:1234";

        // Act
        var success = PackageId.TryParse(id, null, out var packageId);

        // Assert
        success.ShouldBeTrue();
        packageId.CheckIn.ShouldBe(DateOnly.ParseExact("250101", "yyMMdd"));
        packageId.StayLength.ShouldBe(7);
        packageId.MarketId.ShouldBe("pl");
        packageId.MetaCode.ShouldBe(1234);
    }

    [Fact]
    public void TryParse_WithInvalidString_ReturnsFalse()
    {
        // Arrange
        var id = "invalid:id";

        // Act
        var success = PackageId.TryParse(id, null, out var packageId);

        // Assert
        success.ShouldBeFalse();
        packageId.ShouldBe(default);
    }

    [Fact]
    public void IsOldPackageId_WithOldFormat_ReturnsTrue()
    {
        // Arrange
        var oldPackageId = "060525:3:109406:pl-it";

        // Act
        var result = PackageId.IsOldPackageId(oldPackageId);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public void IsOldPackageId_WithNewFormat_ReturnsFalse()
    {
        // Arrange
        var newPackageId = "250522:3:pl:4315873";

        // Act
        var result = PackageId.IsOldPackageId(newPackageId);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public void ParseFromOldPackageId_WithValidOldFormat_ParsesCorrectly()
    {
        // Arrange
        var oldPackageId = "060525:3:109406:pl-it";

        // Act
        var packageId = PackageId.ParseFromOldPackageId(oldPackageId);

        // Assert
        packageId.CheckIn.ShouldBe(DateOnly.ParseExact("250506", "yyMMdd"));
        packageId.StayLength.ShouldBe(3);
        packageId.MarketId.ShouldBe("pl");
        packageId.MetaCode.ShouldBe(109406);
    }
}
