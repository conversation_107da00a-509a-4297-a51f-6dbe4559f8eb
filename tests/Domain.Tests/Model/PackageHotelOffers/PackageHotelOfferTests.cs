using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Common.Tests.Fixtures.Domain;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Model.PackageHotelOffers;

public class PackageHotelOfferTests
{
    [Fact]
    public void Create_WithDifferentHotelOfferQuotesCurrency_ConvertsToPackageHotelOfferCurrency()
    {
        // Arrange
        var fixture = new PackageHotelOfferTestFixture()
            .WithCurrencyConversion(Currency.PLN, 0.2M);
        var occupancy = new Occupancy(adults: 2, childrenAges: []);
        var roomOffers = new Dictionary<MealPlan, Dictionary<Refundability, RoomOffer[]>>
        {
            {
                MealPlan.Breakfast,
                new Dictionary<Refundability, RoomOffer[]>
                {
                    { Refundability.Refundable, [new RoomOffer { Availability = 1, Price = new Money(100M, Currency.PLN), RoomIds = new RoomIds(["123"]) }] }
                }
            }
        };
        var hotelOfferQuote = fixture.CreateHotelOffer(roomOffersByMealPlanByRefundability: roomOffers, occupancy: occupancy);

        // Act
        var packageHotelOffer = fixture.CreatePackageHotelOffer(hotelOffers: [hotelOfferQuote]);

        // Assert
        packageHotelOffer.RoomOffersByProviderConfigurationIdByOccupancy[new ProviderConfigurationId("OTS|123")][PackageOccupancy.FromOccupancy(occupancy)].RoomOffersByMealPlan[MealPlan.Breakfast].First().CompensatedPrice.ShouldBe(20);
        packageHotelOffer.Currency.ShouldBe(Currency.GBP);
    }

    [Fact]
    public void Compare_WithSamePackageHotelOffers_ReturnsTrue()
    {
        // Arrange
        var fixture = new PackageHotelOfferTestFixture()
            .WithAnyCurrencyConversion();
        var packageHotelOffer1 = fixture.CreatePackageHotelOffer();
        var packageHotelOffer2 = fixture.CreatePackageHotelOffer();

        // Act
        var result = packageHotelOffer1.Compare(packageHotelOffer2);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public void Compare_WithDifferentPricesWithinCompensation_ReturnsTrue()
    {
        // Arrange
        var fixture = new PackageHotelOfferTestFixture()
            .WithAnyCurrencyConversion();
        var roomOffers1 = new Dictionary<MealPlan, Dictionary<Refundability, RoomOffer[]>>
        {
            {
                MealPlan.Breakfast,
                new Dictionary<Refundability, RoomOffer[]>
                {
                    { Refundability.Refundable, [new RoomOffer { Availability = 1, Price = new Money(100M, Currency.GBP), RoomIds = new RoomIds(["123"]) }] }
                }
            }
        };
        var roomOffers2 = new Dictionary<MealPlan, Dictionary<Refundability, RoomOffer[]>>
        {
            {
                MealPlan.Breakfast,
                new Dictionary<Refundability, RoomOffer[]>
                {
                    { Refundability.Refundable, [new RoomOffer { Availability = 1, Price = new Money(101M, Currency.GBP), RoomIds = new RoomIds(["123"]) }] }
                }
            }
        };

        var hotelOfferQuote1 = fixture.CreateHotelOffer(roomOffersByMealPlanByRefundability: roomOffers1);
        var hotelOfferQuote2 = fixture.CreateHotelOffer(roomOffersByMealPlanByRefundability: roomOffers2);

        var packageHotelOffer1 = fixture.CreatePackageHotelOffer(hotelOffers: [hotelOfferQuote1]);
        var packageHotelOffer2 = fixture.CreatePackageHotelOffer(hotelOffers: [hotelOfferQuote2]);

        // Act
        var result = packageHotelOffer1.Compare(packageHotelOffer2);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public void Compare_WithDifferentPricesExceedingCompensation_ReturnsFalse()
    {
        // Arrange
        var fixture = new PackageHotelOfferTestFixture()
            .WithAnyCurrencyConversion();
        var roomOffers1 = new Dictionary<MealPlan, Dictionary<Refundability, RoomOffer[]>>
        {
            {
                MealPlan.Breakfast,
                new Dictionary<Refundability, RoomOffer[]>
                {
                    { Refundability.Refundable, [new RoomOffer { Availability = 1, Price = new Money(100M, Currency.GBP), RoomIds = new RoomIds(["123"]) }] }
                }
            }
        };
        var roomOffers2 = new Dictionary<MealPlan, Dictionary<Refundability, RoomOffer[]>>
        {
            {
                MealPlan.Breakfast,
                new Dictionary<Refundability, RoomOffer[]>
                {
                    { Refundability.Refundable, [new RoomOffer { Availability = 1, Price = new Money(200M, Currency.GBP), RoomIds = new RoomIds(["123"]) }] }
                }
            }
        };

        var hotelOfferQuote1 = fixture.CreateHotelOffer(roomOffersByMealPlanByRefundability: roomOffers1);
        var hotelOfferQuote2 = fixture.CreateHotelOffer(roomOffersByMealPlanByRefundability: roomOffers2);

        var packageHotelOffer1 = fixture.CreatePackageHotelOffer(hotelOffers: [hotelOfferQuote1]);
        var packageHotelOffer2 = fixture.CreatePackageHotelOffer(hotelOffers: [hotelOfferQuote2]);

        // Act
        var result = packageHotelOffer1.Compare(packageHotelOffer2);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public void ApplyQuotes_WithEmptyQuotes_DoesNotUpdatePrices()
    {
        // Arrange
        var fixture = new PackageHotelOfferTestFixture()
            .WithAnyCurrencyConversion();
        var packageHotelOffer = fixture.CreatePackageHotelOffer();
        var originalUpdatedAt = packageHotelOffer.UpdatedAt;

        // Act
        var isUpdated = packageHotelOffer.ApplyHotelOffersByStayKey([]);

        // Assert
        isUpdated.ShouldBeFalse();
        packageHotelOffer.UpdatedAt.ShouldBe(originalUpdatedAt);
    }

    [Fact]
    public void ApplyQuotes_WithNonMatchingStayKey_DoesNotUpdatePrices()
    {
        // Arrange
        var fixture = new PackageHotelOfferTestFixture()
            .WithAnyCurrencyConversion();
        var notExistingMetaCode = 999;
        var packageHotelOffer = fixture.CreatePackageHotelOffer();
        var originalUpdatedAt = packageHotelOffer.UpdatedAt;

        var nonMatchingStayKey = fixture.CreatePackageHotelOfferStayKey(metaCode: notExistingMetaCode);
        var quotes = new Dictionary<PackageHotelOfferStayKey, List<HotelOffer>>
        {
            { nonMatchingStayKey, [fixture.CreateHotelOffer()] }
        };

        // Act
        var isUpdated = packageHotelOffer.ApplyHotelOffersByStayKey(quotes);

        // Assert
        isUpdated.ShouldBeFalse();
        packageHotelOffer.UpdatedAt.ShouldBe(originalUpdatedAt);
    }

    [Fact]
    public void ApplyQuotes_WithOldHotelOfferQuote_DoesNotUpdatePrices()
    {
        // Arrange
        var fixture = new PackageHotelOfferTestFixture()
            .WithAnyCurrencyConversion();
        var hotelOfferQuote = fixture.CreateHotelOffer(updatedAt: new DateTime(2020, 1, 2));
        var packageHotelOffer = fixture.CreatePackageHotelOffer(hotelOffers: [hotelOfferQuote]);
        var originalUpdatedAt = packageHotelOffer.UpdatedAt;

        var stayKey = fixture.CreatePackageHotelOfferStayKey();
        var quotes = new Dictionary<PackageHotelOfferStayKey, List<HotelOffer>>
        {
            { stayKey, [fixture.CreateHotelOffer(updatedAt: new DateTime(2025, 1, 1))] }
        };

        // Act
        var isUpdated = packageHotelOffer.ApplyHotelOffersByStayKey(quotes);

        // Assert
        isUpdated.ShouldBeFalse();
        packageHotelOffer.UpdatedAt.ShouldBe(originalUpdatedAt);
    }

    [Fact]
    public void ApplyQuotes_WithValidHotelOfferQuotes_UpdatePrices()
    {
        // Arrange
        var fixture = new PackageHotelOfferTestFixture()
            .WithAnyCurrencyConversion();
        var packageHotelOffer = fixture.CreatePackageHotelOffer();
        var originalUpdatedAt = packageHotelOffer.UpdatedAt;

        var stayKey = fixture.CreatePackageHotelOfferStayKey();
        var roomOffers = new Dictionary<MealPlan, Dictionary<Refundability, RoomOffer[]>>
        {
            {
                MealPlan.Breakfast, new Dictionary<Refundability, RoomOffer[]>
                {
                    { Refundability.Refundable, [new RoomOffer { Availability = 1, Price = new Money(50M, Currency.GBP), RoomIds = new RoomIds(["123"]) }] }
                }
            }
        };
        
        var quotes = new Dictionary<PackageHotelOfferStayKey, List<HotelOffer>>
        {
            { stayKey, [fixture.CreateHotelOffer(roomOffersByMealPlanByRefundability: roomOffers, updatedAt: new DateTime(2025, 1, 3))] }
        };

        // Act
        var isUpdated = packageHotelOffer.ApplyHotelOffersByStayKey(quotes);

        // Assert
        isUpdated.ShouldBeTrue();
        packageHotelOffer.UpdatedAt.ShouldBeGreaterThan(originalUpdatedAt);
    }

    [Fact]
    public void ApplyQuotes_WithDiffentHotelOfferQuotesCurrency_ConvertsToPackageHotelOfferCurrency()
    {
        // Arrange
        var fixture = new PackageHotelOfferTestFixture()
            .WithAnyCurrencyConversion()
            .WithCurrencyConversion(Currency.USD, 0.77M);
        var occupancy = new Occupancy(adults: 2, childrenAges: []);
        var packageHotelOffer = fixture.CreatePackageHotelOffer();
        var originalUpdatedAt = packageHotelOffer.UpdatedAt;

        var roomOffers = new Dictionary<MealPlan, Dictionary<Refundability, RoomOffer[]>>
        {
            {
                MealPlan.Breakfast, new Dictionary<Refundability, RoomOffer[]>
                {
                    { Refundability.Refundable, [new RoomOffer { Availability = 1, Price = new Money(50M, Currency.USD), RoomIds = new RoomIds(["123"]) }] }
                }
            }
        };

        var quotes = new Dictionary<PackageHotelOfferStayKey, List<HotelOffer>>
        {
            {
                fixture.CreatePackageHotelOfferStayKey(),
                [
                    fixture.CreateHotelOffer(roomOffersByMealPlanByRefundability: roomOffers, occupancy: occupancy,
                        updatedAt: new DateTime(2025, 1, 3))
                ]
            }
        };

        // Act
        var isUpdated = packageHotelOffer.ApplyHotelOffersByStayKey(quotes);

        // Assert
        isUpdated.ShouldBeTrue();
        packageHotelOffer.UpdatedAt.ShouldBeGreaterThan(originalUpdatedAt);
        packageHotelOffer.RoomOffersByProviderConfigurationIdByOccupancy[new ProviderConfigurationId("OTS|123")][PackageOccupancy.FromOccupancy(occupancy)].RoomOffersByMealPlan[MealPlan.Breakfast].First().CompensatedPrice.ShouldBe(39);
        packageHotelOffer.Currency.ShouldBe(Currency.GBP);
    }
}