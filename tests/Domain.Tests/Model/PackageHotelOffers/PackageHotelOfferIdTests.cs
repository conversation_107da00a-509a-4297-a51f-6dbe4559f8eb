using Esky.Packages.Domain.Model.PackageHotelOffers;

namespace Esky.Packages.Domain.Tests.Model.PackageHotelOffers;

public class PackageHotelOfferIdTests
{
    [Fact]
    public void Constructor_WithValidStringId_ParsesCorrectly()
    {
        // Arrange
        var id = "250101:7:pl:1234";

        // Act
        var packageHotelOfferId = new PackageHotelOfferId(id);

        // Assert
        packageHotelOfferId.CheckIn.ShouldBe(DateOnly.ParseExact("250101", "yyMMdd"));
        packageHotelOfferId.StayLength.ShouldBe(7);
        packageHotelOfferId.MarketId.ShouldBe("pl");
        packageHotelOfferId.MetaCode.ShouldBe(1234);
    }

    [Fact]
    public void Constructor_WithInvalidStringId_ThrowsFormatException()
    {
        // Arrange
        var id = "invalid:id";

        // Act & Assert
        Should.Throw<FormatException>(() => new PackageHotelOfferId(id));
    }

    [Fact]
    public void Equals_WithSameValues_ReturnsTrue()
    {
        // Arrange
        var id1 = new PackageHotelOfferId("250101:7:pl:1234");
        var id2 = new PackageHotelOfferId("250101:7:pl:1234");

        // Act & Assert
        id1.Equals(id2).ShouldBeTrue();
    }

    [Fact]
    public void Equals_WithDifferentValues_ReturnsFalse()
    {
        // Arrange
        var id1 = new PackageHotelOfferId("250101:7:pl:1234");
        var id2 = new PackageHotelOfferId("250101:7:pl:5678");

        // Act & Assert
        id1.Equals(id2).ShouldBeFalse();
    }

    [Fact]
    public void ToString_ReturnsExpectedFormat()
    {
        // Arrange
        var packageHotelOfferId = new PackageHotelOfferId(DateOnly.ParseExact("250101", "yyMMdd"), 7, "pl", 1234);

        // Act
        var result = packageHotelOfferId.ToString();

        // Assert
        result.ShouldBe("250101:7:pl:1234");
    }

    [Fact]
    public void TryParse_WithValidString_ReturnsTrue()
    {
        // Arrange
        var id = "250101:7:pl:1234";

        // Act
        var success = PackageHotelOfferId.TryParse(id, null, out var packageHotelOfferId);

        // Assert
        success.ShouldBeTrue();
        packageHotelOfferId.CheckIn.ShouldBe(DateOnly.ParseExact("250101", "yyMMdd"));
        packageHotelOfferId.StayLength.ShouldBe(7);
        packageHotelOfferId.MarketId.ShouldBe("pl");
        packageHotelOfferId.MetaCode.ShouldBe(1234);
    }

    [Fact]
    public void TryParse_WithInvalidString_ReturnsFalse()
    {
        // Arrange
        var id = "invalid:id";

        // Act
        var success = PackageHotelOfferId.TryParse(id, null, out var packageHotelOfferId);

        // Assert
        success.ShouldBeFalse();
        packageHotelOfferId.ShouldBe(default);
    }
}
