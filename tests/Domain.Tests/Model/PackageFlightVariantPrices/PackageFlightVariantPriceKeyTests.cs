using Esky.Packages.Domain.Model.PackageFlightVariantPrices;

namespace Esky.Packages.Domain.Tests.Model.PackageFlightVariantPrices;

public class PackageFlightVariantPriceKeyTests
{
    [Theory]
    [InlineData("B", true)]
    [InlineData("N", false)]
    public void Parse_ValidString_ReturnsExpected(string input, bool expected)
    {
        var key = PackageFlightVariantPriceKey.Parse(input, null);
        Assert.Equal(expected, key.BaggageIncluded);
    }

    [Theory]
    [InlineData("")]
    [InlineData("X")]
    [InlineData("BB")]
    public void Parse_InvalidString_ReturnsDefault(string input)
    {
        var key = PackageFlightVariantPriceKey.Parse(input, null);
        Assert.False(key.BaggageIncluded);
    }

    [Theory]
    [InlineData(true, "B")]
    [InlineData(false, "N")]
    public void ToString_ReturnsExpected(bool baggageIncluded, string expected)
    {
        var key = new PackageFlightVariantPriceKey { BaggageIncluded = baggageIncluded };
        Assert.Equal(expected, key.ToString());
    }
}