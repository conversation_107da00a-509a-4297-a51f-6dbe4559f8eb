using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlightVariantPrices;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Tests.Model.PackageFlightVariantPrices;

public class PackageFlightVariantPriceTests
{
    [Fact]
    public void ApplyPrices_MergesOldAndNewPrices_TakesLowest()
    {
        var keyB = new PackageFlightVariantPriceKey { BaggageIncluded = true };
        var keyN = new PackageFlightVariantPriceKey { BaggageIncluded = false };

        var price = PackageFlightVariantPrice.Create(
            PackageFlightVariantPriceId.Create(
                DateOnly.FromDateTime(DateTime.UtcNow),
                DateOnly.FromDateTime(DateTime.UtcNow.AddDays(7)),
                "market1",
                new Airport("LON"),
                new Airport("NYC"),
                new FlightNumbers(["FL123"]),
                new FlightNumbers(["FL456"]),
                new PackageOccupancy(2, 0, 0, 0),
                DateOnly.FromDateTime(DateTime.UtcNow)
            ),
            new Dictionary<PackageFlightVariantPriceKey, int>
            {
                { keyB, 1000 },
                { keyN, 1300 }
            }
        );
        
        var oldPrices = new Dictionary<PackageFlightVariantPriceKey, int>
        {
            { keyB, 1200 },
            { keyN, 1100 }
        };

        price.ApplyPrices(oldPrices);

        Assert.Equal(1000, price.Prices[keyB]); // lowest for B
        Assert.Equal(1100, price.Prices[keyN]); // lowest for N
    }

    [Fact]
    public void ApplyPrices_ReturnsTrue_WhenPriceIsChangedOrAdded()
    {
        var keyB = new PackageFlightVariantPriceKey { BaggageIncluded = true };
        var keyN = new PackageFlightVariantPriceKey { BaggageIncluded = false };

        // Case 1: Lower price for existing key (should return true)
        var price1 = PackageFlightVariantPrice.Create(
            new PackageFlightVariantPriceId(),
            new Dictionary<PackageFlightVariantPriceKey, int>
            {
                { keyB, 1200 },
                { keyN, 1100 }
            }
        );
        var newPrices1 = new Dictionary<PackageFlightVariantPriceKey, int>
        {
            { keyB, 1000 },
            { keyN, 1300 }
        };
        Assert.True(price1.ApplyPrices(newPrices1));

        // Case 2: New key added (should return true)
        var price2 = PackageFlightVariantPrice.Create(
            new PackageFlightVariantPriceId(),
            new Dictionary<PackageFlightVariantPriceKey, int>
            {
                { keyB, 1200 }
            }
        );
        var newPrices2 = new Dictionary<PackageFlightVariantPriceKey, int>
        {
            { keyN, 1300 }
        };
        Assert.True(price2.ApplyPrices(newPrices2));

        // Case 3: No change (should return false)
        var price3 = PackageFlightVariantPrice.Create(
            new PackageFlightVariantPriceId(),
            new Dictionary<PackageFlightVariantPriceKey, int>
            {
                { keyB, 1200 }
            }
        );
        var newPrices3 = new Dictionary<PackageFlightVariantPriceKey, int>
        {
            { keyB, 1200 }
        };
        Assert.False(price3.ApplyPrices(newPrices3));
    }
}