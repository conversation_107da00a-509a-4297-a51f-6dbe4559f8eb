using Esky.Packages.Domain.Model.PackageDefinitions;

namespace Esky.Packages.Domain.Tests.Model.PackageDefinitions;

public class OneDayPartitionerTests
{
    [Theory]
    [InlineData(0, "2025-01-01")]
    [InlineData(36, "2025-02-06")]
    [InlineData(50, "2025-02-20")]
    [InlineData(75, "2025-03-17")]
    [InlineData(99, "2025-04-10")]
    public void GetPartitionDates_ShouldReturnPartitions(int partition, string expectedDate)
    {
        // Arrange
        var checkIn = DateOnly.Parse("2025-01-01");
        var expected = DateOnly.Parse(expectedDate);

        // Act
        var partitionDates = OneDayPartitioner.GetPartitionDates(checkIn, checkIn.AddDays(100), partition)!;

        // Assert
        partitionDates.Value.Start.ShouldBe(expected);
        partitionDates.Value.End.ShouldBe(expected);
    }

    [Fact]
    public void GetPartitionDates_ShouldReturnNull_OnPartitionExceedingMaxCheckin()
    {
        // Arrange
        var checkIn = DateOnly.Parse("2025-01-01");

        // Act
        var partitionDates = OneDayPartitioner.GetPartitionDates(checkIn, checkIn.AddDays(10), partition: 11);

        // Assert
        partitionDates.ShouldBeNull();
    }
    [Theory]
    [InlineData("2025-01-01", "2025-01-11", 10)]
    [InlineData("2025-01-01", "2025-01-02", 1)]
    [InlineData("2025-01-01", "2025-12-31", 364)]
    [InlineData("2024-01-01", "2024-12-31", 365)] // Leap year
    public void GetTotalPartitions_ShouldReturnTotalPartitions(string startDate, string endDate, int expectedPartitions)
    {
        // Arrange
        var start = DateOnly.Parse(startDate);
        var end = DateOnly.Parse(endDate);

        // Act
        var totalPartitions = OneDayPartitioner.GetTotalPartitions(start, end);

        // Assert
        totalPartitions.ShouldBe(expectedPartitions);
    }
}
