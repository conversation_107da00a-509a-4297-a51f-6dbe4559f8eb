using Esky.Packages.Application.Abstractions.Gateways.PriceTrackerGateway;
using Esky.Packages.Application.Services;
using Esky.Packages.Common.Tests.Fixtures.Domain;
using Esky.Packages.Domain.Events;
using Esky.Packages.Domain.Events.PackageFlightVariants;
using Esky.Packages.Domain.Types;
using Microsoft.Extensions.Logging;
using NSubstitute;

namespace Esky.Packages.Application.Tests.Services;

public class PriceTrackerServiceTests
{
    private readonly IPriceTrackerGateway _priceTrackerGateway = Substitute.For<IPriceTrackerGateway>();
    private readonly TimeProvider _timeProvider = Substitute.For<TimeProvider>();
    private readonly ILogger<PriceTrackerService> _logger = Substitute.For<ILogger<PriceTrackerService>>();

    [Fact]
    public async Task EmitPriceHistoryEvents_DeduplicatesFlightEvents_WhenPackageVariantFlightDataIsTheSameForMultipleVariants()
    {
        // Arrange
        var utcNow = new DateTime(2024, 1, 15, 10, 0, 0, DateTimeKind.Utc);
        _timeProvider.GetUtcNow().Returns(new DateTimeOffset(utcNow));

        var market = MarketTestFixture.CreateMarket();
        var variant1 = PackageVariantTestFixture.CreatePackageVariant(
            refundability: Refundability.Refundable,
            roomIds: new RoomIds(["room1"]));
        var variant2 = PackageVariantTestFixture.CreatePackageVariant(
            refundability: Refundability.NonRefundable,
            roomIds: new RoomIds(["room2"]));

        var service = new PriceTrackerService(_priceTrackerGateway, _timeProvider, _logger);

        // Act
        await service.EmitPriceHistoryEvents([variant1, variant2], market, CancellationToken.None);

        // Assert
        await _priceTrackerGateway.Received(1).PublishEvents(
            Arg.Is<ICollection<PackageFlightVariantPriceEvent>>(events => events.Count == 1), 
            Arg.Any<CancellationToken>());

        await _priceTrackerGateway.Received(1).PublishEvents(
            Arg.Is<ICollection<PackageHotelOfferVariantPriceEvent>>(events => events.Count == 2), 
            Arg.Any<CancellationToken>());
    }
}
