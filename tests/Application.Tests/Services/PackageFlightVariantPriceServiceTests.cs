using Esky.Packages.Application.Services;
using Esky.Packages.Domain.Model.PackageFlightVariantPrices;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;
using NSubstitute;

namespace Esky.Packages.Application.Tests.Services;

public class PackageFlightVariantPriceServiceTests
{
    private readonly IPackageFlightVariantPriceRepository _repository = Substitute.For<IPackageFlightVariantPriceRepository>();

    [Fact]
    public async Task ApplyPrices_InsertsNew_WhenNotExists()
    {
        _repository.GetByIds(Arg.Any<List<PackageFlightVariantPriceId>>(), Arg.Any<CancellationToken>()).Returns([]);
        var service = new PackageFlightVariantPriceService(_repository);
        var price = PackageFlightVariantPrice.Create(new PackageFlightVariantPriceId(), new Dictionary<PackageFlightVariantPriceKey, int>
        {
            { new PackageFlightVariantPriceKey { BaggageIncluded = true }, 1000 }
        });
        await service.ApplyPrices([price], CancellationToken.None);
        await _repository.Received(1).Upsert(Arg.Is<List<PackageFlightVariantPrice>>(l => l.Count == 1 && l[0].Id.Equals(price.Id)), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task ApplyPrices_Updates_WhenChanged()
    {
        var id = new PackageFlightVariantPriceId();
        var existing = PackageFlightVariantPrice.Create(id, new Dictionary<PackageFlightVariantPriceKey, int>
        {
            { new PackageFlightVariantPriceKey { BaggageIncluded = true }, 1000 }
        });
        _repository.GetByIds(Arg.Any<List<PackageFlightVariantPriceId>>(), Arg.Any<CancellationToken>()).Returns([existing]);
        var service = new PackageFlightVariantPriceService(_repository);
        var price = PackageFlightVariantPrice.Create(id, new Dictionary<PackageFlightVariantPriceKey, int>
        {
            { new PackageFlightVariantPriceKey { BaggageIncluded = true }, 900 }
        });
        await service.ApplyPrices([price], CancellationToken.None);
        await _repository.Received(1).Upsert(Arg.Is<List<PackageFlightVariantPrice>>(l => l.Count == 1 && l[0].Prices[new PackageFlightVariantPriceKey { BaggageIncluded = true }] == 900), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task ApplyPrices_DoesNotUpsert_WhenNoChange()
    {
        var id = new PackageFlightVariantPriceId();
        var existing = PackageFlightVariantPrice.Create(id, new Dictionary<PackageFlightVariantPriceKey, int>
        {
            { new PackageFlightVariantPriceKey { BaggageIncluded = true }, 1000 }
        });
        _repository.GetByIds(Arg.Any<List<PackageFlightVariantPriceId>>(), Arg.Any<CancellationToken>()).Returns([existing]);
        var service = new PackageFlightVariantPriceService(_repository);
        var price = PackageFlightVariantPrice.Create(id, new Dictionary<PackageFlightVariantPriceKey, int>
        {
            { new PackageFlightVariantPriceKey { BaggageIncluded = true }, 1000 }
        });
        await service.ApplyPrices([price], CancellationToken.None);
        await _repository.DidNotReceive().Upsert(Arg.Any<List<PackageFlightVariantPrice>>(), Arg.Any<CancellationToken>());
    }
}
