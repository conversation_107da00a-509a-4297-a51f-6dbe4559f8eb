using Esky.Packages.Application.Services;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageHotelOfferVariantPrices;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;
using NSubstitute;

namespace Esky.Packages.Application.Tests.Services;

public class PackageHotelOfferVariantPriceServiceTests
{
    private readonly IPackageHotelOfferVariantPriceRepository _repository = Substitute.For<IPackageHotelOfferVariantPriceRepository>();

    [Fact]
    public async Task ApplyPrices_InsertsNew_WhenNotExists()
    {
        _repository.GetByIds(Arg.Any<List<PackageHotelOfferVariantPriceId>>(), Arg.Any<CancellationToken>()).Returns([]);
        var service = new PackageHotelOfferVariantPriceService(_repository);
        var priceId = PackageHotelOfferVariantPriceId.Create(
            checkIn: DateOnly.FromDateTime(new DateTime(2025, 3, 24)),
            stayLength: 7,
            marketId: "uk",
            metaCode: 123,
            mealPlan: MealPlan.Breakfast,
            occupancy: new PackageOccupancy(2, 0, 0, 0),
            priceDate: DateOnly.FromDateTime(DateTime.UtcNow));
        var price = PackageHotelOfferVariantPrice.Create(priceId, new Dictionary<PackageHotelOfferVariantPriceKey, int>
        {
            { new PackageHotelOfferVariantPriceKey { Refundability = Refundability.Refundable, RoomIds = RoomIds.Empty }, 1000 }
        });
        await service.ApplyPrices([price], CancellationToken.None);
        await _repository.Received(1).Upsert(Arg.Is<List<PackageHotelOfferVariantPrice>>(l => l.Count == 1 && l[0].Id.Equals(price.Id)), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task ApplyPrices_UpdatesExisting_WhenLowerPrice()
    {
        var id = PackageHotelOfferVariantPriceId.Create(
            checkIn: DateOnly.FromDateTime(new DateTime(2025, 3, 24)),
            stayLength: 7,
            marketId: "uk",
            metaCode: 123,
            mealPlan: MealPlan.Breakfast,
            occupancy: new PackageOccupancy(2, 0, 0, 0),
            priceDate: DateOnly.FromDateTime(DateTime.UtcNow));
        var existing = PackageHotelOfferVariantPrice.Create(id, new Dictionary<PackageHotelOfferVariantPriceKey, int>
        {
            { new PackageHotelOfferVariantPriceKey { Refundability = Refundability.Refundable, RoomIds = RoomIds.Empty }, 1000 }
        });
        _repository.GetByIds(Arg.Any<List<PackageHotelOfferVariantPriceId>>(), Arg.Any<CancellationToken>()).Returns([existing]);
        var service = new PackageHotelOfferVariantPriceService(_repository);
        var price = PackageHotelOfferVariantPrice.Create(id, new Dictionary<PackageHotelOfferVariantPriceKey, int>
        {
            { new PackageHotelOfferVariantPriceKey { Refundability = Refundability.Refundable, RoomIds = RoomIds.Empty }, 900 }
        });
        await service.ApplyPrices([price], CancellationToken.None);
        await _repository.Received(1).Upsert(Arg.Is<List<PackageHotelOfferVariantPrice>>(l => l.Count == 1 && l[0].Prices[new PackageHotelOfferVariantPriceKey { Refundability = Refundability.Refundable, RoomIds = RoomIds.Empty }] == 900), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task ApplyPrices_DoesNotUpdate_WhenHigherPrice()
    {
        var id = PackageHotelOfferVariantPriceId.Create(
            checkIn: DateOnly.FromDateTime(new DateTime(2025, 3, 24)),
            stayLength: 7,
            marketId: "uk",
            metaCode: 123,
            mealPlan: MealPlan.Breakfast,
            occupancy: new PackageOccupancy(2, 0, 0, 0),
            priceDate: DateOnly.FromDateTime(DateTime.UtcNow));
        var existing = PackageHotelOfferVariantPrice.Create(id, new Dictionary<PackageHotelOfferVariantPriceKey, int>
        {
            { new PackageHotelOfferVariantPriceKey { Refundability = Refundability.Refundable, RoomIds = RoomIds.Empty }, 1000 }
        });
        _repository.GetByIds(Arg.Any<List<PackageHotelOfferVariantPriceId>>(), Arg.Any<CancellationToken>()).Returns([existing]);
        var service = new PackageHotelOfferVariantPriceService(_repository);
        var price = PackageHotelOfferVariantPrice.Create(id, new Dictionary<PackageHotelOfferVariantPriceKey, int>
        {
            { new PackageHotelOfferVariantPriceKey { Refundability = Refundability.Refundable, RoomIds = RoomIds.Empty }, 1000 }
        });
        await service.ApplyPrices([price], CancellationToken.None);
        await _repository.DidNotReceive().Upsert(Arg.Any<List<PackageHotelOfferVariantPrice>>(), Arg.Any<CancellationToken>());
    }
}
