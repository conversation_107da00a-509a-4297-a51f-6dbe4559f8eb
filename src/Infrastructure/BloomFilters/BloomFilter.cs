using BloomFilter;

namespace Esky.Packages.Infrastructure.BloomFilters;

public class BloomFilter(int expectedCount, float falsePositiveRate)
{
    private readonly IBloomFilter _filter = FilterBuilder.Build(expectedCount, falsePositiveRate, HashMethod.XXHash3);

    public int Items { get; private set; }
    public int UniqueHashes { get; private set; }
    public DateTime LastFullUpdate { get; set; }

    public int Queries { get; private set; }
    public int TrueQueries { get; private set; }

    public int AddedOrUpdatedStatistics { get; private set; }

    public int SizeInKB => (int)(_filter is FilterMemory f ? f.Capacity / 8 / 1024 : -1);

    public void Add(ReadOnlySpan<char> item)
    {
        if (_filter.Add(item))
            UniqueHashes++;

        Items++;
    }

    public void Clear()
    {
        _filter.Clear();
        Items = UniqueHashes = 0;
        AddedOrUpdatedStatistics = 0;
        LastFullUpdate = DateTime.MinValue;
    }

    public bool Contains(ReadOnlySpan<char> item)
    {
        Queries++;
        var contains = _filter.Contains(item);
        if (contains)
            TrueQueries++;

        return contains;
    }
}