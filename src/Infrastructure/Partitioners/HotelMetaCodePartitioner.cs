using Confluent.Kafka;

namespace Esky.Packages.Infrastructure.Partitioners;

public class HotelMetaCodePartitioner
{
    private TopicPartition[] _partitions = default!;
    
    public void Initialize(string topic, string bootstrapServers)
    {
        using var adminClient = new AdminClientBuilder(new AdminClientConfig 
            { BootstrapServers = bootstrapServers }).Build();
        
        var meta = adminClient.GetMetadata(topic, TimeSpan.FromSeconds(10));

        var t = meta.Topics.Single(x => x.Topic == topic);
        var count = t.Partitions.Count;

        _partitions = new TopicPartition[count];
        for (var i = 0; i < count; i++)
            _partitions[i] = new TopicPartition(topic, new Partition(i));
    }
    
    public TopicPartition GetTopicPartition(int metaCode)
    {
        return _partitions[(uint)metaCode % _partitions.Length];
    }
}