using Confluent.Kafka;

namespace Esky.Packages.Infrastructure.Partitioners;

public class AirportPartitioner
{
    private TopicPartition[] _partitions = default!;
    
    public void Initialize(string topic, string bootstrapServers)
    {
        using var adminClient = new AdminClientBuilder(new AdminClientConfig 
            { BootstrapServers = bootstrapServers }).Build();
        
        var meta = adminClient.GetMetadata(topic, TimeSpan.FromSeconds(10));

        var t = meta.Topics.Single(x => x.Topic == topic);
        var count = t.Partitions.Count;

        _partitions = new TopicPartition[count];
        for (var i = 0; i < count; i++)
            _partitions[i] = new TopicPartition(topic, new Partition(i));
    }
    
    public TopicPartition GetTopicPartition(string airport)
    {
        var hash = (uint)GetStableHashCode(airport);
        return _partitions[hash % _partitions.Length];
    }
    
    private static int GetStableHashCode(string str)
    {
        unchecked
        {
            var hash1 = (5381 << 16) + 5381;
            var hash2 = hash1;

            foreach (var t in str)
            {
                hash1 = ((hash1 << 5) + hash1) ^ t;
            }

            return hash1 + (hash2 * 1566083941);
        }
    }
}