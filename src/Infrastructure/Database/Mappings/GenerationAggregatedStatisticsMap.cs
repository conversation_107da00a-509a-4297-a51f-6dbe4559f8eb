using Esky.Packages.Application.Abstractions.Pipelines;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Infrastructure.Database.Mappings;

public class GenerationAggregatedStatisticsMap : BsonClassMap<GenerationAggregatedStatistics>
{
    public GenerationAggregatedStatisticsMap()
    {
        MapProperty(x => x.DefinitionId).SetElementName("definitionId");
        MapProperty(x => x.PipelineGroupId).SetElementName("pipelineGroupId");
        MapProperty(x => x.LatestFinishedAt).SetElementName("latestFinishedAt");
        MapProperty(x => x.Completed).SetElementName("completed");
        MapProperty(x => x.Running).SetElementName("running");
        MapProperty(x => x.Waiting).SetElementName("waiting");
        MapProperty(x => x.Failed).SetElementName("failed");
        MapProperty(x => x.Total).SetElementName("total");
    }
}