using Esky.Packages.Domain.Model.PackageVariants;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Infrastructure.Database.Mappings;

public class PackageVariantAggregationMap : BsonClassMap<PackageVariantAggregation>
{
    public PackageVariantAggregationMap()
    {
        MapProperty(x => x.DepartureAirports)
            .SetElementName("a");

        MapProperty(x => x.MealPlans)
            .SetElementName("mp");
        
        MapProperty(x => x.Occupancies)
            .SetElementName("o");
    }
}