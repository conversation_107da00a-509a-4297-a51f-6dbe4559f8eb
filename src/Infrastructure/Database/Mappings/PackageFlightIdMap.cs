using Esky.Packages.Domain.Model.PackageFlights;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Infrastructure.Database.Mappings;

public class PackageFlightIdMap : BsonClassMap<PackageFlightId>
{
    public PackageFlightIdMap()
    {
        MapProperty(x => x.CheckIn)
            .SetElementName("c")
            .SetOrder(1);

        MapProperty(x => x.<PERSON>ength)
            .SetElementName("s")
            .SetOrder(2);

        MapProperty(x => x.MarketId)
            .SetElementName("k")
            .SetOrder(3);

        MapProperty(x => x.ArrivalAirport)
            .SetElementName("a")
            .SetOrder(4);
        
        MapProperty(x => x.DepartureAirport)
            .SetElementName("d")
            .SetOrder(5);
    }
}