using Esky.Packages.Domain.Model.PackageHotelOfferVariantPrices;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Infrastructure.Database.Mappings;

public class PackageHotelOfferVariantPriceMap : BsonClassMap<PackageHotelOfferVariantPrice>
{
    public PackageHotelOfferVariantPriceMap()
    {
        MapIdProperty(x => x.Id);
        
        MapProperty(x => x.Prices)
            .SetElementName("p")
            .SetOrder(1);
    }
}

public class PackageHotelOfferVariantPriceIdMap : BsonClassMap<PackageHotelOfferVariantPriceId>
{
    public PackageHotelOfferVariantPriceIdMap()
    {
        MapProperty(x => x.CheckIn)
            .SetElementName("c")
            .SetOrder(1);
        
        MapProperty(x => x.Stay<PERSON>ength)
            .SetElementName("s")
            .SetOrder(2);
        
        MapProperty(x => x.MarketId)
            .SetElementName("k")
            .SetOrder(3);
        
        MapProperty(x => x.MetaCode)
            .SetElementName("m")
            .SetOrder(4);
        
        MapProperty(x => x.Occupancy)
            .SetElementName("o")
            .SetOrder(5);
        
        MapProperty(x => x.MealPlan)
            .SetElementName("mp")
            .SetOrder(6);
        
        MapProperty(x => x.PriceDate)
            .SetElementName("pd")
            .SetOrder(7);
    }
}
