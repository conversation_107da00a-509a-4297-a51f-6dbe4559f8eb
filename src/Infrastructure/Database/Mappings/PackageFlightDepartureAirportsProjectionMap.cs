using Esky.Packages.Infrastructure.Database.Projections;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Infrastructure.Database.Mappings;

public class PackageFlightDepartureAirportsProjectionMap : BsonClassMap<PackageFlightDepartureAirportsProjection>
{
    public PackageFlightDepartureAirportsProjectionMap()
    {
        MapProperty(x => x.DepartureAirport)
            .SetElementName("d");
    }
}