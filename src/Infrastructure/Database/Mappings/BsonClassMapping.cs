using System.Text.Json.Serialization;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Infrastructure.Database.Mappings;

internal static class BsonClassMapping
{
    public static void RegisterClassMapTypeWithJsonDerivedType<T>()
    {
        var derivedTypeAttributes = typeof(T)
            .GetCustomAttributes(typeof(JsonDerivedTypeAttribute), inherit: false)
            .Cast<JsonDerivedTypeAttribute>();

        foreach (var derivedTypeAttribute in derivedTypeAttributes)
        {
            var derivedType = derivedTypeAttribute.DerivedType;
            if (BsonClassMap.IsClassMapRegistered(derivedType))
            {
                continue;
            }

            var bsonClassMap = new BsonClassMap(derivedType);
            bsonClassMap.AutoMap();
            bsonClassMap.SetDiscriminator(derivedTypeAttribute.TypeDiscriminator.ToString());

            BsonClassMap.RegisterClassMap(bsonClassMap);
        }
    }
}
