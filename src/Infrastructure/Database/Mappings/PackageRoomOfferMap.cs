using Esky.Packages.Domain.Model.PackageHotelOffers;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Infrastructure.Database.Mappings;

public class PackageRoomOfferMap : BsonClassMap<PackageRoomOffer>
{
    public PackageRoomOfferMap()
    {
        AutoMap();

        MapProperty(r => r.Availability)
            .SetElementName("a")
            .SetOrder(1);

        MapProperty(r => r.CompensatedPrice)
            .SetElementName("cp")
            .SetOrder(2);

        MapProperty(r => r.RoomIds)
            .SetElementName("r")
            .SetOrder(3);
    }
}