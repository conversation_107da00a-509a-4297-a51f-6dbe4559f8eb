using Esky.Packages.Domain.Model.PackageFlightVariants;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Infrastructure.Database.Mappings;

public class PackageFlightVariantIdMap : BsonClassMap<PackageFlightVariantId>
{
    public PackageFlightVariantIdMap()
    {
        MapProperty(x => x.CheckIn)
            .SetElementName("c")
            .SetOrder(1);        
        
        MapProperty(x => x.StayLength)
            .SetElementName("s")
            .SetOrder(2);
        
        MapProperty(x => x.MarketId)
            .SetElementName("k")
            .SetOrder(3);
        
        MapProperty(x => x.ArrivalAirport)
            .SetElementName("a")
            .SetOrder(4);
        
        MapProperty(x => x.DepartureAirport)
            .SetElementName("d")
            .SetOrder(5);
        
        MapProperty(x => x.Occupancy)
            .SetElementName("o")
            .SetOrder(6);
        
        MapProperty(x => x.InboundDeparture)
            .SetElementName("id")
            .SetOrder(6);
        
        MapProperty(x => x.OutboundDeparture)
            .SetElementName("od")
            .SetOrder(6);
        
    }
}