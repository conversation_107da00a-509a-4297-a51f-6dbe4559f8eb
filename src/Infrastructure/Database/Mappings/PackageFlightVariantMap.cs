using Esky.Packages.Domain.Model.PackageFlightVariants;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Infrastructure.Database.Mappings;

public class PackageFlightVariantMap : BsonClassMap<PackageFlightVariant>
{
    public PackageFlightVariantMap()
    {
        MapIdProperty(x => x.Id);

        MapProperty(x => x.OfferId)
            .SetElementName("o")
            .SetOrder(1);

        MapProperty(x => x.DepartureDate)
            .SetElementName("d")
            .SetOrder(2);

        MapProperty(x => x.ReturnArrivalDate)
            .SetElementName("ra")
            .SetOrder(3);

        MapProperty(x => x.ReturnDepartureDate)
            .SetElementName("rd")
            .SetOrder(4);

        MapProperty(x => x.FlightNumbers)
            .SetElementName("fn")
            .SetOrder(5);

        MapProperty(x => x.ReturnFlightNumbers)
            .SetElementName("rfn")
            .SetOrder(6);

        MapProperty(x => x.BaggageIncluded)
            .SetElementName("bi")
            .SetOrder(7);

        MapProperty(x => x.Price)
            .SetElementName("p")
            .SetOrder(8);
    }
}