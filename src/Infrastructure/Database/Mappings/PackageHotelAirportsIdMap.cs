using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageHotelAirports;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Infrastructure.Database.Mappings;

public class PackageHotelAirportsIdMap : BsonClassMap<PackageHotelAirportsId>
{
    public PackageHotelAirportsIdMap()
    {
        MapProperty(x => x.MarketId)
            .SetElementName("k")
            .SetOrder(1);

        MapProperty(x => x.MetaCode)
            .SetElementName("m")
            .SetOrder(2);
    }
}