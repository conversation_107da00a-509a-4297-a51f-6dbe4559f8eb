using Esky.Packages.Domain.Model.PackageFlightVariantPrices;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Infrastructure.Database.Mappings;

public class PackageFlightVariantPriceMap : BsonClassMap<PackageFlightVariantPrice>
{
    public PackageFlightVariantPriceMap()
    {
        MapIdProperty(x => x.Id);
        
        MapProperty(x => x.Partition<PERSON>ey)
            .SetElementName("pk")
            .SetOrder(1);

        MapProperty(x => x.Prices)
            .SetElementName("p")
            .SetOrder(2);
    }
}

public class PackageFlightVariantPriceIdMap : BsonClassMap<PackageFlightVariantPriceId>
{
    public PackageFlightVariantPriceIdMap()
    {
        MapProperty(x => x.DepartureDate)
            .SetElementName("dd")
            .SetOrder(1);
        
        MapProperty(x => x.ReturnDepartureDate)
            .SetElementName("rdd")
            .SetOrder(2);
        
        MapProperty(x => x.MarketId)
            .SetElementName("k")
            .SetOrder(3);
        
        MapProperty(x => x.ArrivalAirport)
            .SetElementName("a")
            .SetOrder(4);
        
        MapProperty(x => x.DepartureAirport)
            .SetElementName("d")
            .SetOrder(5);
        
        MapProperty(x => x.FlightNumbers)
            .SetElementName("fn")
            .SetOrder(6);
        
        MapProperty(x => x.ReturnFlightNumbers)
            .SetElementName("rfn")
            .SetOrder(7);
        
        MapProperty(x => x.Occupancy)
            .SetElementName("o")
            .SetOrder(8);
        
        MapProperty(x => x.PriceDate)
            .SetElementName("pd")
            .SetOrder(9);
    }
}
