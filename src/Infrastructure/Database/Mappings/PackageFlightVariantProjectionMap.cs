using Esky.Packages.Infrastructure.Database.Projections;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Infrastructure.Database.Mappings;

public class PackageFlightVariantProjectionMap : BsonClassMap<PackageFlightVariantProjection>
{
    public PackageFlightVariantProjectionMap()
    {
        MapProperty(x => x.CheckIn)
            .SetElementName("c");

        MapProperty(x => x.<PERSON>th)
            .SetElementName("s");

        MapProperty(x => x.ArrivalAirport)
            .SetElementName("a");

        MapProperty(x => x.DepartureAirport)
            .SetElementName("d");

        MapProperty(x => x.DepartureDate)
            .SetElementName("dd");

        MapProperty(x => x.ReturnArrivalDate)
            .SetElementName("rad");

        MapProperty(x => x.ReturnDepartureDate)
            .SetElementName("rdd");

        MapProperty(x => x.OfferId)
            .SetElementName("o");

        MapProperty(x => x.InboundDeparture)
            .SetElementName("id");

        MapProperty(x => x.OutboundDeparture)
            .SetElementName("od");

        MapProperty(x => x.FlightNumbers)
            .SetElementName("fn");

        MapProperty(x => x.ReturnFlightNumbers)
            .SetElementName("rfn");

        MapProperty(x => x.BaggageIncluded)
            .SetElementName("bi");

        MapProperty(x => x.Price)
            .SetElementName("p");
    }
}