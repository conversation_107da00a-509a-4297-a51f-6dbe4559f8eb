using Esky.Packages.Domain.Model.PackageHotelOffers;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Infrastructure.Database.Mappings;

public class PackageMealPlanRoomOffersMap : BsonClassMap<PackageMealPlanRoomOffers>
{
    public PackageMealPlanRoomOffersMap()
    {
        AutoMap();
        
        MapProperty(m => m.RoomOffersByMealPlan)
            .SetElementName("r")
            .SetOrder(1);

        MapProperty(m => m.UpdatedAt)
            .SetElementName("u")
            .SetOrder(2);
    }
}