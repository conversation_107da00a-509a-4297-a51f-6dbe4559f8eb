using Esky.Packages.Domain.Model.PackageAvailabilities;
using MongoDB.Bson.Serialization;

namespace Esky.Packages.Infrastructure.Database.Mappings;

public class PackageAvailabilitiesIdMap : BsonClassMap<PackageAvailabilitiesId>
{
    public PackageAvailabilitiesIdMap()
    {
        MapProperty(x => x.DefinitionId)
            .SetElementName("d")
            .SetOrder(1);

        MapProperty(x => x.Partition)
            .SetElementName("p")
            .SetOrder(2);
    }
}