using MongoDB.Bson.Serialization.Serializers;
using MongoDB.Bson.Serialization;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Infrastructure.Database.Serializers;

internal class RoomIdsSerializer : StructSerializerBase<RoomIds>
{
    public override RoomIds Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        return RoomIds.Parse(context.Reader.ReadString(), null);
    }

    public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, RoomIds value)
    {
        context.Writer.WriteString(value.ToString());
    }
}