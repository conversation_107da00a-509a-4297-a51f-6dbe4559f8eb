using Esky.Packages.Domain.Types;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;

namespace Esky.Packages.Infrastructure.Database.Serializers;

internal class AirportSerializer : StructSerializerBase<Airport>
{
    public override Airport Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        return Airport.Parse(context.Reader.ReadString(), null);
    }

    public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, Airport value)
    {
        context.Writer.WriteString(value.ToString());
    }
}