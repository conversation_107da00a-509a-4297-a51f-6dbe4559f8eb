using Esky.Packages.Domain.Types;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;

namespace Esky.Packages.Infrastructure.Database.Serializers;

internal class MealPlanSerializer : StructSerializerBase<MealPlan>
{
    public override MealPlan Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        return MealPlan.FromShortString(context.Reader.ReadString());
    }

    public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, MealPlan value)
    {
        context.Writer.WriteString(value.ToShortString());
    }
}