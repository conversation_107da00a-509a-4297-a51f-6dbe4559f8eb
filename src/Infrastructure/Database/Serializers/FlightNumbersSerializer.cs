using Esky.Packages.Domain.Types;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;

namespace Esky.Packages.Infrastructure.Database.Serializers;

internal class FlightNumbersSerializer : StructSerializerBase<FlightNumbers>
{
    public override FlightNumbers Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        var bsonType = context.Reader.GetCurrentBsonType();
        
        return bsonType switch
        {
            BsonType.Array => DeserializeFromArray(context),
            BsonType.String => DeserializeFromString(context),
            _ => throw new FormatException($"Cannot deserialize FlightNumbers from BsonType: {bsonType}")
        };
    }

    private static FlightNumbers DeserializeFromArray(BsonDeserializationContext context)
    {
        context.Reader.ReadStartArray();
        var flightNumbers = new List<string>();
        
        while (context.Reader.ReadBsonType() != BsonType.EndOfDocument)
        {
            flightNumbers.Add(context.Reader.ReadString());
        }
        
        context.Reader.ReadEndArray();
        return new FlightNumbers(flightNumbers.ToArray());
    }

    private static FlightNumbers DeserializeFromString(BsonDeserializationContext context)
    {
        return FlightNumbers.Parse(context.Reader.ReadString(), null);
    }

    public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, FlightNumbers value)
    {
        context.Writer.WriteString(value.ToString());
    }
}
