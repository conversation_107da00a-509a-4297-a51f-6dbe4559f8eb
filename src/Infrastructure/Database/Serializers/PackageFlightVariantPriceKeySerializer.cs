using Esky.Packages.Domain.Model.PackageFlightVariantPrices;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;

namespace Esky.Packages.Infrastructure.Database.Serializers;

internal class PackageFlightVariantPriceKeySerializer : StructSerializerBase<PackageFlightVariantPriceKey>
{
    public override PackageFlightVariantPriceKey Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        return PackageFlightVariantPriceKey.Parse(context.Reader.ReadString(), null);
    }

    public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, PackageFlightVariantPriceKey value)
    {
        context.Writer.WriteString(value.ToString());
    }
}