using Esky.Packages.Domain.Types;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;

namespace Esky.Packages.Infrastructure.Database.Serializers;

public class ProviderConfigurationIdSerializer : StructSerializerBase<ProviderConfigurationId>
{
    public override ProviderConfigurationId Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        return new ProviderConfigurationId(context.Reader.ReadString()!);
    }

    public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, ProviderConfigurationId value)
    {
        context.Writer.WriteString(value.Value);
    }
}