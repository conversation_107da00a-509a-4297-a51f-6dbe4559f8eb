using Esky.Packages.Domain.Model.PackageHotelOfferVariantPrices;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;

namespace Esky.Packages.Infrastructure.Database.Serializers;

internal class PackageHotelOfferVariantPriceKeySerializer : StructSerializerBase<PackageHotelOfferVariantPriceKey>
{
    public override PackageHotelOfferVariantPriceKey Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        return PackageHotelOfferVariantPriceKey.Parse(context.Reader.ReadString(), null);
    }

    public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, PackageHotelOfferVariantPriceKey value)
    {
        context.Writer.WriteString(value.ToString());
    }
}
