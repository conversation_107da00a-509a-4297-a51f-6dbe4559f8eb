using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;

namespace Esky.Packages.Infrastructure.Database.Serializers;

public class TimeZoneInfoSerializer : SerializerBase<TimeZoneInfo>
{
    public override TimeZoneInfo Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        var timeZoneId = context.Reader.ReadString();
        return TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
    }

    public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, TimeZoneInfo value)
    {
        context.Writer.WriteString(value.Id);
    }
} 