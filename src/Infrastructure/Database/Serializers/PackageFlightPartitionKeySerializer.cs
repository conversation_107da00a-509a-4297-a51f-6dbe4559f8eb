using Esky.Packages.Domain.Model.PackageFlights;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;

namespace Esky.Packages.Infrastructure.Database.Serializers;

public class PackageFlightPartitionKeySerializer : StructSerializerBase<PackageFlightPartitionKey>
{
    public override PackageFlightPartitionKey Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        return PackageFlightPartitionKey.Parse(context.Reader.ReadString(), null);
    }

    public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, PackageFlightPartitionKey value)
    {
        context.Writer.WriteString(value.ToString());
    }
}