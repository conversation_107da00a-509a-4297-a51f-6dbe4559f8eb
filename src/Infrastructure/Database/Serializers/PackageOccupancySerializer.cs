using Esky.Packages.Domain.Model.Common;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;

namespace Esky.Packages.Infrastructure.Database.Serializers;

internal class PackageOccupancySerializer : StructSerializerBase<PackageOccupancy>
{
    public override PackageOccupancy Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        return PackageOccupancy.Parse(context.Reader.ReadString(), null);
    }

    public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, PackageOccupancy value)
    {
        context.Writer.WriteString(value.ToString());
    }
}
