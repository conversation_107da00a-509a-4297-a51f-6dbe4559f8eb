using MongoDB.Bson.Serialization.Options;
using MongoDB.Bson.Serialization.Serializers;
using MongoDB.Bson;

namespace Esky.Packages.Infrastructure.Database.Serializers;

internal class IntDictionarySerializer<TValue> : DictionarySerializerBase<Dictionary<int, TValue>>
{
    public IntDictionarySerializer() : base(
        dictionaryRepresentation: DictionaryRepresentation.Document, 
        keySerializer: new Int32Serializer(BsonType.String),
        valueSerializer: new ObjectSerializer())
    {
    }

    protected override Dictionary<int, TValue> CreateInstance()
    {
        return [];
    }
}
