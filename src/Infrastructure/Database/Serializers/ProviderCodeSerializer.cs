using Esky.Packages.Domain.Types;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;

namespace Esky.Packages.Infrastructure.Database.Serializers;

internal class ProviderCodeSerializer : StructSerializerBase<ProviderCode>
{
    public override ProviderCode Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        return ProviderCode.Parse(context.Reader.ReadString(), null);
    }

    public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, ProviderCode value)
    {
        context.Writer.WriteString(value.ToString());
    }
}