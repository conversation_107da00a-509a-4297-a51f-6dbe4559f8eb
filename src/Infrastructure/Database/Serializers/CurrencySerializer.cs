using Esky.Packages.Domain.Types;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;

namespace Esky.Packages.Infrastructure.Database.Serializers;

internal class CurrencySerializer : StructSerializerBase<Currency>
{
    public override Currency Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        return Currency.Parse(context.Reader.ReadString(), null);
    }

    public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, Currency value)
    {
        context.Writer.WriteString(value.ToString());
    }
}