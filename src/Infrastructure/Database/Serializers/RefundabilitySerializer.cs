using Esky.Packages.Domain.Types;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;

namespace Esky.Packages.Infrastructure.Database.Serializers;

internal class RefundabilitySerializer : StructSerializerBase<Refundability>
{
    public override Refundability Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        return Refundability.FromString(context.Reader.ReadString());
    }

    public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, Refundability value)
    {
        context.Writer.WriteString(value.ToString());
    }
}