using Esky.Packages.Domain.Types;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;

namespace Esky.Packages.Infrastructure.Database.Serializers;

public class TimeOfDaySerializer : StructSerializerBase<TimeOfDay>
{
    public override TimeOfDay Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        return TimeOfDay.FromShortString(context.Reader.ReadString());
    }

    public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, TimeOfDay value)
    {
        context.Writer.WriteString(value.ToShortString());
    }
}