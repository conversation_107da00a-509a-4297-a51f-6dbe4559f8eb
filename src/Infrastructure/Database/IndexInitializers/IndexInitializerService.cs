using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;

namespace Esky.Packages.Infrastructure.Database.IndexInitializers;

internal class IndexInitializerService(
    IEnumerable<IIndexInitializer> indexInitializers,
    ILogger<IndexInitializerService> logger)
    : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        logger.LogInformation("Creating mongo indexes");

        foreach (var indexInitializer in indexInitializers)
        {
            try
            {
                await indexInitializer.EnsureIndexes();
            }
            catch (MongoCommandException e)
            {
                logger.LogWarning(e, "Error during index creation for {typeName}", indexInitializer.GetType().Name);
            }
        }
    }
}
