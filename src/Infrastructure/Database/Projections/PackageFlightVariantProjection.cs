using Esky.Packages.Domain.Types;

namespace Esky.Packages.Infrastructure.Database.Projections;

public class PackageFlightVariantProjection
{
    public required DateOnly CheckIn { get; init; }
    public required int StayLength { get; init; }
    public required string ArrivalAirport { get; init; }
    public required string DepartureAirport { get; init; }
    public string OfferId { get; init; } = null!; // TODO: Add required
    public required DateOnly DepartureDate { get; init; }
    public required DateOnly ReturnArrivalDate { get; set; }
    public DateOnly ReturnDepartureDate { get; set; } // TODO: Add required
    public required TimeOfDay InboundDeparture { get; set; }
    public required TimeOfDay OutboundDeparture { get; set; }
    public FlightNumbers? FlightNumbers { get; set; } // TODO: Add required and remove nullability
    public FlightNumbers? ReturnFlightNumbers { get; set; } // TODO: Add required and remove nullability
    public bool BaggageIncluded { get; set; } // TODO: Add required
    public required int Price { get; init; }
}