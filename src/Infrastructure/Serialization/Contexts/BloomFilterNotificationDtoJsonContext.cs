using System.Text.Json.Serialization;
using Esky.Packages.Application.Dtos.BloomFilterNotifications;

namespace Esky.Packages.Infrastructure.Serialization.Contexts;

[JsonSourceGenerationOptions(
    PropertyNamingPolicy = JsonKnownNamingPolicy.CamelCase,
    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull)]
[JsonSerializable(typeof(BloomFilterNotificationDto))]
[JsonSerializable(typeof(BloomFilterFlightUpsertedNotificationDto))]
[JsonSerializable(typeof(BloomFilterHotelOfferUpsertedNotificationDto))]
[JsonSerializable(typeof(BloomFilterFlightFalsePositiveNotificationDto))]
[JsonSerializable(typeof(BloomFilterHotelOfferFalsePositiveNotificationDto))]
public partial class BloomFilterNotificationDtoJsonContext : JsonSerializerContext;