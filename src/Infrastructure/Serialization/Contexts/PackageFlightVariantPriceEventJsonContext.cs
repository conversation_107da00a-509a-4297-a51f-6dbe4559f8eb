using System.Text.Json.Serialization;
using Esky.Packages.Domain.Events.PackageFlightVariants;
using Esky.Packages.Infrastructure.Serialization.Converters;

namespace Esky.Packages.Infrastructure.Serialization.Contexts;

[JsonSourceGenerationOptions(
    PropertyNamingPolicy = JsonKnownNamingPolicy.CamelCase,
    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
    Converters =
    [
        typeof(JsonStringEnumConverter),
        typeof(FlightNumbersJsonConverter),
        typeof(PackageOccupancyJsonConverter),
        typeof(CurrencyJsonConverter),
        typeof(AirportJsonConverter)
    ])]
[JsonSerializable(typeof(PackageFlightVariantPriceEvent))]
public partial class PackageFlightVariantPriceEventJsonContext : JsonSerializerContext;