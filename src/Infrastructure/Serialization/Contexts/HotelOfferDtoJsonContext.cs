using System.Text.Json.Serialization;
using Esky.Packages.Application.Dtos.HotelOffers;
using Esky.Packages.Infrastructure.Serialization.Converters;

namespace Esky.Packages.Infrastructure.Serialization.Contexts;

[JsonSourceGenerationOptions(
    PropertyNamingPolicy = JsonKnownNamingPolicy.CamelCase,
    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
    Converters = [typeof(JsonStringEnumConverter), typeof(HotelOfferOccupancyJsonConverter)])]
[JsonSerializable(typeof(HotelOfferDto))]
public partial class HotelOfferDtoJsonContext : JsonSerializerContext;