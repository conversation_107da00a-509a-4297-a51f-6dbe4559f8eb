using Esky.Packages.Domain.Events;
using Esky.Packages.Infrastructure.Serialization.Converters;
using System.Text.Json.Serialization;

namespace Esky.Packages.Infrastructure.Serialization.Contexts;

[JsonSourceGenerationOptions(
    PropertyNamingPolicy = JsonKnownNamingPolicy.CamelCase,
    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
    Converters =
    [
        typeof(JsonStringEnumConverter),
        typeof(MealPlanJsonConverter),
        typeof(RefundabilityJsonConverter),
        typeof(PackageOccupancyJsonConverter),
        typeof(CurrencyJsonConverter),
        typeof(RoomIdsJsonConverter)
    ])]
[JsonSerializable(typeof(PackageHotelOfferVariantPriceEvent))]
public partial class PackageHotelOfferVariantPriceEventJsonContext : JsonSerializerContext;
