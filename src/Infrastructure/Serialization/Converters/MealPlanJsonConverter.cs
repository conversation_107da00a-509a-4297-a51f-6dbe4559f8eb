using System.Text.Json;
using System.Text.Json.Serialization;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Infrastructure.Serialization.Converters;

public class MealPlanJsonConverter : JsonConverter<MealPlan>
{
    public override MealPlan Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        => MealPlan.Parse(reader.GetString()!, null);

    public override void Write(Utf8JsonWriter writer, MealPlan value, JsonSerializerOptions options)
        => writer.WriteStringValue(value.ToString());

    public override MealPlan ReadAsPropertyName(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        => MealPlan.Parse(reader.GetString()!, null);
    
    public override void WriteAsPropertyName(Utf8JsonWriter writer, MealPlan value, JsonSerializerOptions options)
        => writer.WritePropertyName(value.ToString());
}