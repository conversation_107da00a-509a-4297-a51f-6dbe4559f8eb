using System.Text.Json;
using System.Text.Json.Serialization;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Infrastructure.Serialization.Converters;

public class ProviderConfigurationIdJsonConverter : JsonConverter<ProviderConfigurationId>
{
    public override ProviderConfigurationId Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        return new ProviderConfigurationId(reader.GetString()!);
    }

    public override void Write(Utf8JsonWriter writer, ProviderConfigurationId value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value.Value);
    }
    
    public override ProviderConfigurationId ReadAsPropertyName(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        return new ProviderConfigurationId(reader.GetString()!);
    }
    
    public override void WriteAsPropertyName(Utf8JsonWriter writer, ProviderConfigurationId value, JsonSerializerOptions options)
    {
        writer.WritePropertyName(value.Value);
    }
}