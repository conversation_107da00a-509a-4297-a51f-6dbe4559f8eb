using Esky.Packages.Domain.Types;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Esky.Packages.Infrastructure.Serialization.Converters;

public class RoomIdsJsonConverter : JsonConverter<RoomIds>
{
    public override RoomIds Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options) 
        => RoomIds.Parse(reader.GetString(), null);

    public override void Write(Utf8JsonWriter writer, RoomIds value, JsonSerializerOptions options) 
        => writer.WriteStringValue(value.ToString());

    public override RoomIds ReadAsPropertyName(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        => RoomIds.Parse(reader.GetString(), null);

    public override void WriteAsPropertyName(Utf8JsonWriter writer, RoomIds value, JsonSerializerOptions options)
        => writer.WritePropertyName(value.ToString());
}
