using System.Text.Json;
using System.Text.Json.Serialization;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Infrastructure.Serialization.Converters;

public class RefundabilityJsonConverter : JsonConverter<Refundability>
{
    public override Refundability Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        => Refundability.FromString(reader.GetString());

    public override void Write(Utf8JsonWriter writer, Refundability value, JsonSerializerOptions options)
        => writer.WriteStringValue(value.ToString());

    public override Refundability ReadAsPropertyName(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        => Refundability.FromString(reader.GetString());

    public override void WriteAsPropertyName(Utf8JsonWriter writer, Refundability value, JsonSerializerOptions options)
        => writer.WritePropertyName(value.ToString());
}