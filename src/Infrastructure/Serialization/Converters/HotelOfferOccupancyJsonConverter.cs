using System.Text.Json;
using System.Text.Json.Serialization;
using Esky.Packages.Application.Dtos.HotelOffers;

namespace Esky.Packages.Infrastructure.Serialization.Converters;

public class HotelOfferOccupancyJsonConverter : JsonConverter<HotelOfferOccupancy>
{
    public override HotelOfferOccupancy Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        => HotelOfferOccupancy.Parse(reader.GetString(), null);

    public override void Write(Utf8JsonWriter writer, HotelOfferOccupancy occupancy, JsonSerializerOptions options)
        => writer.WriteStringValue(occupancy.ToString());

    public override HotelOfferOccupancy ReadAsPropertyName(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        => HotelOfferOccupancy.Parse(reader.GetString(), null);
    
    public override void WriteAsPropertyName(Utf8JsonWriter writer, HotelOfferOccupancy value, JsonSerializerOptions options)
        => writer.WritePropertyName(value.ToString());
}