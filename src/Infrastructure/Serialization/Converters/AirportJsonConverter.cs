using System.Text.Json;
using System.Text.Json.Serialization;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Infrastructure.Serialization.Converters;

public class AirportJsonConverter : JsonConverter<Airport>
{
    public override Airport Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        => Airport.Parse(reader.GetString()!, null);
    
    public override void Write(Utf8JsonWriter writer, Airport value, JsonSerializerOptions options)
        => writer.WriteStringValue(value.ToString());
    
    public override Airport ReadAsPropertyName(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        => Airport.Parse(reader.GetString()!, null);    
    
    public override void WriteAsPropertyName(Utf8JsonWriter writer, Airport value, JsonSerializerOptions options)
        => writer.WritePropertyName(value.ToString());
}