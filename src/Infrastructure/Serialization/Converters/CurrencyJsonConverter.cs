using System.Text.Json;
using System.Text.Json.Serialization;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Infrastructure.Serialization.Converters;

public class CurrencyJsonConverter : JsonConverter<Currency>
{
    public override Currency Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        => Currency.Parse(reader.GetString(), null);

    public override void Write(Utf8JsonWriter writer, Currency occupancy, JsonSerializerOptions options)
        => writer.WriteStringValue(occupancy.ToString());
}