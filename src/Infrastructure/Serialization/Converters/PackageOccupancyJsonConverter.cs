using System.Text.Json;
using System.Text.Json.Serialization;
using Esky.Packages.Domain.Model.Common;

namespace Esky.Packages.Infrastructure.Serialization.Converters;

public class PackageOccupancyJsonConverter : JsonConverter<PackageOccupancy>
{
    public override PackageOccupancy Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        => PackageOccupancy.Parse(reader.GetString(), null);

    public override void Write(Utf8JsonWriter writer, PackageOccupancy packageOccupancy, JsonSerializerOptions options)
        => writer.WriteStringValue(packageOccupancy.ToString());

    public override PackageOccupancy ReadAsPropertyName(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        => PackageOccupancy.Parse(reader.GetString(), null);
    
    public override void WriteAsPropertyName(Utf8JsonWriter writer, PackageOccupancy value, JsonSerializerOptions options)
        => writer.WritePropertyName(value.ToString());
}