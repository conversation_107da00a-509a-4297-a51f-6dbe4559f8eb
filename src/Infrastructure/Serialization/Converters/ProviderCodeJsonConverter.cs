using System.Text.Json;
using System.Text.Json.Serialization;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Infrastructure.Serialization.Converters;

public class ProviderCodeJsonConverter : JsonConverter<ProviderCode>
{
    public override ProviderCode Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options) 
        => new(reader.GetInt32());

    public override void Write(Utf8JsonWriter writer, ProviderCode value, JsonSerializerOptions options)
        => writer.WriteNumberValue(value);

    public override ProviderCode ReadAsPropertyName(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        => new(reader.GetInt32());

    public override void WriteAsPropertyName(Utf8JsonWriter writer, ProviderCode value, JsonSerializerOptions options)
        => writer.WritePropertyName(value.ToString());
}