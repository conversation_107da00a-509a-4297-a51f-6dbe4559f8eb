using System.Text.Json;
using System.Text.Json.Serialization;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Infrastructure.Serialization.Converters;

public class FlightNumbersJsonConverter : JsonConverter<FlightNumbers>
{
    public override FlightNumbers Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        return reader.TokenType switch
        {
            JsonTokenType.StartArray => ReadFromArray(ref reader),
            JsonTokenType.String => ReadFromString(ref reader),
            _ => throw new JsonException($"Cannot deserialize FlightNumbers from JsonTokenType: {reader.TokenType}")
        };
    }

    private static FlightNumbers ReadFromArray(ref Utf8JsonReader reader)
    {
        var flightNumbers = new List<string>();
        
        while (reader.Read() && reader.TokenType != JsonTokenType.EndArray)
        {
            if (reader.TokenType == JsonTokenType.String)
            {
                flightNumbers.Add(reader.GetString()!);
            }
        }
        
        return new FlightNumbers(flightNumbers.ToArray());
    }

    private static FlightNumbers ReadFromString(ref Utf8JsonReader reader)
    {
        var value = reader.GetString();
        return FlightNumbers.Parse(value!, null);
    }

    public override void Write(Utf8JsonWriter writer, FlightNumbers value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value.ToString());
    }
}