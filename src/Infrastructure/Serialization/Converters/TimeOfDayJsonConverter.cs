using System.Text.Json;
using System.Text.Json.Serialization;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Infrastructure.Serialization.Converters;

public class TimeOfDayJsonConverter : JsonConverter<TimeOfDay>
{
    public override TimeOfDay Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        => TimeOfDay.Parse(reader.GetString()!, null);
    

    public override void Write(Utf8JsonWriter writer, TimeOfDay value, JsonSerializerOptions options)
        => writer.WriteStringValue(value.ToString());

    public override TimeOfDay ReadAsPropertyName(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        => TimeOfDay.Parse(reader.GetString()!, null);
    
    public override void WriteAsPropertyName(Utf8JsonWriter writer, TimeOfDay value, JsonSerializerOptions options)
        => writer.WritePropertyName(value.ToString());
}