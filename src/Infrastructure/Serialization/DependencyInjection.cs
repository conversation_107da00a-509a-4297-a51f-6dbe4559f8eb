using System.Text.Json.Serialization;
using Esky.Packages.Infrastructure.Serialization.Converters;
using Microsoft.AspNetCore.Http.Json;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.Packages.Infrastructure.Serialization;

public static class DependencyInjection
{
    public static IServiceCollection AddJsonSerialization(this IServiceCollection services)
    {
        return services
            .Configure<JsonOptions>(options =>
            {
                options.SerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
                options.SerializerOptions.Converters.Add(new JsonStringEnumConverter());
                options.SerializerOptions.Converters.Add(new PackageOccupancyJsonConverter());
                options.SerializerOptions.Converters.Add(new CurrencyJsonConverter());
                options.SerializerOptions.Converters.Add(new MealPlanJsonConverter());
                options.SerializerOptions.Converters.Add(new RefundabilityJsonConverter());
                options.SerializerOptions.Converters.Add(new AirportJsonConverter());
                options.SerializerOptions.Converters.Add(new ProviderCodeJsonConverter());
                options.SerializerOptions.Converters.Add(new ProviderConfigurationIdJsonConverter());
                options.SerializerOptions.Converters.Add(new FlightNumbersJsonConverter());
                options.SerializerOptions.Converters.Add(new RoomIdsJsonConverter());
            });
    }
}