using Esky.Packages.Domain.Model.PackageAvailabilities;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Infrastructure.Database;
using Esky.Packages.Infrastructure.Database.IndexInitializers;
using MongoDB.Driver;

namespace Esky.Packages.Infrastructure.Repositories;

internal class PackageAvailabilitiesRepository(PackageDatabase database)
    : IPackageAvailabilitiesRepository, IIndexInitializer
{
    private const string CollectionName = "packageAvailabilities";

    private readonly IMongoCollection<PackageAvailabilities> _packageAvailabilities =
        database.Database.GetCollection<PackageAvailabilities>(CollectionName);

    public async Task<List<PackageAvailabilities>> GetByDefinitionId(string definitionId,
        CancellationToken cancellationToken = default)
    {
        var packagesAvailabilities = await _packageAvailabilities
            .Find(f => f.Id.DefinitionId == definitionId)
            .ToListAsync(cancellationToken: cancellationToken);

        return packagesAvailabilities;
    }

    public async Task UpsertWithoutConcurrency(PackageAvailabilities packageAvailabilities,
        CancellationToken cancellationToken = default)
    {
        await _packageAvailabilities
            .ReplaceOneAsync(
                Builders<PackageAvailabilities>.Filter.Eq(x => x.Id, packageAvailabilities.Id),
                packageAvailabilities,
                new ReplaceOptions { IsUpsert = true },
                cancellationToken
            );
    }

    public async Task<int> RemoveByPartitionsGreaterThan(string definitionId, int partition,
        CancellationToken cancellationToken = default)
    {
        var result = await _packageAvailabilities
            .DeleteManyAsync(
                Builders<PackageAvailabilities>.Filter.And(
                    Builders<PackageAvailabilities>.Filter.Where(x => x.Id.DefinitionId == definitionId),
                    Builders<PackageAvailabilities>.Filter.Gt(x => x.Id.Partition, partition)),
                cancellationToken);

        return (int)result.DeletedCount;
    }

    public async Task EnsureIndexes()
    {
        await database.Database.CreateCollectionAsync(CollectionName);

        await _packageAvailabilities.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageAvailabilities>(
                Builders<PackageAvailabilities>.IndexKeys
                    .Ascending(x => x.Id.DefinitionId)
                    .Ascending(x => x.Id.Partition)
            )
        );

        await _packageAvailabilities.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageAvailabilities>(
                Builders<PackageAvailabilities>.IndexKeys
                    .Hashed(x => x.Id.DefinitionId)
            )
        );
    }
}