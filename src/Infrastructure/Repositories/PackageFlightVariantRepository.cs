using Esky.Packages.Domain.Events.PackageFlightVariants;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlightVariants;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;
using Esky.Packages.Infrastructure.Database;
using Esky.Packages.Infrastructure.Database.IndexInitializers;
using Esky.Packages.Infrastructure.Database.Projections;
using MongoDB.Bson;
using MongoDB.Driver;

namespace Esky.Packages.Infrastructure.Repositories;

internal class PackageFlightVariantRepository(PackageDatabase database)
    : IPackageFlightVariantRepository, IIndexInitializer
{
    private const string CollectionName = "packageFlightVariants";

    private const int MaxVariantsPerGroupAllMonths = 12;

    private readonly IMongoCollection<PackageFlightVariant> _packageFlightVariants =
        database.Database.GetCollection<PackageFlightVariant>(CollectionName);

    public async Task UpdateVariants(List<PackageFlightVariantEvent> events, CancellationToken cancellationToken)
    {
        var updates = new List<WriteModel<PackageFlightVariant>>();

        var f = Builders<PackageFlightVariant>.Filter;

        var uniqueEvents = events
            .GroupBy(e => new PackageFlightVariantId(
                checkIn: e.CheckIn,
                stayLength: e.StayLength,
                marketId: e.MarketId,
                arrivalAirport: e.ArrivalAirport,
                departureAirport: e.DepartureAirport,
                occupancy: e.Occupancy,
                inboundDeparture: e.InboundDeparture,
                outboundDeparture: e.OutboundDeparture))
            .ToDictionary(g => g.Key, g => g.Last())
            .Select(g => g.Value)
            .ToList();

        foreach (var uniqueEvent in uniqueEvents)
        {
            if (uniqueEvent is PackageFlightVariantUpdatedEvent updatedEvent)
            {
                var flightVariant = PackageFlightVariant.Create(
                    checkIn: updatedEvent.CheckIn,
                    stayLength: updatedEvent.StayLength,
                    marketId: updatedEvent.MarketId,
                    packageOccupancy: updatedEvent.Occupancy,
                    arrivalAirport: updatedEvent.ArrivalAirport,
                    departureAirport: updatedEvent.DepartureAirport,
                    inboundDeparture: updatedEvent.InboundDeparture,
                    outboundDeparture: updatedEvent.OutboundDeparture,
                    offerId: updatedEvent.OfferId,
                    departureDate: updatedEvent.DepartureDate,
                    returnArrivalDate: updatedEvent.ReturnArrivalDate,
                    returnDepartureDate: updatedEvent.ReturnDepartureDate,
                    flightNumbers: updatedEvent.FlightNumbers,
                    returnFlightNumbers: updatedEvent.ReturnFlightNumbers,
                    baggageIncluded: updatedEvent.BaggageIncluded,
                    price: updatedEvent.Price);

                var filter = f.Eq(x => x.Id, flightVariant.Id);

                var update = new BsonDocument
                {
                    {
                        "$set", new BsonDocument
                        {
                            // TODO: Use property names from mapping instead of hardcoded values
                            { "d", flightVariant.DepartureDate.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc) },
                            { "ra", flightVariant.ReturnArrivalDate.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc) },
                            { "rd", flightVariant.ReturnDepartureDate.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc) },
                            { "fn", flightVariant.FlightNumbers.ToString() },
                            { "rfn", flightVariant.ReturnFlightNumbers.ToString() },
                            { "bi", flightVariant.BaggageIncluded },
                            { "p", flightVariant.Price }
                        }
                    }
                };

                // TODO: Remove this condition when OfferId is not nullable
                if (!string.IsNullOrEmpty(flightVariant.OfferId))
                {
                    update["$set"]["o"] = flightVariant.OfferId;
                }

                updates.Add(new UpdateOneModel<PackageFlightVariant>(filter, update) { IsUpsert = true });
            }
            else if (uniqueEvent is PackageFlightVariantDeletedEvent deletedEvent)
            {
                var id = new PackageFlightVariantId(
                    checkIn: deletedEvent.CheckIn,
                    stayLength: deletedEvent.StayLength,
                    marketId: deletedEvent.MarketId,
                    arrivalAirport: deletedEvent.ArrivalAirport,
                    departureAirport: deletedEvent.DepartureAirport,
                    occupancy: deletedEvent.Occupancy,
                    inboundDeparture: deletedEvent.InboundDeparture,
                    outboundDeparture: deletedEvent.OutboundDeparture);

                var filter = f.Eq(x => x.Id, id);

                updates.Add(new DeleteOneModel<PackageFlightVariant>(filter));
            }
            else
            {
                throw new NotSupportedException($"Event type {uniqueEvent.GetType().Name} is not supported.");
            }
        }

        if (updates.Count > 0)
        {
            await _packageFlightVariants.BulkWriteAsync(updates, options: new BulkWriteOptions
            {
                IsOrdered = false
            }, cancellationToken: cancellationToken);
        }
    }

    public async Task<PackageFlightVariant[]> GetCheapestsPerDates(string marketId, DateOnly departureDateFrom,
        DateOnly departureDateTo, int[] stayLengths, Airport[] departureAirports, Airport[] arrivalAirports,
        PackageOccupancy occupancy,
        TimeOfDay[] inboundDepartures, TimeOfDay[] outboundDepartures, CancellationToken cancellationToken = default)
    {
        var match = new BsonDocument("$match",
            new BsonDocument
            {
                { "_id.o", occupancy.ToString() },
                { "_id.k", marketId },
                {
                    "_id.a", new BsonDocument("$in", new BsonArray(arrivalAirports.Select(a => a.ToString())))
                },
                {
                    "_id.s", new BsonDocument("$in", new BsonArray(stayLengths))
                },
                {
                    "d", new BsonDocument
                    {
                        { "$gte", departureDateFrom.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc) },
                        { "$lte", departureDateTo.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc) }
                    }
                }
            });

        if (departureAirports.Length > 0)
        {
            match["$match"]["_id.d"] = new BsonDocument
            {
                { "$in", new BsonArray(departureAirports.Select(a => a.ToString())) }
            };
        }

        if (inboundDepartures.Length > 0)
        {
            match["$match"]["_id.id"] = new BsonDocument("$in",
                new BsonArray(inboundDepartures.Select(td => td.ToShortString())));
        }

        if (outboundDepartures.Length > 0)
        {
            match["$match"]["_id.od"] = new BsonDocument("$in",
                new BsonArray(outboundDepartures.Select(td => td.ToShortString())));
        }

        var pipeline = new[]
        {
            match,
            new("$group",
                new BsonDocument
                {
                    {
                        "_id", new BsonDocument
                        {
                            { "d", "$d" },
                            { "s", "$_id.s" },
                        }
                    },
                    {
                        "c",
                        new BsonDocument("$min", new BsonDocument
                        {
                            { "p", "$p" },
                            { "ra", "$ra" },
                            { "rd", "$rd" },
                            { "d", "$_id.d" },
                            { "a", "$_id.a" },
                            { "c", "$_id.c" },
                            { "id", "$_id.id" },
                            { "od", "$_id.od" },
                            { "fn", "$fn" },
                            { "rfn", "$rfn" },
                            { "bi", "$bi" },
                            { "o", "$o" }
                        })
                    }
                }),
            new("$project", new BsonDocument
            {
                { "d", "$c.d" },
                { "a", "$c.a" },
                { "s", "$_id.s" },
                { "c", "$c.c" },
                { "dd", "$_id.d" },
                { "rad", "$c.ra" },
                { "rdd", "$c.rd" },
                { "id", "$c.id" },
                { "od", "$c.od" },
                { "fn", "$c.fn" },
                { "rfn", "$c.rfn" },
                { "bi", "$c.bi" },
                { "p", "$c.p" },
                { "o", "$c.o" },
                { "_id", 0 }
            })
        };

        var cursor = await _packageFlightVariants.AggregateAsync<PackageFlightVariantProjection>(pipeline,
            cancellationToken: cancellationToken);
        var result = await cursor.ToListAsync(cancellationToken);

        return result
            .Select(x => PackageFlightVariant.Create(
                checkIn: x.CheckIn,
                stayLength: x.StayLength,
                marketId: marketId,
                packageOccupancy: occupancy,
                departureAirport: x.DepartureAirport,
                arrivalAirport: x.ArrivalAirport,
                offerId: x.OfferId,
                inboundDeparture: x.InboundDeparture,
                outboundDeparture: x.OutboundDeparture,
                departureDate: x.DepartureDate,
                returnArrivalDate: x.ReturnArrivalDate,
                returnDepartureDate: x.ReturnDepartureDate,
                flightNumbers: x.FlightNumbers ?? FlightNumbers.Empty,
                returnFlightNumbers: x.ReturnFlightNumbers ?? FlightNumbers.Empty,
                baggageIncluded: x.BaggageIncluded,
                price: x.Price))
            .ToArray();
    }

    public async Task<PackageFlightVariant[]> GetCheapestsPerAirports(string marketId, int[] stayLengths,
        Airport[] departureAirports, Airport[] arrivalAirports, DateOnly departureDateFrom,
        DateOnly departureDateTo, PackageOccupancy occupancy, TimeOfDay[] inboundDepartures,
        TimeOfDay[] outboundDepartures, CancellationToken cancellationToken)
    {
        var monthsCount = GetMonthsCount(departureDateFrom, departureDateTo);
        var limit = Math.Max((int)Math.Ceiling((double)MaxVariantsPerGroupAllMonths / monthsCount), 3);

        var parallelTasks = arrivalAirports.Select(arrivalAirport =>
            GetCheapestsForSingleAirport(marketId, stayLengths, departureAirports, arrivalAirport,
                departureDateFrom, departureDateTo, occupancy, inboundDepartures, outboundDepartures, limit, cancellationToken));

        var results = await Task.WhenAll(parallelTasks);

        return results.SelectMany(result => result).ToArray();
    }

    public async Task<IEnumerable<DateOnly>> GetAvailableDepartureDates(string marketId, int stayLength,
        Airport[] departureAirports, Airport[] arrivalAirports, PackageOccupancy occupancy,
        CancellationToken cancellationToken)
    {
        var parallelTasks = arrivalAirports.Select(arrivalAirport =>
            GetAvailableDepartureDatesForSingleAirport(marketId, stayLength, departureAirports, arrivalAirport,
                occupancy, cancellationToken));

        var results = await Task.WhenAll(parallelTasks);

        return results.SelectMany(result => result).Distinct();
    }

    private async Task<DateOnly[]> GetAvailableDepartureDatesForSingleAirport(string marketId, int stayLength,
        Airport[] departureAirports, Airport arrivalAirport, PackageOccupancy occupancy,
        CancellationToken cancellationToken)
    {
        var match = new BsonDocument("$match",
            new BsonDocument
            {
                { "_id.k", marketId },
                { "_id.s", stayLength },
                { "_id.a", arrivalAirport.ToString() },
                { "_id.o", occupancy.ToString() },
                {
                    "_id.d", new BsonDocument("$in", new BsonArray(departureAirports.Select(d => d.ToString())))
                }
            });

        var group = new BsonDocument("$group",
            new BsonDocument("_id", "$d"));

        var pipeline = new[] { match, group };

        var cursor = await _packageFlightVariants.AggregateAsync<BsonDocument>(pipeline, cancellationToken: cancellationToken);
        var results = await cursor.ToListAsync(cancellationToken);

        return results.Select(doc => DateOnly.FromDateTime(doc["_id"].ToUniversalTime())).ToArray();
    }

    private static int GetMonthsCount(DateOnly from, DateOnly to)
    {
        return (to.Year - from.Year) * 12 + (to.Month - from.Month) + 1;
    }

    private async Task<PackageFlightVariant[]> GetCheapestsForSingleAirport(string marketId, int[] stayLengths,
        Airport[] departureAirports, Airport arrivalAirport, DateOnly departureDateFrom,
        DateOnly departureDateTo, PackageOccupancy occupancy, TimeOfDay[] inboundDepartures,
        TimeOfDay[] outboundDepartures, int limit, CancellationToken cancellationToken)
    {
        var departureDateTimeFrom = departureDateFrom.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc);
        var departureDateTimeTo = departureDateTo.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc);

        var match = new BsonDocument("$match",
            new BsonDocument
            {
                { "_id.o", occupancy.ToString() },
                { "_id.k", marketId },
                { "_id.a", arrivalAirport.ToString() },
                { "_id.s", new BsonDocument("$in", new BsonArray(stayLengths)) },
                { "d", new BsonDocument
                    {
                        { "$gte", departureDateTimeFrom },
                        { "$lte", departureDateTimeTo }
                    }
                }
            });

        if (departureAirports.Length > 0)
        {
            match["$match"]["_id.d"] = new BsonDocument("$in", new BsonArray(departureAirports.Select(a => a.ToString())));
        }

        if (inboundDepartures.Length > 0)
        {
            match["$match"]["_id.id"] = new BsonDocument("$in", new BsonArray(inboundDepartures.Select(td => td.ToShortString())));
        }

        if (outboundDepartures.Length > 0)
        {
            match["$match"]["_id.od"] = new BsonDocument("$in", new BsonArray(outboundDepartures.Select(td => td.ToShortString())));
        }

        var pipeline = new[]
        {
            match,
            new("$group",
                new BsonDocument
                {
                    {
                        "_id", new BsonDocument
                        {
                            { "d", "$d" },
                            { "s", "$_id.s" }
                        }
                    },
                    {
                        "f", new BsonDocument("$first", new BsonDocument
                        {
                            { "p", "$p" },
                            {
                                "_id", new BsonDocument
                                {
                                    { "c", "$_id.c" },
                                    { "s", "$_id.s" },
                                    { "k", "$_id.k" },
                                    { "a", "$_id.a" },
                                    { "d", "$_id.d" },
                                    { "o", "$_id.o" },
                                    { "id", "$_id.id" },
                                    { "od", "$_id.od" }
                                }
                            }
                        })
                    }
                }),
            new("$group",
                new BsonDocument
                {
                    {
                        "_id", new BsonDocument
                        {
                            { "s", "$_id.s" },
                            { "m", new BsonDocument("$dateToString", new BsonDocument
                                {
                                    { "date", "$_id.d" },
                                    { "format", "%Y%m" }
                                })
                            }
                        }
                    },
                    {
                        "f", new BsonDocument("$minN", new BsonDocument
                        {
                            { "n", limit },
                            {
                                "input", new BsonDocument
                                {
                                    { "p", "$f.p" },
                                    { "_id", "$f._id" }
                                }
                            }
                        })
                    }
                }),
            new("$unwind", "$f"),
            new("$lookup", new BsonDocument
            {
                { "from", "packageFlightVariants" },
                { "let", new BsonDocument("id", "$f._id") },
                {
                    "pipeline", new BsonArray
                    {
                        new BsonDocument("$match", new BsonDocument
                        {
                            {
                                "$and", new BsonArray
                                {
                                    new BsonDocument("$expr", new BsonDocument("$eq", new BsonArray { "$_id", "$$id" })),
                                    new BsonDocument("$expr", new BsonDocument("$eq", new BsonArray { "$_id.a", "$$id.a" }))
                                }
                            }
                        }),
                        new BsonDocument("$limit", 1)
                    }
                },
                { "as", "doc" }
            }),
            new("$unwind", "$doc"),
            new("$project", new BsonDocument
            {
                { "c", "$doc._id.c" },
                { "s", "$doc._id.s" },
                { "a", "$doc._id.a" },
                { "d", "$doc._id.d" },
                { "o", "$doc.o" },
                { "dd", "$doc.d" },
                { "rad", "$doc.ra" },
                { "rdd", "$doc.rd" },
                { "id", "$doc._id.id" },
                { "od", "$doc._id.od" },
                { "fn", "$doc.fn" },
                { "rfn", "$doc.rfn" },
                { "bi", "$doc.bi" },
                { "p", "$doc.p" },
                { "_id", 0 }
            })
        };

        var cursor = await _packageFlightVariants.AggregateAsync<PackageFlightVariantProjection>(pipeline,
            cancellationToken: cancellationToken);
        var result = await cursor.ToListAsync(cancellationToken);

        return result
            .Select(x => PackageFlightVariant.Create(
                checkIn: x.CheckIn,
                stayLength: x.StayLength,
                marketId: marketId,
                packageOccupancy: occupancy,
                arrivalAirport: x.ArrivalAirport,
                departureAirport: x.DepartureAirport,
                offerId: x.OfferId,
                inboundDeparture: x.InboundDeparture,
                outboundDeparture: x.OutboundDeparture,
                departureDate: x.DepartureDate,
                returnArrivalDate: x.ReturnArrivalDate,
                returnDepartureDate: x.ReturnDepartureDate,
                flightNumbers: x.FlightNumbers ?? FlightNumbers.Empty,
                returnFlightNumbers: x.ReturnFlightNumbers ?? FlightNumbers.Empty,
                baggageIncluded: x.BaggageIncluded,
                price: x.Price))
            .ToArray();
    }

    public async Task EnsureIndexes()
    {
        await database.Database.CreateCollectionAsync(CollectionName);

        await _packageFlightVariants.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageFlightVariant>(
                Builders<PackageFlightVariant>.IndexKeys
                    .Ascending(x => x.Id.Occupancy)
                    .Ascending(x => x.Id.MarketId)
                    .Ascending(x => x.Id.ArrivalAirport)
                    .Ascending(x => x.Id.StayLength)
                    .Ascending(x => x.DepartureDate)
                    .Ascending(x => x.Price)
                    .Ascending(x => x.Id.DepartureAirport)
                    .Ascending(x => x.Id.InboundDeparture)
                    .Ascending(x => x.Id.OutboundDeparture)
                    .Ascending(x => x.Id.CheckIn)
            )
        );

        // TODO: Add index for Inbound/Outbound times when needed

        // for sharding purposes
        await _packageFlightVariants.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageFlightVariant>(
                Builders<PackageFlightVariant>.IndexKeys
                    .Hashed(x => x.Id.ArrivalAirport)
            )
        );
    }
}