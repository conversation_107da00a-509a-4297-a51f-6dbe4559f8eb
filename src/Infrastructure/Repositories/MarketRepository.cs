using Esky.Packages.Domain.Model.Markets;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Infrastructure.Database;
using Esky.Packages.Infrastructure.Database.IndexInitializers;
using MongoDB.Driver;

namespace Esky.Packages.Infrastructure.Repositories;

public class MarketRepository(PackageDatabase database) : IMarketRepository, IIndexInitializer
{
    private const string CollectionName = "markets";
    
    private readonly IMongoCollection<Market> _markets = database.Database.GetCollection<Market>(CollectionName);
    
    public async Task Add(Market market, CancellationToken cancellationToken = default)
    {
        await _markets.InsertOneAsync(market, cancellationToken: cancellationToken);
    }

    public async Task Upsert(Market market, CancellationToken cancellationToken = default)
    {
        var filter = Builders<Market>.Filter.Eq(x => x.Id, market.Id);
        
        await _markets.ReplaceOneAsync(filter, market, options: new ReplaceOptions
        {
            IsUpsert = true
        }, cancellationToken: cancellationToken);
    }

    public async Task<Market?> GetById(string id, CancellationToken cancellationToken = default)
    {
        return await _markets
            .Find(x => x.Id == id)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<bool> Remove(string id, CancellationToken cancellationToken = default)
    {
        var result = await _markets
            .DeleteOneAsync(Builders<Market>.Filter.Eq(f => f.Id, id), cancellationToken);

        return result.IsAcknowledged && result.DeletedCount == 1;
    }

    public async Task<IReadOnlyCollection<Market>> GetAll(CancellationToken cancellationToken = default)
    {
        return await _markets
            .Find(_ => true)
            .ToListAsync(cancellationToken);
    }

    public async Task EnsureIndexes()
    {
        await database.Database.CreateCollectionAsync(CollectionName);
    }
}