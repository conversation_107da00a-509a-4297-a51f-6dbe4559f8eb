using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Infrastructure.Database;
using Esky.Packages.Infrastructure.Database.IndexInitializers;
using MongoDB.Bson;
using MongoDB.Driver;

namespace Esky.Packages.Infrastructure.Repositories;

internal class PipelineRepository(PackageDatabase database) : IPipelineRepository, IIndexInitializer
{
    private const string CollectionName = "pipelines";

    private readonly IMongoCollection<Pipeline> _pipelines = database.Database.GetCollection<Pipeline>(CollectionName);

    public async Task UpsertWithoutConcurrency(List<Pipeline> pipelines, CancellationToken cancellationToken)
    {
        var bulkOperations = pipelines
            .Select(item =>
                new ReplaceOneModel<Pipeline>(Builders<Pipeline>.Filter.Eq(d => d.Id, item.Id), item)
                {
                    IsUpsert = true
                });

        var bulkWriteOptions = new BulkWriteOptions();

        await _pipelines.BulkWriteAsync(bulkOperations, bulkWriteOptions, cancellationToken);
    }

    public async Task<Pipeline?> GetByIdAndDefinitionId(string id, string definitionId, 
        CancellationToken cancellationToken = default)
    {
        return await _pipelines
            .Find(x => x.Id == id && x.DefinitionId == definitionId)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task MarkForRetry(string id, string definitionId, string startedByUserId, 
        CancellationToken cancellationToken)
    {
        await _pipelines
            .UpdateOneAsync(x => x.Id == id && x.DefinitionId == definitionId,
                Builders<Pipeline>.Update
                    .Set(x => x.StartedByUserId, startedByUserId)
                    .Set(x => x.ErrorMessage, null)
                    .Set(x => x.FinishedAt, null)
                    .Set(x => x.Status, PipelineStatus.Waiting), cancellationToken: cancellationToken);
    }

    public async Task MarkAsRunning(string id, string definitionId, CancellationToken cancellationToken)
    {
        await _pipelines
            .UpdateOneAsync(x => x.Id == id && x.DefinitionId == definitionId,
                Builders<Pipeline>.Update
                    .Set(x => x.Status, PipelineStatus.Running), cancellationToken: cancellationToken);
    }

    public async Task MarkAsCompleted(string id, string definitionId, CancellationToken cancellationToken)
    {
        await _pipelines
            .UpdateOneAsync(x => x.Id == id && x.DefinitionId == definitionId,
                Builders<Pipeline>.Update
                    .Set(x => x.Status, PipelineStatus.Completed)
                    .Set(x => x.FinishedAt, DateTime.UtcNow), cancellationToken: cancellationToken);
    }

    public async Task MarkAsFailed(string id, string definitionId, string errorMessage, 
        CancellationToken cancellationToken)
    {
        await _pipelines
            .UpdateOneAsync(x => x.Id == id && x.DefinitionId == definitionId,
                Builders<Pipeline>.Update
                    .Set(x => x.Status, PipelineStatus.Failed)
                    .Set(x => x.ErrorMessage, errorMessage)
                    .Set(x => x.FinishedAt, DateTime.UtcNow), cancellationToken: cancellationToken);
    }

    public async Task RemoveOutdatedPipelines(string definitionId, CancellationToken cancellationToken)
    {
        var aggregation = new[]
        {
            new BsonDocument("$match", new BsonDocument("definitionId", definitionId)),
            new BsonDocument("$sort", new BsonDocument("groupId", -1)),
            new BsonDocument("$group", new BsonDocument
            {
                { "_id", "$groupId" },
                { "groupId", new BsonDocument("$first", "$groupId") }
            }),
            new BsonDocument("$limit", 1)
         };

        var latestGroups = await _pipelines
            .Aggregate<BsonDocument>(aggregation, cancellationToken: cancellationToken)
            .ToListAsync(cancellationToken);

        var latestGroupIds = latestGroups.Select(g => g["_id"].AsString).ToList();

        var filter = Builders<Pipeline>.Filter.And(
            Builders<Pipeline>.Filter.Eq(p => p.DefinitionId, definitionId),
            Builders<Pipeline>.Filter.Nin(p => p.GroupId, latestGroupIds)
        );

        await _pipelines.DeleteManyAsync(filter, cancellationToken);
    }

    public async Task<List<GenerationAggregatedStatistics>> GenerationsAggregatedStatistics(
        CancellationToken cancellationToken)
    {
        var aggregation = new[]
        {
            new BsonDocument("$sort", new BsonDocument
            {
                { "definitionId", 1 },
                { "groupId", -1 }
            }),
            new BsonDocument("$group", new BsonDocument
            {
                { "_id", "$definitionId" },
                { "definitionId", new BsonDocument("$first", "$definitionId") },
                { "groupId", new BsonDocument("$first", "$groupId") }
            }),
            new BsonDocument("$lookup", new BsonDocument
            {
                { "from", CollectionName },
                { "let", new BsonDocument
                    {
                        { "definitionId", "$definitionId" },
                        { "groupId", "$groupId" }
                    }
                },
                { "pipeline", new BsonArray
                    {
                        new BsonDocument("$match", new BsonDocument("$expr", new BsonDocument("$eq", new BsonArray { "$groupId", "$$groupId" })))
                    }
                },
                { "as", "pipelines" }
            }),
            new BsonDocument("$unwind", "$pipelines"),
            new BsonDocument("$group", new BsonDocument
            {
                { "_id", "$definitionId" },
                { "pipelineGroupId", new BsonDocument("$first", "$groupId") },
                { "latestFinishedAt", new BsonDocument("$max", "$pipelines.finishedAt") },
                { "completed", CountPipelineStatuses(PipelineStatus.Completed) },
                { "running", CountPipelineStatuses(PipelineStatus.Running) },
                { "waiting", CountPipelineStatuses(PipelineStatus.Waiting) },
                { "failed", CountPipelineStatuses(PipelineStatus.Failed) },
                { "total", new BsonDocument("$sum", 1) },
            }),
            new BsonDocument("$sort", new BsonDocument("latestFinishedAt", -1)),
            new BsonDocument("$project", new BsonDocument
            {
                { "definitionId", "$_id" },
                { "pipelineGroupId", 1 },
                { "latestFinishedAt", 1 },
                { "completed", 1 },
                { "running", 1 },
                { "waiting", 1 },
                { "failed", 1 },
                { "total", 1 }
            })
        };

        return await _pipelines
            .Aggregate<GenerationAggregatedStatistics>(aggregation, cancellationToken: cancellationToken)
            .ToListAsync(cancellationToken);
    }

    public async Task<GenerationDetailedStatistics?> GenerationDetailedStatistics(string definitionId, 
        CancellationToken cancellationToken)
    {
        var aggregation = new[]
        {
            new BsonDocument("$match", new BsonDocument("definitionId", definitionId)),
            new BsonDocument("$sort", new BsonDocument
            {
                { "groupId", -1 }
            }),
            new BsonDocument("$group", new BsonDocument
            {
                { "_id", "$definitionId" },
                { "definitionId", new BsonDocument("$first", "$definitionId") },
                { "groupId", new BsonDocument("$first", "$groupId") }
            }),
            new BsonDocument("$lookup", new BsonDocument
            {
                { "from", CollectionName },
                { "let", new BsonDocument
                    {
                        { "definitionId", "$definitionId" },
                        { "groupId", "$groupId" }
                    }
                },
                { "pipeline", new BsonArray
                    {
                        new BsonDocument("$match", new BsonDocument("$expr", new BsonDocument("$eq", new BsonArray { "$groupId", "$$groupId" })))
                    }
                },
                { "as", "pipelines" }
            }),
            new BsonDocument("$unwind", "$pipelines"),
            new BsonDocument("$group", new BsonDocument
            {
                { "_id", "$definitionId" },
                { "pipelineGroupId", new BsonDocument("$first", "$groupId") },
                { "latestFinishedAt", new BsonDocument("$max", "$pipelines.finishedAt") },
                { "completed", CountPipelineStatuses(PipelineStatus.Completed) },
                { "running", CountPipelineStatuses(PipelineStatus.Running) },
                { "waiting", CountPipelineStatuses(PipelineStatus.Waiting) },
                { "failed", CountPipelineStatuses(PipelineStatus.Failed) },
                { "total", new BsonDocument("$sum", 1) },
                { "failedPipelines", new BsonDocument("$push", new BsonDocument("$cond", new BsonArray
                    {
                        new BsonDocument("$eq", new BsonArray { "$pipelines.status", PipelineStatus.Failed }),
                        new BsonDocument
                        {
                            { "pipelineId", "$pipelines._id" },
                            { "errorMessage", "$pipelines.errorMessage" }
                        },
                        "$$REMOVE"
                    }))
                }
            }),
            new BsonDocument("$project", new BsonDocument
            {
                { "definitionId", "$_id" },
                { "pipelineGroupId", 1 },
                { "latestFinishedAt", 1 },
                { "completed", 1 },
                { "running", 1 },
                { "waiting", 1 },
                { "failed", 1 },
                { "total", 1 },
                { "failedPipelines", 1 }
            })
        };

        return await _pipelines
            .Aggregate<GenerationDetailedStatistics>(aggregation, cancellationToken: cancellationToken)
            .FirstOrDefaultAsync(cancellationToken);
    }

    private static BsonDocument CountPipelineStatuses(PipelineStatus status)
    {
        return new BsonDocument("$sum", new BsonDocument("$cond", new BsonArray { new BsonDocument("$eq", new BsonArray { "$pipelines.status", status }), 1, 0 }));
    }

    public async Task<List<DefinitionFailedPipelines>> GetAllFailedPipelinesByDefinitionId(CancellationToken cancellationToken = default)
    {
        var aggregation = new[]
        {
            new BsonDocument("$sort", new BsonDocument
            {
                { "definitionId", 1 },
                { "groupId", -1 }
            }),
            new BsonDocument("$group", new BsonDocument
            {
                { "_id", "$definitionId" },
                { "definitionId", new BsonDocument("$first", "$definitionId") },
                { "groupId", new BsonDocument("$first", "$groupId") }
            }),
            new BsonDocument("$lookup", new BsonDocument
            {
                { "from", CollectionName },
                { "let", new BsonDocument
                    {
                        { "definitionId", "$definitionId" },
                        { "groupId", "$groupId" }
                    }
                },
                { "pipeline", new BsonArray
                    {
                        new BsonDocument("$match", new BsonDocument("$expr", new BsonDocument("$and", new BsonArray
                        {
                            new BsonDocument("$eq", new BsonArray { "$groupId", "$$groupId" }),
                            new BsonDocument("$eq", new BsonArray { "$status", PipelineStatus.Failed })
                        })))
                    }
                },
                { "as", "pipelines" }
            }),
            new BsonDocument("$unwind", "$pipelines"),
            new BsonDocument("$group", new BsonDocument
            {
                { "_id", "$definitionId" },
                { "definitionId", new BsonDocument("$first", "$definitionId") },
                { "failedPipelineIds", new BsonDocument("$push", "$pipelines._id") }
            }),
            new BsonDocument("$project", new BsonDocument
            {
                { "definitionId", 1 },
                { "failedPipelineIds", 1 }
            })
        };

        return await _pipelines
            .Aggregate<DefinitionFailedPipelines>(aggregation, cancellationToken: cancellationToken)
            .ToListAsync(cancellationToken);
    }

    public async Task EnsureIndexes()
    {
        await database.Database.CreateCollectionAsync(CollectionName);

        await _pipelines.Indexes.CreateOneAsync(
            new CreateIndexModel<Pipeline>(
                Builders<Pipeline>.IndexKeys
                    .Ascending(g => g.DefinitionId)
                    .Descending(g => g.GroupId)
            )
        );

        await _pipelines.Indexes.CreateOneAsync(
            new CreateIndexModel<Pipeline>(
                Builders<Pipeline>.IndexKeys
                    .Ascending(g => g.GroupId)
                    .Ascending(g => g.Status)
            )
        );

        // for sharding purposes
        await _pipelines.Indexes.CreateOneAsync(
            new CreateIndexModel<Pipeline>(
                Builders<Pipeline>.IndexKeys
                    .Hashed(x => x.DefinitionId)
            )
        );
    }
}
