using Esky.Packages.Domain.Model.PackageHotelOfferVariantPrices;
using Esky.Packages.Domain.Model.PackageVariants;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Infrastructure.Database;
using Esky.Packages.Infrastructure.Database.IndexInitializers;
using MongoDB.Driver;

namespace Esky.Packages.Infrastructure.Repositories;

internal class PackageHotelOfferVariantPriceRepository(PackageDatabase database) : IPackageHotelOfferVariantPriceRepository, IIndexInitializer
{
    private const string CollectionName = "packageHotelOfferVariantPrices";

    private readonly IMongoCollection<PackageHotelOfferVariantPrice> _packageHotelOfferVariantPrices = database.Database.GetCollection<PackageHotelOfferVariantPrice>(CollectionName);

    public async Task EnsureIndexes()
    {
        await database.Database.CreateCollectionAsync(CollectionName);

        await _packageHotelOfferVariantPrices.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageHotelOfferVariantPrice>(
                Builders<PackageHotelOfferVariantPrice>.IndexKeys.Ascending(x => x.Id.CheckIn),
                new CreateIndexOptions { ExpireAfter = TimeSpan.FromHours(24) }
            )
        );

        //sharding
        await _packageHotelOfferVariantPrices.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageHotelOfferVariantPrice>(
                Builders<PackageHotelOfferVariantPrice>.IndexKeys
                    .Hashed(x => x.Id.MetaCode)
            )
        );
    }

    public async Task<IReadOnlyCollection<PackageHotelOfferVariantPrice>> GetByIds(IReadOnlyCollection<PackageHotelOfferVariantPriceId> ids, CancellationToken cancellationToken)
    {
        if (ids.Count == 0) return [];

        var result = await _packageHotelOfferVariantPrices
            .Find(Builders<PackageHotelOfferVariantPrice>.Filter.In(x => x.Id, ids))
            .ToListAsync(cancellationToken);

        return result;
    }

    public async Task<IReadOnlyCollection<PackageHotelOfferVariantPrice>> GetByMultiplePackageVariantsAndDateRange(
        IReadOnlyCollection<(PackageVariantId Id, DateOnly FromPriceDate, DateOnly ToPriceDate)> packageVariantDateRanges,
        CancellationToken cancellationToken)
    {
        if (packageVariantDateRanges.Count == 0)
        {
            return [];
        }

        var orFilters = packageVariantDateRanges.Select(packageVariantDateRange =>
        {
            var startId = PackageHotelOfferVariantPriceId.Create(
                checkIn: packageVariantDateRange.Id.CheckIn,
                stayLength: packageVariantDateRange.Id.StayLength,
                marketId: packageVariantDateRange.Id.MarketId,
                metaCode: packageVariantDateRange.Id.MetaCode,
                mealPlan: packageVariantDateRange.Id.MealPlan,
                occupancy: packageVariantDateRange.Id.Occupancy,
                priceDate: packageVariantDateRange.FromPriceDate);

            var endId = PackageHotelOfferVariantPriceId.Create(
                checkIn: packageVariantDateRange.Id.CheckIn,
                stayLength: packageVariantDateRange.Id.StayLength,
                marketId: packageVariantDateRange.Id.MarketId,
                metaCode: packageVariantDateRange.Id.MetaCode,
                mealPlan: packageVariantDateRange.Id.MealPlan,
                occupancy: packageVariantDateRange.Id.Occupancy,
                priceDate: packageVariantDateRange.ToPriceDate);

            return Builders<PackageHotelOfferVariantPrice>.Filter.And(
                Builders<PackageHotelOfferVariantPrice>.Filter.Eq(x => x.Id.MetaCode, packageVariantDateRange.Id.MetaCode),
                Builders<PackageHotelOfferVariantPrice>.Filter.Gte(x => x.Id, startId),
                Builders<PackageHotelOfferVariantPrice>.Filter.Lte(x => x.Id, endId)
            );
        }).ToList();

        var filter = Builders<PackageHotelOfferVariantPrice>.Filter.Or(orFilters);
        return await _packageHotelOfferVariantPrices.Find(filter).ToListAsync(cancellationToken);
    }

    public async Task Upsert(IReadOnlyCollection<PackageHotelOfferVariantPrice> prices, CancellationToken cancellationToken)
    {
        if (prices.Count == 0) return;

        var filter = Builders<PackageHotelOfferVariantPrice>.Filter;

        var batch = prices
            .Select(item => new ReplaceOneModel<PackageHotelOfferVariantPrice>(filter.Eq(d => d.Id, item.Id), item) { IsUpsert = true });

        await _packageHotelOfferVariantPrices.BulkWriteAsync(batch, new BulkWriteOptions { IsOrdered = false }, cancellationToken: cancellationToken);
    }
}
