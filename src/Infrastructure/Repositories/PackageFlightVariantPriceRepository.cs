using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Model.PackageFlightVariantPrices;
using Esky.Packages.Domain.Model.PackageVariants;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Infrastructure.Database;
using Esky.Packages.Infrastructure.Database.IndexInitializers;
using MongoDB.Driver;

namespace Esky.Packages.Infrastructure.Repositories;

internal class PackageFlightVariantPriceRepository(PackageDatabase database) : IPackageFlightVariantPriceRepository, IIndexInitializer
{
    private const string CollectionName = "packageFlightVariantPrices";

    private readonly IMongoCollection<PackageFlightVariantPrice> _packageFlightVariantPrices = database.Database.GetCollection<PackageFlightVariantPrice>(CollectionName);

    public async Task EnsureIndexes()
    {
        await database.Database.CreateCollectionAsync(CollectionName);

        await _packageFlightVariantPrices.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageFlightVariantPrice>(
                Builders<PackageFlightVariantPrice>.IndexKeys.Ascending(x => x.Id.DepartureDate),
                new CreateIndexOptions { ExpireAfter = TimeSpan.FromHours(24) }
            )
        );

        //sharding
        await _packageFlightVariantPrices.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageFlightVariantPrice>(
                Builders<PackageFlightVariantPrice>.IndexKeys
                    .Hashed(x => x.PartitionKey)
            )
        );
    }

    public async Task<IReadOnlyCollection<PackageFlightVariantPrice>> GetByIds(IReadOnlyCollection<PackageFlightVariantPriceId> ids, CancellationToken cancellationToken)
    {
        if (ids.Count == 0) return [];

        var result = await _packageFlightVariantPrices
            .Find(Builders<PackageFlightVariantPrice>.Filter.In(x => x.Id, ids))
            .ToListAsync(cancellationToken);

        return result;
    }

    public async Task<IReadOnlyCollection<PackageFlightVariantPrice>> GetByMultiplePackageVariantsAndDateRange(
        IReadOnlyCollection<(PackageVariantId Id, DateOnly FromPriceDate, DateOnly ToPriceDate)> packageVariantDateRanges,
        CancellationToken cancellationToken)
    {
        if (packageVariantDateRanges.Count == 0)
        {
            return [];
        }

        var orFilters = packageVariantDateRanges.Select(packageVariantDateRange =>
        {
            var startId = PackageFlightVariantPriceId.Create(
                departureDate: packageVariantDateRange.Id.DepartureDate,
                returnDepartureDate: packageVariantDateRange.Id.ReturnDepartureDate,
                marketId: packageVariantDateRange.Id.MarketId,
                arrivalAirport: packageVariantDateRange.Id.ArrivalAirport,
                departureAirport: packageVariantDateRange.Id.DepartureAirport,
                flightNumbers: packageVariantDateRange.Id.FlightNumbers,
                returnFlightNumbers: packageVariantDateRange.Id.ReturnFlightNumbers,
                occupancy: packageVariantDateRange.Id.Occupancy,
                priceDate: packageVariantDateRange.FromPriceDate);

            var endId = PackageFlightVariantPriceId.Create(
                departureDate: packageVariantDateRange.Id.DepartureDate,
                returnDepartureDate: packageVariantDateRange.Id.ReturnDepartureDate,
                marketId: packageVariantDateRange.Id.MarketId,
                arrivalAirport: packageVariantDateRange.Id.ArrivalAirport,
                departureAirport: packageVariantDateRange.Id.DepartureAirport,
                flightNumbers: packageVariantDateRange.Id.FlightNumbers,
                returnFlightNumbers: packageVariantDateRange.Id.ReturnFlightNumbers,
                occupancy: packageVariantDateRange.Id.Occupancy,
                priceDate: packageVariantDateRange.ToPriceDate);

            return Builders<PackageFlightVariantPrice>.Filter.And(
                Builders<PackageFlightVariantPrice>.Filter.Eq(x => x.PartitionKey, new PackageFlightPartitionKey([packageVariantDateRange.Id.ArrivalAirport, packageVariantDateRange.Id.DepartureAirport])),
                Builders<PackageFlightVariantPrice>.Filter.Gte(x => x.Id, startId),
                Builders<PackageFlightVariantPrice>.Filter.Lte(x => x.Id, endId)
            );
        }).ToList();

        var filter = Builders<PackageFlightVariantPrice>.Filter.Or(orFilters);

        return await _packageFlightVariantPrices.Find(filter).ToListAsync(cancellationToken);
    }

    public async Task Upsert(IReadOnlyCollection<PackageFlightVariantPrice> prices, CancellationToken cancellationToken)
    {
        if (prices.Count == 0) return;

        var filter = Builders<PackageFlightVariantPrice>.Filter;

        var batch = prices
            .Select(item => new ReplaceOneModel<PackageFlightVariantPrice>(filter.Eq(d => d.Id, item.Id), item) { IsUpsert = true });

        await _packageFlightVariantPrices.BulkWriteAsync(batch, new BulkWriteOptions { IsOrdered = false }, cancellationToken: cancellationToken);
    }
}