using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http;
using System.Diagnostics;
using System.Net;

namespace Esky.Packages.Infrastructure.Observability;

public class RequestLoggerMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ICommunicationLogger _communicationLogger;
    private readonly ILoggingContextProvider _contextProvider;

    public RequestLoggerMiddleware(
        RequestDelegate next,
        ICommunicationLogger requestLogger,
        ILoggingContextProvider contextProvider
        )
    {
        _next = next;
        _communicationLogger = requestLogger;
        _contextProvider = contextProvider;
    }

    public Task Invoke(HttpContext httpContext)
    {
        return ExecuteWithCommunicationLogging(httpContext);
    }

    private async Task ExecuteWithCommunicationLogging(HttpContext httpContext)
    {
        var operationId = Random.Shared.Next(1000, 9999);

        var host = httpContext.Request.Host.HasValue
            ? httpContext.Request.Host.ToString()
            : httpContext.Connection.LocalIpAddress?.ToString();

        var url = $"{httpContext.Request.Scheme}://{host}{httpContext.Request.Path.Value}{httpContext.Request.QueryString}";
        var method = httpContext.Request.Method;

        var context = new CommunicationLogContext(
            _contextProvider.RequestId,
            _contextProvider.SessionId,
            _contextProvider.OperationName,
            operationId,
            _contextProvider.ActionName,
            _contextProvider.Provider,
            url,
            method
        );

        LogCommunicationEntry(context, new LogDetails
        {
            Type = CommunicationLogType.Request,
            Headers = httpContext.Request.Headers.GroupBy(x => x.Key).ToDictionary(x => x.Key, x => x.First().Value.ToString()),
            Content = await ReadRequestBodyAsync(httpContext.Request)
        });

        Stopwatch stopwatch = new();
        var originalResponseBodyStream = httpContext.Response.Body;
        Exception? exception = null;
        string? responseBody = null;

        try
        {
            using (var ms = new MemoryStream())
            {
                httpContext.Response.Body = ms;

                stopwatch.Start();
                await _next(httpContext);
                stopwatch.Stop();

                responseBody = await GetResponseContentAsync(httpContext.Response);
                await ms.CopyToAsync(originalResponseBodyStream);
            }
        }
        catch (Exception e)
        {
            exception = e;
            httpContext.Response.Body = originalResponseBodyStream;
            throw;
        }
        finally
        {
            var responseHeaders = httpContext.Response.Headers.GroupBy(x => x.Key).ToDictionary(x => x.Key, x => x.First().Value.ToString());
            exception ??= httpContext.Features.Get<IExceptionHandlerFeature>()?.Error;

            // log real response
            LogCommunicationEntry(context, new LogDetails
            {
                Type = CommunicationLogType.Response,
                Headers = responseHeaders,
                Content = responseBody,
                ResponseCode = httpContext.Response.StatusCode,
                ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
            });

            // log exception as response error
            if (exception != null || httpContext.Response.StatusCode == (int)HttpStatusCode.InternalServerError)
            {
                LogCommunicationEntry(context, new LogDetails
                {
                    Type = CommunicationLogType.Error,
                    Headers = responseHeaders,
                    Content = ContentFromException(exception),
                    ResponseCode = httpContext.Response.StatusCode,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                });
            }
        }
    }
    private void LogCommunicationEntry(CommunicationLogContext context, LogDetails details)
    {
        _communicationLogger.Log(new CommunicationLogEntry
        {
            Headers = string.Join("\n", details.Headers.Select(h => $"{h.Key}: {h.Value}")),
            Content = details.Content,
            Type = details.Type,
            ResponseCode = details.ResponseCode,
            ElapsedMilliseconds = details.ElapsedMilliseconds,
            HttpMethod = context.HttpMethod,
            Url = context.Url,
            RequestId = context.RequestId,
            SessionId = context.SessionId,
            OperationName = context.OperationName,
            OperationId = context.OperationId,
            ActionName = context.ActionName,
            Provider = context.Provider,
        });
    }

    private static async Task<string> ReadRequestBodyAsync(HttpRequest request)
    {
        if (request.Method == HttpMethods.Get)
        {
            return request.QueryString.Value ?? string.Empty;
        }

        request.EnableBuffering();

        string requestBody = await new StreamReader(request.Body, leaveOpen: true).ReadToEndAsync();
        request.Body.Seek(0, SeekOrigin.Begin);
        return requestBody;
    }

    private static async Task<string?> GetResponseContentAsync(HttpResponse response)
    {
        if (!response.Body.CanSeek) return null;
        response.Body.Seek(0, SeekOrigin.Begin);
        string text;

        text = await new StreamReader(response.Body, leaveOpen: true).ReadToEndAsync();

        response.Body.Seek(0, SeekOrigin.Begin);

        return text;
    }

    private static string? ContentFromException(Exception? exception)
    {
        if (exception is null)
            return null;

        return exception.InnerException == null
            ? exception.Message
            : $"{exception.Message} << {ContentFromException(exception.InnerException)}";
    }

    private class LogDetails
    {
        public CommunicationLogType Type { get; set; }
        public IDictionary<string, string> Headers { get; set; } = new Dictionary<string, string>();
        public string? Content { get; set; }
        public int? ResponseCode { get; set; }
        public long? ElapsedMilliseconds { get; set; }
    }

    private record CommunicationLogContext(
        string RequestId,
        string SessionId,
        string OperationName,
        int OperationId,
        string ActionName,
        string Provider,
        string Url,
        string HttpMethod
    );
}