using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Esky.Packages.Infrastructure.Observability;

public static class ApplicationBuilderExtensions
{
    public static IApplicationBuilder UseRequestLogging(this IApplicationBuilder app)
    {
        app.UseWhen(ctx => IsApiRequest(ctx), appBuilder =>
        {
            appBuilder.UseMiddleware<RequestLoggerMiddleware>();
        });

        return app;
    }

    private static bool IsApiRequest(HttpContext context) =>
        context.Request.Path.HasValue &&
        context.Request.Path.StartsWithSegments("/api") &&
        context.Request.Method != HttpMethod.Options.ToString();


    public static IServiceCollection AddCommunicationLogging(this IServiceCollection services)
    {
        services.TryAddSingleton<ICommunicationLogger, NLogCommunicationLogger>();
        services.AddHttpContextAccessor();
        services.AddTransient<ILoggingContextProvider, LoggingContextProvider>();
        return services;
    }

    public static IHttpClientBuilder WithLoggingHandler(
        this IHttpClientBuilder builder
        )
    {
        builder.ConfigureHttpClient(client =>
        {
            client.DefaultRequestHeaders.Add("X-Client-Name", builder.Name);
        });
        
        builder.AddHttpMessageHandler(x => new HttpLoggingHandler(
                x.GetRequiredService<ICommunicationLogger>(),
                x.GetRequiredService<ILoggingContextProvider>()
            ));
        return builder;
    }
}