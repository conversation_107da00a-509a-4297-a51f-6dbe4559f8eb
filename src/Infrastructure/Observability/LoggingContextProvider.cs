using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;

namespace Esky.Packages.Infrastructure.Observability;

public interface ILoggingContextProvider
{
    string RequestId { get; }
    string SessionId { get; }
    string OperationName { get; }
    string ActionName { get; }
    string Provider { get; }
}

public class LoggingContextProvider : ILoggingContextProvider
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private const string SessionIdKey = "Esky-SessionId";

    public LoggingContextProvider(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    private HttpContext? HttpContext => _httpContextAccessor.HttpContext;

    public string RequestId => HttpContext?.TraceIdentifier ?? Guid.NewGuid().ToString();

    public string SessionId
    {
        get 
        {
            if (HttpContext != null
                && HttpContext.Request.Headers.TryGetValue(SessionIdKey, out StringValues requestSession)
                && requestSession.Count == 1)
            {
                return requestSession.First();
            }
            else
            {
                return null;
            }
        }
    }

    public string OperationName => HttpContext?.Request?.Path.Value?.Split('/').ElementAtOrDefault(2) ?? string.Empty;

    public string ActionName => HttpContext?.Request?.Path.Value?.Split('/').Last() ?? string.Empty;

    public string Provider => string.Empty;
}
