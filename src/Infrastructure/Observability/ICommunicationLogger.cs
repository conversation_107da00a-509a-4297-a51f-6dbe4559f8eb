using NLog;
using ILogger = NLog.ILogger;
using LogLevel = NLog.LogLevel;

namespace Esky.Packages.Infrastructure.Observability;

public interface ICommunicationLogger
{
    void Log(CommunicationLogEntry item);
}

public sealed class NLogCommunicationLogger : ICommunicationLogger
{
    private readonly ILogger _logger;

    public NLogCommunicationLogger()
    {
        _logger = LogManager.GetLogger("CommunicationLogger");
    }

    public void Log(CommunicationLogEntry item)
    {
        var eventInfo = CreateEventInfo(item);

        _logger.Log(eventInfo);
    }

    private LogEventInfo CreateEventInfo(CommunicationLogEntry item)
    {
        var message = $"{item.Type.ToString()} {item.ResponseCode} {item.HttpMethod}";
        var eventInfo = new LogEventInfo(LogLevel.Info, _logger.Name, message);
        eventInfo = SetEventProperties(eventInfo, item);

        return eventInfo;
    }

    private LogEventInfo SetEventProperties(LogEventInfo eventInfo, CommunicationLogEntry item)
    {
        eventInfo.Properties["Type"] = (int)item.Type;
        eventInfo.Properties["RequestId"] = item.RequestId;
        eventInfo.Properties["SessionId"] = item.SessionId;
        eventInfo.Properties["Url"] = item.Url;
        eventInfo.Properties["Headers"] = item.Headers;
        eventInfo.Properties["Content"] = item.Content;
        eventInfo.Properties["Environment"] = "";
        eventInfo.Properties["OperationName"] = item.OperationName;
        eventInfo.Properties["ActionName"] = item.ActionName;
        eventInfo.Properties["Provider"] = item.Provider.ToString();
        eventInfo.Properties["ElapsedMilliseconds"] = item.ElapsedMilliseconds;
        eventInfo.Properties["TraceId"] = "";
        eventInfo.Properties["OperationId"] = item.OperationId;
        eventInfo.Properties["HttpMethod"] = item.HttpMethod;
        eventInfo.Properties["ResponseCode"] = item.ResponseCode;

        return eventInfo;
    }
}