using System.Diagnostics;

namespace Esky.Packages.Infrastructure.Observability;

public class HttpLoggingHandler : DelegatingHandler
{
    private readonly ICommunicationLogger _communicationLogger;
    private readonly ILoggingContextProvider _loggingContextProvider;

    private record Context(
        string Provider,
        string Url,
        string SessionId,
        string RequestId,
        string OperationName,
        string ActionName,
        int OperationId
        );

    private class LogDetails
    {
        public string Headers { get; init; } = string.Empty;
        public string HttpMethod { get; init; } = string.Empty;
        public string Content { get; init; } = string.Empty;
        public int? ResponseCode { get; init; }
        public long? ElapsedMilliseconds { get; init; }
        public CommunicationLogType Type { get; init; }
    }

    public HttpLoggingHandler(
        ICommunicationLogger communicationLogger,
        ILoggingContextProvider loggingContextProvider
        )
    {
        _communicationLogger = communicationLogger ?? throw new ArgumentNullException(nameof(communicationLogger));
        _loggingContextProvider = loggingContextProvider;
    }

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request,
        CancellationToken cancellationToken)
    {
        var context = new Context(
            GetProviderFromClientName(request),
            request.RequestUri?.ToString() ?? string.Empty,
            _loggingContextProvider.SessionId,
            _loggingContextProvider.RequestId,
            _loggingContextProvider.OperationName,
            _loggingContextProvider.ActionName,
            Random.Shared.Next(100, 999)
            );

        if (!string.IsNullOrEmpty(_loggingContextProvider.SessionId))
        {
            request.Headers.Add("Esky-SessionId", _loggingContextProvider.SessionId);
        }

        Log(context, new LogDetails
        {
            Headers = request.Headers.ToString() + request.Content?.Headers.ToString(),
            HttpMethod = request.Method.Method,
            Content = GetContent(request.Content),
            Type = CommunicationLogType.Request
        });

        Stopwatch stopwatch = new();
        try
        {
            stopwatch.Start();
            var response = await base.SendAsync(request, cancellationToken);

            stopwatch.Stop();
            Log(context, new LogDetails
            {
                Headers = response.Headers.ToString() + response.Content?.Headers.ToString(),
                HttpMethod = response.RequestMessage?.Method.Method ?? string.Empty,
                Content = GetContent(response.Content),
                ResponseCode = (int)response.StatusCode,
                ElapsedMilliseconds = stopwatch.ElapsedMilliseconds,
                Type = CommunicationLogType.Response
            });

            return response;
        }
        catch (Exception ex)
        {
            Log(context, new LogDetails
            {
                Headers = request.Headers.ToString() + request.Content?.Headers.ToString(),
                HttpMethod = request.Method.Method,
                Content = "ERROR: " + ex.ToString(),
                ElapsedMilliseconds = stopwatch.ElapsedMilliseconds,
                Type = CommunicationLogType.Error
            });
            throw;
        }
    }

    private void Log(Context context, LogDetails details)
    {
        var entry = new CommunicationLogEntry
        {
            Url = context.Url,
            Headers = details.Headers,
            HttpMethod = details.HttpMethod,
            Content = details.Content,
            ResponseCode = details.ResponseCode,
            ActionName = context.ActionName,
            OperationName = context.OperationName,
            Provider = context.Provider,
            SessionId = context.SessionId,
            RequestId = context.RequestId,
            OperationId = context.OperationId,
            ElapsedMilliseconds = details.ElapsedMilliseconds,
            Type = details.Type,
        };

        _communicationLogger.Log(entry);
    }

    private static string GetContent(HttpContent? content)
    {
        var requestBody = content?.ReadAsStringAsync();
        return requestBody?.Result ?? "NO CONTENT";
    }

    private static string GetProviderFromClientName(HttpRequestMessage request)
    {
        var clientName = request.Headers.GetValues("X-Client-Name").FirstOrDefault() ?? string.Empty;

        return clientName.Replace("Client", string.Empty);
    }
}

