namespace Esky.Packages.Infrastructure.Observability;

public class BucketMeter
{
    private readonly int _sizeSeconds;
    private readonly int[] _values;
    private int _previousBucketIndex;

    public BucketMeter(int sizeSeconds)
    {
        if (sizeSeconds <= 0)
        {
            throw new ArgumentException("Size must be greater then 0.");
        }
        
        _sizeSeconds = sizeSeconds;
        _values = new int[_sizeSeconds + 1];
        _previousBucketIndex = GetBucketIndex();
    }

    public void Add(int count)
    {
        var bucketIndex = GetBucketIndex();
        if (bucketIndex != _previousBucketIndex)
        {
            _previousBucketIndex = bucketIndex;
            _values[bucketIndex] = 0;
        }

        _values[bucketIndex] += count;
    }
    
    public float GetRatePerSecond(int intervalSeconds)
    {
        return GetIncrease(intervalSeconds) / intervalSeconds;
    }

    public float GetIncrease(int intervalSeconds)
    {
        if (intervalSeconds <= 0)
        {
            throw new ArgumentException("Interval must be greater than 0.");
        }
        if (intervalSeconds > _sizeSeconds)
        {
            throw new ArgumentException("Interval cannot be greater than the meter size.");
        }

        // always skip the current bucket
        var bucketIndex = GetPreviousBucketIndex(GetBucketIndex());
        var sum = 0;
        for (var i = 0; i < intervalSeconds; i++)
        {
            sum += _values[bucketIndex];
            bucketIndex = GetPreviousBucketIndex(bucketIndex);
        }

        return sum;
    }
    
    public void Clear()
    {
        for (var i = 0; i < _sizeSeconds; i++)
        {
            _values[i] = 0;
        }
    }
    
    private int GetBucketIndex()
    {
        var seconds = ((DateTimeOffset)DateTime.UtcNow).ToUnixTimeSeconds();
        return (int)(seconds % _sizeSeconds);
    }
    
    private int GetPreviousBucketIndex(int index)
    {
        return (index - 1 + _sizeSeconds) % _sizeSeconds;
    }
}