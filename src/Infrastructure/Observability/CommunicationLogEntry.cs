namespace Esky.Packages.Infrastructure.Observability;

public class CommunicationLogEntry
{
    public required string Url { get; set; }
    public string Headers { get; set; }
    public int? ResponseCode { get; set; }
    public string HttpMethod { get; set; }
    public required string Content { get; set; }
    public string OperationName { get; set; }
    public required string ActionName { get; set; }
    public string Provider { get; set; }
    public long? ElapsedMilliseconds { get; set; }
    public string RequestId { get; set; }
    public required string SessionId { get; set; }
    public int? OperationId { get; set; }
    public CommunicationLogType Type { get; set; }
}


public enum CommunicationLogType
{
    Request = 0,
    Response = 1,
    Error = 2
}