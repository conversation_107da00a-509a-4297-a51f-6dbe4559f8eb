using Esky.Packages.Application.Abstractions.Gateways.DataAnalyticsGateway;
using Esky.Packages.Infrastructure.Gateways.DataAnalyticsGateway.BigQuery;
using Esky.Packages.Infrastructure.Gateways.DataAnalyticsGateway.BigQuery.Converters;

namespace Esky.Packages.Infrastructure.Gateways.DataAnalyticsGateway;

public class DataAnalyticsGateway(IBigQueryStorage bigQueryStorage) : IDataAnalyticsGateway
{
    public async Task LogGeneratedPackages(IEnumerable<GeneratedPackage> packages,
        CancellationToken cancellationToken)
    {
        await bigQueryStorage.InsertRows(IBigQueryStorage.GeneratedPackage,
            packages.Select(GeneratedPackageConverter.Convert), cancellationToken);
    }
}