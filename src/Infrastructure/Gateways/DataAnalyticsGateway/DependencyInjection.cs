using Esky.Packages.Application.Abstractions.Gateways.DataAnalyticsGateway;
using Esky.Packages.Infrastructure.Configuration;
using Esky.Packages.Infrastructure.Gateways.DataAnalyticsGateway.BigQuery;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.Packages.Infrastructure.Gateways.DataAnalyticsGateway;

internal static class DependencyInjection
{
    public static IServiceCollection AddDataAnalyticsGateway(this IServiceCollection services, 
        IConfiguration configuration)
    {
        services.RegisterOptions<BigQueryOptions>(configuration, BigQueryOptions.ConfigurationSection);
        services.AddSingleton<IBigQueryStorage, BigQueryStorage>();
        
        services.AddSingleton<IDataAnalyticsGateway, DataAnalyticsGateway>();

        return services;
    }
}