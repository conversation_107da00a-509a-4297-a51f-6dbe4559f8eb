using Esky.Packages.Infrastructure.Gateways.DataAnalyticsGateway.BigQuery.Converters;
using Google.Apis.Bigquery.v2.Data;
using Google.Cloud.BigQuery.V2;

namespace Esky.Packages.Infrastructure.Gateways.DataAnalyticsGateway.BigQuery;

public interface IBigQueryStorage
{
    public const string GeneratedPackage = "GeneratedPackage";
    
    Task InsertRows(string tableName, IEnumerable<BigQueryInsertRow> rows, CancellationToken cancellationToken);
}

public class BigQueryStorage : IBigQueryStorage
{
    private const int ChunkSize = 300;
    private readonly Dictionary<string, BigQueryTable> _createdTables = [];
    private readonly Dictionary<string, TableSchema> _schemaMap = new()
    {
        { IBigQueryStorage.GeneratedPackage, GeneratedPackageConverter.GetSchema() }
    };

    private readonly Dictionary<string, Table> _tableConfigurationMap = new()
    {
        { 
            IBigQueryStorage.GeneratedPackage, 
            new Table
            {
                TimePartitioning = new TimePartitioning
                {
                    Type = "DAY",
                    Field = "Timestamp",
                    ExpirationMs = 36 * 60 * 60 * 1000 // 36 hours TTL
                },
                Clustering = new Clustering
                {
                    Fields = ["MetaCode"]
                }
            }
        }
    };
    
    private readonly Lazy<BigQueryDataset> _dataset;
    
    public BigQueryStorage(BigQueryOptions options)
    {
        _dataset = new Lazy<BigQueryDataset>(() => {
            var client = BigQueryClient.Create(options.ProjectId);
            return client.GetOrCreateDataset(options.Dataset);
        });
    }

    public async Task InsertRows(string tableName, IEnumerable<BigQueryInsertRow> rows, 
        CancellationToken cancellationToken)
    {
        if (!_createdTables.TryGetValue(tableName, out var table))
        {
            table = await GetOrCreateTable(tableName, cancellationToken);
            _createdTables.Add(tableName, table);
        }

        try
        {
            foreach (var data in rows.Chunk(ChunkSize))
            {
                await table.InsertRowsAsync(data, cancellationToken: cancellationToken);
            }
        }
        catch (Exception ex)
        {
            throw new Exception($"Failed to insert rows into table {tableName}", ex);
        }
    }
    
    private async Task<BigQueryTable> GetOrCreateTable(string tableName, CancellationToken cancellationToken)
    {
        try
        {
            var schema = _schemaMap[tableName];
            var table = new Table
            {
                Schema = schema
            };

            if (_tableConfigurationMap.TryGetValue(tableName, out var tableConfig))
            {
                table.TimePartitioning = tableConfig.TimePartitioning;
                table.Clustering = tableConfig.Clustering;
            }

            var createdTable = await _dataset.Value.GetOrCreateTableAsync(tableName, table, 
                cancellationToken: cancellationToken);
            
            if (createdTable.Resource.Schema?.Fields?.Count != schema.Fields.Count)
            {
                createdTable.Resource.Schema = schema;
                createdTable = await createdTable.UpdateAsync(cancellationToken: cancellationToken);
            }

            return createdTable;
        }
        catch (Exception ex)
        {
            throw new Exception($"Failed to ensure schema for table {tableName}", ex);
        }
    }
}