using Esky.Packages.Application.Abstractions.Gateways.DataAnalyticsGateway;
using Google.Apis.Bigquery.v2.Data;
using Google.Cloud.BigQuery.V2;

namespace Esky.Packages.Infrastructure.Gateways.DataAnalyticsGateway.BigQuery.Converters;

public static class GeneratedPackageConverter
{
    public static TableSchema GetSchema()
    {
        return new TableSchema
        {
            Fields = new List<TableFieldSchema>
            {
                new() { Name = "Timestamp", Type = "TIMESTAMP" },
                new() { Name = "Id", Type = "STRING" },
                new() { Name = "DefinitionId", Type = "STRING" },
                new() { Name = "MetaCode", Type = "INT64" },
                new() { Name = "CheckIn", Type = "DATE" },
                new() { Name = "StayLength", Type = "INT64" },
                new() { Name = "Currency", Type = "STRING" },
                new()
                {
                    Name = "HotelOffers",
                    Type = "RECORD",
                    Mode = "REPEATED",
                    Fields = new List<TableFieldSchema>
                    {
                        new() { Name = "Occupancy", Type = "STRING" },
                        new() { Name = "MealPlan", Type = "STRING" },
                        new() { Name = "Price", Type = "NUMERIC" }
                    }
                },
                new()
                {
                    Name = "FlightOffers",
                    Type = "RECORD",
                    Mode = "REPEATED",
                    Fields = new List<TableFieldSchema>
                    {
                        new() { Name = "Ids", Type = "STRING", Mode = "REPEATED" },
                        new() { Name = "DepartureAirport", Type = "STRING" },
                        new() { Name = "ArrivalAirport", Type = "STRING" },
                        new() { Name = "DepartureDate", Type = "DATE" },
                        new() { Name = "ReturnArrivalDate", Type = "DATE" },
                        new() { Name = "Occupancy", Type = "STRING" },
                        new() { Name = "Price", Type = "NUMERIC" }
                    }
                }
            }
        };
    }

    public static BigQueryInsertRow Convert(GeneratedPackage package)
    {
        return new BigQueryInsertRow
        {
            { "Timestamp", package.Timestamp },
            { "Id", package.Id },
            { "DefinitionId", package.DefinitionId },
            { "MetaCode", package.MetaCode },
            { "CheckIn", package.CheckIn.ToString(Constants.DateFormat) },
            { "StayLength", package.StayLength },
            { "Currency", package.Currency },
            { "HotelOffers", package.HotelOffers?.ConvertAll(h => new BigQueryInsertRow
                {
                    { "Occupancy", h.Occupancy },
                    { "MealPlan", h.MealPlan },
                    { "Price", (double)h.Price }
                }) ?? [] },
            { "FlightOffers", package.FlightOffers?.ConvertAll(f => new BigQueryInsertRow
                {
                    { "Ids", f.Ids },
                    { "DepartureAirport", f.DepartureAirport },
                    { "ArrivalAirport", f.ArrivalAirport },
                    { "DepartureDate", f.DepartureDate.ToString(Constants.DateFormat) },
                    { "ReturnArrivalDate", f.ReturnArrivalDate.ToString(Constants.DateFormat) },
                    { "Occupancy", f.Occupancy },
                    { "Price", (double)f.Price }
                }) ?? [] }
        };
    }
} 