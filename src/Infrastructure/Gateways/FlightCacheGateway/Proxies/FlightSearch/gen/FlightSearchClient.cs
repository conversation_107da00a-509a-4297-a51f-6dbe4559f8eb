//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

#pragma warning disable 108 // Disable "CS0108 '{derivedDto}.To<PERSON><PERSON>()' hides inherited member '{dtoBase}.To<PERSON>son()'. Use the new keyword if hiding was intended."
#pragma warning disable 114 // Disable "CS0114 '{derivedDto}.RaisePropertyChanged(String)' hides inherited member 'dtoBase.RaisePropertyChanged(String)'. To make the current member override that implementation, add the override keyword. Otherwise add the new keyword."
#pragma warning disable 472 // Disable "CS0472 The result of the expression is always 'false' since a value of type 'Int32' is never equal to 'null' of type 'Int32?'
#pragma warning disable 612 // Disable "CS0612 '...' is obsolete"
#pragma warning disable 649 // Disable "CS0649 Field is never assigned to, and will always have its default value null"
#pragma warning disable 1573 // Disable "CS1573 Parameter '...' has no matching param tag in the XML comment for ...
#pragma warning disable 1591 // Disable "CS1591 Missing XML comment for publicly visible type or member ..."
#pragma warning disable 8073 // Disable "CS8073 The result of the expression is always 'false' since a value of type 'T' is never equal to 'null' of type 'T?'"
#pragma warning disable 3016 // Disable "CS3016 Arrays as attribute arguments is not CLS-compliant"
#pragma warning disable 8603 // Disable "CS8603 Possible null reference return"
#pragma warning disable 8604 // Disable "CS8604 Possible null reference argument for parameter"
#pragma warning disable 8625 // Disable "CS8625 Cannot convert null literal to non-nullable reference type"
#pragma warning disable 8765 // Disable "CS8765 Nullability of type of parameter doesn't match overridden member (possibly because of nullability attributes)."

namespace Esky.Packages.Infrastructure.Gateways.FlightCacheGateway.Proxies.FlightSearch
{
    using System = global::System;

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightSearchClient 
    {
        private System.Net.Http.HttpClient _httpClient;
        private static System.Lazy<System.Text.Json.JsonSerializerOptions> _settings = new System.Lazy<System.Text.Json.JsonSerializerOptions>(CreateSerializerSettings, true);
        private System.Text.Json.JsonSerializerOptions _instanceSettings;

    #pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public FlightSearchClient(System.Net.Http.HttpClient httpClient)
    #pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        {
            _httpClient = httpClient;
            Initialize();
        }

        private static System.Text.Json.JsonSerializerOptions CreateSerializerSettings()
        {
            var settings = new System.Text.Json.JsonSerializerOptions();
            UpdateJsonSerializerSettings(settings);
            return settings;
        }

        protected System.Text.Json.JsonSerializerOptions JsonSerializerSettings { get { return _instanceSettings ?? _settings.Value; } }

        static partial void UpdateJsonSerializerSettings(System.Text.Json.JsonSerializerOptions settings);

        partial void Initialize();

        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task CheapestDateAsync(DateTime departureDate, string partner_Code, string departureCode, string arrivalCode)
        {
            return CheapestDateAsync(departureDate, partner_Code, departureCode, arrivalCode, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task CheapestDateAsync(DateTime departureDate, string partner_Code, string departureCode, string arrivalCode, System.Threading.CancellationToken cancellationToken)
        {
            if (departureDate == null)
                throw new System.ArgumentNullException("departureDate");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {

                    if (partner_Code != null)
                        request_.Headers.TryAddWithoutValidation("Partner-Code", ConvertToString(partner_Code, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/Calendar/rt/route/{departureCode}-{arrivalCode}/departure/{departureDate}/cheapestDate"
                    urlBuilder_.Append("api/v1.0/Calendar/rt/route/");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(departureCode, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append('-');
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(arrivalCode, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/departure/");
                    urlBuilder_.Append(System.Uri.EscapeDataString(departureDate.ToString("s", System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/cheapestDate");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            return;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task MinPricesForAirlineAsync(string airlineCode, string currency, string partner_Code, string environment, string configProfile)
        {
            return MinPricesForAirlineAsync(airlineCode, currency, partner_Code, environment, configProfile, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task MinPricesForAirlineAsync(string airlineCode, string currency, string partner_Code, string environment, string configProfile, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {

                    if (partner_Code != null)
                        request_.Headers.TryAddWithoutValidation("Partner-Code", ConvertToString(partner_Code, System.Globalization.CultureInfo.InvariantCulture));

                    if (environment != null)
                        request_.Headers.TryAddWithoutValidation("Environment", ConvertToString(environment, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/Calendar/rt/airlineCode/{airlineCode}/currency/{currency}/minPricesForAirline"
                    urlBuilder_.Append("api/v1.0/Calendar/rt/airlineCode/");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(airlineCode, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/currency/");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(currency, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/minPricesForAirline");
                    urlBuilder_.Append('?');
                    if (configProfile != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("ConfigProfile")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(configProfile, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    urlBuilder_.Length--;

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            return;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<SpecialOfferFlightsView> SpecialOfferFlightsAsync(SpecialOfferFlightsQuery body)
        {
            return SpecialOfferFlightsAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<SpecialOfferFlightsView> SpecialOfferFlightsAsync(SpecialOfferFlightsQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/FlightCache/SpecialOfferFlights"
                    urlBuilder_.Append("api/v1.0/FlightCache/SpecialOfferFlights");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<SpecialOfferFlightsView>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<BestOffersQueryResult> BestOffersQueryAsync(BestOffersQuery body)
        {
            return BestOffersQueryAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<BestOffersQueryResult> BestOffersQueryAsync(BestOffersQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/FlightCache/BestOffersQuery"
                    urlBuilder_.Append("api/v1.0/FlightCache/BestOffersQuery");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<BestOffersQueryResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task OwAsync(string partner_Code, string departureCode, string arrivalCode)
        {
            return OwAsync(partner_Code, departureCode, arrivalCode, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task OwAsync(string partner_Code, string departureCode, string arrivalCode, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {

                    if (partner_Code != null)
                        request_.Headers.TryAddWithoutValidation("Partner-Code", ConvertToString(partner_Code, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/flights/route/{departureCode}-{arrivalCode}/ow"
                    urlBuilder_.Append("api/v1.0/flights/route/");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(departureCode, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append('-');
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(arrivalCode, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/ow");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            return;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task StayAsync(int stayLength, string partner_Code, string departureCode, string arrivalCode)
        {
            return StayAsync(stayLength, partner_Code, departureCode, arrivalCode, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task StayAsync(int stayLength, string partner_Code, string departureCode, string arrivalCode, System.Threading.CancellationToken cancellationToken)
        {
            if (stayLength == null)
                throw new System.ArgumentNullException("stayLength");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {

                    if (partner_Code != null)
                        request_.Headers.TryAddWithoutValidation("Partner-Code", ConvertToString(partner_Code, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/flights/route/{departureCode}-{arrivalCode}/rt/stay/{stayLength}"
                    urlBuilder_.Append("api/v1.0/flights/route/");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(departureCode, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append('-');
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(arrivalCode, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/rt/stay/");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(stayLength, System.Globalization.CultureInfo.InvariantCulture)));

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            return;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <param name="orderBy">Optional ordering field. If set, selected value has to be present in fields list. Default: order by date</param>
        /// <param name="limit">Limits result set to specified number of items. Value has to be greater than 0. Default: no limit</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task Stay2Async(int minStayLength, int maxStayLength, System.Collections.Generic.IEnumerable<CalendarResultFields> fields, CalendarResultFields? orderBy, int? limit, string partner_Code, string departureCode, string arrivalCode)
        {
            return Stay2Async(minStayLength, maxStayLength, fields, orderBy, limit, partner_Code, departureCode, arrivalCode, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <param name="orderBy">Optional ordering field. If set, selected value has to be present in fields list. Default: order by date</param>
        /// <param name="limit">Limits result set to specified number of items. Value has to be greater than 0. Default: no limit</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task Stay2Async(int minStayLength, int maxStayLength, System.Collections.Generic.IEnumerable<CalendarResultFields> fields, CalendarResultFields? orderBy, int? limit, string partner_Code, string departureCode, string arrivalCode, System.Threading.CancellationToken cancellationToken)
        {
            if (minStayLength == null)
                throw new System.ArgumentNullException("minStayLength");

            if (maxStayLength == null)
                throw new System.ArgumentNullException("maxStayLength");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {

                    if (partner_Code != null)
                        request_.Headers.TryAddWithoutValidation("Partner-Code", ConvertToString(partner_Code, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/flights/route/{departureCode}-{arrivalCode}/rt/stay/{minStayLength}-{maxStayLength}"
                    urlBuilder_.Append("api/v1.0/flights/route/");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(departureCode, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append('-');
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(arrivalCode, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/rt/stay/");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(minStayLength, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append('-');
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(maxStayLength, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append('?');
                    if (fields != null)
                    {
                        foreach (var item_ in fields) { urlBuilder_.Append(System.Uri.EscapeDataString("fields")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(item_, System.Globalization.CultureInfo.InvariantCulture))).Append('&'); }
                    }
                    if (orderBy != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("orderBy")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(orderBy, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (limit != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("limit")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(limit, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    urlBuilder_.Length--;

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            return;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task DepartureAsync(DateTime? departureDate, string partner_Code, string departureCode, string arrivalCode)
        {
            return DepartureAsync(departureDate, partner_Code, departureCode, arrivalCode, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task DepartureAsync(DateTime? departureDate, string partner_Code, string departureCode, string arrivalCode, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {

                    if (partner_Code != null)
                        request_.Headers.TryAddWithoutValidation("Partner-Code", ConvertToString(partner_Code, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/flights/route/{departureCode}-{arrivalCode}/rt/departure/{departureDate}"
                    urlBuilder_.Append("api/v1.0/flights/route/");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(departureCode, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append('-');
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(arrivalCode, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/rt/departure/");
                    urlBuilder_.Append(System.Uri.EscapeDataString(departureDate.ToString("s", System.Globalization.CultureInfo.InvariantCulture)));

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            return;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task RangeAsync(DateTime departureDate, int flex, int? adt, int? yth, int? chd, int? inf, bool? useNearbyAirports, string partner_Code, string departureCode, string arrivalCode)
        {
            return RangeAsync(departureDate, flex, adt, yth, chd, inf, useNearbyAirports, partner_Code, departureCode, arrivalCode, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task RangeAsync(DateTime departureDate, int flex, int? adt, int? yth, int? chd, int? inf, bool? useNearbyAirports, string partner_Code, string departureCode, string arrivalCode, System.Threading.CancellationToken cancellationToken)
        {
            if (departureDate == null)
                throw new System.ArgumentNullException("departureDate");

            if (flex == null)
                throw new System.ArgumentNullException("flex");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {

                    if (partner_Code != null)
                        request_.Headers.TryAddWithoutValidation("Partner-Code", ConvertToString(partner_Code, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/flights/route/{departureCode}-{arrivalCode}/ow/departure/{departureDate}/range/{flex}"
                    urlBuilder_.Append("api/v1.0/flights/route/");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(departureCode, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append('-');
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(arrivalCode, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/ow/departure/");
                    urlBuilder_.Append(System.Uri.EscapeDataString(departureDate.ToString("s", System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/range/");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(flex, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append('?');
                    if (adt != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("adt")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(adt, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (yth != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("yth")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(yth, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (chd != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("chd")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(chd, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (inf != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("inf")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(inf, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (useNearbyAirports != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("useNearbyAirports")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(useNearbyAirports, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    urlBuilder_.Length--;

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            return;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task Range2Async(DateTime departureDate, DateTime returnDate, int flex, int? adt, int? yth, int? chd, int? inf, bool? useNearbyAirports, string partner_Code, string departureCode, string arrivalCode)
        {
            return Range2Async(departureDate, returnDate, flex, adt, yth, chd, inf, useNearbyAirports, partner_Code, departureCode, arrivalCode, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task Range2Async(DateTime departureDate, DateTime returnDate, int flex, int? adt, int? yth, int? chd, int? inf, bool? useNearbyAirports, string partner_Code, string departureCode, string arrivalCode, System.Threading.CancellationToken cancellationToken)
        {
            if (departureDate == null)
                throw new System.ArgumentNullException("departureDate");

            if (returnDate == null)
                throw new System.ArgumentNullException("returnDate");

            if (flex == null)
                throw new System.ArgumentNullException("flex");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {

                    if (partner_Code != null)
                        request_.Headers.TryAddWithoutValidation("Partner-Code", ConvertToString(partner_Code, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/flights/route/{departureCode}-{arrivalCode}/rt/departure/{departureDate}/return/{returnDate}/range/{flex}"
                    urlBuilder_.Append("api/v1.0/flights/route/");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(departureCode, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append('-');
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(arrivalCode, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/rt/departure/");
                    urlBuilder_.Append(System.Uri.EscapeDataString(departureDate.ToString("s", System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/return/");
                    urlBuilder_.Append(System.Uri.EscapeDataString(returnDate.ToString("s", System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/range/");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(flex, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append('?');
                    if (adt != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("adt")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(adt, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (yth != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("yth")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(yth, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (chd != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("chd")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(chd, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (inf != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("inf")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(inf, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (useNearbyAirports != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("useNearbyAirports")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(useNearbyAirports, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    urlBuilder_.Length--;

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            return;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<Response> FindAsync(Request body)
        {
            return FindAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<Response> FindAsync(Request body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/LandingPages/Deals/Find"
                    urlBuilder_.Append("api/v1.0/LandingPages/Deals/Find");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<Response>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <summary>
        /// Retrieves best prices calendar.
        /// </summary>
        /// <param name="body">Request object</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<InboundSpecialOfferCalendarFromCacheView> GetSpecialOfferCalendarFromCacheAsync(GetSpecialOfferCalendarFromCacheQuery body)
        {
            return GetSpecialOfferCalendarFromCacheAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieves best prices calendar.
        /// </summary>
        /// <param name="body">Request object</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<InboundSpecialOfferCalendarFromCacheView> GetSpecialOfferCalendarFromCacheAsync(GetSpecialOfferCalendarFromCacheQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/Legacy/GetSpecialOfferCalendarFromCache"
                    urlBuilder_.Append("api/v1.0/Legacy/GetSpecialOfferCalendarFromCache");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<InboundSpecialOfferCalendarFromCacheView>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <summary>
        /// Retrieves list of flights for specified search conditions
        /// </summary>
        /// <param name="body">Request object</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<SpecialOfferFlightsFromCacheView> GetSpecialOfferFlightsFromCacheAsync(GetSpecialOfferFlightsFromCacheQuery body)
        {
            return GetSpecialOfferFlightsFromCacheAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieves list of flights for specified search conditions
        /// </summary>
        /// <param name="body">Request object</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<SpecialOfferFlightsFromCacheView> GetSpecialOfferFlightsFromCacheAsync(GetSpecialOfferFlightsFromCacheQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/Legacy/GetSpecialOfferFlightsFromCache"
                    urlBuilder_.Append("api/v1.0/Legacy/GetSpecialOfferFlightsFromCache");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<SpecialOfferFlightsFromCacheView>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task SearchFlightsFromCacheAsync(SearchFlightsFromCacheQuery body)
        {
            return SearchFlightsFromCacheAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task SearchFlightsFromCacheAsync(SearchFlightsFromCacheQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/Legacy/SearchFlightsFromCache"
                    urlBuilder_.Append("api/v1.0/Legacy/SearchFlightsFromCache");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            return;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task GetAggregatedDestinationPricesAsync(GetAggregatedDestinationPricesQuery body)
        {
            return GetAggregatedDestinationPricesAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task GetAggregatedDestinationPricesAsync(GetAggregatedDestinationPricesQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/Legacy/GetAggregatedDestinationPrices"
                    urlBuilder_.Append("api/v1.0/Legacy/GetAggregatedDestinationPrices");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            return;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task GetPriceAlertCalendarAsync(GetPriceAlertCalendarQuery body)
        {
            return GetPriceAlertCalendarAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task GetPriceAlertCalendarAsync(GetPriceAlertCalendarQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/Legacy/GetPriceAlertCalendar"
                    urlBuilder_.Append("api/v1.0/Legacy/GetPriceAlertCalendar");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            return;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task GetAtractiveAlternativeFlightsFromCacheAsync(GetAtractiveAlternativeFlightsFromCacheQuery body)
        {
            return GetAtractiveAlternativeFlightsFromCacheAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task GetAtractiveAlternativeFlightsFromCacheAsync(GetAtractiveAlternativeFlightsFromCacheQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/Legacy/GetAtractiveAlternativeFlightsFromCache"
                    urlBuilder_.Append("api/v1.0/Legacy/GetAtractiveAlternativeFlightsFromCache");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            return;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<GetFlightsById_Response> GetFlightsByIdAsync(GetFlightsById_Query body)
        {
            return GetFlightsByIdAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<GetFlightsById_Response> GetFlightsByIdAsync(GetFlightsById_Query body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/Packages/GetFlightsById"
                    urlBuilder_.Append("api/v1.0/Packages/GetFlightsById");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<GetFlightsById_Response>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<FlightOffersForPackagesView> GetFlightsByFlightOfferKeyAsync(GetFlightsByFlightOfferKeyQuery body)
        {
            return GetFlightsByFlightOfferKeyAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<FlightOffersForPackagesView> GetFlightsByFlightOfferKeyAsync(GetFlightsByFlightOfferKeyQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/Packages/GetFlightsByFlightOfferKey"
                    urlBuilder_.Append("api/v1.0/Packages/GetFlightsByFlightOfferKey");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<FlightOffersForPackagesView>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<FlightsForPackagesView> GetFlightsFromCacheAsync(FlightsForPackagesQuery body)
        {
            return GetFlightsFromCacheAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<FlightsForPackagesView> GetFlightsFromCacheAsync(FlightsForPackagesQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/Packages/GetFlightsFromCache"
                    urlBuilder_.Append("api/v1.0/Packages/GetFlightsFromCache");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<FlightsForPackagesView>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        [System.Obsolete]
        public virtual System.Threading.Tasks.Task<FlightOffersForPackagesView> GetFlightOffersAsync(FlightOffersForPackagesQuery body)
        {
            return GetFlightOffersAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        [System.Obsolete]
        public virtual async System.Threading.Tasks.Task<FlightOffersForPackagesView> GetFlightOffersAsync(FlightOffersForPackagesQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/Packages/GetFlightOffers"
                    urlBuilder_.Append("api/v1.0/Packages/GetFlightOffers");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<FlightOffersForPackagesView>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<FlightOffersForPackagesView> GetFlightOffers2Async(FlightOffersQuery body)
        {
            return GetFlightOffers2Async(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<FlightOffersForPackagesView> GetFlightOffers2Async(FlightOffersQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v2.0/Packages/GetFlightOffers"
                    urlBuilder_.Append("api/v2.0/Packages/GetFlightOffers");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<FlightOffersForPackagesView>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        [System.Obsolete]
        public virtual System.Threading.Tasks.Task<FlightOffersForPackagesView> GetAlternativeFlightsAsync(AlternativeFlights_Query body)
        {
            return GetAlternativeFlightsAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        [System.Obsolete]
        public virtual async System.Threading.Tasks.Task<FlightOffersForPackagesView> GetAlternativeFlightsAsync(AlternativeFlights_Query body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/Packages/GetAlternativeFlights"
                    urlBuilder_.Append("api/v1.0/Packages/GetAlternativeFlights");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<FlightOffersForPackagesView>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<FlightOffersForPackagesView> GetAlternativeFlights2Async(AlternativeOffersQuery body)
        {
            return GetAlternativeFlights2Async(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<FlightOffersForPackagesView> GetAlternativeFlights2Async(AlternativeOffersQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v2.0/Packages/GetAlternativeFlights"
                    urlBuilder_.Append("api/v2.0/Packages/GetAlternativeFlights");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<FlightOffersForPackagesView>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<DynamicPackages_Response> GetFlightOffersForDynamicPackagesAsync(DynamicPackages_Request body)
        {
            return GetFlightOffersForDynamicPackagesAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<DynamicPackages_Response> GetFlightOffersForDynamicPackagesAsync(DynamicPackages_Request body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v2.0/Packages/GetFlightOffersForDynamicPackages"
                    urlBuilder_.Append("api/v2.0/Packages/GetFlightOffersForDynamicPackages");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<DynamicPackages_Response>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<FlightsForTravelExplorerView> GetFlightsFromCache2Async(FlightsForTravelExplorerQuery body)
        {
            return GetFlightsFromCache2Async(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<FlightsForTravelExplorerView> GetFlightsFromCache2Async(FlightsForTravelExplorerQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/TravelExplorer/GetFlightsFromCache"
                    urlBuilder_.Append("api/v1.0/TravelExplorer/GetFlightsFromCache");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<FlightsForTravelExplorerView>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<FlightsForTravelExplorerView> GetFlightsByOfferKeyAsync(GetByFlightOfferKey_Query body)
        {
            return GetFlightsByOfferKeyAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<FlightsForTravelExplorerView> GetFlightsByOfferKeyAsync(GetByFlightOfferKey_Query body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("text/plain"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/TravelExplorer/GetFlightsByOfferKey"
                    urlBuilder_.Append("api/v1.0/TravelExplorer/GetFlightsByOfferKey");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<FlightsForTravelExplorerView>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        if (status_ == 400)
                        {
                            string responseText_ = ( response_.Content == null ) ? string.Empty : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("Bad Request", status_, responseText_, headers_, null);
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        protected struct ObjectResponseResult<T>
        {
            public ObjectResponseResult(T responseObject, string responseText)
            {
                this.Object = responseObject;
                this.Text = responseText;
            }

            public T Object { get; }

            public string Text { get; }
        }

        public bool ReadResponseAsString { get; set; }

        protected virtual async System.Threading.Tasks.Task<ObjectResponseResult<T>> ReadObjectResponseAsync<T>(System.Net.Http.HttpResponseMessage response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Threading.CancellationToken cancellationToken)
        {
            if (response == null || response.Content == null)
            {
                return new ObjectResponseResult<T>(default(T), string.Empty);
            }

            if (ReadResponseAsString)
            {
                var responseText = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                try
                {
                    var typedBody = System.Text.Json.JsonSerializer.Deserialize<T>(responseText, JsonSerializerSettings);
                    return new ObjectResponseResult<T>(typedBody, responseText);
                }
                catch (System.Text.Json.JsonException exception)
                {
                    var message = "Could not deserialize the response body string as " + typeof(T).FullName + ".";
                    throw new ApiException(message, (int)response.StatusCode, responseText, headers, exception);
                }
            }
            else
            {
                try
                {
                    using (var responseStream = await response.Content.ReadAsStreamAsync().ConfigureAwait(false))
                    {
                        var typedBody = await System.Text.Json.JsonSerializer.DeserializeAsync<T>(responseStream, JsonSerializerSettings, cancellationToken).ConfigureAwait(false);
                        return new ObjectResponseResult<T>(typedBody, string.Empty);
                    }
                }
                catch (System.Text.Json.JsonException exception)
                {
                    var message = "Could not deserialize the response body stream as " + typeof(T).FullName + ".";
                    throw new ApiException(message, (int)response.StatusCode, string.Empty, headers, exception);
                }
            }
        }

        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value == null)
            {
                return "";
            }

            if (value is System.Enum)
            {
                var name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value != null ? attribute.Value : name;
                        }
                    }

                    var converted = System.Convert.ToString(System.Convert.ChangeType(value, System.Enum.GetUnderlyingType(value.GetType()), cultureInfo));
                    return converted == null ? string.Empty : converted;
                }
            }
            else if (value is bool) 
            {
                return System.Convert.ToString((bool)value, cultureInfo).ToLowerInvariant();
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value is string[])
            {
                return string.Join(",", (string[])value);
            }
            else if (value.GetType().IsArray)
            {
                var valueArray = (System.Array)value;
                var valueTextArray = new string[valueArray.Length];
                for (var i = 0; i < valueArray.Length; i++)
                {
                    valueTextArray[i] = ConvertToString(valueArray.GetValue(i), cultureInfo);
                }
                return string.Join(",", valueTextArray);
            }

            var result = System.Convert.ToString(value, cultureInfo);
            return result == null ? "" : result;
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum PersonTypeEnum
    {

        [System.Runtime.Serialization.EnumMember(Value = @"None")]
        None = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Adult")]
        Adult = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"Child")]
        Child = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"Youth")]
        Youth = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"Infant")]
        Infant = 4,

        [System.Runtime.Serialization.EnumMember(Value = @"Senior")]
        Senior = 5,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SearchFlightsRequestPassenger
    {

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public PersonTypeEnum Code { get; set; }

        /// <summary>
        /// Number of passengers of a specified type
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("count")]
        public int Count { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("ages")]
        public System.Collections.Generic.ICollection<int> Ages { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SpecialOfferFlightsQuery
    {
        /// <summary>
        /// Requested partner code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        /// <summary>
        /// Requested currency code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        /// <summary>
        /// Departure date
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public DateTime DepartureDate { get; set; }

        /// <summary>
        /// Departure date of a return flight (for roundtrip queries)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("returnDepartureDate")]
        public DateTime? ReturnDepartureDate { get; set; }

        /// <summary>
        /// Code of departure location
        /// <br/>Can be a 3-letter IATA airport code, city code, or an eSky multiport code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureCode")]
        public string DepartureCode { get; set; }

        /// <summary>
        /// Code of arrival location
        /// <br/>Can be a 3-letter IATA airport code, city code, or an eSky multiport code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalCode")]
        public string ArrivalCode { get; set; }

        /// <summary>
        /// Passenger configuration
        /// <br/>Default is a single adult configuration
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("passengers")]
        public System.Collections.Generic.ICollection<SearchFlightsRequestPassenger> Passengers { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SpecialOfferSegment
    {
        /// <summary>
        /// Airline code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ac")]
        public string Ac { get; set; }

        /// <summary>
        /// Departure location code
        /// <br/>3-letter IATA airport code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("dac")]
        public string Dac { get; set; }

        /// <summary>
        /// Departure date
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("dd")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public DateTime Dd { get; set; }

        /// <summary>
        /// Arrival location code
        /// <br/>3-letter IATA airport code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("aac")]
        public string Aac { get; set; }

        /// <summary>
        /// Arrival date
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ad")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public DateTime Ad { get; set; }

        /// <summary>
        /// Flight time in hh:mm format
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ft")]
        public string Ft { get; set; }

        /// <summary>
        /// Flight number
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fn")]
        public string Fn { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SpecialOfferLeg
    {
        /// <summary>
        /// Flight time in hh:mm format
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ft")]
        public string Ft { get; set; }

        /// <summary>
        /// Leg segments
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("segs")]
        public System.Collections.Generic.ICollection<SpecialOfferSegment> Segs { get; set; }

        /// <summary>
        /// Deeplink for pricing
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("deepLink")]
        public string DeepLink { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SpecialOfferFlight
    {
        /// <summary>
        /// Currency code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("c")]
        public string C { get; set; }

        /// <summary>
        /// Flight price
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("p")]
        public decimal P { get; set; }

        /// <summary>
        /// Flight legs
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("legs")]
        public System.Collections.Generic.ICollection<SpecialOfferLeg> Legs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("legthOfStay")]
        public int LegthOfStay { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SpecialOfferFlightsView
    {
        /// <summary>
        /// List of grouped flight results
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("flights")]
        public System.Collections.Generic.ICollection<SpecialOfferFlight> Flights { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum BestOffersQuery_RuntimeModeEnum
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Live")]
        Live = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Test")]
        Test = 1,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class DestinationSet
    {

        [System.Text.Json.Serialization.JsonPropertyName("airportCodes")]
        public System.Collections.Generic.ICollection<string> AirportCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("multiportCodes")]
        public System.Collections.Generic.ICollection<string> MultiportCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("cityCodes")]
        public System.Collections.Generic.ICollection<string> CityCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("countryCodes")]
        public System.Collections.Generic.ICollection<string> CountryCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("continentCodes")]
        public System.Collections.Generic.ICollection<string> ContinentCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("regionCodes")]
        public System.Collections.Generic.ICollection<string> RegionCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("anywhere")]
        public bool Anywhere { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class BestOffersQuery_GroupByDefinition
    {

        [System.Text.Json.Serialization.JsonPropertyName("by")]
        public string By { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("limit")]
        public int Limit { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("offset")]
        public int Offset { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum OrderTypeEnum
    {

        [System.Runtime.Serialization.EnumMember(Value = @"None")]
        None = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Price")]
        Price = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"StandardScore")]
        StandardScore = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"PricePerDistance")]
        PricePerDistance = 3,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class BestOffersQuery
    {

        [System.Text.Json.Serialization.JsonPropertyName("runtimeMode")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public BestOffersQuery_RuntimeModeEnum RuntimeMode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("airlineCodes")]
        public System.Collections.Generic.ICollection<string> AirlineCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureDestinationSet")]
        public DestinationSet DepartureDestinationSet { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalDestinationSet")]
        public DestinationSet ArrivalDestinationSet { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureMonth")]
        public DateTime? DepartureMonth { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("groupBy")]
        public System.Collections.Generic.ICollection<BestOffersQuery_GroupByDefinition> GroupBy { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("orderBy")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public OrderTypeEnum OrderBy { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxStandardScore")]
        public double? MaxStandardScore { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxCountryToCountryStandardScore")]
        public double? MaxCountryToCountryStandardScore { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("showDebugInfo")]
        public bool ShowDebugInfo { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class BestOffersQueryResult_Price
    {

        [System.Text.Json.Serialization.JsonPropertyName("amount")]
        public decimal Amount { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("currency")]
        public string Currency { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class BestOffersQueryResult_GroupSummary
    {

        [System.Text.Json.Serialization.JsonPropertyName("byCode")]
        public string ByCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("minimalPrice")]
        public BestOffersQueryResult_Price MinimalPrice { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class BestOffersQueryResult_Group
    {

        [System.Text.Json.Serialization.JsonPropertyName("groupSummary")]
        public BestOffersQueryResult_GroupSummary GroupSummary { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class BestOffersQueryResult_LocationWithDate
    {

        [System.Text.Json.Serialization.JsonPropertyName("airportCode")]
        public string AirportCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("cityCode")]
        public string CityCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("countryCode")]
        public string CountryCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("date")]
        public DateTime Date { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class BestOffersQueryResult_Offer
    {

        [System.Text.Json.Serialization.JsonPropertyName("airlineCode")]
        public string AirlineCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        public int ProviderCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("price")]
        public BestOffersQueryResult_Price Price { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("stopovers")]
        public int Stopovers { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("stopoversTime")]
        public System.Collections.Generic.ICollection<int> StopoversTime { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departure")]
        public BestOffersQueryResult_LocationWithDate Departure { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrival")]
        public BestOffersQueryResult_LocationWithDate Arrival { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("refreshDate")]
        public DateTime? RefreshDate { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class BestOffersQueryResult
    {

        [System.Text.Json.Serialization.JsonPropertyName("groups")]
        public System.Collections.Generic.ICollection<BestOffersQueryResult_Group> Groups { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("totalGroupsCount")]
        public int? TotalGroupsCount { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("offers")]
        public System.Collections.Generic.ICollection<BestOffersQueryResult_Offer> Offers { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("totalOffersCount")]
        public int? TotalOffersCount { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum CalendarResultFields
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Amount")]
        Amount = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"StayLength")]
        StayLength = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"QualityScore")]
        QualityScore = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"TransferCount")]
        TransferCount = 3,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Request_ConcreteDestinations
    {

        [System.Text.Json.Serialization.JsonPropertyName("airportCodes")]
        public System.Collections.Generic.ICollection<string> AirportCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("multiportCodes")]
        public System.Collections.Generic.ICollection<string> MultiportCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("cityCodes")]
        public System.Collections.Generic.ICollection<string> CityCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("countryCodes")]
        public System.Collections.Generic.ICollection<string> CountryCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("continentCodes")]
        public System.Collections.Generic.ICollection<string> ContinentCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("regionCodes")]
        public System.Collections.Generic.ICollection<string> RegionCodes { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Request_AnyDestinations
    {

        [System.Text.Json.Serialization.JsonPropertyName("anywhere")]
        public bool Anywhere { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("airportCodes")]
        public System.Collections.Generic.ICollection<string> AirportCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("multiportCodes")]
        public System.Collections.Generic.ICollection<string> MultiportCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("cityCodes")]
        public System.Collections.Generic.ICollection<string> CityCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("countryCodes")]
        public System.Collections.Generic.ICollection<string> CountryCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("continentCodes")]
        public System.Collections.Generic.ICollection<string> ContinentCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("regionCodes")]
        public System.Collections.Generic.ICollection<string> RegionCodes { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum Request_SortingOption
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Price")]
        Price = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Score")]
        Score = 1,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Request
    {

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureDestinations")]
        [System.ComponentModel.DataAnnotations.Required]
        public Request_ConcreteDestinations DepartureDestinations { get; set; } = new Request_ConcreteDestinations();

        [System.Text.Json.Serialization.JsonPropertyName("arrivalDestinations")]
        [System.ComponentModel.DataAnnotations.Required]
        public Request_AnyDestinations ArrivalDestinations { get; set; } = new Request_AnyDestinations();

        [System.Text.Json.Serialization.JsonPropertyName("departureDateFrom")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public DateOnly DepartureDateFrom { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureDateTo")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public DateOnly DepartureDateTo { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("stayDurationFrom")]
        public int StayDurationFrom { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("stayDurationTo")]
        public int StayDurationTo { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("onlyDirectFlights")]
        public bool OnlyDirectFlights { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("sorting")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public Request_SortingOption Sorting { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("limit")]
        public int Limit { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("sessionId")]
        public string SessionId { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Segment
    {

        [System.Text.Json.Serialization.JsonPropertyName("airlineCode")]
        public string AirlineCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureCode")]
        public string DepartureCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public DateTime DepartureDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalCode")]
        public string ArrivalCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalDate")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public DateTime ArrivalDate { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Leg
    {

        [System.Text.Json.Serialization.JsonPropertyName("segments")]
        public System.Collections.Generic.ICollection<Segment> Segments { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceModel
    {

        [System.Text.Json.Serialization.JsonPropertyName("amount")]
        public decimal Amount { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("transactionFee")]
        public decimal TransactionFee { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Offer
    {

        [System.Text.Json.Serialization.JsonPropertyName("legs")]
        public System.Collections.Generic.ICollection<Leg> Legs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("price")]
        [System.ComponentModel.DataAnnotations.Required]
        public PriceModel Price { get; set; } = new PriceModel();

        [System.Text.Json.Serialization.JsonPropertyName("stayDuration")]
        public int StayDuration { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("nqs")]
        public decimal Nqs { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Response
    {

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("offers")]
        public System.Collections.Generic.ICollection<Offer> Offers { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    /// <summary>
    /// Trip duration
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum LengthOfStayEnum
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Any")]
        Any = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Weekends")]
        Weekends = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"Range0To3Days")]
        Range0To3Days = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"Range4To7Days")]
        Range4To7Days = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"Range8To14Days")]
        Range8To14Days = 4,

        [System.Runtime.Serialization.EnumMember(Value = @"Range15To21Days")]
        Range15To21Days = 5,

        [System.Runtime.Serialization.EnumMember(Value = @"Custom")]
        Custom = 6,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ISearchFlightsRequestPassenger
    {

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public PersonTypeEnum Code { get; set; }

        /// <summary>
        /// Number of passengers of a specified type
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("count")]
        public int Count { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetSpecialOfferCalendarFromCacheQuery
    {
        /// <summary>
        /// True for roundtrip flights, false for one way flights
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("isRoundTrip")]
        public bool IsRoundTrip { get; set; }

        /// <summary>
        /// Departure day selected on calendar.
        /// <br/>When specified, service returns best outbound price only for a selected day (paired with a collection of return flights)
        /// <br/>When not specified, a full 12-month calendar of outbound best prices is returned - without any return flight information
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("selectedDepartureDay")]
        public DateTime? SelectedDepartureDay { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("lengthOfStay")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public LengthOfStayEnum LengthOfStay { get; set; }

        /// <summary>
        /// Id of eSky special offer to retrieve
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("specialOfferId")]
        public int? SpecialOfferId { get; set; }

        /// <summary>
        /// Code of departure location
        /// <br/>Can be a 3-letter IATA airport code, city code, or an eSky multiport code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureCode")]
        public string DepartureCode { get; set; }

        /// <summary>
        /// Code of arrival location
        /// <br/>Can be a 3-letter IATA airport code, city code, or an eSky multiport code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalCode")]
        public string ArrivalCode { get; set; }

        /// <summary>
        /// Passenger configuration
        /// <br/>Default is a single adult configuration
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("passengers")]
        public System.Collections.Generic.ICollection<ISearchFlightsRequestPassenger> Passengers { get; set; }

        /// <summary>
        /// filter by airlines codes
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airlineCodes")]
        public System.Collections.Generic.ICollection<string> AirlineCodes { get; set; }

        /// <summary>
        /// Requested partner code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        /// <summary>
        /// Requested currency code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    /// <summary>
    /// Single calendar cell
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CalendarItem
    {
        /// <summary>
        /// Date of flight
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public DateTime DepartureDate { get; set; }

        /// <summary>
        /// Best price in requested currency
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("minPriceAmount")]
        public decimal MinPriceAmount { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    /// <summary>
    /// Single calendar item (cell)
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class InboundSpecialOfferCalendarFromCacheView_CalendarItem
    {

        [System.Text.Json.Serialization.JsonPropertyName("outboundFlight")]
        [System.ComponentModel.DataAnnotations.Required]
        public CalendarItem OutboundFlight { get; set; } = new CalendarItem();

        /// <summary>
        /// Collection of cheapest inbound flight for each day
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("inboundFlights")]
        public System.Collections.Generic.ICollection<CalendarItem> InboundFlights { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    /// <summary>
    /// Describes month price
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SpecialOfferCalendarFromCacheView_MonthPrice
    {
        /// <summary>
        /// Value in requested currency
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("amount")]
        public decimal Amount { get; set; }

        /// <summary>
        /// Date representing corresponding month
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("month")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public DateTime Month { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    /// <summary>
    /// Inbound calendar data container
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class InboundSpecialOfferCalendarFromCacheView
    {
        /// <summary>
        /// Collection of calendar items (cells) representing price of cheapest outbound flight for a single day
        /// <br/>paired with a collection of cheapest return (inbound) flights for each day
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("calendarItems")]
        public System.Collections.Generic.ICollection<InboundSpecialOfferCalendarFromCacheView_CalendarItem> CalendarItems { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airTrafficRuleId")]
        [System.Obsolete]
        public int AirTrafficRuleId { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airTrafficRuleVersion")]
        [System.Obsolete]
        public string AirTrafficRuleVersion { get; set; }

        /// <summary>
        /// Best month prices for outbound flights
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("bestOutboundMonthPrices")]
        public System.Collections.Generic.ICollection<SpecialOfferCalendarFromCacheView_MonthPrice> BestOutboundMonthPrices { get; set; }

        /// <summary>
        /// Best month prices for inbound flights
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("bestInboundMonthPrices")]
        public System.Collections.Generic.ICollection<SpecialOfferCalendarFromCacheView_MonthPrice> BestInboundMonthPrices { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetSpecialOfferFlightsFromCacheQuery
    {
        /// <summary>
        /// Departure date
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public DateTime DepartureDate { get; set; }

        /// <summary>
        /// Departure date of a return flight (for roundtrip queries)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("returnDepartureDate")]
        public DateTime? ReturnDepartureDate { get; set; }

        /// <summary>
        /// Language code used for descriptions translation
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("languageCode")]
        public string LanguageCode { get; set; }

        /// <summary>
        /// Id of eSky special offer to retrieve
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("specialOfferId")]
        public int? SpecialOfferId { get; set; }

        /// <summary>
        /// Code of departure location
        /// <br/>Can be a 3-letter IATA airport code, city code, or an eSky multiport code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureCode")]
        public string DepartureCode { get; set; }

        /// <summary>
        /// Code of arrival location
        /// <br/>Can be a 3-letter IATA airport code, city code, or an eSky multiport code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalCode")]
        public string ArrivalCode { get; set; }

        /// <summary>
        /// Passenger configuration
        /// <br/>Default is a single adult configuration
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("passengers")]
        public System.Collections.Generic.ICollection<ISearchFlightsRequestPassenger> Passengers { get; set; }

        /// <summary>
        /// filter by airlines codes
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airlineCodes")]
        public System.Collections.Generic.ICollection<string> AirlineCodes { get; set; }

        /// <summary>
        /// Requested partner code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        /// <summary>
        /// Requested currency code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class StopoverItem
    {

        [System.Text.Json.Serialization.JsonPropertyName("airportCode")]
        public string AirportCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalDate")]
        public DateTime? ArrivalDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        public DateTime? DepartureDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalTerminal")]
        public string ArrivalTerminal { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureTerminal")]
        public string DepartureTerminal { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("cityCode")]
        public string CityCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("countryCode")]
        public string CountryCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FareItem
    {
        /// <summary>
        /// Passenger code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("pc")]
        public int Pc { get; set; }

        /// <summary>
        /// Fare code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fc")]
        public string Fc { get; set; }

        /// <summary>
        /// Additional data
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("addData")]
        public string AddData { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum FlightFacilityType
    {

        [System.Runtime.Serialization.EnumMember(Value = @"None")]
        None = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"RegisteredBaggage")]
        RegisteredBaggage = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"Wifi")]
        Wifi = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"Meal")]
        Meal = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"Refreshments")]
        Refreshments = 4,

        [System.Runtime.Serialization.EnumMember(Value = @"PowerPlug")]
        PowerPlug = 5,

        [System.Runtime.Serialization.EnumMember(Value = @"Entertainment")]
        Entertainment = 6,

        [System.Runtime.Serialization.EnumMember(Value = @"LegRoom")]
        LegRoom = 7,

        [System.Runtime.Serialization.EnumMember(Value = @"SeatConfiguration")]
        SeatConfiguration = 8,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightFacility
    {

        [System.Text.Json.Serialization.JsonPropertyName("t")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public FlightFacilityType T { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("d")]
        public string D { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("o")]
        public bool O { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightAttribute
    {
        /// <summary>
        /// Flight attribute type
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("t")]
        public string T { get; set; }

        /// <summary>
        /// Flight attribute description
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("d")]
        public string D { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightProperty
    {
        /// <summary>
        /// Flight property type
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("t")]
        public string T { get; set; }

        /// <summary>
        /// Flight property key
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("k")]
        public string K { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightAmenityPrice
    {
        /// <summary>
        /// Flight amenity price currency
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("c")]
        public string C { get; set; }

        /// <summary>
        /// Flight amenity price amount
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("a")]
        public decimal A { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightAmenity
    {
        /// <summary>
        /// Flight amenity type
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("t")]
        public string T { get; set; }

        /// <summary>
        /// Flight amenity key
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("k")]
        public string K { get; set; }

        /// <summary>
        /// Flight amenity is available
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ia")]
        public bool Ia { get; set; }

        /// <summary>
        /// Flight amenity is paid
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ip")]
        public bool Ip { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("p")]
        public FlightAmenityPrice P { get; set; }

        /// <summary>
        /// Flight amenity weight in kgs
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("w")]
        public int W { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SegmentItem
    {
        /// <summary>
        /// Airline code, example values: LO, W6, FR,
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ac")]
        public string Ac { get; set; }

        /// <summary>
        /// Departure location code,
        /// <br/>3-letter IATA airport code, example values: WAW, LTN
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("dac")]
        public string Dac { get; set; }

        /// <summary>
        /// Departure city code, example values: WAW, LON
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("dcc")]
        public string Dcc { get; set; }

        /// <summary>
        /// Departure date, example values: Date(1709099700000), Date(1713870900000)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("dd")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public DateTime Dd { get; set; }

        /// <summary>
        /// Arrival location code,
        /// <br/>3-letter IATA airport code, example values: WAW, LTN
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("aac")]
        public string Aac { get; set; }

        /// <summary>
        /// Arrival city code, example values: WAW, LON
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("acc")]
        public string Acc { get; set; }

        /// <summary>
        /// Arrival date, example values: Date(1709099700000), Date(1713962400000)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ad")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public DateTime Ad { get; set; }

        /// <summary>
        /// Flight time in hh:mm format, example values: 2:35
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ft")]
        public string Ft { get; set; }

        /// <summary>
        /// Flight number, example values: 286, 3883
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fn")]
        public string Fn { get; set; }

        /// <summary>
        /// Provider code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("pc")]
        public int Pc { get; set; }

        /// <summary>
        /// Stopovers
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("sac")]
        public System.Collections.Generic.ICollection<StopoverItem> Sac { get; set; }

        /// <summary>
        /// Fares
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fares")]
        public System.Collections.Generic.ICollection<FareItem> Fares { get; set; }

        /// <summary>
        /// Booking class code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("bc")]
        public string Bc { get; set; }

        /// <summary>
        /// Service class code
        /// <br/>Any = 0, Economy = 1, First = 2, Business = 3, EconomyPremium = 4
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("sc")]
        public int Sc { get; set; }

        /// <summary>
        /// Mixed flight identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("mid")]
        public int Mid { get; set; }

        /// <summary>
        /// Flight facilities
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ff")]
        public System.Collections.Generic.ICollection<FlightFacility> Ff { get; set; }

        /// <summary>
        /// Flight attributes
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fa")]
        public System.Collections.Generic.ICollection<FlightAttribute> Fa { get; set; }

        /// <summary>
        /// Flight properties
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fp")]
        public System.Collections.Generic.ICollection<FlightProperty> Fp { get; set; }

        /// <summary>
        /// Flight amenities
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fam")]
        public System.Collections.Generic.ICollection<FlightAmenity> Fam { get; set; }

        /// <summary>
        /// Aircraft code, example values: Boeing737, Embraer195, CanadairRegionalJet900
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("a")]
        public string A { get; set; }

        /// <summary>
        /// Is self transfer
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("iST")]
        public bool IST { get; set; }

        /// <summary>
        /// Is protected
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("iP")]
        public bool? IP { get; set; }

        /// <summary>
        /// Flight is operated by, example values: LO, W6, FR,
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ob")]
        public string Ob { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class LegItem
    {
        /// <summary>
        /// Leg identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fid")]
        public string Fid { get; set; }

        /// <summary>
        /// Unique offer identifier in search results scope. Should return the same value across search requests.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("oid")]
        public string Oid { get; set; }

        /// <summary>
        /// Flight time in hh:mm format, example values: 2:35
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ft")]
        public string Ft { get; set; }

        /// <summary>
        /// Available seats count, max to 9
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("asc")]
        public int Asc { get; set; }

        /// <summary>
        /// Encoded leg locator
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ll")]
        public string Ll { get; set; }

        /// <summary>
        /// Encoded leg locators for bundled flights
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ffll")]
        public System.Collections.Generic.IDictionary<string, string> Ffll { get; set; }

        /// <summary>
        /// Negative quality score
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("nqs")]
        public decimal Nqs { get; set; }

        /// <summary>
        /// DeepLink (pricing indicator) used by metasearch
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("pi")]
        public string Pi { get; set; }

        /// <summary>
        /// Optional reservation (pending airlines)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("or")]
        public bool Or { get; set; }

        /// <summary>
        /// Is the departure airport different from requested airport (nearby airport)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("dn")]
        public bool Dn { get; set; }

        /// <summary>
        /// Is the arrival airport different from requested airport (nearby airport)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("an")]
        public bool An { get; set; }

        /// <summary>
        /// Is the departure date different from requested date (flex result)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ddn")]
        public bool Ddn { get; set; }

        /// <summary>
        /// Leg segments
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("segs")]
        public System.Collections.Generic.ICollection<SegmentItem> Segs { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    /// <summary>
    /// Leg group
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightItemWithLegGroups_LegGroup
    {
        /// <summary>
        /// Flight legs in group
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("legs")]
        public System.Collections.Generic.ICollection<LegItem> Legs { get; set; }

        /// <summary>
        /// Is the departure airport different from requested airport (nearby airport)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("dn")]
        public bool Dn { get; set; }

        /// <summary>
        /// Is the arrival airport different from requested airport (nearby airport)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("an")]
        public bool An { get; set; }

        /// <summary>
        /// Is the departure date different from requested date (flex result)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ddn")]
        public bool Ddn { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightItemWithLegGroups
    {
        /// <summary>
        /// Legs grouped by price
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("lg")]
        public System.Collections.Generic.ICollection<FlightItemWithLegGroups_LegGroup> Lg { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("cacheOfferKeys")]
        public System.Collections.Generic.IDictionary<string, string> CacheOfferKeys { get; set; }

        /// <summary>
        /// Offer ids of best legs, according to NQS
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("nqsoid")]
        public System.Collections.Generic.ICollection<string> Nqsoid { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("__type")]
        [System.Obsolete]
        public string __type { get; set; }

        /// <summary>
        /// Currency code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("c")]
        public string C { get; set; }

        /// <summary>
        /// Currency symbol
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("cs")]
        public string Cs { get; set; }

        /// <summary>
        /// DU fee
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("du")]
        public decimal Du { get; set; }

        /// <summary>
        /// Provider code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("pc")]
        public int Pc { get; set; }

        /// <summary>
        /// Flight price
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("p")]
        public decimal P { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("tp")]
        public decimal Tp { get; set; }

        /// <summary>
        /// Price per passenger
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ppx")]
        public decimal Ppx { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("tbf")]
        public decimal Tbf { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("ttf")]
        public decimal Ttf { get; set; }

        /// <summary>
        /// Transaction fee amount
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("tf")]
        public decimal Tf { get; set; }

        /// <summary>
        /// Discount
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("d")]
        public decimal D { get; set; }

        /// <summary>
        /// Flight identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fid")]
        public string Fid { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("fmt")]
        public string Fmt { get; set; }

        /// <summary>
        /// Negative quality score
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("nqs")]
        public decimal Nqs { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    /// <summary>
    /// Dictionary item
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class DictionaryItem_1OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e
    {
        /// <summary>
        /// Item key
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("key")]
        public string Key { get; set; }

        /// <summary>
        /// Item type
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("type")]
        public string Type { get; set; }

        /// <summary>
        /// Item value
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("value")]
        public string Value { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class KeyValuePair_2OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798eAndOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e
    {

        [System.Text.Json.Serialization.JsonPropertyName("key")]
        public string Key { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("value")]
        public string Value { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    /// <summary>
    /// Price object
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Price
    {
        /// <summary>
        /// Currency
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        /// <summary>
        /// Amount
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("amount")]
        public decimal Amount { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SearchFiltersView
    {
        /// <summary>
        /// List of departure location codes
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureCodes")]
        public System.Collections.Generic.ICollection<string> DepartureCodes { get; set; }

        /// <summary>
        /// List of arrival location codes
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalCodes")]
        public System.Collections.Generic.ICollection<string> ArrivalCodes { get; set; }

        /// <summary>
        /// List of transfer codes
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("transferCodes")]
        public System.Collections.Generic.ICollection<string> TransferCodes { get; set; }

        /// <summary>
        /// List of airport names
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airportsNames")]
        public System.Collections.Generic.ICollection<DictionaryItem_1OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e> AirportsNames { get; set; }

        /// <summary>
        /// List of airport city mappings
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airportsCityCodes")]
        public System.Collections.Generic.ICollection<DictionaryItem_1OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e> AirportsCityCodes { get; set; }

        /// <summary>
        /// List of airport city mappings
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airportsCountryCodes")]
        public System.Collections.Generic.ICollection<KeyValuePair_2OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798eAndOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e> AirportsCountryCodes { get; set; }

        /// <summary>
        /// List of city names
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("cityNames")]
        public System.Collections.Generic.ICollection<DictionaryItem_1OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e> CityNames { get; set; }

        /// <summary>
        /// List of country names
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("countryNames")]
        public System.Collections.Generic.ICollection<KeyValuePair_2OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798eAndOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e> CountryNames { get; set; }

        /// <summary>
        /// List of airline codes
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airlineCodes")]
        public System.Collections.Generic.ICollection<string> AirlineCodes { get; set; }

        /// <summary>
        /// List of airline names
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airlineNames")]
        public System.Collections.Generic.ICollection<DictionaryItem_1OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e> AirlineNames { get; set; }

        /// <summary>
        /// List of airline types
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airlineTypes")]
        public System.Collections.Generic.ICollection<DictionaryItem_1OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e> AirlineTypes { get; set; }

        /// <summary>
        /// List of provider codes
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("providerCodes")]
        public System.Collections.Generic.ICollection<DictionaryItem_1OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e> ProviderCodes { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airlineAlliances")]
        public System.Collections.Generic.ICollection<DictionaryItem_1OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e> AirlineAlliances { get; set; }

        /// <summary>
        /// Stopovers
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("stopovers")]
        public System.Collections.Generic.ICollection<int> Stopovers { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airlinePopularity")]
        [System.Obsolete]
        public System.Collections.Generic.ICollection<string> AirlinePopularity { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airportsWebNames")]
        [System.Obsolete]
        public System.Collections.Generic.ICollection<DictionaryItem_1OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e> AirportsWebNames { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("maxPromotedFlightsForHighlightCategory")]
        [System.Obsolete]
        public int MaxPromotedFlightsForHighlightCategory { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("maxPromotedFlightsForRaiseCategory")]
        [System.Obsolete]
        public int MaxPromotedFlightsForRaiseCategory { get; set; }

        /// <summary>
        /// Shortest flight time returned
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("maxPromotedFlightsForTooltipCategory")]
        [System.Obsolete]
        public int MaxPromotedFlightsForTooltipCategory { get; set; }

        /// <summary>
        /// Longest flight time returned
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("maximalFlightTime")]
        [System.Obsolete]
        public string MaximalFlightTime { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maximalPrice")]
        public Price MaximalPrice { get; set; }

        /// <summary>
        /// Shortest flight time returned
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("minimalFlightTime")]
        [System.Obsolete]
        public string MinimalFlightTime { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("minimalPrice")]
        public Price MinimalPrice { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    /// <summary>
    /// Flights data
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SpecialOfferFlightsFromCacheView_FlightsView
    {
        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airTrafficRuleId")]
        [System.Obsolete]
        public int AirTrafficRuleId { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airTrafficRuleVersion")]
        [System.Obsolete]
        public string AirTrafficRuleVersion { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("moreResultsAvailable")]
        [System.Obsolete]
        public bool MoreResultsAvailable { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("pagingLocator")]
        [System.Obsolete]
        public string PagingLocator { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("flexDateCalendarPercentCoverage")]
        [System.Obsolete]
        public int FlexDateCalendarPercentCoverage { get; set; }

        /// <summary>
        /// Search session identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("sessionId")]
        [System.Obsolete]
        public string SessionId { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("alternativeItems")]
        [System.Obsolete]
        public System.Collections.Generic.ICollection<string> AlternativeItems { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("isPricePerPax")]
        [System.Obsolete]
        public bool IsPricePerPax { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("priceType")]
        [System.Obsolete]
        public int PriceType { get; set; }

        /// <summary>
        /// List of grouped flight results
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("items")]
        public System.Collections.Generic.ICollection<FlightItemWithLegGroups> Items { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("searchFilters")]
        [System.ComponentModel.DataAnnotations.Required]
        public SearchFiltersView SearchFilters { get; set; } = new SearchFiltersView();

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    /// <summary>
    /// Flights data container
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SpecialOfferFlightsFromCacheView
    {
        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airTrafficRuleId")]
        [System.Obsolete]
        public int AirTrafficRuleId { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airTrafficRuleVersion")]
        [System.Obsolete]
        public string AirTrafficRuleVersion { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flights")]
        [System.ComponentModel.DataAnnotations.Required]
        public SpecialOfferFlightsFromCacheView_FlightsView Flights { get; set; } = new SpecialOfferFlightsFromCacheView_FlightsView();

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum ServiceClassEnum
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Any")]
        Any = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Economy")]
        Economy = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"First")]
        First = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"Business")]
        Business = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"EconomyPremium")]
        EconomyPremium = 4,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SearchFlightsRequestLeg
    {

        [System.Text.Json.Serialization.JsonPropertyName("arrivalCityCode")]
        public string ArrivalCityCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureCityCode")]
        public string DepartureCityCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureCode")]
        public string DepartureCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        public DateTime DepartureDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalCode")]
        public string ArrivalCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flexDate")]
        public int? FlexDate { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SearchConfig
    {

        [System.Text.Json.Serialization.JsonPropertyName("searchId")]
        public string SearchId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("portionId")]
        public int PortionId { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlexConfig
    {

        [System.Text.Json.Serialization.JsonPropertyName("enabled")]
        public bool Enabled { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("range")]
        public int Range { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("limit")]
        public int Limit { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum RuntimeModeEnum
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Default")]
        Default = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Live")]
        Live = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"Test")]
        Test = 2,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SearchFlightsFromCacheQuery
    {

        [System.Text.Json.Serialization.JsonPropertyName("airlines")]
        public System.Collections.Generic.ICollection<string> Airlines { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("directFlights")]
        public bool DirectFlights { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("groupByPriceOnly")]
        public bool GroupByPriceOnly { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("packageSearch")]
        public bool? PackageSearch { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("serviceClass")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public ServiceClassEnum ServiceClass { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("priceType")]
        public int? PriceType { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flexDate")]
        public int? FlexDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("pricePerPax")]
        public bool? PricePerPax { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("originUrl")]
        public string OriginUrl { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("sessionId")]
        public string SessionId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("legs")]
        public System.Collections.Generic.ICollection<SearchFlightsRequestLeg> Legs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("alternativeCurrencyCode")]
        public string AlternativeCurrencyCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("languageCode")]
        public string LanguageCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("deepLink")]
        public bool DeepLink { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("offset")]
        public int? Offset { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("searchConfig")]
        public SearchConfig SearchConfig { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flex")]
        public FlexConfig Flex { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("runtimeMode")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public RuntimeModeEnum RuntimeMode { get; set; }

        /// <summary>
        /// Id of eSky special offer to retrieve
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("specialOfferId")]
        public int? SpecialOfferId { get; set; }

        /// <summary>
        /// Code of departure location
        /// <br/>Can be a 3-letter IATA airport code, city code, or an eSky multiport code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureCode")]
        public string DepartureCode { get; set; }

        /// <summary>
        /// Code of arrival location
        /// <br/>Can be a 3-letter IATA airport code, city code, or an eSky multiport code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalCode")]
        public string ArrivalCode { get; set; }

        /// <summary>
        /// Passenger configuration
        /// <br/>Default is a single adult configuration
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("passengers")]
        public System.Collections.Generic.ICollection<ISearchFlightsRequestPassenger> Passengers { get; set; }

        /// <summary>
        /// filter by airlines codes
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airlineCodes")]
        public System.Collections.Generic.ICollection<string> AirlineCodes { get; set; }

        /// <summary>
        /// Requested partner code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        /// <summary>
        /// Requested currency code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetAggregatedDestinationPricesQuery
    {

        [System.Text.Json.Serialization.JsonPropertyName("airlineCodes")]
        public System.Collections.Generic.ICollection<string> AirlineCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureDestinationSet")]
        public DestinationSet DepartureDestinationSet { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalDestinationSet")]
        public DestinationSet ArrivalDestinationSet { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureMonthFrom")]
        public DateTime? DepartureMonthFrom { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureMonthTo")]
        public DateTime? DepartureMonthTo { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("languageCode")]
        public string LanguageCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("lengthOfStayCategory")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public LengthOfStayEnum LengthOfStayCategory { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("aggregateToMultiport")]
        public bool AggregateToMultiport { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("showFlightDetails")]
        public bool ShowFlightDetails { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxResults")]
        public int? MaxResults { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("orderType")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public OrderTypeEnum OrderType { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("showDebugInfo")]
        public bool ShowDebugInfo { get; set; }

        /// <summary>
        /// Requested partner code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        /// <summary>
        /// Requested currency code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum DestinationTypeEnum
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Airport")]
        Airport = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"City")]
        City = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"Country")]
        Country = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"Continent")]
        Continent = 3,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetPriceAlertCalendarQuery
    {

        [System.Text.Json.Serialization.JsonPropertyName("isRoundTrip")]
        public bool IsRoundTrip { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureCode")]
        public string DepartureCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureDestinationType")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public DestinationTypeEnum DepartureDestinationType { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalCode")]
        public string ArrivalCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalDestinationType")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public DestinationTypeEnum ArrivalDestinationType { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureYear")]
        public int? DepartureYear { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureMonth")]
        public int? DepartureMonth { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("packageSearch")]
        public bool PackageSearch { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("languageCode")]
        public string LanguageCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("deepLink")]
        public bool? DeepLink { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("priceAmountLimit")]
        public double? PriceAmountLimit { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("selectedDepartureDay")]
        public DateTime? SelectedDepartureDay { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("selectedReturnDepartureMonth")]
        public DateTime? SelectedReturnDepartureMonth { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxFlightsCount")]
        public int? MaxFlightsCount { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxConnectionsCount")]
        public int? MaxConnectionsCount { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("lengthOfStay")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public LengthOfStayEnum LengthOfStay { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("showDebugInfo")]
        public bool ShowDebugInfo { get; set; }

        /// <summary>
        /// Requested partner code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        /// <summary>
        /// Requested currency code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetAtractiveAlternativeFlightsFromCacheQuery
    {
        /// <summary>
        /// Departure date
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public DateTime DepartureDate { get; set; }

        /// <summary>
        /// Departure date of a return flight (for roundtrip queries)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("returnDepartureDate")]
        public DateTime? ReturnDepartureDate { get; set; }

        /// <summary>
        /// Departure airport code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureCode")]
        public string DepartureCode { get; set; }

        /// <summary>
        /// Arrival airport code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalCode")]
        public string ArrivalCode { get; set; }

        /// <summary>
        /// Two-letter ISO language code used for description translation
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("languageCode")]
        public string LanguageCode { get; set; }

        /// <summary>
        /// Highest allowed price amount
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("price")]
        public decimal Price { get; set; }

        /// <summary>
        /// Currency code related to price amount
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        /// <summary>
        /// Partner code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetFlightsById_Query
    {

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flightIds")]
        public System.Collections.Generic.ICollection<string> FlightIds { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("includeNqs")]
        public bool IncludeNqs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("includeLegLocators")]
        public bool IncludeLegLocators { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PaxData
    {

        [System.Text.Json.Serialization.JsonPropertyName("totalPrice")]
        public decimal TotalPrice { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightPrice
    {

        [System.Text.Json.Serialization.JsonPropertyName("currency")]
        public string Currency { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("paxConfigurations")]
        public System.Collections.Generic.IDictionary<string, PaxData> PaxConfigurations { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("refreshDate")]
        public DateTime? RefreshDate { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetFlightsById_Response_Flight
    {

        [System.Text.Json.Serialization.JsonPropertyName("flightId")]
        public string FlightId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flightPrices")]
        public FlightPrice FlightPrices { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("legLocators")]
        public System.Collections.Generic.ICollection<string> LegLocators { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("nqs")]
        public double? Nqs { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetFlightsById_Response
    {

        [System.Text.Json.Serialization.JsonPropertyName("flights")]
        public System.Collections.Generic.ICollection<GetFlightsById_Response_Flight> Flights { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetFlightsByFlightOfferKeyQuery
    {

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flightOfferKeys")]
        public System.Collections.Generic.ICollection<string> FlightOfferKeys { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("includeNqs")]
        public bool IncludeNqs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("includeLegLocators")]
        public bool IncludeLegLocators { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("includeFlightDetails")]
        public bool IncludeFlightDetails { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("passengers")]
        public System.Collections.Generic.ICollection<SearchFlightsRequestPassenger> Passengers { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum FlightOffersForPackagesView_OfferType
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Regular")]
        Regular = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"TourOperator")]
        TourOperator = 1,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightOffersForPackagesView_FlightSegment
    {

        [System.Text.Json.Serialization.JsonPropertyName("departureAirport")]
        public string DepartureAirport { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalAirport")]
        public string ArrivalAirport { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        public DateTime DepartureDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalDate")]
        public DateTime ArrivalDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flightDuration")]
        public string FlightDuration { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flightNumber")]
        public string FlightNumber { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("airlineCode")]
        public string AirlineCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("operatedByAirlineCode")]
        public string OperatedByAirlineCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("offerType")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public FlightOffersForPackagesView_OfferType OfferType { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        public string ProviderCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("lastTicketingDate")]
        public DateTime? LastTicketingDate { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightOffersForPackagesView_FlightLeg
    {

        [System.Text.Json.Serialization.JsonPropertyName("segments")]
        public System.Collections.Generic.ICollection<FlightOffersForPackagesView_FlightSegment> Segments { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightOffersForPackagesView_Flight
    {

        [System.Text.Json.Serialization.JsonPropertyName("key")]
        public string Key { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flightPrices")]
        public System.Collections.Generic.IDictionary<string, FlightPrice> FlightPrices { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureAirport")]
        public string DepartureAirport { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalAirport")]
        public string ArrivalAirport { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        public DateTime DepartureDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalDate")]
        public DateTime ArrivalDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flightDuration")]
        public string FlightDuration { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("returnDepartureDate")]
        public DateTime ReturnDepartureDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("returnArrivalDate")]
        public DateTime ReturnArrivalDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("returnFlightDuration")]
        public string ReturnFlightDuration { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("legLocators")]
        public System.Collections.Generic.ICollection<string> LegLocators { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flightOfferKey")]
        public string FlightOfferKey { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        public int ProviderCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("stops")]
        public int Stops { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("baggageIncluded")]
        public bool BaggageIncluded { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("nqs")]
        public decimal Nqs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("airlineCodes")]
        public System.Collections.Generic.ICollection<string> AirlineCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("legs")]
        public System.Collections.Generic.ICollection<FlightOffersForPackagesView_FlightLeg> Legs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flightNumbers")]
        public System.Collections.Generic.ICollection<string> FlightNumbers { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("returnFlightNumbers")]
        public System.Collections.Generic.ICollection<string> ReturnFlightNumbers { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightOffersForPackagesView
    {

        [System.Text.Json.Serialization.JsonPropertyName("flights")]
        public System.Collections.Generic.ICollection<FlightOffersForPackagesView_Flight> Flights { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightsForPackagesQuery_Route
    {

        [System.Text.Json.Serialization.JsonPropertyName("departureAirport")]
        public string DepartureAirport { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalAirport")]
        public string ArrivalAirport { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum FlightsForPackagesQuery_GroupingMode
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Route")]
        Route = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"DepartureDate")]
        DepartureDate = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"ReturnDate")]
        ReturnDate = 2,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightsForPackagesQuery
    {

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        /// <summary>
        /// Passenger configuration
        /// <br/>Default is a single adult configuration
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("passengers")]
        public System.Collections.Generic.ICollection<ISearchFlightsRequestPassenger> Passengers { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("destinations")]
        public System.Collections.Generic.ICollection<FlightsForPackagesQuery_Route> Destinations { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("minDepartureDate")]
        public DateTime MinDepartureDate { get; set; }

        /// <summary>
        /// Either MaxReturnDate or MaxDepartureDate should be given
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("maxDepartureDate")]
        public DateTime? MaxDepartureDate { get; set; }

        /// <summary>
        /// Either MaxReturnDate or MaxDepartureDate should be given
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("maxReturnDate")]
        public DateTime? MaxReturnDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("minStayLength")]
        public int MinStayLength { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxStayLength")]
        public int MaxStayLength { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("limit")]
        public int? Limit { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureWeekDays")]
        public System.Collections.Generic.ICollection<int> DepartureWeekDays { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("returnWeekDays")]
        public System.Collections.Generic.ICollection<int> ReturnWeekDays { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("airlineCodes")]
        public System.Collections.Generic.ICollection<string> AirlineCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("groupBy")]

        // TODO(system.text.json): Add string enum item converter
        public System.Collections.Generic.ICollection<FlightsForPackagesQuery_GroupingMode> GroupBy { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxStops")]
        public int? MaxStops { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum OfferType
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Regular")]
        Regular = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"TourOperator")]
        TourOperator = 1,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightsForPackagesView_Flight
    {

        [System.Text.Json.Serialization.JsonPropertyName("key")]
        public string Key { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flightIds")]
        public System.Collections.Generic.ICollection<string> FlightIds { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flightOfferKey")]
        public string FlightOfferKey { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flightPrices")]
        public System.Collections.Generic.IDictionary<string, FlightPrice> FlightPrices { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureAirport")]
        public string DepartureAirport { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalAirport")]
        public string ArrivalAirport { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        public DateTime DepartureDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalDate")]
        public DateTime ArrivalDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flightDuration")]
        public string FlightDuration { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("returnDepartureDate")]
        public DateTime ReturnDepartureDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("returnArrivalDate")]
        public DateTime ReturnArrivalDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("returnFlightDuration")]
        public string ReturnFlightDuration { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("price")]
        public decimal Price { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("transactionFee")]
        public decimal TransactionFee { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("nqs")]
        public decimal Nqs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("legLocators")]
        public System.Collections.Generic.ICollection<string> LegLocators { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        public int ProviderCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("stops")]
        public int Stops { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("baggageIncluded")]
        public bool BaggageIncluded { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("offerType")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public OfferType OfferType { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightsForPackagesView
    {

        [System.Text.Json.Serialization.JsonPropertyName("flights")]
        public System.Collections.Generic.ICollection<FlightsForPackagesView_Flight> Flights { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightOffersForPackagesQuery_Route
    {

        [System.Text.Json.Serialization.JsonPropertyName("departureAirport")]
        public string DepartureAirport { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalAirport")]
        public string ArrivalAirport { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class TripLengthDuration
    {

        [System.Text.Json.Serialization.JsonPropertyName("minDepartureDate")]
        public DateOnly MinDepartureDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxDepartureDate")]
        public DateOnly? MaxDepartureDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("minReturnArrivalDate")]
        public DateOnly? MinReturnArrivalDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxReturnArrivalDate")]
        public DateOnly MaxReturnArrivalDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("tripLengthInDays")]
        public System.Collections.Generic.ICollection<int> TripLengthInDays { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("earlyDepartureInHours")]
        public int EarlyDepartureInHours { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("lateReturnArrivalInHours")]
        public int LateReturnArrivalInHours { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class DaysAtDestinationDuration
    {

        [System.Text.Json.Serialization.JsonPropertyName("minArrivalDate")]
        public DateOnly MinArrivalDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxArrivalDate")]
        public DateOnly? MaxArrivalDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("minReturnDepartureDate")]
        public DateOnly? MinReturnDepartureDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxReturnDepartureDate")]
        public DateOnly MaxReturnDepartureDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("daysAtDestination")]
        public System.Collections.Generic.ICollection<int> DaysAtDestination { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightOffersForPackagesQuery
    {

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("destinations")]
        public System.Collections.Generic.ICollection<FlightOffersForPackagesQuery_Route> Destinations { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxStops")]
        public int? MaxStops { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("limitPerDate")]
        public int? LimitPerDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxLegDurationInMinutes")]
        public int? MaxLegDurationInMinutes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("tripLengthDuration")]
        public TripLengthDuration TripLengthDuration { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("daysAtDestinationDuration")]
        public DaysAtDestinationDuration DaysAtDestinationDuration { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureWeekDays")]
        public System.Collections.Generic.ICollection<int> DepartureWeekDays { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("includeWeekDays")]
        public System.Collections.Generic.ICollection<int> IncludeWeekDays { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("airlineCodes")]
        public System.Collections.Generic.ICollection<string> AirlineCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("includeNqs")]
        public bool IncludeNqs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("includeLegLocators")]
        public bool IncludeLegLocators { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightOffersQuery_Route
    {

        [System.Text.Json.Serialization.JsonPropertyName("departureAirport")]
        public string DepartureAirport { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalAirport")]
        public string ArrivalAirport { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightOffersQuery
    {

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("destinations")]
        public System.Collections.Generic.ICollection<FlightOffersQuery_Route> Destinations { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("minCheckIn")]
        public DateOnly MinCheckIn { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxCheckIn")]
        public DateOnly MaxCheckIn { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("stayLengths")]
        public System.Collections.Generic.ICollection<int> StayLengths { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxStops")]
        public int? MaxStops { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("limitPerGroup")]
        public int LimitPerGroup { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxLegDurationInMinutes")]
        public int? MaxLegDurationInMinutes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureWeekDays")]
        public System.Collections.Generic.ICollection<int> DepartureWeekDays { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("airlineCodes")]
        public System.Collections.Generic.ICollection<string> AirlineCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("includeNqs")]
        public bool IncludeNqs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("includeLegLocators")]
        public bool IncludeLegLocators { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("hourBoundaries")]
        public System.Collections.Generic.ICollection<int> HourBoundaries { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class AlternativeFlights_Query
    {

        [System.Text.Json.Serialization.JsonPropertyName("departureAirports")]
        public System.Collections.Generic.ICollection<string> DepartureAirports { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalAirports")]
        public System.Collections.Generic.ICollection<string> ArrivalAirports { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalDate")]
        public DateTime ArrivalDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("returnDate")]
        public DateTime ReturnDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxLegDurationInMinutes")]
        public int? MaxLegDurationInMinutes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxStops")]
        public int? MaxStops { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("passengers")]
        public System.Collections.Generic.ICollection<ISearchFlightsRequestPassenger> Passengers { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class AlternativeOffersQuery
    {

        [System.Text.Json.Serialization.JsonPropertyName("departureAirports")]
        public System.Collections.Generic.ICollection<string> DepartureAirports { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalAirports")]
        public System.Collections.Generic.ICollection<string> ArrivalAirports { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("checkIn")]
        public DateOnly CheckIn { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("stayLength")]
        public int StayLength { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxLegDurationInMinutes")]
        public int? MaxLegDurationInMinutes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxStops")]
        public int? MaxStops { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("passengers")]
        public System.Collections.Generic.ICollection<SearchFlightsRequestPassenger> Passengers { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("includeLegLocators")]
        public bool? IncludeLegLocators { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("includeFlightDetails")]
        public bool? IncludeFlightDetails { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("limit")]
        public int? Limit { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class DynamicPackages_Request_Route
    {

        [System.Text.Json.Serialization.JsonPropertyName("departure")]
        public string Departure { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrival")]
        public string Arrival { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class DynamicPackages_Request
    {

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("minCheckIn")]
        public DateOnly MinCheckIn { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxCheckIn")]
        public DateOnly MaxCheckIn { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("routes")]
        public System.Collections.Generic.ICollection<DynamicPackages_Request_Route> Routes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("stayLengths")]
        public System.Collections.Generic.ICollection<int> StayLengths { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxStops")]
        public int MaxStops { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("limitPerArrivalAirport")]
        public int LimitPerArrivalAirport { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("passengers")]
        public System.Collections.Generic.ICollection<SearchFlightsRequestPassenger> Passengers { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class DynamicPackages_Response_Price
    {

        [System.Text.Json.Serialization.JsonPropertyName("totalPrice")]
        public decimal TotalPrice { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("currency")]
        public string Currency { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class DynamicPackages_Response_Flight
    {

        [System.Text.Json.Serialization.JsonPropertyName("flightOfferKey")]
        public string FlightOfferKey { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureAirport")]
        public string DepartureAirport { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalAirport")]
        public string ArrivalAirport { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        public DateTime DepartureDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalDate")]
        public DateTime ArrivalDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("returnDepartureDate")]
        public DateTime ReturnDepartureDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("returnArrivalDate")]
        public DateTime ReturnArrivalDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        public int ProviderCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("stops")]
        public int Stops { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("baggageIncluded")]
        public bool BaggageIncluded { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flightPrices")]
        public System.Collections.Generic.IDictionary<string, DynamicPackages_Response_Price> FlightPrices { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class DynamicPackages_Response
    {

        [System.Text.Json.Serialization.JsonPropertyName("flights")]
        public System.Collections.Generic.ICollection<DynamicPackages_Response_Flight> Flights { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightsForTravelExplorerQuery
    {

        [System.Text.Json.Serialization.JsonPropertyName("keys")]
        public System.Collections.Generic.ICollection<string> Keys { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("languageCode")]
        public string LanguageCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("limit")]
        public int Limit { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightsForTravelExplorerView_Segment
    {

        [System.Text.Json.Serialization.JsonPropertyName("airlineCode")]
        public string AirlineCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureAirport")]
        public string DepartureAirport { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        public DateTime DepartureDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalAirport")]
        public string ArrivalAirport { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalDate")]
        public DateTime ArrivalDate { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightsForTravelExplorerView_Leg
    {

        [System.Text.Json.Serialization.JsonPropertyName("segments")]
        public System.Collections.Generic.ICollection<FlightsForTravelExplorerView_Segment> Segments { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flightDuration")]
        public string FlightDuration { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("legLocator")]
        public string LegLocator { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightsForTravelExplorerView_Flight
    {

        [System.Text.Json.Serialization.JsonPropertyName("flightOfferKey")]
        public string FlightOfferKey { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("price")]
        public decimal Price { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("transactionFee")]
        public decimal TransactionFee { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("legs")]
        public System.Collections.Generic.ICollection<FlightsForTravelExplorerView_Leg> Legs { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightsForTravelExplorerView
    {

        [System.Text.Json.Serialization.JsonPropertyName("flights")]
        public System.Collections.Generic.ICollection<FlightsForTravelExplorerView_Flight> Flights { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("airlineNames")]
        public System.Collections.Generic.IDictionary<string, string> AirlineNames { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetByFlightOfferKey_Query
    {

        [System.Text.Json.Serialization.JsonPropertyName("flightOfferKey")]
        public string FlightOfferKey { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("paxConfiguration")]
        public string PaxConfiguration { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }



    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ApiException : System.Exception
    {
        public int StatusCode { get; private set; }

        public string Response { get; private set; }

        public System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> Headers { get; private set; }

        public ApiException(string message, int statusCode, string response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Exception innerException)
            : base(message + "\n\nStatus: " + statusCode + "\nResponse: \n" + ((response == null) ? "(null)" : response.Substring(0, response.Length >= 512 ? 512 : response.Length)), innerException)
        {
            StatusCode = statusCode;
            Response = response;
            Headers = headers;
        }

        public override string ToString()
        {
            return string.Format("HTTP Response: \n\n{0}\n\n{1}", Response, base.ToString());
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ApiException<TResult> : ApiException
    {
        public TResult Result { get; private set; }

        public ApiException(string message, int statusCode, string response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, TResult result, System.Exception innerException)
            : base(message, statusCode, response, headers, innerException)
        {
            Result = result;
        }
    }

}

#pragma warning restore  108
#pragma warning restore  114
#pragma warning restore  472
#pragma warning restore  612
#pragma warning restore 1573
#pragma warning restore 1591
#pragma warning restore 8073
#pragma warning restore 3016
#pragma warning restore 8603
#pragma warning restore 8604
#pragma warning restore 8625