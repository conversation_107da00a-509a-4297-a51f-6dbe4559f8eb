using Esky.Packages.Application.Abstractions.Gateways.FlightGateway;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Types;
using Esky.Packages.Infrastructure.Gateways.FlightCacheGateway.Proxies.FlightSearch;
using Esky.Packages.Infrastructure.HttpClients.Constants;

namespace Esky.Packages.Infrastructure.Gateways.FlightCacheGateway;

public class FlightCacheGateway : IFlightGateway
{
    private readonly FlightSearchClient _flightClient;

    public FlightCacheGateway(IHttpClientFactory httpClientFactory)
    {
        var httpClient = httpClientFactory.CreateClient(ApiHttpClientConsts.FlightCacheClient);

        _flightClient = new FlightSearchClient(httpClient);
    }
    
    public async Task<IEnumerable<FlightOffer>> GetFlightsByOfferKeyAsync(
        FlightByOfferKeySearchCriteria criteria, CancellationToken cancellationToken)
    {
        var request = MapRequest(criteria);
        var response = await _flightClient.GetFlightsByFlightOfferKeyAsync(request, cancellationToken);
    
        return response
            .Flights
            .Select(MapFlightOffer);
    }

    public async Task<IEnumerable<FlightOffer>> GetFlightOffers(FlightSearchCriteria criteria, 
        CancellationToken cancellationToken)
    {
        var request = MapRequest(criteria);
        var response = await _flightClient.GetFlightOffers2Async(request, cancellationToken);

        return response
            .Flights
            .Select(MapFlightOffer);
    }

    public async Task<IEnumerable<FlightOffer>> GetAlternativeFlightOffers(
        AlternativeFlightOffersCriteria criteria, CancellationToken cancellationToken)
    {
        var request = MapRequest(criteria);
        var response = await _flightClient.GetAlternativeFlights2Async(request, cancellationToken);

        return response
            .Flights
            .Select(MapFlightOffer);
    }

    private static FlightOffersQuery MapRequest(FlightSearchCriteria criteria)
    {
        return new FlightOffersQuery
        {
            PartnerCode = criteria.PartnerCode,
            Destinations = MapDestinations(criteria).ToArray(),
            MaxStops = criteria.MaxStop,
            LimitPerGroup = criteria.EnableInboundOutboundDepartureHours ? 2 : 5,
            MaxLegDurationInMinutes = (int?)criteria.MaxLegDuration?.TotalMinutes,
            MinCheckIn = criteria.CheckIn,
            MaxCheckIn = criteria.CheckIn,
            StayLengths = criteria.StayLengths,
            IncludeNqs = false,
            IncludeLegLocators = false,
            HourBoundaries = criteria.EnableInboundOutboundDepartureHours ? (ICollection<int>)TimeOfDay.HourBoundaries : null,
        };
    }

    private static GetFlightsByFlightOfferKeyQuery MapRequest(FlightByOfferKeySearchCriteria criteria)
    {
        return new GetFlightsByFlightOfferKeyQuery
        {
            PartnerCode = criteria.PartnerCode,
            Passengers = MapOccupancy(criteria.Occupancy).ToArray(),
            FlightOfferKeys = criteria.FlightOfferKeys,
            IncludeNqs = false,
            IncludeLegLocators = criteria.IncludeLegLocators,
            IncludeFlightDetails = criteria.IncludeFlightDetails
        };
    }

    private static IEnumerable<FlightOffersQuery_Route> MapDestinations(FlightSearchCriteria criteria)
    {
        return criteria
            .Destinations
            .Select(d => new FlightOffersQuery_Route
            {
                DepartureAirport = d.DepartureAirport,
                ArrivalAirport = d.ArrivalAirport
            });
    }

    private static AlternativeOffersQuery MapRequest(AlternativeFlightOffersCriteria criteria)
    {
        return new AlternativeOffersQuery 
        {
            DepartureAirports = criteria.DepartureAirports,
            ArrivalAirports = criteria.ArrivalAirports,
            CheckIn = criteria.CheckIn,
            StayLength = criteria.StayLength,
            MaxLegDurationInMinutes = (int?)criteria.MaxLegDuration?.TotalMinutes,
            MaxStops = criteria.MaxStops,
            PartnerCode = criteria.PartnerCode,
            Passengers = MapOccupancy(criteria.Occupancy).ToArray(),
            IncludeLegLocators = criteria.IncludeLegLocators,
            Limit = criteria.Limit
        };
    }

    private static IEnumerable<SearchFlightsRequestPassenger> MapOccupancy(Occupancy occupancy)
    {
        var passengers = FlightPassengers.FromOccupancy(occupancy);

        if (passengers.Adults > 0)
        {
            yield return new SearchFlightsRequestPassenger
            {
                Code = PersonTypeEnum.Adult,
                Count = passengers.Adults,
            };
        }

        if (passengers.Youths > 0)
        {
            yield return new SearchFlightsRequestPassenger
            {
                Code = PersonTypeEnum.Youth,
                Count = passengers.Youths
            };
        }

        if (passengers.Children > 0)
        {
            yield return new SearchFlightsRequestPassenger
            {
                Code = PersonTypeEnum.Child,
                Count = passengers.Children
            };
        }

        if (passengers.Infants > 0)
        {
            yield return new SearchFlightsRequestPassenger
            {
                Code = PersonTypeEnum.Infant,
                Count = passengers.Infants
            };
        }
    }

    private static FlightOffer MapFlightOffer(FlightOffersForPackagesView_Flight dto)
    {
        return new FlightOffer {
            Id = dto.FlightOfferKey,
            DepartureAirport = dto.DepartureAirport,
            ArrivalAirport = dto.ArrivalAirport,
            DepartureDate = dto.DepartureDate,
            ArrivalDate = dto.ArrivalDate,
            ReturnDepartureDate = dto.ReturnDepartureDate,
            ReturnArrivalDate = dto.ReturnArrivalDate,
            ProviderCode = dto.ProviderCode,
            Stops = dto.Stops,
            FlightIds = dto.FlightPrices.Select(x => x.Key).ToArray(),
            AirlineCodes = dto.AirlineCodes.ToArray(),
            FlightNumbers = new FlightNumbers(dto.FlightNumbers.ToArray()),
            ReturnFlightNumbers = new FlightNumbers(dto.ReturnFlightNumbers.ToArray()),
            LegLocators = dto.LegLocators?.ToArray(),
            Prices = dto.FlightPrices.ToDictionary(flightPrice => flightPrice.Key, flightPrice => MapFlightPrices(flightPrice.Value)),
            BaggageIncluded = dto.BaggageIncluded
        };
    }

    private static FlightPrices MapFlightPrices(FlightPrice dto)
    {
        return new FlightPrices
        {
            Prices = MapPrices(dto),
            Currency = dto.Currency,
            // fcache confirmed that it's always set (it still nullable for backward compatibility)
            UpdatedAt = dto.RefreshDate ?? DateTime.UtcNow
        };
    }

    private static Dictionary<FlightOccupancy, decimal> MapPrices(FlightPrice dto)
    {
        return dto.PaxConfigurations
            .Where(x => x.Value is not null)
            .ToDictionary(
                flightPrice => ParsePaxConfiguration(flightPrice.Key),
                flightPrice => flightPrice.Value.TotalPrice);
    }

    private static FlightOccupancy ParsePaxConfiguration(string paxConfig)
    {
        var flightPassengers = FlightPassengers.FromPaxConfiguration(paxConfig);
        
        return new FlightOccupancy(
            Adults: flightPassengers.Adults,
            Youths: flightPassengers.Youths,
            Children: flightPassengers.Children,
            Infants: flightPassengers.Infants);
    }
}