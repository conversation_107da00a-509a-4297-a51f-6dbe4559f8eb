{"openapi": "3.0.4", "info": {"title": "API 1.0", "version": "1.0"}, "paths": {"/api/v{version}/Offer/Details": {"get": {"tags": ["Offer"], "parameters": [{"name": "offerId", "in": "query", "schema": {"type": "string"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.OfferIdDetails"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}}}, "/api/v{version}/PriceCheck": {"post": {"tags": ["PriceCheck"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Request.HotelPriceCheckParameters"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Request.HotelPriceCheckParameters"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Request.HotelPriceCheckParameters"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.PriceCheck.HotelPriceCheckResult"}}}}}, "deprecated": true}}, "/api/v{version}/PriceCheck/offer": {"post": {"tags": ["PriceCheck"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Request.HotelPriceCheckOfferParameters"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Request.HotelPriceCheckOfferParameters"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Request.HotelPriceCheckOfferParameters"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.PriceCheck.HotelPriceCheckOfferResult"}}}}}}}, "/api/v{version}/Pricing": {"post": {"tags": ["Pricing"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "esky-hotel-pricing-contextname", "in": "header", "schema": {"type": "String"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Pricing.HotelPriceParameters"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Pricing.HotelPriceParameters"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Pricing.HotelPriceParameters"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Pricing.HotelPriceResult"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "410": {"description": "Gone", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "422": {"description": "Unprocessable Content", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/api/v{version}/Pricing/Offer": {"post": {"tags": ["Pricing"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Pricing.PriceOfferParameters"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Pricing.PriceOfferParameters"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Pricing.PriceOfferParameters"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Pricing.PriceOfferResult"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "410": {"description": "Gone", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "422": {"description": "Unprocessable Content", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/v{version}/Repricing/Hotel": {"post": {"tags": ["Repricing"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Repricing.HotelRepriceParameters"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Repricing.HotelRepriceParameters"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Repricing.HotelRepriceParameters"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Repricing.HotelRepriceResult"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/v{version}/Reservation": {"post": {"tags": ["Reservation"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Request.HotelBookParameters"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Request.HotelBookParameters"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Request.HotelBookParameters"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.HotelBook.HotelBookResult"}}}}, "500": {"description": "Internal Server Error"}}}, "get": {"tags": ["Reservation"], "parameters": [{"name": "ProviderBookingId", "in": "query", "schema": {"type": "string"}}, {"name": "ConfirmationId", "in": "query", "schema": {"type": "string"}}, {"name": "ProviderCode", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PartnerCode", "in": "query", "schema": {"type": "string"}}, {"name": "Locale", "in": "query", "schema": {"type": "string"}}, {"name": "ProviderConfigurationId", "in": "query", "description": "If provided, it will override the ProviderConfigurationId read from the used provider.", "schema": {"type": "string"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.GetReservation.GetReservationResult"}}}}}}, "delete": {"tags": ["Reservation"], "parameters": [{"name": "ProviderBookingId", "in": "query", "schema": {"type": "string"}}, {"name": "ConfirmationId", "in": "query", "schema": {"type": "string"}}, {"name": "ProviderCode", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PartnerCode", "in": "query", "schema": {"type": "string"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.CancelReservation.CancelReservationResult"}}}}}}}}, "components": {"schemas": {"Esky.Hotels.ApiContract.BuyoutType": {"enum": ["<PERSON><PERSON>", "Client"], "type": "string"}, "Esky.Hotels.ApiContract.Pricing.HotelPriceParameters": {"type": "object", "properties": {"metaCode": {"type": "integer", "format": "int32"}, "providerCode": {"type": "integer", "format": "int32"}, "packageId": {"type": "string", "nullable": true}, "userInformation": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.UserInformation"}, "searchContext": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.SearchContext"}, "currency": {"type": "string", "nullable": true}, "language": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Pricing.HotelPriceResult": {"type": "object", "properties": {"offer": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Pricing.Offer"}, "context": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.Context"}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Pricing.Offer": {"type": "object", "properties": {"packageId": {"type": "string", "description": "Provider-specific package identifier (raw format, no encoding)", "nullable": true}, "metaCode": {"type": "integer", "format": "int32"}, "qualifiedHotelCode": {"type": "integer", "format": "int32"}, "internalPriceDetails": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.InternalPriceDetailsWithBreakdown"}, "priceDetails": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.PriceDetailsWithBreakdown"}, "rooms": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Pricing.Offer.Room"}, "nullable": true}, "providerCode": {"type": "integer", "format": "int32"}, "providerConfigurationId": {"type": "string", "nullable": true}, "mealPlan": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.MealPlan"}, "buyoutType": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.BuyoutType"}, "clientCreditCardRequired": {"type": "boolean"}, "depositRequired": {"type": "boolean"}, "cancellationInfo": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.CancellationInfo"}, "policies": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.Information"}, "nullable": true}, "importantInformation": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.Information"}, "nullable": true}, "providerHotel": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.ProviderHotel"}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Pricing.Offer.Room": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "bedConfigurations": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.BedConfiguration"}, "nullable": true}, "roomConfiguration": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.RoomConfiguration"}, "photos": {"type": "array", "items": {"type": "object", "properties": {"Thumbnail": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.Photo"}, "Medium": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.Photo"}, "Large": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.Photo"}}, "additionalProperties": false}, "nullable": true}, "facilities": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Pricing.PriceOfferParameters": {"type": "object", "properties": {"offerId": {"type": "string", "nullable": true}, "settingsKey": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.SettingsKey"}, "userInformation": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.UserInformation"}, "currency": {"type": "string", "nullable": true}, "language": {"type": "string", "nullable": true}, "hotelOfferId": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Pricing.PriceOfferResult": {"type": "object", "properties": {"offers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Pricing.Offer"}, "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Repricing.HotelRepriceParameters": {"type": "object", "properties": {"hotelMetaCode": {"type": "integer", "format": "int32"}, "partnerCode": {"type": "string", "nullable": true}, "stayInformation": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.StayInformation"}, "occupancy": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.RoomConfiguration"}, "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Repricing.HotelRepriceResult": {"type": "object", "properties": {"metaCode": {"type": "integer", "format": "int32"}, "variants": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Repricing.HotelRepriceResult.Variant"}, "nullable": true}, "groupedVariants": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Repricing.HotelRepriceResult.Variant"}}}, "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Repricing.HotelRepriceResult.Variant": {"type": "object", "properties": {"offerId": {"type": "string", "nullable": true}, "metaRoomIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "mealPlan": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.MealPlan"}, "pricing": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Repricing.HotelRepriceResult.VariantPricing"}, "refundability": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.Refundability"}, "paymentType": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.PaymentType"}, "receivedAt": {"type": "string", "format": "date-time"}, "availability": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Repricing.HotelRepriceResult.VariantPricing": {"type": "object", "properties": {"providerNetPrice": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.Price"}, "summaryToPayAtHotel": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.Price"}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Request.Booking.Booker": {"type": "object", "properties": {"phone": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Request.Booking.Phone"}, "email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Request.Booking.BookingRoom": {"type": "object", "properties": {"roomId": {"type": "string", "nullable": true}, "specialRequests": {"type": "string", "nullable": true}, "smoking": {"type": "boolean"}, "bedType": {"type": "string", "nullable": true}, "guests": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Request.Booking.Guest"}, "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Request.Booking.CreditCard": {"type": "object", "properties": {"cardId": {"type": "string", "format": "uuid"}, "cardType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Request.Booking.Guest": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Request.Booking.Phone": {"type": "object", "properties": {"countryCode": {"type": "string", "nullable": true}, "areaCode": {"type": "string", "nullable": true}, "number": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Request.HotelBookParameters": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "booker": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Request.Booking.Booker"}, "creditCard": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Request.Booking.CreditCard"}, "bookingRooms": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Request.Booking.BookingRoom"}, "nullable": true}, "userInformation": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.UserInformation"}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Request.HotelPriceCheckOfferParameters": {"type": "object", "properties": {"metaCode": {"type": "integer", "format": "int32"}, "providerCode": {"type": "integer", "format": "int32"}, "packageId": {"type": "string", "nullable": true}, "bedConfigurationCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "userInformation": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.UserInformation"}, "searchContext": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.SearchContext"}, "currency": {"type": "string", "nullable": true}, "language": {"type": "string", "nullable": true}, "hotelOfferId": {"type": "string", "nullable": true}, "providerConfigurationId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Request.HotelPriceCheckParameters": {"type": "object", "properties": {"metaCode": {"type": "integer", "format": "int32"}, "providerCode": {"type": "integer", "format": "int32"}, "packageId": {"type": "string", "description": "Provider-specific package identifier (raw format, no encoding)", "nullable": true}, "bedConfigurationCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "userInformation": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.UserInformation"}, "searchContext": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.SearchContext"}, "currency": {"type": "string", "nullable": true}, "language": {"type": "string", "nullable": true}, "providerConfigurationId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.BedConfiguration": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "bedTypes": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.BedType"}, "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.BedType": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.CancelReservation.CancelError": {"type": "object", "properties": {"errorCode": {"type": "integer", "format": "int32"}, "errorMesage": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.CancelReservation.CancelReservationResult": {"type": "object", "properties": {"reservationCancelled": {"type": "boolean"}, "cancelError": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.CancelReservation.CancelError"}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.CancellationInfo": {"type": "object", "properties": {"cancellationDetails": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.CancellationInfoDetails"}, "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.CancellationInfoDetails": {"type": "object", "properties": {"until": {"type": "string", "description": "Last day for cancellation for a specified price", "format": "date"}, "price": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.Price"}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.Context": {"type": "object", "properties": {"partnerCode": {"type": "string", "nullable": true}, "contextsApplied": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.Context"}, "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.Coordinates": {"type": "object", "properties": {"longitude": {"type": "number", "format": "double"}, "latitude": {"type": "number", "format": "double"}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.GetReservation.GetReservationResult": {"type": "object", "properties": {"metaCode": {"type": "integer", "format": "int32", "nullable": true}, "stayInformation": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.StayInformation"}, "reservationStatus": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.GetReservation.ReservationStatus"}, "reservationDate": {"type": "string", "format": "date-time"}, "hotel": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.ProviderHotel"}, "rooms": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.GetReservation.ReservationRoom"}, "nullable": true}, "cancellationInfo": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.CancellationInfo"}, "internalPriceDetails": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.InternalPriceDetailsWithBreakdown"}, "priceDetails": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.PriceDetailsWithBreakdown"}, "buyoutType": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.BuyoutType"}, "information": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.Information"}, "nullable": true}, "providerInfo": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.GetReservation.ProviderInfo"}, "paymentType": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.PaymentType"}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.GetReservation.Guest": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.GetReservation.ProviderInfo": {"type": "object", "properties": {"providerCode": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.ProviderCode"}, "supplierName": {"type": "string", "nullable": true}, "supplierOrderReference": {"type": "string", "nullable": true}, "optimizer": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.GetReservation.ReservationRoom": {"type": "object", "properties": {"roomName": {"type": "string", "nullable": true}, "roomId": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "bedType": {"type": "string", "nullable": true}, "specialRequests": {"type": "string", "nullable": true}, "smoking": {"type": "boolean"}, "facilities": {"type": "array", "items": {"type": "string"}, "nullable": true}, "roomConfiguration": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.RoomConfiguration"}, "mealPlan": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.MealPlan"}, "guests": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.GetReservation.Guest"}, "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.GetReservation.ReservationStatus": {"enum": ["Unknown", "Created", "Cancelled", "NoShow", "Stayed"], "type": "string"}, "Esky.Hotels.ApiContract.Response.HotelAddress": {"type": "object", "properties": {"street": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "countryCode": {"type": "string", "nullable": true}, "coordinates": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.Coordinates"}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.HotelBook.HotelBookResult": {"type": "object", "properties": {"providerBookingId": {"type": "string", "nullable": true}, "confirmationId": {"type": "string", "nullable": true}, "providerCode": {"type": "integer", "format": "int32"}, "providerConfigurationId": {"type": "string", "nullable": true}, "context": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.Context"}, "isTest": {"type": "boolean"}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.Information": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "text": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.InternalPriceDetailsWithBreakdown": {"type": "object", "properties": {"providerIncome": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.Price"}, "providerPriceDetails": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.ProviderPriceDetails"}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.OfferIdDetails": {"type": "object", "properties": {"metaCode": {"type": "integer", "format": "int32"}, "stayInformation": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.StayInformation"}, "partnerCode": {"type": "string", "nullable": true}, "providerCode": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.ProviderCode"}, "occupancy": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.RoomConfiguration"}, "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.PriceBreakdown": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "includedInNet": {"type": "boolean"}, "price": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.Price"}, "code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.PriceCheck.HotelPriceCheckOfferResult": {"type": "object", "properties": {"offers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.PriceCheck.HotelPriceCheckResult"}, "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.PriceCheck.HotelPriceCheckResult": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "internalPriceDetails": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.InternalPriceDetailsWithBreakdown"}, "priceDetails": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.PriceDetailsWithBreakdown"}, "rooms": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.PriceCheck.PriceCheckRoom"}, "nullable": true}, "providerCode": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.ProviderCode"}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.PriceCheck.PriceCheckRoom": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "bedConfigurationCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.PriceDetails": {"type": "object", "properties": {"currency": {"type": "string", "nullable": true}, "netPrice": {"type": "number", "format": "decimal"}, "taxPrice": {"type": "number", "format": "decimal"}, "extraCharges": {"type": "number", "format": "decimal"}, "providerNetPrice": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.Price"}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.PriceDetailsWithBreakdown": {"type": "object", "properties": {"priceDetails": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.PriceDetails"}, "summaryToPayNow": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.Price"}, "summaryToPayAtHotel": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.Price"}, "summaryToPayAtHotelInClientCurrency": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.Price"}, "taxesBreakdown": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.PriceBreakdown"}, "nullable": true}, "extraChargesBreakdown": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.PriceBreakdown"}, "nullable": true}, "chargesBreakdown": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.PriceBreakdown"}, "nullable": true}, "salesMargin": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.Price"}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.ProviderHotel": {"type": "object", "properties": {"originalHotelCode": {"type": "string", "nullable": true}, "providerCode": {"type": "integer", "format": "int32"}, "hotelName": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "address": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.Response.HotelAddress"}, "countryCode": {"type": "string", "nullable": true}, "webPage": {"type": "string", "nullable": true}, "stars": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.ProviderPriceDetails": {"type": "object", "properties": {"currency": {"type": "string", "nullable": true}, "netPrice": {"type": "number", "format": "decimal"}, "taxPrice": {"type": "number", "format": "decimal"}, "providerIncome": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.Price"}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.Response.Refundability": {"type": "object", "properties": {"isRefundable": {"type": "boolean"}, "freeRefundUntil": {"type": "string", "format": "date", "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.SearchContext": {"type": "object", "properties": {"stayInformation": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.StayInformation"}, "settingsKey": {"$ref": "#/components/schemas/Esky.Hotels.ApiContract.SettingsKey"}, "roomsConfiguration": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.Hotels.Contract.Common.RoomConfiguration"}, "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.SettingsKey": {"type": "object", "properties": {"partnerCode": {"type": "string", "nullable": true}, "flightBookingId": {"type": "string", "nullable": true}, "mobileDevice": {"type": "boolean", "nullable": true}, "userId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.ApiContract.UserInformation": {"type": "object", "properties": {"userAgent": {"type": "string", "nullable": true}, "userIp": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.Contract.Common.Context": {"enum": ["CROSSSELLING", "MOBILE", "MEMBER"], "type": "string"}, "Esky.Hotels.Contract.Common.MealPlan": {"enum": ["Unknown", "None", "Breakfast", "HalfBoard", "FullBoard", "AllInclusive"], "type": "string"}, "Esky.Hotels.Contract.Common.PaymentType": {"enum": ["Unknown", "OnSite", "AtCheckout", "Installments"], "type": "string"}, "Esky.Hotels.Contract.Common.Photo": {"type": "object", "properties": {"src": {"type": "string", "nullable": true}, "width": {"type": "integer", "format": "int32"}, "height": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Esky.Hotels.Contract.Common.Price": {"type": "object", "properties": {"amount": {"type": "number", "format": "decimal"}, "currency": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.Contract.Common.ProviderCode": {"enum": ["BookingCom", "Expedia", "HotelBeds", "Travolutionary", "TestHouseExpress", "<PERSON><PERSON><PERSON>", "RateHawk", "WebBeds", "OTS", "Alturabeds", "Yalago", "TravelgateDodo", "TravelgateW2M", "TravelgateB4G"], "type": "string"}, "Esky.Hotels.Contract.Common.RoomConfiguration": {"type": "object", "properties": {"adults": {"type": "integer", "format": "int32"}, "childrenAges": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "Esky.Hotels.Contract.Common.StayInformation": {"type": "object", "properties": {"checkInDate": {"type": "string", "format": "date-time"}, "checkOutDate": {"type": "string", "format": "date-time"}, "nights": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "Microsoft.AspNetCore.Mvc.ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}}}}