//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

#pragma warning disable 108 // Disable "CS0108 '{derivedDto}.To<PERSON><PERSON>()' hides inherited member '{dtoBase}.To<PERSON>son()'. Use the new keyword if hiding was intended."
#pragma warning disable 114 // Disable "CS0114 '{derivedDto}.RaisePropertyChanged(String)' hides inherited member 'dtoBase.RaisePropertyChanged(String)'. To make the current member override that implementation, add the override keyword. Otherwise add the new keyword."
#pragma warning disable 472 // Disable "CS0472 The result of the expression is always 'false' since a value of type 'Int32' is never equal to 'null' of type 'Int32?'
#pragma warning disable 612 // Disable "CS0612 '...' is obsolete"
#pragma warning disable 649 // Disable "CS0649 Field is never assigned to, and will always have its default value null"
#pragma warning disable 1573 // Disable "CS1573 Parameter '...' has no matching param tag in the XML comment for ...
#pragma warning disable 1591 // Disable "CS1591 Missing XML comment for publicly visible type or member ..."
#pragma warning disable 8073 // Disable "CS8073 The result of the expression is always 'false' since a value of type 'T' is never equal to 'null' of type 'T?'"
#pragma warning disable 3016 // Disable "CS3016 Arrays as attribute arguments is not CLS-compliant"
#pragma warning disable 8600 // Disable "CS8600 Converting null literal or possible null value to non-nullable type"
#pragma warning disable 8602 // Disable "CS8602 Dereference of a possibly null reference"
#pragma warning disable 8603 // Disable "CS8603 Possible null reference return"
#pragma warning disable 8604 // Disable "CS8604 Possible null reference argument for parameter"
#pragma warning disable 8625 // Disable "CS8625 Cannot convert null literal to non-nullable reference type"
#pragma warning disable 8765 // Disable "CS8765 Nullability of type of parameter doesn't match overridden member (possibly because of nullability attributes)."

namespace Infrastructure.Gateways.HotelTransactionGateway.HotelTransaction
{
    using System = global::System;

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelTransactionClient 
    {
        private System.Net.Http.HttpClient _httpClient;
        private static System.Lazy<System.Text.Json.JsonSerializerOptions> _settings = new System.Lazy<System.Text.Json.JsonSerializerOptions>(CreateSerializerSettings, true);
        private System.Text.Json.JsonSerializerOptions _instanceSettings;

    #pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public HotelTransactionClient(System.Net.Http.HttpClient httpClient)
    #pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        {
            _httpClient = httpClient;
            Initialize();
        }

        private static System.Text.Json.JsonSerializerOptions CreateSerializerSettings()
        {
            var settings = new System.Text.Json.JsonSerializerOptions();
            UpdateJsonSerializerSettings(settings);
            return settings;
        }

        protected System.Text.Json.JsonSerializerOptions JsonSerializerSettings { get { return _instanceSettings ?? _settings.Value; } }

        static partial void UpdateJsonSerializerSettings(System.Text.Json.JsonSerializerOptions settings);

        partial void Initialize();

        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);

        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<OfferIdDetails> DetailsAsync(string offerId, string version)
        {
            return DetailsAsync(offerId, version, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<OfferIdDetails> DetailsAsync(string offerId, string version, System.Threading.CancellationToken cancellationToken)
        {
            if (version == null)
                throw new System.ArgumentNullException("version");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v{version}/Offer/Details"
                    urlBuilder_.Append("api/v");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(version, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/Offer/Details");
                    urlBuilder_.Append('?');
                    if (offerId != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("offerId")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(offerId, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    urlBuilder_.Length--;

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<OfferIdDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        if (status_ == 400)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Bad Request", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await ReadAsStringAsync(response_.Content, cancellationToken).ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        [System.Obsolete]
        public virtual System.Threading.Tasks.Task<HotelPriceCheckResult> PriceCheckAsync(string version, HotelPriceCheckParameters body)
        {
            return PriceCheckAsync(version, body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        [System.Obsolete]
        public virtual async System.Threading.Tasks.Task<HotelPriceCheckResult> PriceCheckAsync(string version, HotelPriceCheckParameters body, System.Threading.CancellationToken cancellationToken)
        {
            if (version == null)
                throw new System.ArgumentNullException("version");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v{version}/PriceCheck"
                    urlBuilder_.Append("api/v");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(version, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/PriceCheck");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<HotelPriceCheckResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await ReadAsStringAsync(response_.Content, cancellationToken).ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<HotelPriceCheckOfferResult> OfferAsync(string version, HotelPriceCheckOfferParameters body)
        {
            return OfferAsync(version, body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<HotelPriceCheckOfferResult> OfferAsync(string version, HotelPriceCheckOfferParameters body, System.Threading.CancellationToken cancellationToken)
        {
            if (version == null)
                throw new System.ArgumentNullException("version");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v{version}/PriceCheck/offer"
                    urlBuilder_.Append("api/v");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(version, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/PriceCheck/offer");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<HotelPriceCheckOfferResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await ReadAsStringAsync(response_.Content, cancellationToken).ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        [System.Obsolete]
        public virtual System.Threading.Tasks.Task<HotelPriceResult> PricingAsync(string version, object esky_hotel_pricing_contextname, HotelPriceParameters body)
        {
            return PricingAsync(version, esky_hotel_pricing_contextname, body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        [System.Obsolete]
        public virtual async System.Threading.Tasks.Task<HotelPriceResult> PricingAsync(string version, object esky_hotel_pricing_contextname, HotelPriceParameters body, System.Threading.CancellationToken cancellationToken)
        {
            if (version == null)
                throw new System.ArgumentNullException("version");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {

                    if (esky_hotel_pricing_contextname != null)
                        request_.Headers.TryAddWithoutValidation("esky-hotel-pricing-contextname", ConvertToString(esky_hotel_pricing_contextname, System.Globalization.CultureInfo.InvariantCulture));
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v{version}/Pricing"
                    urlBuilder_.Append("api/v");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(version, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/Pricing");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<HotelPriceResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        if (status_ == 400)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Bad Request", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        if (status_ == 404)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Not Found", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        if (status_ == 410)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Gone", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        if (status_ == 422)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Unprocessable Content", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        if (status_ == 500)
                        {
                            string responseText_ = ( response_.Content == null ) ? string.Empty : await ReadAsStringAsync(response_.Content, cancellationToken).ConfigureAwait(false);
                            throw new ApiException("Internal Server Error", status_, responseText_, headers_, null);
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await ReadAsStringAsync(response_.Content, cancellationToken).ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<PriceOfferResult> OfferAsync(string version, PriceOfferParameters body)
        {
            return OfferAsync(version, body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<PriceOfferResult> OfferAsync(string version, PriceOfferParameters body, System.Threading.CancellationToken cancellationToken)
        {
            if (version == null)
                throw new System.ArgumentNullException("version");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v{version}/Pricing/Offer"
                    urlBuilder_.Append("api/v");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(version, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/Pricing/Offer");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<PriceOfferResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        if (status_ == 400)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Bad Request", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        if (status_ == 404)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Not Found", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        if (status_ == 410)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Gone", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        if (status_ == 422)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Unprocessable Content", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        if (status_ == 500)
                        {
                            string responseText_ = ( response_.Content == null ) ? string.Empty : await ReadAsStringAsync(response_.Content, cancellationToken).ConfigureAwait(false);
                            throw new ApiException("Internal Server Error", status_, responseText_, headers_, null);
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await ReadAsStringAsync(response_.Content, cancellationToken).ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<HotelRepriceResult> HotelAsync(string version, HotelRepriceParameters body)
        {
            return HotelAsync(version, body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<HotelRepriceResult> HotelAsync(string version, HotelRepriceParameters body, System.Threading.CancellationToken cancellationToken)
        {
            if (version == null)
                throw new System.ArgumentNullException("version");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v{version}/Repricing/Hotel"
                    urlBuilder_.Append("api/v");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(version, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/Repricing/Hotel");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<HotelRepriceResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        if (status_ == 400)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Bad Request", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        if (status_ == 500)
                        {
                            string responseText_ = ( response_.Content == null ) ? string.Empty : await ReadAsStringAsync(response_.Content, cancellationToken).ConfigureAwait(false);
                            throw new ApiException("Internal Server Error", status_, responseText_, headers_, null);
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await ReadAsStringAsync(response_.Content, cancellationToken).ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Created</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<HotelBookResult> ReservationPOSTAsync(string version, HotelBookParameters body)
        {
            return ReservationPOSTAsync(version, body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Created</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<HotelBookResult> ReservationPOSTAsync(string version, HotelBookParameters body, System.Threading.CancellationToken cancellationToken)
        {
            if (version == null)
                throw new System.ArgumentNullException("version");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v{version}/Reservation"
                    urlBuilder_.Append("api/v");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(version, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/Reservation");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 201)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<HotelBookResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        if (status_ == 500)
                        {
                            string responseText_ = ( response_.Content == null ) ? string.Empty : await ReadAsStringAsync(response_.Content, cancellationToken).ConfigureAwait(false);
                            throw new ApiException("Internal Server Error", status_, responseText_, headers_, null);
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await ReadAsStringAsync(response_.Content, cancellationToken).ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <param name="providerConfigurationId">If provided, it will override the ProviderConfigurationId read from the used provider.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<GetReservationResult> ReservationGETAsync(string providerBookingId, string confirmationId, int? providerCode, string partnerCode, string locale, string providerConfigurationId, string version)
        {
            return ReservationGETAsync(providerBookingId, confirmationId, providerCode, partnerCode, locale, providerConfigurationId, version, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <param name="providerConfigurationId">If provided, it will override the ProviderConfigurationId read from the used provider.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<GetReservationResult> ReservationGETAsync(string providerBookingId, string confirmationId, int? providerCode, string partnerCode, string locale, string providerConfigurationId, string version, System.Threading.CancellationToken cancellationToken)
        {
            if (version == null)
                throw new System.ArgumentNullException("version");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v{version}/Reservation"
                    urlBuilder_.Append("api/v");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(version, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/Reservation");
                    urlBuilder_.Append('?');
                    if (providerBookingId != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("ProviderBookingId")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(providerBookingId, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (confirmationId != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("ConfirmationId")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(confirmationId, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (providerCode != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("ProviderCode")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(providerCode, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (partnerCode != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("PartnerCode")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(partnerCode, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (locale != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("Locale")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(locale, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (providerConfigurationId != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("ProviderConfigurationId")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(providerConfigurationId, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    urlBuilder_.Length--;

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<GetReservationResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await ReadAsStringAsync(response_.Content, cancellationToken).ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<CancelReservationResult> ReservationDELETEAsync(string providerBookingId, string confirmationId, int? providerCode, string partnerCode, string version)
        {
            return ReservationDELETEAsync(providerBookingId, confirmationId, providerCode, partnerCode, version, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<CancelReservationResult> ReservationDELETEAsync(string providerBookingId, string confirmationId, int? providerCode, string partnerCode, string version, System.Threading.CancellationToken cancellationToken)
        {
            if (version == null)
                throw new System.ArgumentNullException("version");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Method = new System.Net.Http.HttpMethod("DELETE");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v{version}/Reservation"
                    urlBuilder_.Append("api/v");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(version, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/Reservation");
                    urlBuilder_.Append('?');
                    if (providerBookingId != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("ProviderBookingId")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(providerBookingId, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (confirmationId != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("ConfirmationId")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(confirmationId, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (providerCode != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("ProviderCode")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(providerCode, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    if (partnerCode != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("PartnerCode")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(partnerCode, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    urlBuilder_.Length--;

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<CancelReservationResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await ReadAsStringAsync(response_.Content, cancellationToken).ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        protected struct ObjectResponseResult<T>
        {
            public ObjectResponseResult(T responseObject, string responseText)
            {
                this.Object = responseObject;
                this.Text = responseText;
            }

            public T Object { get; }

            public string Text { get; }
        }

        [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
        private static System.Threading.Tasks.Task<string> ReadAsStringAsync(System.Net.Http.HttpContent content, System.Threading.CancellationToken cancellationToken)
        {
    #if NET5_0_OR_GREATER
            return content.ReadAsStringAsync(cancellationToken);
    #else
            return content.ReadAsStringAsync();
    #endif
        }

        [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
        private static System.Threading.Tasks.Task<System.IO.Stream> ReadAsStreamAsync(System.Net.Http.HttpContent content, System.Threading.CancellationToken cancellationToken)
        {
    #if NET5_0_OR_GREATER
            return content.ReadAsStreamAsync(cancellationToken);
    #else
            return content.ReadAsStreamAsync();
    #endif
        }

        public bool ReadResponseAsString { get; set; }

        protected virtual async System.Threading.Tasks.Task<ObjectResponseResult<T>> ReadObjectResponseAsync<T>(System.Net.Http.HttpResponseMessage response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Threading.CancellationToken cancellationToken)
        {
            if (response == null || response.Content == null)
            {
                return new ObjectResponseResult<T>(default(T), string.Empty);
            }

            if (ReadResponseAsString)
            {
                var responseText = await ReadAsStringAsync(response.Content, cancellationToken).ConfigureAwait(false);
                try
                {
                    var typedBody = System.Text.Json.JsonSerializer.Deserialize<T>(responseText, JsonSerializerSettings);
                    return new ObjectResponseResult<T>(typedBody, responseText);
                }
                catch (System.Text.Json.JsonException exception)
                {
                    var message = "Could not deserialize the response body string as " + typeof(T).FullName + ".";
                    throw new ApiException(message, (int)response.StatusCode, responseText, headers, exception);
                }
            }
            else
            {
                try
                {
                    using (var responseStream = await ReadAsStreamAsync(response.Content, cancellationToken).ConfigureAwait(false))
                    {
                        var typedBody = await System.Text.Json.JsonSerializer.DeserializeAsync<T>(responseStream, JsonSerializerSettings, cancellationToken).ConfigureAwait(false);
                        return new ObjectResponseResult<T>(typedBody, string.Empty);
                    }
                }
                catch (System.Text.Json.JsonException exception)
                {
                    var message = "Could not deserialize the response body stream as " + typeof(T).FullName + ".";
                    throw new ApiException(message, (int)response.StatusCode, string.Empty, headers, exception);
                }
            }
        }

        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value == null)
            {
                return "";
            }

            if (value is System.Enum)
            {
                var name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value != null ? attribute.Value : name;
                        }
                    }

                    var converted = System.Convert.ToString(System.Convert.ChangeType(value, System.Enum.GetUnderlyingType(value.GetType()), cultureInfo));
                    return converted == null ? string.Empty : converted;
                }
            }
            else if (value is bool) 
            {
                return System.Convert.ToString((bool)value, cultureInfo).ToLowerInvariant();
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value is string[])
            {
                return string.Join(",", (string[])value);
            }
            else if (value.GetType().IsArray)
            {
                var valueArray = (System.Array)value;
                var valueTextArray = new string[valueArray.Length];
                for (var i = 0; i < valueArray.Length; i++)
                {
                    valueTextArray[i] = ConvertToString(valueArray.GetValue(i), cultureInfo);
                }
                return string.Join(",", valueTextArray);
            }

            var result = System.Convert.ToString(value, cultureInfo);
            return result == null ? "" : result;
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum BuyoutType
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Esky")]
        Esky = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Client")]
        Client = 1,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelPriceParameters
    {

        [System.Text.Json.Serialization.JsonPropertyName("metaCode")]
        public int MetaCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        public int ProviderCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("packageId")]
        public string PackageId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("userInformation")]
        public UserInformation UserInformation { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("searchContext")]
        public SearchContext SearchContext { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("currency")]
        public string Currency { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("language")]
        public string Language { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelPriceResult
    {

        [System.Text.Json.Serialization.JsonPropertyName("offer")]
        public Offer Offer { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("context")]
        public Context Context { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Offer
    {

        /// <summary>
        /// Provider-specific package identifier (raw format, no encoding)
        /// </summary>
        [System.Text.Json.Serialization.JsonPropertyName("packageId")]
        public string PackageId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("metaCode")]
        public int MetaCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("qualifiedHotelCode")]
        public int QualifiedHotelCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("internalPriceDetails")]
        public InternalPriceDetailsWithBreakdown InternalPriceDetails { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("priceDetails")]
        public PriceDetailsWithBreakdown PriceDetails { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("rooms")]
        public System.Collections.Generic.ICollection<Room> Rooms { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        public int ProviderCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerConfigurationId")]
        public string ProviderConfigurationId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("mealPlan")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public MealPlan MealPlan { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("buyoutType")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public BuyoutType BuyoutType { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("clientCreditCardRequired")]
        public bool ClientCreditCardRequired { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("depositRequired")]
        public bool DepositRequired { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("cancellationInfo")]
        public CancellationInfo CancellationInfo { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("policies")]
        public System.Collections.Generic.ICollection<Information> Policies { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("importantInformation")]
        public System.Collections.Generic.ICollection<Information> ImportantInformation { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerHotel")]
        public ProviderHotel ProviderHotel { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Room
    {

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public string Code { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string Name { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("description")]
        public string Description { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("bedConfigurations")]
        public System.Collections.Generic.ICollection<BedConfiguration> BedConfigurations { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("roomConfiguration")]
        public RoomConfiguration RoomConfiguration { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("photos")]
        public System.Collections.Generic.ICollection<Photos> Photos { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("facilities")]
        public System.Collections.Generic.ICollection<string> Facilities { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceOfferParameters
    {

        [System.Text.Json.Serialization.JsonPropertyName("offerId")]
        public string OfferId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("settingsKey")]
        public SettingsKey SettingsKey { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("userInformation")]
        public UserInformation UserInformation { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("currency")]
        public string Currency { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("language")]
        public string Language { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("hotelOfferId")]
        public string HotelOfferId { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceOfferResult
    {

        [System.Text.Json.Serialization.JsonPropertyName("offers")]
        public System.Collections.Generic.ICollection<Offer> Offers { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelRepriceParameters
    {

        [System.Text.Json.Serialization.JsonPropertyName("hotelMetaCode")]
        public int HotelMetaCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("stayInformation")]
        public StayInformation StayInformation { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("occupancy")]
        public System.Collections.Generic.ICollection<RoomConfiguration> Occupancy { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelRepriceResult
    {

        [System.Text.Json.Serialization.JsonPropertyName("metaCode")]
        public int MetaCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("variants")]
        public System.Collections.Generic.ICollection<Variant> Variants { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("groupedVariants")]
        public System.Collections.Generic.ICollection<System.Collections.Generic.IDictionary<string, System.Collections.Generic.ICollection<Variant>>> GroupedVariants { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Variant
    {

        [System.Text.Json.Serialization.JsonPropertyName("offerId")]
        public string OfferId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("metaRoomIds")]
        public System.Collections.Generic.ICollection<string> MetaRoomIds { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("mealPlan")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public MealPlan MealPlan { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("pricing")]
        public VariantPricing Pricing { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("refundability")]
        public Refundability Refundability { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("paymentType")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public PaymentType PaymentType { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("receivedAt")]
        public DateTime ReceivedAt { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("availability")]
        public int Availability { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class VariantPricing
    {

        [System.Text.Json.Serialization.JsonPropertyName("providerNetPrice")]
        public Price ProviderNetPrice { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("summaryToPayAtHotel")]
        public Price SummaryToPayAtHotel { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Booker
    {

        [System.Text.Json.Serialization.JsonPropertyName("phone")]
        public Phone Phone { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("email")]
        public string Email { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class BookingRoom
    {

        [System.Text.Json.Serialization.JsonPropertyName("roomId")]
        public string RoomId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("specialRequests")]
        public string SpecialRequests { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("smoking")]
        public bool Smoking { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("bedType")]
        public string BedType { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("guests")]
        public System.Collections.Generic.ICollection<Guest> Guests { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CreditCard
    {

        [System.Text.Json.Serialization.JsonPropertyName("cardId")]
        public System.Guid CardId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("cardType")]
        public string CardType { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Guest
    {

        [System.Text.Json.Serialization.JsonPropertyName("firstName")]
        public string FirstName { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("lastName")]
        public string LastName { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Phone
    {

        [System.Text.Json.Serialization.JsonPropertyName("countryCode")]
        public string CountryCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("areaCode")]
        public string AreaCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("number")]
        public string Number { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelBookParameters
    {

        [System.Text.Json.Serialization.JsonPropertyName("token")]
        public string Token { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("booker")]
        public Booker Booker { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("creditCard")]
        public CreditCard CreditCard { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("bookingRooms")]
        public System.Collections.Generic.ICollection<BookingRoom> BookingRooms { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("userInformation")]
        public UserInformation UserInformation { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelPriceCheckOfferParameters
    {

        [System.Text.Json.Serialization.JsonPropertyName("metaCode")]
        public int MetaCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        public int ProviderCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("packageId")]
        public string PackageId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("bedConfigurationCodes")]
        public System.Collections.Generic.ICollection<string> BedConfigurationCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("userInformation")]
        public UserInformation UserInformation { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("searchContext")]
        public SearchContext SearchContext { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("currency")]
        public string Currency { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("language")]
        public string Language { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("hotelOfferId")]
        public string HotelOfferId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerConfigurationId")]
        public string ProviderConfigurationId { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelPriceCheckParameters
    {

        [System.Text.Json.Serialization.JsonPropertyName("metaCode")]
        public int MetaCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        public int ProviderCode { get; set; }

        /// <summary>
        /// Provider-specific package identifier (raw format, no encoding)
        /// </summary>
        [System.Text.Json.Serialization.JsonPropertyName("packageId")]
        public string PackageId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("bedConfigurationCodes")]
        public System.Collections.Generic.ICollection<string> BedConfigurationCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("userInformation")]
        public UserInformation UserInformation { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("searchContext")]
        public SearchContext SearchContext { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("currency")]
        public string Currency { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("language")]
        public string Language { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerConfigurationId")]
        public string ProviderConfigurationId { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class BedConfiguration
    {

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public string Code { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("bedTypes")]
        public System.Collections.Generic.ICollection<BedType> BedTypes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("description")]
        public string Description { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class BedType
    {

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string Name { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("description")]
        public string Description { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CancelError
    {

        [System.Text.Json.Serialization.JsonPropertyName("errorCode")]
        public int ErrorCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("errorMesage")]
        public string ErrorMesage { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CancelReservationResult
    {

        [System.Text.Json.Serialization.JsonPropertyName("reservationCancelled")]
        public bool ReservationCancelled { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("cancelError")]
        public CancelError CancelError { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CancellationInfo
    {

        [System.Text.Json.Serialization.JsonPropertyName("cancellationDetails")]
        public System.Collections.Generic.ICollection<CancellationInfoDetails> CancellationDetails { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CancellationInfoDetails
    {

        /// <summary>
        /// Last day for cancellation for a specified price
        /// </summary>
        [System.Text.Json.Serialization.JsonPropertyName("until")]
        public DateOnly Until { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("price")]
        public Price Price { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Context
    {

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("contextsApplied")]
        // TODO(system.text.json): Add string enum item converter
        public System.Collections.Generic.ICollection<Context2> ContextsApplied { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Coordinates
    {

        [System.Text.Json.Serialization.JsonPropertyName("longitude")]
        public double Longitude { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("latitude")]
        public double Latitude { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetReservationResult
    {

        [System.Text.Json.Serialization.JsonPropertyName("metaCode")]
        public int? MetaCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("stayInformation")]
        public StayInformation StayInformation { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("reservationStatus")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public ReservationStatus ReservationStatus { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("reservationDate")]
        public DateTime ReservationDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("hotel")]
        public ProviderHotel Hotel { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("rooms")]
        public System.Collections.Generic.ICollection<ReservationRoom> Rooms { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("cancellationInfo")]
        public CancellationInfo CancellationInfo { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("internalPriceDetails")]
        public InternalPriceDetailsWithBreakdown InternalPriceDetails { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("priceDetails")]
        public PriceDetailsWithBreakdown PriceDetails { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("buyoutType")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public BuyoutType BuyoutType { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("information")]
        public System.Collections.Generic.ICollection<Information> Information { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerInfo")]
        public ProviderInfo ProviderInfo { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("paymentType")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public PaymentType PaymentType { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Guest2
    {

        [System.Text.Json.Serialization.JsonPropertyName("firstName")]
        public string FirstName { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("lastName")]
        public string LastName { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ProviderInfo
    {

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public ProviderCode ProviderCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("supplierName")]
        public string SupplierName { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("supplierOrderReference")]
        public string SupplierOrderReference { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("optimizer")]
        public string Optimizer { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ReservationRoom
    {

        [System.Text.Json.Serialization.JsonPropertyName("roomName")]
        public string RoomName { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("roomId")]
        public string RoomId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("description")]
        public string Description { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("bedType")]
        public string BedType { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("specialRequests")]
        public string SpecialRequests { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("smoking")]
        public bool Smoking { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("facilities")]
        public System.Collections.Generic.ICollection<string> Facilities { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("roomConfiguration")]
        public RoomConfiguration RoomConfiguration { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("mealPlan")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public MealPlan MealPlan { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("guests")]
        public System.Collections.Generic.ICollection<Guest2> Guests { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum ReservationStatus
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Unknown")]
        Unknown = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Created")]
        Created = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"Cancelled")]
        Cancelled = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"NoShow")]
        NoShow = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"Stayed")]
        Stayed = 4,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelAddress
    {

        [System.Text.Json.Serialization.JsonPropertyName("street")]
        public string Street { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("city")]
        public string City { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("countryCode")]
        public string CountryCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("coordinates")]
        public Coordinates Coordinates { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelBookResult
    {

        [System.Text.Json.Serialization.JsonPropertyName("providerBookingId")]
        public string ProviderBookingId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("confirmationId")]
        public string ConfirmationId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        public int ProviderCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerConfigurationId")]
        public string ProviderConfigurationId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("context")]
        public Context Context { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("isTest")]
        public bool IsTest { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Information
    {

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public string Code { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("text")]
        public string Text { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class InternalPriceDetailsWithBreakdown
    {

        [System.Text.Json.Serialization.JsonPropertyName("providerIncome")]
        public Price ProviderIncome { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerPriceDetails")]
        public ProviderPriceDetails ProviderPriceDetails { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class OfferIdDetails
    {

        [System.Text.Json.Serialization.JsonPropertyName("metaCode")]
        public int MetaCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("stayInformation")]
        public StayInformation StayInformation { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public ProviderCode ProviderCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("occupancy")]
        public System.Collections.Generic.ICollection<RoomConfiguration> Occupancy { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceBreakdown
    {

        [System.Text.Json.Serialization.JsonPropertyName("type")]
        public string Type { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string Name { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("includedInNet")]
        public bool IncludedInNet { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("price")]
        public Price Price { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public string Code { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelPriceCheckOfferResult
    {

        [System.Text.Json.Serialization.JsonPropertyName("offers")]
        public System.Collections.Generic.ICollection<HotelPriceCheckResult> Offers { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelPriceCheckResult
    {

        [System.Text.Json.Serialization.JsonPropertyName("token")]
        public string Token { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("internalPriceDetails")]
        public InternalPriceDetailsWithBreakdown InternalPriceDetails { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("priceDetails")]
        public PriceDetailsWithBreakdown PriceDetails { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("rooms")]
        public System.Collections.Generic.ICollection<PriceCheckRoom> Rooms { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public ProviderCode ProviderCode { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceCheckRoom
    {

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public string Id { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string Name { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("bedConfigurationCode")]
        public string BedConfigurationCode { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceDetails
    {

        [System.Text.Json.Serialization.JsonPropertyName("currency")]
        public string Currency { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("netPrice")]
        public decimal NetPrice { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("taxPrice")]
        public decimal TaxPrice { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("extraCharges")]
        public decimal ExtraCharges { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerNetPrice")]
        public Price ProviderNetPrice { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceDetailsWithBreakdown
    {

        [System.Text.Json.Serialization.JsonPropertyName("priceDetails")]
        public PriceDetails PriceDetails { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("summaryToPayNow")]
        public Price SummaryToPayNow { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("summaryToPayAtHotel")]
        public Price SummaryToPayAtHotel { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("summaryToPayAtHotelInClientCurrency")]
        public Price SummaryToPayAtHotelInClientCurrency { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("taxesBreakdown")]
        public System.Collections.Generic.ICollection<PriceBreakdown> TaxesBreakdown { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("extraChargesBreakdown")]
        public System.Collections.Generic.ICollection<PriceBreakdown> ExtraChargesBreakdown { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("chargesBreakdown")]
        public System.Collections.Generic.ICollection<PriceBreakdown> ChargesBreakdown { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("salesMargin")]
        public Price SalesMargin { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ProviderHotel
    {

        [System.Text.Json.Serialization.JsonPropertyName("originalHotelCode")]
        public string OriginalHotelCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        public int ProviderCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("hotelName")]
        public string HotelName { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("phone")]
        public string Phone { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("address")]
        public HotelAddress Address { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("countryCode")]
        public string CountryCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("webPage")]
        public string WebPage { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("stars")]
        public int Stars { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ProviderPriceDetails
    {

        [System.Text.Json.Serialization.JsonPropertyName("currency")]
        public string Currency { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("netPrice")]
        public decimal NetPrice { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("taxPrice")]
        public decimal TaxPrice { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerIncome")]
        public Price ProviderIncome { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Refundability
    {

        [System.Text.Json.Serialization.JsonPropertyName("isRefundable")]
        public bool IsRefundable { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("freeRefundUntil")]
        public DateOnly? FreeRefundUntil { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SearchContext
    {

        [System.Text.Json.Serialization.JsonPropertyName("stayInformation")]
        public StayInformation StayInformation { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("settingsKey")]
        public SettingsKey SettingsKey { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("roomsConfiguration")]
        public System.Collections.Generic.ICollection<RoomConfiguration> RoomsConfiguration { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SettingsKey
    {

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flightBookingId")]
        public string FlightBookingId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("mobileDevice")]
        public bool? MobileDevice { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("userId")]
        public string UserId { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class UserInformation
    {

        [System.Text.Json.Serialization.JsonPropertyName("userAgent")]
        public string UserAgent { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("userIp")]
        public string UserIp { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum Context2
    {

        [System.Runtime.Serialization.EnumMember(Value = @"CROSSSELLING")]
        CROSSSELLING = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"MOBILE")]
        MOBILE = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"MEMBER")]
        MEMBER = 2,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum MealPlan
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Unknown")]
        Unknown = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"None")]
        None = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"Breakfast")]
        Breakfast = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"HalfBoard")]
        HalfBoard = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"FullBoard")]
        FullBoard = 4,

        [System.Runtime.Serialization.EnumMember(Value = @"AllInclusive")]
        AllInclusive = 5,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum PaymentType
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Unknown")]
        Unknown = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"OnSite")]
        OnSite = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"AtCheckout")]
        AtCheckout = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"Installments")]
        Installments = 3,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Photo
    {

        [System.Text.Json.Serialization.JsonPropertyName("src")]
        public string Src { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("width")]
        public int Width { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("height")]
        public int Height { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Price
    {

        [System.Text.Json.Serialization.JsonPropertyName("amount")]
        public decimal Amount { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("currency")]
        public string Currency { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum ProviderCode
    {

        [System.Runtime.Serialization.EnumMember(Value = @"BookingCom")]
        BookingCom = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Expedia")]
        Expedia = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"HotelBeds")]
        HotelBeds = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"Travolutionary")]
        Travolutionary = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"TestHouseExpress")]
        TestHouseExpress = 4,

        [System.Runtime.Serialization.EnumMember(Value = @"Anixe")]
        Anixe = 5,

        [System.Runtime.Serialization.EnumMember(Value = @"RateHawk")]
        RateHawk = 6,

        [System.Runtime.Serialization.EnumMember(Value = @"WebBeds")]
        WebBeds = 7,

        [System.Runtime.Serialization.EnumMember(Value = @"OTS")]
        OTS = 8,

        [System.Runtime.Serialization.EnumMember(Value = @"Alturabeds")]
        Alturabeds = 9,

        [System.Runtime.Serialization.EnumMember(Value = @"Yalago")]
        Yalago = 10,

        [System.Runtime.Serialization.EnumMember(Value = @"TravelgateDodo")]
        TravelgateDodo = 11,

        [System.Runtime.Serialization.EnumMember(Value = @"TravelgateW2M")]
        TravelgateW2M = 12,

        [System.Runtime.Serialization.EnumMember(Value = @"TravelgateB4G")]
        TravelgateB4G = 13,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class RoomConfiguration
    {

        [System.Text.Json.Serialization.JsonPropertyName("adults")]
        public int Adults { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("childrenAges")]
        public System.Collections.Generic.ICollection<int> ChildrenAges { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class StayInformation
    {

        [System.Text.Json.Serialization.JsonPropertyName("checkInDate")]
        public DateTime CheckInDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("checkOutDate")]
        public DateTime CheckOutDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("nights")]
        public int Nights { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ProblemDetails
    {

        [System.Text.Json.Serialization.JsonPropertyName("type")]
        public string Type { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("title")]
        public string Title { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public int? Status { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("detail")]
        public string Detail { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("instance")]
        public string Instance { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Photos
    {

        [System.Text.Json.Serialization.JsonPropertyName("Thumbnail")]
        public Photo Thumbnail { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("Medium")]
        public Photo Medium { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("Large")]
        public Photo Large { get; set; }

    }



    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ApiException : System.Exception
    {
        public int StatusCode { get; private set; }

        public string Response { get; private set; }

        public System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> Headers { get; private set; }

        public ApiException(string message, int statusCode, string response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Exception innerException)
            : base(message + "\n\nStatus: " + statusCode + "\nResponse: \n" + ((response == null) ? "(null)" : response.Substring(0, response.Length >= 512 ? 512 : response.Length)), innerException)
        {
            StatusCode = statusCode;
            Response = response;
            Headers = headers;
        }

        public override string ToString()
        {
            return string.Format("HTTP Response: \n\n{0}\n\n{1}", Response, base.ToString());
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ApiException<TResult> : ApiException
    {
        public TResult Result { get; private set; }

        public ApiException(string message, int statusCode, string response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, TResult result, System.Exception innerException)
            : base(message, statusCode, response, headers, innerException)
        {
            Result = result;
        }
    }

}

#pragma warning restore  108
#pragma warning restore  114
#pragma warning restore  472
#pragma warning restore  612
#pragma warning restore  649
#pragma warning restore 1573
#pragma warning restore 1591
#pragma warning restore 8073
#pragma warning restore 3016
#pragma warning restore 8600
#pragma warning restore 8602
#pragma warning restore 8603
#pragma warning restore 8604
#pragma warning restore 8625
#pragma warning restore 8765