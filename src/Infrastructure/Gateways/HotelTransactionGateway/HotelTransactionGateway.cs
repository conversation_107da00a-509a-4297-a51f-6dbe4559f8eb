using Esky.Packages.Application.Abstractions.Gateways.HotelTransactionGateway;
using Esky.Packages.Application.Dtos.HotelOfferVariants;
using Esky.Packages.Infrastructure.HttpClients.Constants;
using Infrastructure.Gateways.HotelTransactionGateway.HotelTransaction;

namespace Esky.Packages.Infrastructure.Gateways.HotelTransactionGateway;

public class HotelTransactionGateway : IHotelTransactionGateway
{
    private readonly HotelTransactionClient _client;

    public HotelTransactionGateway(IHttpClientFactory httpClientFactory)
    {
        var httpClient = httpClientFactory.CreateClient(ApiHttpClientConsts.HotelTransactionClient);

        _client = new HotelTransactionClient(httpClient);
    }

    public async Task<List<Dictionary<string, List<HotelOfferVariantDto>>>> GetVariantsGroupedByRooms(
        HotelOfferVariantsCritieria criteria,
        CancellationToken cancellationToken = default)
    {
        var res = await _client.HotelAsync("1", new HotelRepriceParameters
        {
            HotelMetaCode = criteria.MetaCode,
            PartnerCode = criteria.PartnerCode,
            StayInformation = new StayInformation
            {
                CheckInDate = criteria.CheckIn.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc),
                CheckOutDate = criteria.CheckOut.ToDateTime(TimeOnly.MaxValue, DateTimeKind.Utc),
            },
            Occupancy = criteria.Occupancies.Select(occupancy => new RoomConfiguration
            {
                Adults = occupancy.Adults,
                ChildrenAges = occupancy.ChildrenAges.Select(childAge => childAge).ToList()
            }).ToList()
        }, cancellationToken);

        return res.GroupedVariants.Select(g =>
            g.ToDictionary(
                d => d.Key,
                d => d.Value.Select(x => new HotelOfferVariantDto(
                    OfferId: x.OfferId,
                    RoomIds: x.MetaRoomIds?.ToArray() ?? [],
                    MealPlan: MapMealPlan(x.MealPlan),
                    Price: new HotelVariantMoneyDto(
                        Value: x.Pricing.ProviderNetPrice.Amount,
                        Currency: x.Pricing.ProviderNetPrice.Currency),
                    PriceAtHotel: new HotelVariantMoneyDto(
                        Value: x.Pricing.SummaryToPayAtHotel.Amount,
                        Currency: x.Pricing.SummaryToPayAtHotel.Currency),
                    Refundability: MapRefundability(x.Refundability),
                    FreeRefundUntil: x.Refundability.FreeRefundUntil,
                    Availability: x.Availability)).ToList()
        )).ToList();
    }

    private static HotelVariantRefundabilityDto MapRefundability(Refundability refundability)
    {
        return refundability.IsRefundable
            ? HotelVariantRefundabilityDto.Refundable 
            : HotelVariantRefundabilityDto.NonRefundable;
    }

    private static HotelVariantMealPlanDto MapMealPlan(MealPlan mealPlan)
    {
        return mealPlan switch
        {
            MealPlan.Breakfast => HotelVariantMealPlanDto.Breakfast,
            MealPlan.HalfBoard => HotelVariantMealPlanDto.HalfBoard,
            MealPlan.FullBoard => HotelVariantMealPlanDto.FullBoard,
            MealPlan.AllInclusive => HotelVariantMealPlanDto.AllInclusive,
            _ => HotelVariantMealPlanDto.None
        };
    }
}