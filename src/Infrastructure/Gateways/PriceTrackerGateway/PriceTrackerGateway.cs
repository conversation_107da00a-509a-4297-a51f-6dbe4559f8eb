using System.Text.Json;
using Confluent.Kafka;
using Esky.Hotels.Infrastructure.Kafka.Producer;
using Esky.Packages.Application.Abstractions.Gateways.PriceTrackerGateway;
using Esky.Packages.Domain.Events;
using Esky.Packages.Domain.Events.PackageFlightVariants;
using Esky.Packages.Infrastructure.Partitioners;
using Esky.Packages.Infrastructure.Serialization.Contexts;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Infrastructure.Gateways.PriceTrackerGateway;

public class PriceTrackerGateway : IPriceTrackerGateway
{
    private readonly KafkaProducer<string, byte[]> _producer;

    private readonly HotelMetaCodePartitioner _hotelOfferPricesPartitioner = new();
    private readonly AirportPartitioner _flightPricesPartitioner = new();
    private readonly ILogger<PriceTrackerGateway> _logger;

    public PriceTrackerGateway(
        ILogger<PriceTrackerGateway> logger,
        KafkaProducerFactory kafkaProducerFactory,
        PriceTrackerProducerOptions options)
    {
        _logger = logger;

        _hotelOfferPricesPartitioner.Initialize(options.HotelOfferPricesTopic, options.BootstrapServers);
        _flightPricesPartitioner.Initialize(options.FlightPricesTopic, options.BootstrapServers);

        _producer = kafkaProducerFactory.Create<string, byte[]>(o => o
            .WithBootstrapServers(options.BootstrapServers)
            .WithCompression(options.Compression)
            .WithDeliveryReportFields(ProducerDeliveryReportFields.Status)
        );
    }

    public async Task PublishEvents(ICollection<PackageFlightVariantPriceEvent> events, CancellationToken cancellationToken)
    {
        foreach (var @event in events)
        {
            var partition = _flightPricesPartitioner.GetTopicPartition(@event.ArrivalAirport);

            var value = JsonSerializer.SerializeToUtf8Bytes(@event,
                PackageFlightVariantPriceEventJsonContext.Default.PackageFlightVariantPriceEvent);

            _producer.Produce(partition, new Message<string, byte[]>
            {
                Key = Guid.NewGuid().ToString(),
                Value = value
            });
        }

        _logger.LogDebug("Sent {count} PackageFlightVariantPriceEvents", events.Count);

        await Task.CompletedTask;
    }

    public async Task PublishEvents(ICollection<PackageHotelOfferVariantPriceEvent> events, CancellationToken cancellationToken)
    {
        foreach (var @event in events)
        {
            var partition = _hotelOfferPricesPartitioner.GetTopicPartition(@event.MetaCode);

            var value = JsonSerializer.SerializeToUtf8Bytes(@event,
                PackageHotelOfferVariantPriceEventJsonContext.Default.PackageHotelOfferVariantPriceEvent);

            _producer.Produce(partition, new Message<string, byte[]>
            {
                Key = Guid.NewGuid().ToString(),
                Value = value
            });
        }

        _logger.LogDebug("Sent {count} PackageHotelOfferVariantPriceEvents", events.Count);

        await Task.CompletedTask;
    }
}