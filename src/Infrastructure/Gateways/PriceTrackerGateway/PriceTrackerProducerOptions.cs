using Confluent.Kafka;

namespace Esky.Packages.Infrastructure.Gateways.PriceTrackerGateway;

public class PriceTrackerProducerOptions
{
    public const string ConfigurationSection = "PriceTrackerProducer";
    public string BootstrapServers { get; set; } = default!;
    public string FlightPricesTopic { get; set; } = default!;
    public string HotelOfferPricesTopic { get; set; } = default!;
    public CompressionType Compression { get; set; } = CompressionType.Lz4;
}