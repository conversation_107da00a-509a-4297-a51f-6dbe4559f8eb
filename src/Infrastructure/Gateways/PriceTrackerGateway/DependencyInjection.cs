using Esky.Hotels.Infrastructure.Kafka.Producer;
using Esky.Hotels.Infrastructure.Kafka;
using Esky.Packages.Application.Abstractions.Gateways.PriceTrackerGateway;
using Esky.Packages.Infrastructure.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Esky.Packages.Infrastructure.Gateways.PriceTrackerGateway;

internal static class DependencyInjection
{
    public static IServiceCollection AddPriceTrackerGateway(this IServiceCollection services, IConfiguration configuration)
    {
        services.TryAddSingleton<KafkaOptions>();
        services.TryAddSingleton<KafkaProducerFactory>();

        services.RegisterOptions<PriceTrackerProducerOptions>(configuration,
            PriceTrackerProducerOptions.ConfigurationSection);

        services.AddSingleton<IPriceTrackerGateway, PriceTrackerGateway>();

        return services;
    }
}