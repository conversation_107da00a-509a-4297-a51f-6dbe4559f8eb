using System.Text.Json;
using Confluent.Kafka;
using Esky.Hotels.Infrastructure.Kafka.Producer;
using Esky.Packages.Application.Abstractions.Gateways.BloomFilterNotificationGateway;
using Esky.Packages.Application.Dtos.BloomFilterNotifications;
using Esky.Packages.Infrastructure.Serialization.Contexts;

namespace Esky.Packages.Infrastructure.Gateways.BloomFilterNotificationGateway;

public class BloomFilterNotificationGateway : IBloomFilterNotificationGateway
{
    private readonly KafkaProducer<string, byte[]> _producer;
    private readonly string _topic;
    
    public BloomFilterNotificationGateway(KafkaProducerFactory kafkaProducerFactory, 
        BloomFilterNotificationProducerOptions options)
    {
        _producer = kafkaProducerFactory.Create<string, byte[]>(o => o
            .WithBootstrapServers(options.BootstrapServers)
            .WithCompression(options.Compression)
            .WithDeliveryReportFields(ProducerDeliveryReportFields.Status)
        );

        _topic = options.Topic;
    }
    
    public async Task PublishNotifications(IEnumerable<BloomFilterNotificationDto> notifications, 
        CancellationToken cancellationToken)
    {
        var messages = new List<Message<string, byte[]>>();

        foreach (var notification in notifications)
        {
            var message = new Message<string, byte[]>
            {
                Value = JsonSerializer.SerializeToUtf8Bytes(notification, 
                    BloomFilterNotificationDtoJsonContext.Default.BloomFilterNotificationDto)
            };
            
            messages.Add(message);
        }
        
        var sentMessagesCount = 0;
        var totalMessagesCount = messages.Count;
        var tcs = new TaskCompletionSource();

        foreach (var message in messages)
        {
            _producer.Produce(_topic, message, report =>
            {
                if (report.Status == PersistenceStatus.Persisted)
                {
                    if (Interlocked.Increment(ref sentMessagesCount) == totalMessagesCount)
                    {
                        tcs.SetResult();
                    }
                }
                else
                {
                    tcs.SetException(new Exception("Failed to send message"));
                }
            });
        }
        
        await tcs.Task;
    }
}