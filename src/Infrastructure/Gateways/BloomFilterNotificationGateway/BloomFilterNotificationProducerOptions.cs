using Confluent.Kafka;

namespace Esky.Packages.Infrastructure.Gateways.BloomFilterNotificationGateway;

public class BloomFilterNotificationProducerOptions
{
    public const string ConfigurationSection = "BloomFilterNotificationProducer";
    
    public string BootstrapServers { get; set; } = default!;
    public string Topic { get; set; } = default!;
    public CompressionType Compression { get; set; } = CompressionType.Lz4;
}