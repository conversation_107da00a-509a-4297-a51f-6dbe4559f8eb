using Esky.Packages.Application.Abstractions.Gateways.BloomFilterNotificationGateway;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.Packages.Infrastructure.Gateways.BloomFilterNotificationGateway;

internal static class DependencyInjection
{
    public static IServiceCollection AddBloomFilterNotificationGateway(this IServiceCollection services)
    {
        services.AddSingleton<IBloomFilterNotificationGateway, BloomFilterNotificationGateway>();

        return services;
    }
}