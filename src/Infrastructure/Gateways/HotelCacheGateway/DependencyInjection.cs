using System.Net;
using Esky.Packages.Application.Abstractions.Gateways.HotelOfferGateway;
using Esky.Packages.Infrastructure.HttpClients.Constants;
using Esky.Packages.Infrastructure.HttpClients.Extensions;
using Esky.Packages.Infrastructure.HttpClients.Options;
using Esky.Packages.Infrastructure.Observability;
using Microsoft.Extensions.DependencyInjection;
using Polly;

namespace Esky.Packages.Infrastructure.Gateways.HotelCacheGateway;

internal static class DependencyInjection
{
    public static IServiceCollection AddHotelCacheGateway(this IServiceCollection services, bool enableLoggingHandlers = false)
    {
        var builder = services.AddHttpClient(ApiHttpClientConsts.HotelCacheClient,
                (sp, client) =>
                {
                    var apiSettings = sp.GetRequiredService<ApiUrlsOptions>().HotelCache;
                    client.BaseAddress = new Uri(apiSettings.Url);
                    client.Timeout = TimeSpan.FromMilliseconds(apiSettings.TimeoutInMiliseconds);
                })
            .AddCompression();
            
        if (enableLoggingHandlers)
        {
            builder.WithLoggingHandler();
        }
        
        builder.AddResilienceHandler("hotelCache", (b, _) =>
            b.AddRetry(new()
            {
                ShouldHandle = static args => ValueTask.FromResult(args.Outcome.Result?.StatusCode is
                    HttpStatusCode.BadGateway
                    or HttpStatusCode.ServiceUnavailable
                    or HttpStatusCode.GatewayTimeout
                    or HttpStatusCode.InternalServerError
                ),
                BackoffType = DelayBackoffType.Exponential,
                UseJitter = true,
                MaxRetryAttempts = 5,
                MaxDelay = TimeSpan.FromSeconds(30),
                Delay = TimeSpan.FromMilliseconds(1000),
            })
        );

        services.AddSingleton<IHotelOfferGateway, HotelCacheGateway>();

        return services;
    }
}