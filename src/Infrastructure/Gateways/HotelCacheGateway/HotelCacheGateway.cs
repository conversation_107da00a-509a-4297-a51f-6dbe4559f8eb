using Esky.Packages.Application.Abstractions.Gateways.HotelOfferGateway;
using Esky.Packages.Application.Dtos.HotelOffers;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Types;
using Esky.Packages.Infrastructure.HttpClients.Constants;
using Infrastructure.Gateways.HotelCacheGateway.Client.HotelCache;
using Money = Esky.Packages.Domain.Types.Money;

namespace Esky.Packages.Infrastructure.Gateways.HotelCacheGateway;

public class HotelCacheGateway : IHotelOfferGateway
{
    private readonly HotelCacheClient _client;

    public HotelCacheGateway(IHttpClientFactory httpClientFactory)
    {
        var httpClient = httpClientFactory.CreateClient(ApiHttpClientConsts.HotelCacheClient);
        _client = new HotelCacheClient(httpClient);
    }

    public async Task<IEnumerable<HotelOffer>> Search(HotelOfferSearchCriteria criteria,
        CancellationToken cancellationToken)
    {
        var hotelOffers = await _client.ListAsync(new ListHotelOffersQuery
        {
            CheckIn = criteria.CheckIn,
            StayLength = criteria.StayLength,
            MetaCodes = criteria.MetaCodes,
            Occupancies = criteria.Occupancies.Select(o => o.ToString()).ToArray(),
            ProviderConfigurationIds = criteria.ProviderConfigurationIds,
        }, cancellationToken);

        var results = new List<HotelOffer>(hotelOffers.Count);
        
        foreach (var offer in hotelOffers)
        {
            var hotelOfferOccupancy = HotelOfferOccupancy.Parse(offer.Occupancy, null);
            var dto = new HotelOffer
            {
                CheckIn = offer.CheckIn,
                StayLength = offer.StayLength,
                MetaCode = offer.MetaCode,
                ProviderConfigurationId = offer.ProviderConfigurationId,
                Occupancy = new Occupancy(hotelOfferOccupancy.Adults, hotelOfferOccupancy.Children),
                UpdatedAt = DateTime.UtcNow,
                RoomOffersByMealPlanByRefundability = CreateRoomOffersDictionary(offer.RoomOffersByMealPlanByRefundability),
            };
            
            results.Add(dto);
        }
        
        return results;
    }

    public async Task<IEnumerable<HotelOffer>> SearchMany(HotelOfferSearchManyCriteria criteria,
        CancellationToken cancellationToken)
    {
        var hotelOffers = await _client.ListManyAsync(new ListManyHotelOffersQuery
        {
            StayKeys = criteria.StayKeys.Select(s => new HotelOfferStayKey
            {
                CheckIn = s.CheckIn,
                StayLength = s.StayLength,
                MetaCode = s.MetaCode,
            }).ToArray(),
            Occupancies = criteria.Occupancies.Select(o => o.ToString()).ToArray(),
            ProviderConfigurationIds = criteria.ProviderConfigurationIds,
        }, cancellationToken);

        var results = new List<HotelOffer>(hotelOffers.Count);
        
        foreach (var offer in hotelOffers)
        {
            var hotelOfferOccupancy = HotelOfferOccupancy.Parse(offer.Occupancy, null);
            var dto = new HotelOffer
            {
                CheckIn = offer.CheckIn,
                StayLength = offer.StayLength,
                MetaCode = offer.MetaCode,
                ProviderConfigurationId = offer.ProviderConfigurationId,
                Occupancy = new Occupancy(hotelOfferOccupancy.Adults, hotelOfferOccupancy.Children),
                RoomOffersByMealPlanByRefundability = CreateRoomOffersDictionary(offer.RoomOffersByMealPlanByRefundability),
                UpdatedAt = DateTime.Now,
            };
            
            results.Add(dto);
        }
        
        return results;
    }

    private static Dictionary<MealPlan, Dictionary<Refundability, RoomOffer[]>> CreateRoomOffersDictionary(
        IDictionary<string, IDictionary<string, ICollection<RoomOfferDto>>> offerRoomOffersByMealPlanByRefundability)
    {
        var result = new Dictionary<MealPlan, Dictionary<Refundability, RoomOffer[]>>();
        
        foreach (var mealPlanEntry in offerRoomOffersByMealPlanByRefundability)
        {
            var mealPlan = new MealPlan(mealPlanEntry.Key);
            var refundabilityDict = new Dictionary<Refundability, RoomOffer[]>();
            
            foreach (var refundabilityEntry in mealPlanEntry.Value)
            {
                var refundability = Refundability.FromString(refundabilityEntry.Key);
                var roomOffers = refundabilityEntry.Value.Select(room => new RoomOffer
                {
                   Availability = room.Availability,
                   Price = new Money(room.Price.Value,room.Price.Currency),
                   RoomIds = new RoomIds(room.MetaRoomIds?.ToArray() ?? []) // TODO: Remove null check as we should always get values
                }).ToArray();
                
                refundabilityDict.Add(refundability, roomOffers);
            }
            
            result.Add(mealPlan, refundabilityDict);
        }
        
        return result;
    }
}