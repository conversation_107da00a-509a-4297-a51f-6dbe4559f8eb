using Esky.Packages.Application.Abstractions.Gateways.HotelsApiGateway;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Types;
using Esky.Packages.Domain.Types.Extensions;
using Esky.Packages.Infrastructure.HttpClients.Constants;
using Infrastructure.Gateways.HotelsApiGateway.HotelsApi;

using ApiRoomOffer = Infrastructure.Gateways.HotelsApiGateway.HotelsApi.RoomOffer;

namespace Esky.Packages.Infrastructure.Gateways.HotelApiGateway;

public class HotelApiGateway : IHotelOfferLiveGateway
{
    private readonly HotelsApi _client;

    public HotelApiGateway(IHttpClientFactory httpClientFactory)
    {
        _client = new HotelsApi(httpClientFactory.CreateClient(ApiHttpClientConsts.HotelApiClient));
    }

    public async Task<IEnumerable<HotelOffer>> Search(HotelSearchCriteria criteria, CancellationToken cancellationToken)
    {
        var apiParameters = new CheapestSearchParameters
        {
            HotelMetaCodes = criteria.MetaCodes,
            PartnerCode = criteria.PartnerCode,
            StayInformation = new StayInformation
            {
                CheckInDate = criteria.CheckIn.ToDateTime(TimeOnly.MinValue),
                CheckOutDate = criteria.CheckOut.ToDateTime(TimeOnly.MinValue),
                Nights = (criteria.CheckOut.ToDateTime(TimeOnly.MinValue) - criteria.CheckIn.ToDateTime(TimeOnly.MinValue)).Days
            },
            Occupancies = criteria.Occupancies.Select(rc => new RoomConfiguration
            {
                Adults = rc.Adults,
                ChildrenAges = rc.ChildrenAges
            }).ToList()
        };

        var result = await _client.CheapestAsync(apiParameters, cancellationToken);

        var occupancy = criteria.Occupancies.Merge();

        var hotelOffers = result.HotelOffers.SelectMany(hotel =>
            CreateHotelOffersGroupedByProviderConfigurationId(hotel, criteria.CheckIn, criteria.CheckOut, occupancy));

        return hotelOffers;
    }

    private static IEnumerable<HotelOffer> CreateHotelOffersGroupedByProviderConfigurationId(
        Hotel hotel, DateOnly checkIn, DateOnly checkOut, Occupancy occupancy)
    {
        var roomOffersByProviderConfigurationId = new Dictionary<string, Dictionary<Domain.Types.MealPlan, Dictionary<Domain.Types.Refundability, List<Domain.Model.PackageHotelOffers.RoomOffer>>>>();

        ProcessMealPlanOffers(hotel.RoomOfferByMealPlanByRefundability.None, Domain.Types.MealPlan.None, roomOffersByProviderConfigurationId);
        ProcessMealPlanOffers(hotel.RoomOfferByMealPlanByRefundability.Breakfast, Domain.Types.MealPlan.Breakfast, roomOffersByProviderConfigurationId);
        ProcessMealPlanOffers(hotel.RoomOfferByMealPlanByRefundability.HalfBoard, Domain.Types.MealPlan.HalfBoard, roomOffersByProviderConfigurationId);
        ProcessMealPlanOffers(hotel.RoomOfferByMealPlanByRefundability.FullBoard, Domain.Types.MealPlan.FullBoard, roomOffersByProviderConfigurationId);
        ProcessMealPlanOffers(hotel.RoomOfferByMealPlanByRefundability.AllInclusive, Domain.Types.MealPlan.AllInclusive, roomOffersByProviderConfigurationId);

        return roomOffersByProviderConfigurationId.Select(providerGroup => new HotelOffer
        {
            CheckIn = checkIn,
            StayLength = checkOut.DayNumber - checkIn.DayNumber,
            MetaCode = hotel.MetaCode,
            ProviderConfigurationId = new ProviderConfigurationId(providerGroup.Key),
            Occupancy = new Occupancy(occupancy.Adults, occupancy.ChildrenAges),
            UpdatedAt = DateTime.Now,
            RoomOffersByMealPlanByRefundability = providerGroup.Value.ToDictionary(mealPlanGroup => mealPlanGroup.Key,
                mealPlan => mealPlan.Value.ToDictionary(refundability => refundability.Key,
                    refundabilityGroup => refundabilityGroup.Value.ToArray()))
        });
    }

    private static void ProcessMealPlanOffers(
        IDictionary<string, ApiRoomOffer> offers,
        Domain.Types.MealPlan mealPlan,
        Dictionary<string, Dictionary<Domain.Types.MealPlan, Dictionary<Domain.Types.Refundability, List<Domain.Model.PackageHotelOffers.RoomOffer>>>> roomOffersByProviderConfigurationId)
    {
        if (offers == null || !offers.Any()) return;

        foreach (var refundabilityEntry in offers)
        {
            var refundability = new Domain.Types.Refundability(bool.Parse(refundabilityEntry.Key));
            var apiRoomOffer = refundabilityEntry.Value;

            var roomOffer = new Domain.Model.PackageHotelOffers.RoomOffer
            {
                Availability = apiRoomOffer.Availability,
                Price = new Domain.Types.Money(apiRoomOffer.Price.Value, apiRoomOffer.Price.Currency),
                RoomIds = new RoomIds([.. apiRoomOffer.MetaRoomIds])
            };

            if (!roomOffersByProviderConfigurationId.ContainsKey(apiRoomOffer.ProviderConfigurationId))
            {
                roomOffersByProviderConfigurationId[apiRoomOffer.ProviderConfigurationId] = [];
            }

            if (!roomOffersByProviderConfigurationId[apiRoomOffer.ProviderConfigurationId].ContainsKey(mealPlan))
            {
                roomOffersByProviderConfigurationId[apiRoomOffer.ProviderConfigurationId][mealPlan] = [];
            }

            if (!roomOffersByProviderConfigurationId[apiRoomOffer.ProviderConfigurationId][mealPlan].ContainsKey(refundability))
            {
                roomOffersByProviderConfigurationId[apiRoomOffer.ProviderConfigurationId][mealPlan][refundability] = [];
            }

            roomOffersByProviderConfigurationId[apiRoomOffer.ProviderConfigurationId][mealPlan][refundability].Add(roomOffer);
        }
    }
}
