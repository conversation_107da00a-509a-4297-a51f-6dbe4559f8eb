//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

#pragma warning disable 108 // Disable "CS0108 '{derivedDto}.To<PERSON><PERSON>()' hides inherited member '{dtoBase}.To<PERSON>son()'. Use the new keyword if hiding was intended."
#pragma warning disable 114 // Disable "CS0114 '{derivedDto}.RaisePropertyChanged(String)' hides inherited member 'dtoBase.RaisePropertyChanged(String)'. To make the current member override that implementation, add the override keyword. Otherwise add the new keyword."
#pragma warning disable 472 // Disable "CS0472 The result of the expression is always 'false' since a value of type 'Int32' is never equal to 'null' of type 'Int32?'
#pragma warning disable 612 // Disable "CS0612 '...' is obsolete"
#pragma warning disable 649 // Disable "CS0649 Field is never assigned to, and will always have its default value null"
#pragma warning disable 1573 // Disable "CS1573 Parameter '...' has no matching param tag in the XML comment for ...
#pragma warning disable 1591 // Disable "CS1591 Missing XML comment for publicly visible type or member ..."
#pragma warning disable 8073 // Disable "CS8073 The result of the expression is always 'false' since a value of type 'T' is never equal to 'null' of type 'T?'"
#pragma warning disable 3016 // Disable "CS3016 Arrays as attribute arguments is not CLS-compliant"
#pragma warning disable 8600 // Disable "CS8600 Converting null literal or possible null value to non-nullable type"
#pragma warning disable 8602 // Disable "CS8602 Dereference of a possibly null reference"
#pragma warning disable 8603 // Disable "CS8603 Possible null reference return"
#pragma warning disable 8604 // Disable "CS8604 Possible null reference argument for parameter"
#pragma warning disable 8625 // Disable "CS8625 Cannot convert null literal to non-nullable reference type"
#pragma warning disable 8765 // Disable "CS8765 Nullability of type of parameter doesn't match overridden member (possibly because of nullability attributes)."

namespace Infrastructure.Gateways.HotelsApiGateway.HotelsApi
{
    using System = global::System;

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelsApi 
    {
        private System.Net.Http.HttpClient _httpClient;
        private static System.Lazy<System.Text.Json.JsonSerializerOptions> _settings = new System.Lazy<System.Text.Json.JsonSerializerOptions>(CreateSerializerSettings, true);
        private System.Text.Json.JsonSerializerOptions _instanceSettings;

    #pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public HotelsApi(System.Net.Http.HttpClient httpClient)
    #pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        {
            _httpClient = httpClient;
            Initialize();
        }

        private static System.Text.Json.JsonSerializerOptions CreateSerializerSettings()
        {
            var settings = new System.Text.Json.JsonSerializerOptions();
            UpdateJsonSerializerSettings(settings);
            return settings;
        }

        protected System.Text.Json.JsonSerializerOptions JsonSerializerSettings { get { return _instanceSettings ?? _settings.Value; } }

        static partial void UpdateJsonSerializerSettings(System.Text.Json.JsonSerializerOptions settings);

        partial void Initialize();

        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);

        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<HotelSearchResult> SearchAsync(HotelSearchParameters body)
        {
            return SearchAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<HotelSearchResult> SearchAsync(HotelSearchParameters body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "hapi/Search"
                    urlBuilder_.Append("hapi/Search");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<HotelSearchResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        if (status_ == 400)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Bad Request", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await ReadAsStringAsync(response_.Content, cancellationToken).ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<ProviderHotelsSearchResult> Search2Async(ProviderCode providerCode, bool? includeResponseDetails, ProviderSearchParameters body)
        {
            return Search2Async(providerCode, includeResponseDetails, body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<ProviderHotelsSearchResult> Search2Async(ProviderCode providerCode, bool? includeResponseDetails, ProviderSearchParameters body, System.Threading.CancellationToken cancellationToken)
        {
            if (providerCode == null)
                throw new System.ArgumentNullException("providerCode");

            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "hapi/Provider/{providerCode}/Search"
                    urlBuilder_.Append("hapi/Provider/");
                    urlBuilder_.Append(System.Uri.EscapeDataString(ConvertToString(providerCode, System.Globalization.CultureInfo.InvariantCulture)));
                    urlBuilder_.Append("/Search");
                    urlBuilder_.Append('?');
                    if (includeResponseDetails != null)
                    {
                        urlBuilder_.Append(System.Uri.EscapeDataString("includeResponseDetails")).Append('=').Append(System.Uri.EscapeDataString(ConvertToString(includeResponseDetails, System.Globalization.CultureInfo.InvariantCulture))).Append('&');
                    }
                    urlBuilder_.Length--;

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProviderHotelsSearchResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        if (status_ == 400)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Bad Request", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await ReadAsStringAsync(response_.Content, cancellationToken).ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<CheapestSearchResult> CheapestAsync(CheapestSearchParameters body)
        {
            return CheapestAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<CheapestSearchResult> CheapestAsync(CheapestSearchParameters body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "hapi/Search/Cheapest"
                    urlBuilder_.Append("hapi/Search/Cheapest");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<CheapestSearchResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        if (status_ == 400)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Bad Request", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await ReadAsStringAsync(response_.Content, cancellationToken).ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<HotelVariantsResult> VariantsAsync(HotelVariantsParameters body)
        {
            return VariantsAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<HotelVariantsResult> VariantsAsync(HotelVariantsParameters body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "hapi/Variants"
                    urlBuilder_.Append("hapi/Variants");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<HotelVariantsResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        if (status_ == 400)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Bad Request", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await ReadAsStringAsync(response_.Content, cancellationToken).ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<OffersResult> OffersAsync(OffersParameters body)
        {
            return OffersAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<OffersResult> OffersAsync(OffersParameters body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "hapi/Offers"
                    urlBuilder_.Append("hapi/Offers");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<OffersResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        if (status_ == 400)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Bad Request", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await ReadAsStringAsync(response_.Content, cancellationToken).ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<HotelDetailsResult> DetailsAsync(HotelDetailsParameters body)
        {
            return DetailsAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>OK</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<HotelDetailsResult> DetailsAsync(HotelDetailsParameters body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "hapi/hotels/details"
                    urlBuilder_.Append("hapi/hotels/details");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<HotelDetailsResult>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        if (status_ == 400)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ProblemDetails>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            throw new ApiException<ProblemDetails>("Bad Request", status_, objectResponse_.Text, headers_, objectResponse_.Object, null);
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await ReadAsStringAsync(response_.Content, cancellationToken).ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        protected struct ObjectResponseResult<T>
        {
            public ObjectResponseResult(T responseObject, string responseText)
            {
                this.Object = responseObject;
                this.Text = responseText;
            }

            public T Object { get; }

            public string Text { get; }
        }

        [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
        private static System.Threading.Tasks.Task<string> ReadAsStringAsync(System.Net.Http.HttpContent content, System.Threading.CancellationToken cancellationToken)
        {
    #if NET5_0_OR_GREATER
            return content.ReadAsStringAsync(cancellationToken);
    #else
            return content.ReadAsStringAsync();
    #endif
        }

        [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
        private static System.Threading.Tasks.Task<System.IO.Stream> ReadAsStreamAsync(System.Net.Http.HttpContent content, System.Threading.CancellationToken cancellationToken)
        {
    #if NET5_0_OR_GREATER
            return content.ReadAsStreamAsync(cancellationToken);
    #else
            return content.ReadAsStreamAsync();
    #endif
        }

        public bool ReadResponseAsString { get; set; }

        protected virtual async System.Threading.Tasks.Task<ObjectResponseResult<T>> ReadObjectResponseAsync<T>(System.Net.Http.HttpResponseMessage response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Threading.CancellationToken cancellationToken)
        {
            if (response == null || response.Content == null)
            {
                return new ObjectResponseResult<T>(default(T), string.Empty);
            }

            if (ReadResponseAsString)
            {
                var responseText = await ReadAsStringAsync(response.Content, cancellationToken).ConfigureAwait(false);
                try
                {
                    var typedBody = System.Text.Json.JsonSerializer.Deserialize<T>(responseText, JsonSerializerSettings);
                    return new ObjectResponseResult<T>(typedBody, responseText);
                }
                catch (System.Text.Json.JsonException exception)
                {
                    var message = "Could not deserialize the response body string as " + typeof(T).FullName + ".";
                    throw new ApiException(message, (int)response.StatusCode, responseText, headers, exception);
                }
            }
            else
            {
                try
                {
                    using (var responseStream = await ReadAsStreamAsync(response.Content, cancellationToken).ConfigureAwait(false))
                    {
                        var typedBody = await System.Text.Json.JsonSerializer.DeserializeAsync<T>(responseStream, JsonSerializerSettings, cancellationToken).ConfigureAwait(false);
                        return new ObjectResponseResult<T>(typedBody, string.Empty);
                    }
                }
                catch (System.Text.Json.JsonException exception)
                {
                    var message = "Could not deserialize the response body stream as " + typeof(T).FullName + ".";
                    throw new ApiException(message, (int)response.StatusCode, string.Empty, headers, exception);
                }
            }
        }

        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value == null)
            {
                return "";
            }

            if (value is System.Enum)
            {
                var name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value != null ? attribute.Value : name;
                        }
                    }

                    var converted = System.Convert.ToString(System.Convert.ChangeType(value, System.Enum.GetUnderlyingType(value.GetType()), cultureInfo));
                    return converted == null ? string.Empty : converted;
                }
            }
            else if (value is bool) 
            {
                return System.Convert.ToString((bool)value, cultureInfo).ToLowerInvariant();
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value is string[])
            {
                return string.Join(",", (string[])value);
            }
            else if (value.GetType().IsArray)
            {
                var valueArray = (System.Array)value;
                var valueTextArray = new string[valueArray.Length];
                for (var i = 0; i < valueArray.Length; i++)
                {
                    valueTextArray[i] = ConvertToString(valueArray.GetValue(i), cultureInfo);
                }
                return string.Join(",", valueTextArray);
            }

            var result = System.Convert.ToString(value, cultureInfo);
            return result == null ? "" : result;
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CheapestSearchParameters
    {

        [System.Text.Json.Serialization.JsonPropertyName("hotelMetaCodes")]
        public System.Collections.Generic.ICollection<int> HotelMetaCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("stayInformation")]
        public StayInformation StayInformation { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("occupancies")]
        public System.Collections.Generic.ICollection<RoomConfiguration> Occupancies { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelDetailsParameters
    {

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("hotelMetaCode")]
        public int HotelMetaCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("language")]
        public string Language { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelSearchParameters
    {

        [System.Text.Json.Serialization.JsonPropertyName("hotelMetaCodes")]
        public System.Collections.Generic.ICollection<int> HotelMetaCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("searchContext")]
        public SearchContext SearchContext { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("currency")]
        public string Currency { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("language")]
        public string Language { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelVariantsParameters
    {

        [System.Text.Json.Serialization.JsonPropertyName("hotelMetaCode")]
        public int HotelMetaCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("searchContext")]
        public SearchContext SearchContext { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("currency")]
        public string Currency { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("language")]
        public string Language { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class OffersParameters
    {

        [System.Text.Json.Serialization.JsonPropertyName("offerIds")]
        public System.Collections.Generic.ICollection<string> OfferIds { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("language")]
        public string Language { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ProviderSearchParameters
    {

        [System.Text.Json.Serialization.JsonPropertyName("hotelMetaCodes")]
        public System.Collections.Generic.ICollection<int> HotelMetaCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("stayInformation")]
        public StayInformation StayInformation { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("occupancy")]
        public System.Collections.Generic.ICollection<RoomConfiguration> Occupancy { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerConfigurationId")]
        public string ProviderConfigurationId { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class BedConfiguration
    {

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public string Code { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("bedTypes")]
        public System.Collections.Generic.ICollection<BedType> BedTypes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("description")]
        public string Description { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class BedType
    {

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string Name { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("description")]
        public string Description { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CancellationInfo
    {

        [System.Text.Json.Serialization.JsonPropertyName("cancellationDetails")]
        public System.Collections.Generic.ICollection<CancellationInfoDetails> CancellationDetails { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CancellationInfoDetails
    {

        [System.Text.Json.Serialization.JsonPropertyName("until")]
        public DateOnly Until { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("price")]
        public Price Price { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CheapestSearchResult
    {

        [System.Text.Json.Serialization.JsonPropertyName("checkIn")]
        public DateOnly CheckIn { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("stayLength")]
        public int StayLength { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("occupancies")]
        public System.Collections.Generic.ICollection<RoomOccupancy> Occupancies { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("hotelOffers")]
        public System.Collections.Generic.ICollection<Hotel> HotelOffers { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Hotel
    {

        [System.Text.Json.Serialization.JsonPropertyName("metaCode")]
        public int MetaCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("roomOfferByMealPlanByRefundability")]
        public RoomOfferByMealPlanByRefundability RoomOfferByMealPlanByRefundability { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Money
    {

        [System.Text.Json.Serialization.JsonPropertyName("value")]
        public decimal Value { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("currency")]
        public string Currency { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class RoomOccupancy
    {

        [System.Text.Json.Serialization.JsonPropertyName("adults")]
        public int Adults { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("childrenAges")]
        public System.Collections.Generic.ICollection<int> ChildrenAges { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class RoomOffer
    {

        [System.Text.Json.Serialization.JsonPropertyName("metaRoomIds")]
        public System.Collections.Generic.ICollection<string> MetaRoomIds { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("price")]
        public Money Price { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("availability")]
        public int Availability { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerConfigurationId")]
        public string ProviderConfigurationId { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Context
    {

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("contextsApplied")]
        // TODO(system.text.json): Add string enum item converter
        public System.Collections.Generic.ICollection<Context2> ContextsApplied { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelDetailsResult
    {

        [System.Text.Json.Serialization.JsonPropertyName("providerHotels")]
        public System.Collections.Generic.ICollection<ProviderHotel> ProviderHotels { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ProviderHotel
    {

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public ProviderCode ProviderCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("hotelName")]
        public string HotelName { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("rooms")]
        public System.Collections.Generic.ICollection<ProviderRoom> Rooms { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ProviderRoom
    {

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public string Id { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string Name { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("giataRoomName")]
        public string GiataRoomName { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("description")]
        public string Description { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("roomType")]
        public string RoomType { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("roomClass")]
        public string RoomClass { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("photos")]
        public System.Collections.Generic.ICollection<Photos> Photos { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("facilities")]
        public System.Collections.Generic.ICollection<string> Facilities { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("bedConfigurations")]
        public System.Collections.Generic.ICollection<BedConfiguration> BedConfigurations { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("areaInSqMeters")]
        public int? AreaInSqMeters { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("areaInSqFeets")]
        public int? AreaInSqFeets { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxAdults")]
        public int? MaxAdults { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxChildren")]
        public int? MaxChildren { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelVariantsResult
    {

        [System.Text.Json.Serialization.JsonPropertyName("hotelMetaCode")]
        public int HotelMetaCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("offers")]
        public System.Collections.Generic.ICollection<Variant> Offers { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("context")]
        public Context Context { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Room
    {

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public string Id { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string Name { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("description")]
        public string Description { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("bedConfigurations")]
        public System.Collections.Generic.ICollection<BedConfiguration> BedConfigurations { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("roomConfiguration")]
        public RoomConfiguration RoomConfiguration { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("photos")]
        public System.Collections.Generic.ICollection<Photos2> Photos { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("facilities")]
        public System.Collections.Generic.ICollection<string> Facilities { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("areaInSqMeters")]
        public int? AreaInSqMeters { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("areaInSqFeets")]
        public int? AreaInSqFeets { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxAdults")]
        public int? MaxAdults { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxChildren")]
        public int? MaxChildren { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Variant
    {

        [System.Text.Json.Serialization.JsonPropertyName("offerId")]
        public string OfferId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("packageId")]
        public string PackageId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("hotelPricingLocator")]
        public string HotelPricingLocator { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("qualifiedHotelCode")]
        public int QualifiedHotelCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("offerPrice")]
        public Price OfferPrice { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("priceDetails")]
        public PriceDetails PriceDetails { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("rooms")]
        public System.Collections.Generic.ICollection<Room> Rooms { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        public int ProviderCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("mealPlan")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public MealPlan MealPlan { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("paymentType")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public PaymentType PaymentType { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("cancellationInfo")]
        public CancellationInfo CancellationInfo { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("policies")]
        public System.Collections.Generic.ICollection<Information> Policies { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("importantInformations")]
        public string ImportantInformations { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("receivedAt")]
        public DateTime ReceivedAt { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Hotel2
    {

        [System.Text.Json.Serialization.JsonPropertyName("metaCode")]
        public int MetaCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("qualifiedCode")]
        public int QualifiedCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("roomPackages")]
        public System.Collections.Generic.ICollection<RoomPackage> RoomPackages { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HotelSearchResult
    {

        [System.Text.Json.Serialization.JsonPropertyName("currency")]
        public string Currency { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("hotels")]
        public System.Collections.Generic.ICollection<Hotel2> Hotels { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("context")]
        public Context Context { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Room2
    {

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string Name { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public string Code { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class RoomPackage
    {

        [System.Text.Json.Serialization.JsonPropertyName("offerId")]
        public string OfferId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerOfferGroup")]
        public string ProviderOfferGroup { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("rooms")]
        public System.Collections.Generic.ICollection<Room2> Rooms { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("price")]
        public Price Price { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("priceDetails")]
        public PriceDetails PriceDetails { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("paymentType")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public PaymentType PaymentType { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("mealPlan")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public MealPlan MealPlan { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("cancellationInfo")]
        public CancellationInfo CancellationInfo { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("receivedAt")]
        public DateTime ReceivedAt { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Information
    {

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public string Code { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("text")]
        public string Text { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Offer
    {

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public string Id { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("metaCode")]
        public int MetaCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("mealPlan")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public MealPlan MealPlan { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("stayInformation")]
        public StayInformation StayInformation { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("rooms")]
        public System.Collections.Generic.ICollection<OfferRoom> Rooms { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        [System.Obsolete]
        public int ProviderCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("bookingLocator")]
        [System.Obsolete]
        public string BookingLocator { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class OfferRoom
    {

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public string Id { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("quantity")]
        public int Quantity { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class OffersResult
    {

        [System.Text.Json.Serialization.JsonPropertyName("offers")]
        public System.Collections.Generic.ICollection<Offer> Offers { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("rooms")]
        public System.Collections.Generic.ICollection<Room3> Rooms { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Room3
    {

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public string Id { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string Name { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("description")]
        public string Description { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("roomType")]
        public string RoomType { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("roomClass")]
        public string RoomClass { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("photos")]
        public System.Collections.Generic.ICollection<Photos3> Photos { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("facilities")]
        public System.Collections.Generic.ICollection<string> Facilities { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("bedConfigurations")]
        public System.Collections.Generic.ICollection<BedConfiguration> BedConfigurations { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("areaInSqMeters")]
        public int? AreaInSqMeters { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("areaInSqFeets")]
        public int? AreaInSqFeets { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxAdults")]
        public int? MaxAdults { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maxChildren")]
        public int? MaxChildren { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceDetails
    {

        [System.Text.Json.Serialization.JsonPropertyName("currency")]
        public string Currency { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("netPrice")]
        public decimal NetPrice { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("taxPrice")]
        public decimal TaxPrice { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("extraCharges")]
        public decimal ExtraCharges { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerNetPrice")]
        public Price ProviderNetPrice { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ProviderHotelsSearchResult
    {

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        public ProviderCode ProviderCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("hotelOffers")]
        public System.Collections.Generic.ICollection<ProviderHotelOffers> HotelOffers { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ProviderHotelOffer
    {

        [System.Text.Json.Serialization.JsonPropertyName("offerId")]
        public string OfferId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("priceDetails")]
        public ProviderOfferPriceDetails PriceDetails { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("refundability")]
        public Refundability Refundability { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("receivedAt")]
        public DateTime ReceivedAt { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ProviderHotelOffers
    {

        [System.Text.Json.Serialization.JsonPropertyName("metaCode")]
        public int MetaCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("offers")]
        public System.Collections.Generic.ICollection<ProviderHotelOffer> Offers { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ProviderOfferPriceDetails
    {

        [System.Text.Json.Serialization.JsonPropertyName("currency")]
        public string Currency { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerNetPrice")]
        public decimal ProviderNetPrice { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("taxes")]
        public decimal Taxes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("extraCharges")]
        public decimal ExtraCharges { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Refundability
    {

        [System.Text.Json.Serialization.JsonPropertyName("isRefundable")]
        public bool IsRefundable { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("freeRefundUntil")]
        public DateOnly? FreeRefundUntil { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SearchContext
    {

        [System.Text.Json.Serialization.JsonPropertyName("stayInformation")]
        public StayInformation StayInformation { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("settingsKey")]
        public SettingsKey SettingsKey { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("roomsConfiguration")]
        public System.Collections.Generic.ICollection<RoomConfiguration> RoomsConfiguration { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SettingsKey
    {

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flightBookingId")]
        public string FlightBookingId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("mobileDevice")]
        public bool? MobileDevice { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("userId")]
        public string UserId { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum Context2
    {

        [System.Runtime.Serialization.EnumMember(Value = @"CROSSSELLING")]
        CROSSSELLING = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"MOBILE")]
        MOBILE = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"MEMBER")]
        MEMBER = 2,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum MealPlan
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Unknown")]
        Unknown = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"None")]
        None = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"Breakfast")]
        Breakfast = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"HalfBoard")]
        HalfBoard = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"FullBoard")]
        FullBoard = 4,

        [System.Runtime.Serialization.EnumMember(Value = @"AllInclusive")]
        AllInclusive = 5,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum PaymentType
    {

        [System.Runtime.Serialization.EnumMember(Value = @"Unknown")]
        Unknown = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"OnSite")]
        OnSite = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"AtCheckout")]
        AtCheckout = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"Installments")]
        Installments = 3,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Photo
    {

        [System.Text.Json.Serialization.JsonPropertyName("src")]
        public string Src { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("width")]
        public int Width { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("height")]
        public int Height { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Price
    {

        [System.Text.Json.Serialization.JsonPropertyName("amount")]
        public decimal Amount { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("currency")]
        public string Currency { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum ProviderCode
    {

        [System.Runtime.Serialization.EnumMember(Value = @"BookingCom")]
        BookingCom = 0,

        [System.Runtime.Serialization.EnumMember(Value = @"Expedia")]
        Expedia = 1,

        [System.Runtime.Serialization.EnumMember(Value = @"HotelBeds")]
        HotelBeds = 2,

        [System.Runtime.Serialization.EnumMember(Value = @"Travolutionary")]
        Travolutionary = 3,

        [System.Runtime.Serialization.EnumMember(Value = @"TestHouseExpress")]
        TestHouseExpress = 4,

        [System.Runtime.Serialization.EnumMember(Value = @"Anixe")]
        Anixe = 5,

        [System.Runtime.Serialization.EnumMember(Value = @"RateHawk")]
        RateHawk = 6,

        [System.Runtime.Serialization.EnumMember(Value = @"WebBeds")]
        WebBeds = 7,

        [System.Runtime.Serialization.EnumMember(Value = @"OTS")]
        OTS = 8,

        [System.Runtime.Serialization.EnumMember(Value = @"Alturabeds")]
        Alturabeds = 9,

        [System.Runtime.Serialization.EnumMember(Value = @"Yalago")]
        Yalago = 10,

        [System.Runtime.Serialization.EnumMember(Value = @"TravelgateDodo")]
        TravelgateDodo = 11,

        [System.Runtime.Serialization.EnumMember(Value = @"TravelgateW2M")]
        TravelgateW2M = 12,

        [System.Runtime.Serialization.EnumMember(Value = @"TravelgateB4G")]
        TravelgateB4G = 13,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class RoomConfiguration
    {

        [System.Text.Json.Serialization.JsonPropertyName("adults")]
        public int Adults { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("childrenAges")]
        public System.Collections.Generic.ICollection<int> ChildrenAges { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class StayInformation
    {

        [System.Text.Json.Serialization.JsonPropertyName("checkInDate")]
        public DateTime CheckInDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("checkOutDate")]
        public DateTime CheckOutDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("nights")]
        public int Nights { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ProblemDetails
    {

        [System.Text.Json.Serialization.JsonPropertyName("type")]
        public string Type { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("title")]
        public string Title { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public int? Status { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("detail")]
        public string Detail { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("instance")]
        public string Instance { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class RoomOfferByMealPlanByRefundability
    {

        [System.Text.Json.Serialization.JsonPropertyName("Unknown")]
        public System.Collections.Generic.IDictionary<string, RoomOffer> Unknown { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("None")]
        public System.Collections.Generic.IDictionary<string, RoomOffer> None { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("Breakfast")]
        public System.Collections.Generic.IDictionary<string, RoomOffer> Breakfast { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("HalfBoard")]
        public System.Collections.Generic.IDictionary<string, RoomOffer> HalfBoard { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("FullBoard")]
        public System.Collections.Generic.IDictionary<string, RoomOffer> FullBoard { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("AllInclusive")]
        public System.Collections.Generic.IDictionary<string, RoomOffer> AllInclusive { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Photos
    {

        [System.Text.Json.Serialization.JsonPropertyName("Thumbnail")]
        public Photo Thumbnail { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("Medium")]
        public Photo Medium { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("Large")]
        public Photo Large { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Photos2
    {

        [System.Text.Json.Serialization.JsonPropertyName("Thumbnail")]
        public Photo Thumbnail { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("Medium")]
        public Photo Medium { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("Large")]
        public Photo Large { get; set; }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Photos3
    {

        [System.Text.Json.Serialization.JsonPropertyName("Thumbnail")]
        public Photo Thumbnail { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("Medium")]
        public Photo Medium { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("Large")]
        public Photo Large { get; set; }

    }



    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ApiException : System.Exception
    {
        public int StatusCode { get; private set; }

        public string Response { get; private set; }

        public System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> Headers { get; private set; }

        public ApiException(string message, int statusCode, string response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Exception innerException)
            : base(message + "\n\nStatus: " + statusCode + "\nResponse: \n" + ((response == null) ? "(null)" : response.Substring(0, response.Length >= 512 ? 512 : response.Length)), innerException)
        {
            StatusCode = statusCode;
            Response = response;
            Headers = headers;
        }

        public override string ToString()
        {
            return string.Format("HTTP Response: \n\n{0}\n\n{1}", Response, base.ToString());
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.4.0.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ApiException<TResult> : ApiException
    {
        public TResult Result { get; private set; }

        public ApiException(string message, int statusCode, string response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, TResult result, System.Exception innerException)
            : base(message, statusCode, response, headers, innerException)
        {
            Result = result;
        }
    }

}

#pragma warning restore  108
#pragma warning restore  114
#pragma warning restore  472
#pragma warning restore  612
#pragma warning restore  649
#pragma warning restore 1573
#pragma warning restore 1591
#pragma warning restore 8073
#pragma warning restore 3016
#pragma warning restore 8600
#pragma warning restore 8602
#pragma warning restore 8603
#pragma warning restore 8604
#pragma warning restore 8625
#pragma warning restore 8765