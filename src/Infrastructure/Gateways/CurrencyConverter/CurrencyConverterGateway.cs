using System.Text.Json;
using Esky.Packages.Application.Abstractions.Gateways.CurrencyConverter;
using Esky.Packages.Infrastructure.HttpClients.Constants;

namespace Esky.Packages.Infrastructure.Gateways.CurrencyConverter;

public class CurrencyConverterGateway(IHttpClientFactory httpClientFactory) : ICurrencyConverterGateway
{
    public async Task<List<CurrencyRate>> GetRates(CancellationToken cancellationToken)
    {
        using var httpClient = httpClientFactory.CreateClient(ApiHttpClientConsts.CurrencyConverterClient);
        var response = await httpClient.GetAsync("/api/Rates", cancellationToken);

        response.EnsureSuccessStatusCode();
        
        var result = JsonSerializer.Deserialize<ApiRatesResult>(await response.Content.ReadAsStreamAsync(cancellationToken),
            new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });

        var rates = new List<CurrencyRate>();
        
        if (result is { IsSuccess: true })
        {
            var quotes = result.Data ?? [];
            rates.AddRange(quotes.Select(quote => new CurrencyRate(quote.From!, quote.To!, quote.Rate)));
        }

        return rates;
    }
}

internal class ApiRatesResult
{
    public ApiRatesQuote[]? Data { get; set; }
    public bool IsSuccess { get; set; }
}

internal class ApiRatesQuote
{
    public int ProviderType { get; set; }
    public string? From { get; set; }
    public string? To { get; set; }
    public decimal Rate { get; set; }
}