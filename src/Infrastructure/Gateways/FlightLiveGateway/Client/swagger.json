{"openapi": "3.0.1", "info": {"title": "Flight Search Api", "version": "v1"}, "paths": {"/api/v1.0/FlightQuery/PackagedSearch": {"post": {"tags": ["FlightQuery"], "summary": "Search for flights and retrieve results in portions (packages)", "description": "**[search-client.html](/search-client-nfsr.html)**", "requestBody": {"description": "Search request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.PackagedSearch.Request.PackagedSearchQuery"}, "example": {"partnerCode": "ESKY", "currencyCode": "PLN", "legs": [{"departureCode": "WAW", "departureDate": "2025-03-12", "arrivalCode": "LON"}, {"departureCode": "LON", "departureDate": "2025-03-19", "arrivalCode": "WAW"}], "passengers": {"adults": 1}, "searchId": "8dc09500-8ce5-4859-bb9e-d352ec170db2", "packageNumber": 1, "sessionId": "DBG336ce290"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.PackagedSearch.Response.PackagedSearchResponse"}}}}}}}, "/api/v1.0/FlightQuery/FareFamilies/Fetch": {"post": {"tags": ["FlightQuery"], "summary": "Fetch fare families for a given flight", "requestBody": {"description": "Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.FareFamilies.Request.FareFamiliesQuery"}, "example": {"partnerCode": "ESKY", "currencyCode": "PLN", "providerCode": 80, "legs": [{"segments": [{"departureCode": "WAW", "departureDate": "2025-03-12T12:35:00", "arrivalCode": "CDG", "arrivalDate": "2025-03-12T14:10:00", "airlineCode": "LH", "flightNumber": "1234", "fareDetails": {"fareCode": "ABCD123", "officeId": "WAW1234", "offerId": "offerId1"}}]}, {"segments": [{"departureCode": "CDG", "departureDate": "2025-03-19T08:15:00", "arrivalCode": "WAW", "arrivalDate": "2025-03-19T10:20:00", "airlineCode": "LH", "flightNumber": "5678", "fareDetails": {"fareCode": "ABCD567", "officeId": "WAW1234", "offerId": "offerId1"}}]}], "passengers": {"adults": 2, "youths": 1, "children": 1, "infants": 1}, "sessionId": "DBG69f613ef", "requestSource": {"searchKind": 1, "openSearchSource": 1}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.FareFamilies.Response.FareFamiliesResponse"}}}}}}}, "/api/v1.0/FlightQuery/OpenSearch": {"post": {"tags": ["FlightQuery"], "summary": "Search for flights and retrieve results based on open search criteria", "requestBody": {"description": "Open search request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.OpenSearchFeatures.OpenSearch.Request.OpenSearchQuery"}, "example": {"partnerCode": "ESKY", "currencyCode": "PLN", "legs": [{"departureCode": "WAW", "departureDate": "2025-03-01", "arrivalCode": "LON"}, {"departureCode": "LON", "departureDate": "2025-03-11", "arrivalCode": "WAW"}], "passengers": {"adults": 1}, "flex": 3, "openDates": {"months": [], "stayLengths": [3, 4, 5]}, "sessionId": "DBG040c25e8", "source": 0}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.OpenSearchFeatures.OpenSearch.Response.OpenSearchResponse"}}}}}}}, "/api/v1.0/FlightQuery/PriceCharts": {"post": {"tags": ["FlightQuery"], "summary": "Return price charts results based on open search criteria", "requestBody": {"description": "Price charts request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.OpenSearchFeatures.PriceCharts.Request.PriceChartsQuery"}, "example": {"partnerCode": "ESKY", "currencyCode": "PLN", "legs": [{"departureCode": "WAW", "arrivalCode": "LON"}, {"departureCode": "LON", "arrivalCode": "WAW"}], "passengers": {"adults": 1}, "stayLengths": [3, 4, 5], "filters": {"airlineCodes": ["LO"], "transfers": [0, 1], "travelTimeDepartureOutbound": [{"from": 8, "to": 12}, {"from": 12, "to": 18}], "travelTimeArrivalOutbound": [{"from": 8, "to": 12}, {"from": 12, "to": 18}], "travelTimeDepartureInbound": [{"from": 1, "to": 8}, {"from": 8, "to": 12}], "travelTimeArrivalInbound": [{"from": 1, "to": 8}, {"from": 8, "to": 12}], "providerCodes": []}, "sessionId": "DBGe74f7cfc"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.OpenSearchFeatures.PriceCharts.Response.PriceChartsResponse"}}}}}}}, "/api/v1.0/FlightQuery/BestOffers": {"post": {"tags": ["FlightQuery"], "summary": "Returns best offers based on search criteria", "requestBody": {"description": "Best offers request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.BestOffers.BestOffersQuery"}, "example": {"partnerCode": "ESKY", "currencyCode": "PLN", "originCountryCodes": ["PL"], "destinationCountryCodes": ["ES", "IT", "PT"], "sessionId": "DBG86c8cce6"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.BestOffers.BestOffersResponse"}}}}}}}, "/api/v1.0/FlightQuery/PriceCalendar": {"post": {"tags": ["FlightQuery"], "summary": "Returns price calendar with cheapest flights prices", "requestBody": {"description": "Price calendar request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.AlternativeOffers.PriceCalendar.PriceCalendarQuery"}, "example": {"partnerCode": "ESKY", "currencyCode": "PLN", "legs": [{"departureCode": "WAW", "departureDate": "2025-02-17", "arrivalCode": "LON"}, {"departureCode": "LON", "departureDate": "2025-02-20", "arrivalCode": "WAW"}], "passengers": {"adults": 2}, "sessionId": "DBGb2ec1e62"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.AlternativeOffers.PriceCalendar.PriceCalendarResponse"}}}}}}}, "/api/v1.0/FlightQuery/PriceCalendar/Offer": {"post": {"tags": ["FlightQuery"], "summary": "Returns price calendar offer", "requestBody": {"description": "Price calendar offer request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.AlternativeOffers.PriceCalendarOffer.PriceCalendarOfferQuery"}, "example": {"partnerCode": "ESKY", "currencyCode": "PLN", "legs": [{"departureCode": "WAW", "departureDate": "2025-02-17", "arrivalCode": "LON"}, {"departureCode": "LON", "departureDate": "2025-02-20", "arrivalCode": "WAW"}], "passengers": {"adults": 2}, "sessionId": "DBG85fde325"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.AlternativeOffers.PriceCalendarOffer.PriceCalendarOfferResponse"}}}}}}}, "/api/v1.0/FlightQuery/AlternativeAirports": {"post": {"tags": ["FlightQuery"], "summary": "Returns alternative airports flights", "requestBody": {"description": "Alternative airports request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.AlternativeOffers.AlternativeAirports.AlternativeAirportsQuery"}, "example": {"partnerCode": "ESKY", "currencyCode": "PLN", "legs": [{"departureCode": "KRK", "departureDate": "2025-02-17", "arrivalCode": "LTN"}, {"departureCode": "LTN", "departureDate": "2025-02-20", "arrivalCode": "KRK"}], "passengers": {"adults": 2}, "sessionId": "DBG9bb1f279"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.AlternativeOffers.AlternativeAirports.AlternativeAirportsResponse"}}}}}}}, "/api/v1.0/GoogleLiveSearch/GFSAPI": {"post": {"tags": ["GoogleLiveSearch"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Request.GoogleLiveSearchRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Request.GoogleLiveSearchRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Request.GoogleLiveSearchRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Request.GoogleLiveSearchRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.GoogleLiveSearchResponse"}}}}}}}, "/api/v1.0/Legacy/SearchFlights": {"post": {"tags": ["Legacy"], "summary": "Search for flights from cache and providers", "description": "**[search-client.html](/search-client.html)**", "requestBody": {"description": "Search request", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.Search.Queries.SearchFlights.SearchFlightsQuery"}, "example": {"partnerCode": "ESKY", "currencyCode": "PLN", "languageCode": "PL", "sessionId": "DBG1fa01572-7", "priceType": 14, "legs": [{"departureCode": "WAW", "departureDate": "2025-04-08T00:00:00Z", "arrivalCode": "LON"}, {"departureCode": "LON", "departureDate": "2025-04-15T00:00:00Z", "arrivalCode": "WAW"}], "flex": {"enabled": true, "range": 3, "limit": 5}, "passengers": [{"code": 1, "count": 1}]}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.Search.Queries.SearchFlights.SearchFlightsQuery"}, "example": {"partnerCode": "ESKY", "currencyCode": "PLN", "languageCode": "PL", "sessionId": "DBG1fa01572-7", "priceType": 14, "legs": [{"departureCode": "WAW", "departureDate": "2025-04-08T00:00:00Z", "arrivalCode": "LON"}, {"departureCode": "LON", "departureDate": "2025-04-15T00:00:00Z", "arrivalCode": "WAW"}], "flex": {"enabled": true, "range": 3, "limit": 5}, "passengers": [{"code": 1, "count": 1}]}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.Search.Queries.SearchFlights.SearchFlightsQuery"}, "example": {"partnerCode": "ESKY", "currencyCode": "PLN", "languageCode": "PL", "sessionId": "DBG1fa01572-7", "priceType": 14, "legs": [{"departureCode": "WAW", "departureDate": "2025-04-08T00:00:00Z", "arrivalCode": "LON"}, {"departureCode": "LON", "departureDate": "2025-04-15T00:00:00Z", "arrivalCode": "WAW"}], "flex": {"enabled": true, "range": 3, "limit": 5}, "passengers": [{"code": 1, "count": 1}]}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.Search.Queries.SearchFlights.SearchFlightsQuery"}, "example": {"partnerCode": "ESKY", "currencyCode": "PLN", "languageCode": "PL", "sessionId": "DBG1fa01572-7", "priceType": 14, "legs": [{"departureCode": "WAW", "departureDate": "2025-04-08T00:00:00Z", "arrivalCode": "LON"}, {"departureCode": "LON", "departureDate": "2025-04-15T00:00:00Z", "arrivalCode": "WAW"}], "flex": {"enabled": true, "range": 3, "limit": 5}, "passengers": [{"code": 1, "count": 1}]}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.Search.Queries.SearchFlights.Response.SearchFlightsResponseGrouped"}}}}}}}, "/api/v1.0/OfferLiveCheck/ByFlightKeys": {"post": {"tags": ["OfferLiveCheck"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.OfferLiveCheck.Contract.OfferLiveCheckRequest"}, "example": {"flightOfferKeys": ["LISBER250312145.4||VY8463|VY1886:1:0"], "passengers": [{"code": 1, "count": 2}, {"code": 2, "count": 1}], "partnerCode": "ESKY", "usePartnerCurrency": true}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.OfferLiveCheck.Contract.OfferLiveCheckRequest"}, "example": {"flightOfferKeys": ["LISBER250312145.4||VY8463|VY1886:1:0"], "passengers": [{"code": 1, "count": 2}, {"code": 2, "count": 1}], "partnerCode": "ESKY", "usePartnerCurrency": true}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.OfferLiveCheck.Contract.OfferLiveCheckRequest"}, "example": {"flightOfferKeys": ["LISBER250312145.4||VY8463|VY1886:1:0"], "passengers": [{"code": 1, "count": 2}, {"code": 2, "count": 1}], "partnerCode": "ESKY", "usePartnerCurrency": true}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.OfferLiveCheck.Contract.OfferLiveCheckRequest"}, "example": {"flightOfferKeys": ["LISBER250312145.4||VY8463|VY1886:1:0"], "passengers": [{"code": 1, "count": 2}, {"code": 2, "count": 1}], "partnerCode": "ESKY", "usePartnerCurrency": true}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.OfferLiveCheck.Contract.OfferLiveCheckResponse"}}}}}}}, "/api/v1.0/OfferLiveCheck/ByFlightKeysWithTF": {"post": {"tags": ["OfferLiveCheck"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.OfferLiveCheck.Contract.OfferLiveCheckRequest"}, "example": {"flightOfferKeys": ["LISBER250312145.4||VY8463|VY1886:1:0"], "passengers": [{"code": 1, "count": 2}, {"code": 2, "count": 1}], "partnerCode": "ESKY", "usePartnerCurrency": true}}, "application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.OfferLiveCheck.Contract.OfferLiveCheckRequest"}, "example": {"flightOfferKeys": ["LISBER250312145.4||VY8463|VY1886:1:0"], "passengers": [{"code": 1, "count": 2}, {"code": 2, "count": 1}], "partnerCode": "ESKY", "usePartnerCurrency": true}}, "text/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.OfferLiveCheck.Contract.OfferLiveCheckRequest"}, "example": {"flightOfferKeys": ["LISBER250312145.4||VY8463|VY1886:1:0"], "passengers": [{"code": 1, "count": 2}, {"code": 2, "count": 1}], "partnerCode": "ESKY", "usePartnerCurrency": true}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.OfferLiveCheck.Contract.OfferLiveCheckRequest"}, "example": {"flightOfferKeys": ["LISBER250312145.4||VY8463|VY1886:1:0"], "passengers": [{"code": 1, "count": 2}, {"code": 2, "count": 1}], "partnerCode": "ESKY", "usePartnerCurrency": true}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.OfferLiveCheck.Contract.OfferLiveCheckResponse"}}}}}}}}, "components": {"schemas": {"Esky.FlightQuery.Worker.Common.QueryServiceClass": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "Esky.FlightQuery.Worker.PackagedSearch.Request.Leg": {"required": ["arrivalCode", "departureCode", "departureDate"], "type": "object", "properties": {"departureCode": {"type": "string", "description": "Departure code", "nullable": true}, "departureDate": {"type": "string", "description": "Departure date", "format": "date-time"}, "arrivalCode": {"type": "string", "description": "Arrival code", "nullable": true}}}, "Esky.FlightQuery.Worker.Common.Request.Passengers": {"type": "object", "properties": {"adults": {"type": "integer", "description": "Number of adults", "format": "int32"}, "youths": {"type": "integer", "description": "Number of youths", "format": "int32"}, "children": {"type": "integer", "description": "Number of children", "format": "int32"}, "infants": {"type": "integer", "description": "Number of infants", "format": "int32"}, "childrenAges": {"type": "array", "items": {"type": "integer", "format": "int32"}, "description": "Each number represents a child age. Array length determines number of children", "nullable": true}}}, "Esky.FlightQuery.Worker.Common.PriceType": {"enum": [0, 1], "type": "integer", "format": "int32"}, "Esky.FlightQuery.Worker.Common.RuntimeMode": {"enum": [0, 1], "type": "integer", "format": "int32"}, "Esky.FlightQuery.Worker.PackagedSearch.Request.PackagedSearchQuery": {"required": ["currencyCode", "legs", "packageNumber", "partnerCode", "passengers", "runtimeMode", "searchId", "serviceClass"], "type": "object", "properties": {"partnerCode": {"type": "string", "description": "Partner code", "nullable": true}, "currencyCode": {"type": "string", "description": "Currency code", "nullable": true}, "serviceClass": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.QueryServiceClass"}, "legs": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.PackagedSearch.Request.Leg"}, "description": "Leg collection", "nullable": true}, "passengers": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Request.Passengers"}, "isPricePerPax": {"type": "boolean", "description": "Is price per passenger?"}, "priceType": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.PriceType"}, "searchId": {"type": "string", "description": "Search instance identifier", "nullable": true}, "packageNumber": {"type": "integer", "description": "Package number (one-based)", "format": "int32"}, "runtimeMode": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.RuntimeMode"}, "sessionId": {"type": "string", "description": "Session identifier", "nullable": true}}}, "Esky.FlightQuery.Worker.Common.Response.PriceModel": {"required": ["amount", "transactionFee"], "type": "object", "properties": {"amount": {"type": "number", "description": "Flight price amount", "format": "decimal"}, "transactionFee": {"type": "number", "description": "Transaction fee amount", "format": "decimal"}}}, "Esky.FlightQuery.Worker.Common.Response.Stopover": {"required": ["airportCode"], "type": "object", "properties": {"airportCode": {"type": "string", "nullable": true}, "arrivalDate": {"type": "string", "format": "date-time", "nullable": true}, "departureDate": {"type": "string", "format": "date-time", "nullable": true}}}, "Esky.FlightQuery.Worker.Common.Response.FareDetails": {"type": "object", "properties": {"fareCode": {"type": "string", "nullable": true}, "officeId": {"type": "string", "nullable": true}, "offerId": {"type": "string", "nullable": true}}}, "Esky.FlightQuery.Worker.Common.FlightServiceClass": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "Esky.FlightQuery.Worker.Common.CarrierType": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "Esky.FlightQuery.Worker.Common.Response.Segment": {"required": ["airlineCode", "arrivalCode", "arrivalDate", "bookingClass", "carrierType", "departureCode", "departureDate", "durationMinutes", "flightNumber", "isSelfTransfer", "providerCode", "serviceClass", "stopovers"], "type": "object", "properties": {"airlineCode": {"type": "string", "description": "Marketing airline code", "nullable": true}, "operatedBy": {"type": "string", "description": "Operating airline code", "nullable": true}, "departureCode": {"type": "string", "description": "Departure location code\r\n3-letter IATA airport code", "nullable": true}, "departureDate": {"type": "string", "description": "Departure date", "format": "date-time"}, "arrivalCode": {"type": "string", "description": "Arrival location code\r\n3-letter IATA airport code", "nullable": true}, "arrivalDate": {"type": "string", "description": "Arrival date", "format": "date-time"}, "durationMinutes": {"type": "integer", "description": "Journey duration in minutes", "format": "int32"}, "flightNumber": {"type": "string", "description": "Flight number", "nullable": true}, "providerCode": {"type": "integer", "description": "Provider code", "format": "int32"}, "stopovers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Response.Stopover"}, "description": "Stopovers", "nullable": true}, "fareDetails": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Response.FareDetails"}, "bookingClass": {"type": "string", "description": "Booking class code", "nullable": true}, "serviceClass": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.FlightServiceClass"}, "carrierType": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.CarrierType"}, "isSelfTransfer": {"type": "boolean", "description": "Is self transfer?"}}}, "Esky.FlightQuery.Worker.Common.NearbyAirportsType": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "Esky.FlightQuery.Worker.Common.Response.Leg": {"required": ["durationMinutes", "id", "offerId", "segments"], "type": "object", "properties": {"id": {"type": "string", "description": "Leg identifier", "nullable": true}, "offerId": {"type": "string", "description": "Unique offer identifier in search results scope. Should return the same value across search requests.", "nullable": true}, "segments": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Response.Segment"}, "description": "Segment collection", "nullable": true}, "durationMinutes": {"type": "integer", "description": "Journey duration in minutes", "format": "int32"}, "availableSeatsCount": {"type": "integer", "description": "Number of seats available", "format": "int32", "nullable": true}, "legLocator": {"type": "string", "description": "Encoded leg locator", "nullable": true}, "nqs": {"type": "number", "description": "Negative quality score", "format": "decimal"}, "nearbyAirports": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.NearbyAirportsType"}, "isNearbyDate": {"type": "boolean", "description": "Is the departure date different from requested date (flex result)"}, "fareFamilyName": {"type": "string", "description": "<PERSON><PERSON> family name", "nullable": true}}}, "Esky.FlightQuery.Worker.Common.FlightMixingType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "Esky.FlightQuery.Worker.Common.FlightSource": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "Esky.FlightQuery.Worker.Common.Response.DebugEntry": {"required": ["label", "value"], "type": "object", "properties": {"label": {"type": "string", "description": "Debug data label", "nullable": true}, "value": {"type": "string", "description": "Debug data value", "nullable": true}, "tooltip": {"type": "string", "description": "Debug data tooltip", "nullable": true}}}, "Esky.FlightQuery.Worker.Common.Response.TFModel": {"type": "object", "properties": {"project": {"type": "string", "nullable": true}, "partner": {"type": "string", "nullable": true}, "deploymentId": {"type": "string", "nullable": true}, "currency": {"type": "string", "nullable": true}, "other": {"type": "string", "nullable": true}, "deviceType": {"type": "array", "items": {"type": "string"}, "nullable": true}, "tfChange": {"type": "number", "format": "decimal"}, "jsonWithEncryptedEquation": {"type": "object", "nullable": true}, "adsv": {"type": "string", "nullable": true}}}, "Esky.FlightQuery.Worker.Common.Response.Flight": {"required": ["fareFamilyName", "flightDeduplicationId", "id", "legs", "mixingType", "nqs", "offerId", "price", "providerCode", "relatedFlights", "source"], "type": "object", "properties": {"id": {"type": "string", "description": "Flight identifier", "nullable": true}, "providerCode": {"type": "integer", "description": "Provider code", "format": "int32"}, "validatingCarrier": {"type": "string", "description": "Validating carrier", "nullable": true}, "price": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Response.PriceModel"}, "legs": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Response.Leg"}, "description": "Leg collection", "nullable": true}, "mixingType": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.FlightMixingType"}, "nqs": {"type": "number", "description": "Negative quality score", "format": "decimal"}, "source": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.FlightSource"}, "flightDeduplicationId": {"type": "string", "description": "Groups flights from duplication aspect. Flights with the same value should be overridden by newer package", "nullable": true}, "relatedFlights": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Response.Flight"}, "description": "Related Flights with different fare family name", "nullable": true}, "fareFamilyName": {"type": "string", "description": "<PERSON><PERSON> family name", "nullable": true}, "offerId": {"type": "string", "description": "Offer identifier, will remain the same for the same flight between searches", "nullable": true}, "debug": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Response.DebugEntry"}, "description": "Flight debug data", "nullable": true}, "tfModel": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Response.TFModel"}}}, "Esky.FlightQuery.Worker.Common.Response.NqsModel": {"type": "object", "properties": {"modelId": {"type": "string", "nullable": true}, "modelVersionId": {"type": "string", "nullable": true}, "deployedModelId": {"type": "string", "nullable": true}}}, "Esky.FlightQuery.Worker.PackagedSearch.Response.PackagedSearchResponse": {"required": ["flights", "isLastPackage"], "type": "object", "properties": {"flights": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Response.Flight"}, "description": "Flight collection", "nullable": true}, "nqsModel": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Response.NqsModel"}, "isLastPackage": {"type": "boolean", "description": "Determines whether there will be more search results (true) or if the search is finished and all results have been returned (false)"}}}, "Esky.Settings.Enums.ProviderCodeEnum": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 77, 78, 80, 81, 82, 83, 84, 88, 99, 100, 101, 102, 103, 110, 111, 112, 113, 116, 117, 118, 119, 120, 121, 122, 123, 124, 201], "type": "integer", "format": "int32"}, "Esky.FlightQuery.Worker.FareFamilies.Request.FareDetails": {"type": "object", "properties": {"fareCode": {"type": "string", "description": "Fare code", "nullable": true}, "officeId": {"type": "string", "description": "Office identifier", "nullable": true}, "offerId": {"type": "string", "description": "Offer identifier", "nullable": true}}, "description": "Fare details"}, "Esky.FlightQuery.Worker.FareFamilies.Request.Segment": {"required": ["airlineCode", "arrivalCode", "arrivalDate", "departureCode", "departureDate", "flightNumber"], "type": "object", "properties": {"departureCode": {"type": "string", "description": "Departure code", "nullable": true}, "departureDate": {"type": "string", "description": "Departure date/time - airport local time", "format": "date-time"}, "arrivalCode": {"type": "string", "description": "Arrival code", "nullable": true}, "arrivalDate": {"type": "string", "description": "Arrival date/time - airport local time", "format": "date-time"}, "airlineCode": {"type": "string", "description": "Airline code", "nullable": true}, "flightNumber": {"type": "string", "description": "Flight number", "nullable": true}, "fareDetails": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.FareFamilies.Request.FareDetails"}, "serviceClass": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.FlightServiceClass"}}}, "Esky.FlightQuery.Worker.FareFamilies.Request.Leg": {"type": "object", "properties": {"segments": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.FareFamilies.Request.Segment"}, "description": "Collection of segments", "nullable": true}}}, "Esky.FlightQuery.Worker.Common.SearchKind": {"enum": [0, 1], "type": "integer", "format": "int32"}, "Esky.FlightQuery.Worker.OpenSearchFeatures.Common.Request.OpenSearchRequestSource": {"enum": [0, 1, 2, 3, 4, 5], "type": "integer", "format": "int32"}, "Esky.FlightQuery.Worker.FareFamilies.Request.RequestSourceDetails": {"type": "object", "properties": {"searchKind": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.SearchKind"}, "openSearchSource": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.OpenSearchFeatures.Common.Request.OpenSearchRequestSource"}}}, "Esky.FlightQuery.Worker.FareFamilies.Request.FareFamiliesQuery": {"required": ["currencyCode", "legs", "partnerCode", "passengers", "providerCode", "runtimeMode"], "type": "object", "properties": {"partnerCode": {"type": "string", "description": "Partner code", "nullable": true}, "currencyCode": {"type": "string", "description": "Currency code", "nullable": true}, "providerCode": {"$ref": "#/components/schemas/Esky.Settings.Enums.ProviderCodeEnum"}, "legs": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.FareFamilies.Request.Leg"}, "description": "Leg collection", "nullable": true}, "passengers": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Request.Passengers"}, "isPricePerPax": {"type": "boolean", "description": "Is price per passenger?"}, "priceType": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.PriceType"}, "runtimeMode": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.RuntimeMode"}, "sessionId": {"type": "string", "description": "Session identifier", "nullable": true}, "requestSource": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.FareFamilies.Request.RequestSourceDetails"}}}, "Esky.FlightQuery.Worker.FareFamilies.Response.FareFamiliesResponse": {"required": ["flights"], "type": "object", "properties": {"flights": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Response.Flight"}, "description": "Flight collection", "nullable": true}}}, "Esky.FlightQuery.Worker.OpenSearchFeatures.OpenSearch.Request.Leg": {"required": ["arrivalCode", "departureCode"], "type": "object", "properties": {"departureCode": {"type": "string", "description": "Departure code", "nullable": true}, "departureDate": {"type": "string", "description": "Departure date, `null` if not specified", "nullable": true}, "arrivalCode": {"type": "string", "description": "Arrival code", "nullable": true}}}, "Esky.FlightQuery.Worker.OpenSearchFeatures.OpenSearch.Request.OpenDatesCriteria": {"required": ["months", "stayLengths"], "type": "object", "properties": {"months": {"type": "array", "items": {"type": "string"}, "description": "List of months for which search is performed", "nullable": true}, "stayLengths": {"type": "array", "items": {"type": "integer", "format": "int32"}, "description": "List of days after which return flight is searched", "nullable": true}}}, "Esky.FlightQuery.Worker.OpenSearchFeatures.OpenSearch.Request.OpenSearchQuery": {"required": ["currencyCode", "flex", "legs", "openDates", "partnerCode", "passengers", "runtimeMode", "serviceClass"], "type": "object", "properties": {"partnerCode": {"type": "string", "description": "Partner code", "nullable": true}, "currencyCode": {"type": "string", "description": "Currency code", "nullable": true}, "serviceClass": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.QueryServiceClass"}, "legs": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.OpenSearchFeatures.OpenSearch.Request.Leg"}, "description": "Leg collection", "nullable": true}, "passengers": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Request.Passengers"}, "isPricePerPax": {"type": "boolean", "description": "Is price per passenger?"}, "priceType": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.PriceType"}, "flex": {"type": "integer", "description": "Number of days within which flights are searched (+/- departure and return dates)", "format": "int32"}, "openDates": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.OpenSearchFeatures.OpenSearch.Request.OpenDatesCriteria"}, "runtimeMode": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.RuntimeMode"}, "sessionId": {"type": "string", "description": "Session identifier", "nullable": true}, "source": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.OpenSearchFeatures.Common.Request.OpenSearchRequestSource"}}}, "Esky.FlightQuery.Worker.OpenSearchFeatures.OpenSearch.Response.OpenSearchResponse": {"required": ["flights"], "type": "object", "properties": {"flights": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Response.Flight"}, "description": "Flight collection", "nullable": true}, "nqsModel": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Response.NqsModel"}}}, "Esky.FlightQuery.Worker.OpenSearchFeatures.PriceCharts.Request.Leg": {"required": ["arrivalCode", "departureCode"], "type": "object", "properties": {"departureCode": {"type": "string", "description": "Departure code", "nullable": true}, "arrivalCode": {"type": "string", "description": "Arrival code", "nullable": true}}}, "Esky.FlightQuery.Worker.OpenSearchFeatures.PriceCharts.Request.TransferOption": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "Esky.FlightQuery.Worker.OpenSearchFeatures.PriceCharts.Request.HourRange": {"type": "object", "properties": {"from": {"type": "integer", "format": "int32"}, "to": {"type": "integer", "format": "int32"}}}, "Esky.FlightQuery.Worker.OpenSearchFeatures.PriceCharts.Request.PriceChartsFilters": {"type": "object", "properties": {"airlineCodes": {"type": "array", "items": {"type": "string"}, "description": "Filter flights with given airline codes", "nullable": true}, "transfers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.OpenSearchFeatures.PriceCharts.Request.TransferOption"}, "description": "Filter flights that have corresponding number of transfers", "nullable": true}, "travelTimeDepartureOutbound": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.OpenSearchFeatures.PriceCharts.Request.HourRange"}, "description": "Hour ranges for outbound flight arrival", "nullable": true}, "travelTimeArrivalOutbound": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.OpenSearchFeatures.PriceCharts.Request.HourRange"}, "description": "Hour ranges for outbound flight departure", "nullable": true}, "travelTimeDepartureInbound": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.OpenSearchFeatures.PriceCharts.Request.HourRange"}, "description": "Hour ranges for return flight departure", "nullable": true}, "travelTimeArrivalInbound": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.OpenSearchFeatures.PriceCharts.Request.HourRange"}, "description": "Hour ranges for return flight arrival", "nullable": true}, "providerCodes": {"type": "array", "items": {"type": "integer", "format": "int32"}, "description": "Provider code list (for debug mode)", "nullable": true}}}, "Esky.FlightQuery.Worker.OpenSearchFeatures.PriceCharts.Request.PriceChartsQuery": {"required": ["currencyCode", "legs", "partnerCode", "passengers"], "type": "object", "properties": {"partnerCode": {"type": "string", "description": "Partner code", "nullable": true}, "currencyCode": {"type": "string", "description": "Currency code", "nullable": true}, "legs": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.OpenSearchFeatures.PriceCharts.Request.Leg"}, "description": "Leg collection", "nullable": true}, "passengers": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Request.Passengers"}, "stayLengths": {"type": "array", "items": {"type": "integer", "format": "int32"}, "description": "List of days after which return flight is searched", "nullable": true}, "filters": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.OpenSearchFeatures.PriceCharts.Request.PriceChartsFilters"}, "isPricePerPax": {"type": "boolean", "description": "Is price per passenger?"}, "priceType": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.PriceType"}, "sessionId": {"type": "string", "description": "Session identifier", "nullable": true}}}, "Esky.FlightQuery.Worker.OpenSearchFeatures.PriceCharts.Response.MonthlyPrice": {"required": ["amount", "month"], "type": "object", "properties": {"month": {"type": "string", "description": "Month to which the price applies"}, "amount": {"type": "number", "description": "Price of the flight in a given month", "format": "decimal"}}}, "Esky.FlightQuery.Worker.OpenSearchFeatures.PriceCharts.Response.PriceChartsResponse": {"required": ["monthlyPrices"], "type": "object", "properties": {"monthlyPrices": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.OpenSearchFeatures.PriceCharts.Response.MonthlyPrice"}, "description": "Monthly price model", "nullable": true}}}, "Esky.FlightQuery.Worker.BestOffers.BestOffersQuery": {"required": ["currencyCode", "destinationCountryCodes", "originCountryCodes", "partnerCode"], "type": "object", "properties": {"partnerCode": {"type": "string", "description": "Partner code", "nullable": true}, "currencyCode": {"type": "string", "description": "Currency code", "nullable": true}, "originCountryCodes": {"type": "array", "items": {"type": "string"}, "description": "List of codes for origin countries", "nullable": true}, "destinationCountryCodes": {"type": "array", "items": {"type": "string"}, "description": "List of codes for destination countries", "nullable": true}, "runtimeMode": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.RuntimeMode"}, "sessionId": {"type": "string", "description": "Session identifier", "nullable": true}}}, "Esky.FlightQuery.Worker.BestOffers.Origin": {"type": "object", "properties": {"airportCode": {"type": "string", "description": "Airport code", "nullable": true}, "cityCode": {"type": "string", "description": "City code", "nullable": true}}}, "Esky.FlightQuery.Worker.BestOffers.Destination": {"type": "object", "properties": {"airportCode": {"type": "string", "description": "Airport code", "nullable": true}, "multiportCode": {"type": "string", "description": "Multiport code", "nullable": true}, "cityCode": {"type": "string", "description": "City code", "nullable": true}, "countryCode": {"type": "string", "description": "Country code", "nullable": true}}}, "Esky.FlightQuery.Worker.BestOffers.Offer": {"type": "object", "properties": {"origin": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.BestOffers.Origin"}, "destination": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.BestOffers.Destination"}, "lowestPriceAmount": {"type": "number", "description": "Lowest price amount", "format": "decimal"}, "flightOfferId": {"type": "string", "description": "Offer identifier of the cheapest flight", "nullable": true}}}, "Esky.FlightQuery.Worker.BestOffers.BestOffersResponse": {"type": "object", "properties": {"currencyCode": {"type": "string", "description": "Currency code, common for all prices", "nullable": true}, "offers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.BestOffers.Offer"}, "description": "List of best offers", "nullable": true}}}, "Esky.FlightQuery.Worker.AlternativeOffers.PriceCalendar.Leg": {"required": ["arrivalCode", "departureCode", "departureDate"], "type": "object", "properties": {"departureCode": {"type": "string", "description": "Departure code", "nullable": true}, "departureDate": {"type": "string", "description": "Departure date"}, "arrivalCode": {"type": "string", "description": "Arrival code", "nullable": true}}}, "Esky.FlightQuery.Worker.AlternativeOffers.PriceCalendar.PriceCalendarQuery": {"required": ["currencyCode", "legs", "partnerCode", "passengers", "runtimeMode"], "type": "object", "properties": {"partnerCode": {"type": "string", "description": "Partner code", "nullable": true}, "currencyCode": {"type": "string", "description": "Currency code", "nullable": true}, "legs": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.AlternativeOffers.PriceCalendar.Leg"}, "description": "Leg collection", "nullable": true}, "passengers": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Request.Passengers"}, "isPricePerPax": {"type": "boolean", "description": "Is price per passenger?"}, "priceType": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.PriceType"}, "runtimeMode": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.RuntimeMode"}, "sessionId": {"type": "string", "description": "Session identifier", "nullable": true}}}, "Esky.FlightQuery.Worker.AlternativeOffers.PriceCalendar.PriceCalendarCell": {"required": ["amount", "departureDate"], "type": "object", "properties": {"departureDate": {"type": "string", "description": "Departure date"}, "returnDate": {"type": "string", "description": "Return date, null for one way flights", "nullable": true}, "amount": {"type": "number", "description": "Cheapest flight price", "format": "decimal"}}}, "Esky.FlightQuery.Worker.AlternativeOffers.PriceCalendar.PriceCalendar": {"type": "object", "properties": {"cells": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.AlternativeOffers.PriceCalendar.PriceCalendarCell"}, "description": "List of calendar cells with the cheapest flight price", "nullable": true}}}, "Esky.FlightQuery.Worker.AlternativeOffers.PriceCalendar.PriceCalendarResponse": {"required": ["priceCalendar"], "type": "object", "properties": {"priceCalendar": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.AlternativeOffers.PriceCalendar.PriceCalendar"}}}, "Esky.FlightQuery.Worker.AlternativeOffers.PriceCalendarOffer.PriceCalendarOfferQuery+Leg": {"required": ["arrivalCode", "departureCode", "departureDate"], "type": "object", "properties": {"departureCode": {"type": "string", "description": "Departure code", "nullable": true}, "departureDate": {"type": "string", "description": "Departure date"}, "arrivalCode": {"type": "string", "description": "Arrival code", "nullable": true}}}, "Esky.FlightQuery.Worker.AlternativeOffers.PriceCalendarOffer.PriceCalendarOfferQuery": {"required": ["currencyCode", "legs", "partnerCode", "passengers", "runtimeMode"], "type": "object", "properties": {"partnerCode": {"type": "string", "description": "Partner code", "nullable": true}, "currencyCode": {"type": "string", "description": "Currency code", "nullable": true}, "legs": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.AlternativeOffers.PriceCalendarOffer.PriceCalendarOfferQuery+Leg"}, "description": "Leg collection", "nullable": true}, "passengers": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Request.Passengers"}, "isPricePerPax": {"type": "boolean", "description": "Is price per passenger?"}, "priceType": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.PriceType"}, "runtimeMode": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.RuntimeMode"}, "sessionId": {"type": "string", "description": "Session identifier", "nullable": true}}}, "Esky.FlightQuery.Worker.AlternativeOffers.PriceCalendarOffer.PriceCalendarOfferResponse": {"required": ["flights"], "type": "object", "properties": {"flights": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Response.Flight"}, "description": "List of calendar offers", "nullable": true}, "nqsModel": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Response.NqsModel"}}}, "Esky.FlightQuery.Worker.AlternativeOffers.AlternativeAirports.AlternativeAirportsQuery+Leg": {"required": ["arrivalCode", "departureCode", "departureDate"], "type": "object", "properties": {"departureCode": {"type": "string", "description": "Departure code", "nullable": true}, "departureDate": {"type": "string", "description": "Departure date"}, "arrivalCode": {"type": "string", "description": "Arrival code", "nullable": true}}}, "Esky.FlightQuery.Worker.AlternativeOffers.AlternativeAirports.AlternativeAirportsQuery": {"required": ["currencyCode", "legs", "partnerCode", "passengers", "runtimeMode"], "type": "object", "properties": {"partnerCode": {"type": "string", "description": "Partner code", "nullable": true}, "currencyCode": {"type": "string", "description": "Currency code", "nullable": true}, "legs": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.AlternativeOffers.AlternativeAirports.AlternativeAirportsQuery+Leg"}, "description": "Leg collection", "nullable": true}, "passengers": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Request.Passengers"}, "isPricePerPax": {"type": "boolean", "description": "Is price per passenger?"}, "priceType": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.PriceType"}, "runtimeMode": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.RuntimeMode"}, "sessionId": {"type": "string", "description": "Session identifier", "nullable": true}}}, "Esky.FlightQuery.Worker.AlternativeOffers.AlternativeAirports.Airport": {"required": ["cityCode", "code"], "type": "object", "properties": {"code": {"type": "string", "description": "Airport code", "nullable": true}, "cityCode": {"type": "string", "description": "City code", "nullable": true}}}, "Esky.FlightQuery.Worker.AlternativeOffers.AlternativeAirports.Offer": {"required": ["airport", "flight", "originAirportDistanceKm"], "type": "object", "properties": {"airport": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.AlternativeOffers.AlternativeAirports.Airport"}, "originAirportDistanceKm": {"type": "integer", "description": "Distance to origin airport in kilometers", "format": "int32"}, "flight": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Response.Flight"}}}, "Esky.FlightQuery.Worker.AlternativeOffers.AlternativeAirports.AlternativeAirportsResponse": {"required": ["nearbyArrivalOffers", "nearbyDepartureOffers"], "type": "object", "properties": {"nearbyDepartureOffers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.AlternativeOffers.AlternativeAirports.Offer"}, "description": "Nearby departure airports offers", "nullable": true}, "nearbyArrivalOffers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.AlternativeOffers.AlternativeAirports.Offer"}, "description": "Nearby arrival airports offers", "nullable": true}, "nqsModel": {"$ref": "#/components/schemas/Esky.FlightQuery.Worker.Common.Response.NqsModel"}}}, "Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.CabinCode": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Request.TripSpecification": {"required": ["arrivalAirports", "departureAirports", "departureDate"], "type": "object", "properties": {"departureAirports": {"type": "array", "items": {"type": "string"}, "description": "The list of IATA airport codes to query for. Usually contains a\r\nsingle IATA code, but can sometimes contain several IATA codes.\r\nAs an example, a user querying flight from New York might imply\r\nquerying your API for JFK, LGA, EWR.\r\nIn some rare cases, the list can also contain the IATA code for a\r\ntrain station.\r\nFor partner API implementation not able to support querying multiple\r\nIATA codes, it is recommended to take the first IATA code in the\r\nlist.\r\nIt is allowed to return open-jaws in the response in cases of multiple\r\ndeparture_airports or arrival_airports.", "nullable": true}, "arrivalAirports": {"type": "array", "items": {"type": "string"}, "description": "The list of IATA arrival codes to query for.\r\nSee comment for departure_airports.", "nullable": true}, "departureDate": {"type": "string", "description": "Departure date - \"YYYY-MM-DD\".\r\nRequired."}, "returnDate": {"type": "string", "description": "Return date - \"YYYY-MM-DD\", applies to round-trip queries.\r\nOptional.", "nullable": true}}, "description": "Trip specification (one-way or round-trip)."}, "Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Request.GoogleLiveSearchRequest": {"required": ["countryCode", "currencyCode", "intendedCabin", "languageCode", "tripSpec", "version"], "type": "object", "properties": {"currencyCode": {"type": "string", "description": "The currency selected by the user - ISO 4217 format. Client expects\r\nthis currency to be returned in SearchFlightPricesResponse unless\r\nit is not supported by the server in which case another currency\r\nmay be provided.\r\nProviding different currency results in degraded user experience.\r\nRequired.", "nullable": true}, "languageCode": {"type": "string", "description": "The language selected by the user - ISO 639-2 format.\r\nApplies in particular to the bookingUrl element in the response,\r\nselecting the language.\r\nRequired.", "nullable": true}, "countryCode": {"type": "string", "description": "The country selected by the user - Alpha-2 ISO 3166-1 format.\r\nApplies in particular to the BookingUrl element in the response,\r\ntypically selecting the domain.\r\nRequired.", "nullable": true}, "adults": {"type": "integer", "description": "Total number of adults (13+ years old). Mandatory and must be >= 1.\r\nRequired.", "format": "int32"}, "children": {"type": "integer", "description": "Total number of children/infants in seat (2 - 12 years old).\r\nOptional.", "format": "int32"}, "infants": {"type": "integer", "format": "int32"}, "intendedCabin": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.CabinCode"}, "tripSpec": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Request.TripSpecification"}, "version": {"type": "integer", "description": "Version of the API. The API described in this document is version 1.\r\nRequired.", "format": "int32"}}}, "Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.Leg": {"type": "object", "properties": {"departureAirport": {"type": "string", "description": "3 letter IATA code of the origin airport.\r\nRequired.", "nullable": true}, "departureDateTime": {"type": "string", "description": "Scheduled departure time, ISO 8601 format for the time zone of the\r\ndeparture airport. For example: \"2023-12-31T16:30:00\".\r\nRequired.", "format": "date-time"}, "arrivalAirport": {"type": "string", "description": "3 letter IATA code of the destination airport.\r\nRequired.", "nullable": true}, "arrivalDateTime": {"type": "string", "description": "Scheduled arrival time, ISO 8601 format for the time zone of the\r\narrival airport or station. For example: \"2023-12-31T17:30:00\".\r\nRequired.", "format": "date-time"}, "carrier": {"type": "string", "description": "Marketing carrier of the segment, 2 letter IATA code.\r\nFor example: \"LX\".\r\nRequired.", "nullable": true}, "flightNumber": {"type": "string", "description": "Flight number. For example: \"1624\".\r\nRequired.", "nullable": true}, "operatingCarrier": {"type": "string", "description": "Operating carrier of the segment, 2 letter IATA code.\r\nFor example: \"LH\". If flights is operated and marketed by the same\r\nairline, operating carrier should be set to the same value as\r\ncarrier. If not present, the itinerary might not appear in search\r\nresults.\r\nOptional.", "nullable": true}, "operatingFlightNumber": {"type": "string", "description": "Operating flight number.\r\nFor example: \"431\". If not present, the itinerary might not appear\r\nin search results.\r\nOptional.", "nullable": true}}, "description": "Leg, as defined by IATA.\r\nThe smallest unit of air travel which starts with consists of a\r\nsingle takeoff followed by a single landing. E.g., a segment with a\r\ntechnical stop consists of 2 legs."}, "Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.Baggage": {"type": "object", "properties": {"maxWeight": {"type": "number", "description": "Maximum weight of the baggage per unit, in kg.\r\nRequired.", "format": "float"}, "count": {"type": "integer", "description": "Maximum number of baggages.\r\nRequired.", "format": "int32"}}, "description": "Description of a baggage allowance restricted by per unit weight."}, "Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.TotalBaggage": {"type": "object", "properties": {"totalMaxWeight": {"type": "integer", "description": "Total maximum weight of all the baggage.\r\nRequired.", "format": "int32"}}, "description": "Description of baggage allowance restricted by total maximum\r\nweight."}, "Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.BaggageAllowance": {"type": "object", "properties": {"carryOnBaggage": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.Baggage"}, "carryOnTotalBaggage": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.TotalBaggage"}, "checkedOnBaggage": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.Baggage"}, "checkedTotalBaggage": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.TotalBaggage"}}, "description": "Baggage allowance (carry and checked)"}, "Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.Segment": {"type": "object", "properties": {"legs": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.Leg"}, "description": "Consecutive legs, at least one leg must be defined.", "nullable": true}, "cabinCode": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.CabinCode"}, "baggageAllowance": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.BaggageAllowance"}}, "description": "Segment, as defined by IATA. One or more consecutive legs on the same\r\nflight and within the same itinerary variation."}, "Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.Slice": {"type": "object", "properties": {"segments": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.Segment"}, "description": "Segment, as defined by IATA. One or more consecutive legs on the same\r\nflight and within the same itinerary variation.", "nullable": true}}, "description": "A slice, comprised of multiple segments. Onbound or inbound part of a\r\ntrip."}, "Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.Price": {"type": "object", "properties": {"totalDecimal": {"type": "string", "description": "Total price of the itinerary with the decimal separated by '.'. Must\r\ninclude all fees as displayed on the landing page. For example:\r\n\"345.95\".\r\nRequired.", "nullable": true}, "currencyCode": {"type": "string", "description": "The currency of the fare price and that will be displayed\r\non your landing page - ISO 4217 format. For example: \"USD\".\r\nIt is strongly encouraged to use the same currency as the one in the\r\nrequest.\r\nRequired.", "nullable": true}}}, "Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.VirtualInterlineType": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.Itinerary": {"type": "object", "properties": {"outbound": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.Slice"}, "inbound": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.Slice"}, "price": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.Price"}, "bookingUrl": {"type": "string", "description": "Booking URL redirecting the user to the flight checkout page.\r\nMandatory for OTAs integrating with Google Flights.\r\nOptional otherwise.", "nullable": true}, "validitySeconds": {"type": "integer", "description": "Time to live of the itinerary in seconds. If not present, assumes\r\nthe itinerary to be bookable using the provided BookingUrl\r\nfor 3600 seconds (1 hour).\r\nOptional.", "format": "int32", "nullable": true}, "virtualInterlineType": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.VirtualInterlineType"}, "virtualInterlineDescriptionUrl": {"type": "string", "description": "Link to a help page on partner website that explains the rules\r\nregarding their virtual interline content.\r\nMust be set if virtualInterlineType is not DEFAULT_TYPE.\r\nOptional.", "nullable": true}}, "description": "An itinerary offered on your website."}, "Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.Log": {"type": "object", "properties": {"code": {"type": "string", "description": "Error or warning code semantic defined by the partner.\r\nUsed to aggregate errors on the Google internal dashboard.\r\nShould have limited cardinality.\r\nExample: \"ERR_UNSUPPORTED_AIRPORT\", \"WARN_PARTIAL_RESULT\".\r\nRequired.", "nullable": true}, "description": {"type": "string", "description": "Human readable description of the error.\r\nExample: \"Airport XZY is not supported.\",\r\n\"Partial result returned due to internal timeout (carrier 6E, LH)\"\r\nRequired.", "nullable": true}}}, "Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.GoogleLiveSearchResponse": {"type": "object", "properties": {"itineraries": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.Itinerary"}, "description": "The list of offered itineraries", "nullable": true}, "warnings": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.Log"}, "description": "Warnings if any occurred.\r\nCan be returned along with itineraries.", "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.GoogleLiveSearch.Contract.Response.Log"}, "description": "Errors if any occurred. Mutually exclusive with itineraries.", "nullable": true}}}, "Esky.FlightSearch.Contracts.DomainEnums.ServiceClassEnum": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "Esky.FlightSearch.Providers.Search.Queries.SearchFlights.SearchFlightsQuery+Leg": {"type": "object", "properties": {"departureCode": {"type": "string", "description": "Departure code", "nullable": true}, "departureDate": {"type": "string", "description": "Departure date", "format": "date-time"}, "arrivalCode": {"type": "string", "description": "Arrival code", "nullable": true}}}, "Esky.FlightSearch.Providers.Search.Queries.SearchFlights.SearchFlightsQuery+PackageSearchConfig": {"type": "object", "properties": {"searchId": {"type": "string", "description": "Search instance identifier", "nullable": true}, "portionId": {"type": "integer", "description": "Number of results portion (package) to return", "format": "int32"}}}, "Esky.FlightSearch.Providers.Search.Queries.SearchFlights.SearchFlightsQuery+FlexSearchConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "description": "Determines if flights cache based flex search is enabled"}, "range": {"type": "integer", "description": "Flex range in days", "format": "int32"}, "limit": {"type": "integer", "description": "Flex flights limit", "format": "int32"}}}, "Esky.FlightSearch.Contracts.DomainEnums.RuntimeModeEnum": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "Esky.FlightSearch.Contracts.DomainEnums.PersonTypeEnum": {"enum": [0, 1, 2, 3, 4, 5], "type": "integer", "format": "int32"}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.ISearchFlightsRequestPassenger": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.DomainEnums.PersonTypeEnum"}, "count": {"type": "integer", "description": "Number of passengers of a specified type", "format": "int32", "readOnly": true}}}, "Esky.FlightSearch.Providers.Search.Queries.SearchFlights.SearchFlightsQuery": {"required": ["currencyCode", "partnerCode"], "type": "object", "properties": {"partnerCode": {"type": "string", "description": "Requested partner code", "nullable": true}, "currencyCode": {"type": "string", "description": "Requested currency code", "nullable": true}, "languageCode": {"type": "string", "description": "Language code, determines translation language for dictionary elements", "nullable": true}, "sessionId": {"type": "string", "description": "Search session identifier", "nullable": true}, "packageSearch": {"type": "boolean", "description": "Determines whether the response should contain flights organized in leg groups (true) or ungrouped data (false). Default is false.", "nullable": true}, "serviceClass": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.DomainEnums.ServiceClassEnum"}, "priceType": {"type": "integer", "description": "Price type: 5 - base price, 15 - provider price, otherwise provider price + TF", "format": "int32", "nullable": true}, "pricePerPax": {"type": "boolean", "description": "Determines if price should be returned per pax (true) or as a total (false). Default is false.", "nullable": true}, "legs": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.Search.Queries.SearchFlights.SearchFlightsQuery+Leg"}, "description": "Journey legs", "nullable": true}, "deepLink": {"type": "boolean", "description": "Determines if returned legs should contain deep links"}, "searchConfig": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.Search.Queries.SearchFlights.SearchFlightsQuery+PackageSearchConfig"}, "flex": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.Search.Queries.SearchFlights.SearchFlightsQuery+FlexSearchConfig"}, "runtimeMode": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.DomainEnums.RuntimeModeEnum"}, "passengers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.ISearchFlightsRequestPassenger"}, "description": "Passenger configuration, defaults to single adult", "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.StopoverItem": {"type": "object", "properties": {"airportCode": {"type": "string", "nullable": true}, "arrivalDate": {"type": "string", "format": "date-time", "nullable": true}, "departureDate": {"type": "string", "format": "date-time", "nullable": true}, "arrivalTerminal": {"type": "string", "nullable": true}, "departureTerminal": {"type": "string", "nullable": true}, "cityCode": {"type": "string", "nullable": true}, "countryCode": {"type": "string", "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FareItem": {"type": "object", "properties": {"pc": {"type": "integer", "description": "Passenger code", "format": "int32"}, "fc": {"type": "string", "description": "Fare code", "nullable": true}, "addData": {"type": "string", "description": "Additional data", "nullable": true}}}, "Esky.Common.Enums.FlightFacilityType": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8], "type": "integer", "format": "int32"}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightFacility": {"type": "object", "properties": {"t": {"$ref": "#/components/schemas/Esky.Common.Enums.FlightFacilityType"}, "d": {"type": "string", "nullable": true}, "o": {"type": "boolean"}}}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightAttribute": {"type": "object", "properties": {"t": {"type": "string", "description": "Flight attribute type", "nullable": true}, "d": {"type": "string", "description": "Flight attribute description", "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightProperty": {"type": "object", "properties": {"t": {"type": "string", "description": "Flight property type", "nullable": true}, "k": {"type": "string", "description": "Flight property key", "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightAmenityPrice": {"type": "object", "properties": {"c": {"type": "string", "description": "Flight amenity price currency", "nullable": true}, "a": {"type": "number", "description": "Flight amenity price amount", "format": "decimal"}}}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightAmenity": {"type": "object", "properties": {"t": {"type": "string", "description": "Flight amenity type", "nullable": true}, "k": {"type": "string", "description": "Flight amenity key", "nullable": true}, "ia": {"type": "boolean", "description": "Flight amenity is available"}, "ip": {"type": "boolean", "description": "Flight amenity is paid"}, "p": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightAmenityPrice"}, "w": {"type": "integer", "description": "Flight amenity weight in kgs", "format": "int32"}}}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.SegmentItem": {"required": ["aac", "ac", "acc", "ad", "bc", "dac", "dcc", "dd", "fares", "fn", "ft", "mid", "pc", "sac", "sc"], "type": "object", "properties": {"ac": {"type": "string", "description": "Airline code, example values: LO, W6, FR,", "nullable": true}, "dac": {"type": "string", "description": "Departure location code,\r\n3-letter IATA airport code, example values: WAW, LTN", "nullable": true}, "dcc": {"type": "string", "description": "Departure city code, example values: WAW, LON", "nullable": true}, "dd": {"type": "string", "description": "Departure date, example values: Date(1709099700000), Date(1713870900000)", "format": "date-time"}, "aac": {"type": "string", "description": "Arrival location code,\r\n3-letter IATA airport code, example values: WAW, LTN", "nullable": true}, "acc": {"type": "string", "description": "Arrival city code, example values: WAW, LON", "nullable": true}, "ad": {"type": "string", "description": "Arrival date, example values: Date(1709099700000), Date(1713962400000)", "format": "date-time"}, "ft": {"type": "string", "description": "Flight time in hh:mm format, example values: 2:35", "nullable": true}, "fn": {"type": "string", "description": "Flight number, example values: 286, 3883", "nullable": true}, "pc": {"type": "integer", "description": "Provider code", "format": "int32"}, "sac": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.StopoverItem"}, "description": "Stopovers", "nullable": true}, "fares": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FareItem"}, "description": "<PERSON>es", "nullable": true}, "bc": {"type": "string", "description": "Booking class code", "nullable": true}, "sc": {"type": "integer", "description": "Service class code\r\nAny = 0, Economy = 1, First = 2, Business = 3, EconomyPremium = 4", "format": "int32"}, "mid": {"type": "integer", "description": "Mixed flight identifier", "format": "int32"}, "ff": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightFacility"}, "description": "Flight facilities", "nullable": true}, "fa": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightAttribute"}, "description": "Flight attributes", "nullable": true}, "fp": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightProperty"}, "description": "Flight properties", "nullable": true}, "fam": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.FlightAmenity"}, "description": "Flight amenities", "nullable": true}, "a": {"type": "string", "description": "Aircraft code, example values: Boeing737, Embraer195, CanadairRegionalJet900", "nullable": true}, "iST": {"type": "boolean", "description": "Is self transfer"}, "iP": {"type": "boolean", "description": "Is protected", "nullable": true}, "ob": {"type": "string", "description": "Flight is operated by, example values: LO, W6, FR,", "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.LegItem": {"required": ["asc", "fid", "ft", "oid", "segs"], "type": "object", "properties": {"fid": {"type": "string", "description": "Leg identifier", "nullable": true}, "oid": {"type": "string", "description": "Unique offer identifier in search results scope. Should return the same value across search requests.", "nullable": true}, "ft": {"type": "string", "description": "Flight time in hh:mm format, example values: 2:35", "nullable": true}, "asc": {"type": "integer", "description": "Available seats count, max to 9", "format": "int32"}, "ll": {"type": "string", "description": "Encoded leg locator", "nullable": true}, "ffll": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Encoded leg locators for bundled flights", "nullable": true}, "nqs": {"type": "number", "description": "Negative quality score", "format": "decimal"}, "pi": {"type": "string", "description": "DeepLink (pricing indicator) used by metasearch", "nullable": true}, "or": {"type": "boolean", "description": "Optional reservation (pending airlines)"}, "dn": {"type": "boolean", "description": "Is the departure airport different from requested airport (nearby airport)"}, "an": {"type": "boolean", "description": "Is the arrival airport different from requested airport (nearby airport)"}, "ddn": {"type": "boolean", "description": "Is the departure date different from requested date (flex result)"}, "segs": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.SegmentItem"}, "description": "Leg segments", "nullable": true}}}, "Esky.FlightSearch.Providers.Search.Queries.SearchFlights.Response.FlightItemWithLegGroups+LegGroup": {"required": ["legs"], "type": "object", "properties": {"legs": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.LegItem"}, "description": "Flight legs in group", "nullable": true}, "dn": {"type": "boolean", "description": "Is the departure airport different from requested airport (nearby airport)"}, "an": {"type": "boolean", "description": "Is the arrival airport different from requested airport (nearby airport)"}, "ddn": {"type": "boolean", "description": "Is the departure date different from requested date (flex result)"}}, "description": "Leg group"}, "Esky.FlightSearch.Providers.Search.Queries.SearchFlights.Response.FlightItemWithLegGroups": {"required": ["c", "cs", "fid", "lg", "p", "pc", "ppx", "tbf", "tf", "tp", "ttf"], "type": "object", "properties": {"lg": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.Search.Queries.SearchFlights.Response.FlightItemWithLegGroups+LegGroup"}, "description": "Legs grouped by price", "nullable": true}, "nqs": {"type": "number", "description": "Negative quality score", "format": "decimal"}, "nqsoid": {"type": "array", "items": {"type": "string"}, "description": "Offer ids of best legs, according to NQS", "nullable": true}, "__type": {"type": "string", "description": "Obsolete - for backward compatibility only", "nullable": true, "deprecated": true}, "c": {"type": "string", "description": "Currency code", "nullable": true}, "cs": {"type": "string", "description": "Currency symbol", "nullable": true}, "du": {"type": "number", "description": "DU fee", "format": "decimal"}, "pc": {"type": "integer", "description": "Provider code", "format": "int32"}, "p": {"type": "number", "description": "Flight price", "format": "decimal"}, "tp": {"type": "number", "description": "Total payment price", "format": "decimal"}, "ppx": {"type": "number", "description": "Price per passenger", "format": "decimal"}, "tbf": {"type": "number", "description": "Total base price", "format": "decimal"}, "ttf": {"type": "number", "description": "Total taxes and fees", "format": "decimal"}, "tf": {"type": "number", "description": "Transaction fee amount", "format": "decimal"}, "d": {"type": "number", "description": "Discount", "format": "decimal"}, "fid": {"type": "string", "description": "Flight identifier", "nullable": true}, "fmt": {"type": "string", "description": "Flight mixing type", "nullable": true}}}, "Esky.FlightSearch.Contracts.CommonObjects.DictionaryItem`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"required": ["key", "value"], "type": "object", "properties": {"key": {"type": "string", "description": "Item key", "nullable": true}, "type": {"type": "string", "description": "Item type", "nullable": true}, "value": {"type": "string", "description": "Item value", "nullable": true}}, "description": "Dictionary item"}, "System.Collections.Generic.KeyValuePair`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]": {"type": "object", "properties": {"key": {"type": "string", "nullable": true, "readOnly": true}, "value": {"type": "string", "nullable": true, "readOnly": true}}}, "Esky.FlightSearch.Contracts.CommonObjects.Price": {"type": "object", "properties": {"currencyCode": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>", "nullable": true}, "amount": {"type": "number", "description": "Amount", "format": "decimal"}}, "description": "Price object"}, "Esky.FlightSearch.Providers.Search.Queries.SearchFlights.Response.SearchFilters": {"required": ["airlineCodes", "airlineNames", "airlineTypes", "airportsCityCodes", "airportsCountryCodes", "airportsNames", "arrivalCodes", "cityNames", "countryNames", "departureCodes", "providerCodes", "transferCodes"], "type": "object", "properties": {"departureCodes": {"type": "array", "items": {"type": "string"}, "description": "List of departure location codes", "nullable": true}, "arrivalCodes": {"type": "array", "items": {"type": "string"}, "description": "List of arrival location codes", "nullable": true}, "transferCodes": {"type": "array", "items": {"type": "string"}, "description": "List of transfer codes", "nullable": true}, "airportsNames": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.DictionaryItem`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "description": "List of airport names", "nullable": true}, "airportsCityCodes": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.DictionaryItem`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "description": "List of airport city mappings", "nullable": true}, "airportsCountryCodes": {"type": "array", "items": {"$ref": "#/components/schemas/System.Collections.Generic.KeyValuePair`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "description": "List of airport city mappings", "nullable": true}, "cityNames": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.DictionaryItem`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "description": "List of city names", "nullable": true}, "countryNames": {"type": "array", "items": {"$ref": "#/components/schemas/System.Collections.Generic.KeyValuePair`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "description": "List of country names", "nullable": true}, "airlineCodes": {"type": "array", "items": {"type": "string"}, "description": "List of airline codes", "nullable": true}, "airlineNames": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.DictionaryItem`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "description": "List of airline names", "nullable": true}, "airlineTypes": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.DictionaryItem`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "description": "List of airline types", "nullable": true}, "providerCodes": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.DictionaryItem`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "description": "List of provider codes", "nullable": true}, "airlineAlliances": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.DictionaryItem`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "description": "Obsolete - for backward compatibility only", "nullable": true}, "stopovers": {"type": "array", "items": {"type": "integer", "format": "int32"}, "description": "Stopovers", "nullable": true}, "airlinePopularity": {"type": "array", "items": {"type": "string"}, "description": "Obsolete - for backward compatibility only", "nullable": true, "deprecated": true}, "airportsWebNames": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.DictionaryItem`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"}, "description": "Obsolete - for backward compatibility only", "nullable": true, "deprecated": true}, "maxPromotedFlightsForHighlightCategory": {"type": "integer", "description": "Obsolete - for backward compatibility only", "format": "int32", "deprecated": true}, "maxPromotedFlightsForRaiseCategory": {"type": "integer", "description": "Obsolete - for backward compatibility only", "format": "int32", "deprecated": true}, "maxPromotedFlightsForTooltipCategory": {"type": "integer", "description": "Shortest flight time returned", "format": "int32", "deprecated": true}, "maximalFlightTime": {"type": "string", "description": "Longest flight time returned", "nullable": true, "deprecated": true}, "maximalPrice": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.Price"}, "minimalFlightTime": {"type": "string", "description": "Shortest flight time returned", "nullable": true, "deprecated": true}, "minimalPrice": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.CommonObjects.Price"}}}, "Esky.FlightSearch.Providers.Search.Queries.SearchFlights.Response.AdditionalInformation": {"type": "object", "properties": {"key": {"type": "string", "nullable": true}, "severity": {"type": "integer", "format": "int32"}}}, "Esky.FlightSearch.Providers.Search.Queries.SearchFlights.Response.SearchFlightsResponseGrouped": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.Search.Queries.SearchFlights.Response.FlightItemWithLegGroups"}, "description": "Flight results organized in leg groups", "nullable": true}, "searchFilters": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.Search.Queries.SearchFlights.Response.SearchFilters"}, "moreResultsAvailable": {"type": "boolean", "description": "Determines whether there will be more search results (true) or if the search is finished and all results have been returned (false)"}, "serverName": {"type": "string", "description": "Server name - has to be an empty string for compatibility reasons", "nullable": true, "readOnly": true, "deprecated": true}, "airTrafficRuleId": {"type": "integer", "format": "int32", "readOnly": true, "deprecated": true}, "airTrafficRuleVersion": {"type": "string", "nullable": true, "readOnly": true, "deprecated": true}, "priceType": {"type": "integer", "format": "int32", "deprecated": true}, "isPricePerPax": {"type": "boolean", "deprecated": true}, "alternativeItems": {"type": "array", "items": {"type": "string"}, "nullable": true, "readOnly": true, "deprecated": true}, "pagingLocator": {"type": "string", "nullable": true, "readOnly": true, "deprecated": true}, "flexDateCalendarPercentCoverage": {"type": "integer", "format": "int32", "readOnly": true, "deprecated": true}, "sessionId": {"type": "string", "nullable": true, "readOnly": true, "deprecated": true}, "abVariant": {"type": "string", "description": "Variant code for A/B tests", "nullable": true}, "nqsm": {"type": "string", "description": "Details of quality score ML model, used for NQS calculation", "nullable": true}, "additionalInformation": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.Search.Queries.SearchFlights.Response.AdditionalInformation"}, "description": "Additional messages related to search execution process (e.g. airport redirects)", "nullable": true}}}, "Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.SearchFlightsRequestPassenger": {"required": ["count"], "type": "object", "properties": {"code": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.DomainEnums.PersonTypeEnum"}, "count": {"type": "integer", "description": "Number of passengers of a specified type", "format": "int32"}, "ages": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}}, "Esky.FlightSearch.Providers.OfferLiveCheck.Contract.OfferLiveCheckRequest": {"type": "object", "properties": {"flightOfferKeys": {"type": "array", "items": {"type": "string"}, "nullable": true}, "passengers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Contracts.ViewModels.Legacy.Flights.SearchFlightsRequestPassenger"}, "nullable": true}, "partnerCode": {"type": "string", "nullable": true}, "usePartnerCurrency": {"type": "boolean", "description": "If true, prices are returned in default partner currency. \r\nOtherwise provider currency is returned."}, "calculateTF": {"type": "boolean"}}}, "Esky.FlightSearch.Providers.OfferLiveCheck.Contract.OfferAvailability": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "Esky.FlightSearch.Providers.OfferLiveCheck.Contract.CacheFlightOfferDetails": {"type": "object", "properties": {"price": {"type": "number", "format": "decimal"}}}, "Esky.FlightSearch.Providers.OfferLiveCheck.Contract.ProviderFlightOfferDetails": {"type": "object", "properties": {"legLocators": {"type": "array", "items": {"type": "string"}, "nullable": true}, "price": {"type": "number", "format": "decimal"}, "tf": {"type": "number", "format": "decimal"}}}, "Esky.FlightSearch.Providers.OfferLiveCheck.Contract.FlightOffer": {"type": "object", "properties": {"flightOfferKey": {"type": "string", "description": "Flight offer key from cache", "nullable": true}, "status": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.OfferLiveCheck.Contract.OfferAvailability"}, "cacheOfferDetails": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.OfferLiveCheck.Contract.CacheFlightOfferDetails"}, "providerOfferDetails": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.OfferLiveCheck.Contract.ProviderFlightOfferDetails"}, "currency": {"type": "string", "description": "Common currency code for provider and cache offer prices", "nullable": true}, "providerCode": {"type": "integer", "description": "Provider code used for search", "format": "int32"}}}, "Esky.FlightSearch.Providers.OfferLiveCheck.Contract.OfferLiveCheckResponse": {"type": "object", "properties": {"offers": {"type": "array", "items": {"$ref": "#/components/schemas/Esky.FlightSearch.Providers.OfferLiveCheck.Contract.FlightOffer"}, "nullable": true}}}}}}