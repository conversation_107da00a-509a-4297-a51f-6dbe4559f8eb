//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

#pragma warning disable 108 // Disable "CS0108 '{derivedDto}.To<PERSON><PERSON>()' hides inherited member '{dtoBase}.To<PERSON>son()'. Use the new keyword if hiding was intended."
#pragma warning disable 114 // Disable "CS0114 '{derivedDto}.RaisePropertyChanged(String)' hides inherited member 'dtoBase.RaisePropertyChanged(String)'. To make the current member override that implementation, add the override keyword. Otherwise add the new keyword."
#pragma warning disable 472 // Disable "CS0472 The result of the expression is always 'false' since a value of type 'Int32' is never equal to 'null' of type 'Int32?'
#pragma warning disable 612 // Disable "CS0612 '...' is obsolete"
#pragma warning disable 649 // Disable "CS0649 Field is never assigned to, and will always have its default value null"
#pragma warning disable 1573 // Disable "CS1573 Parameter '...' has no matching param tag in the XML comment for ...
#pragma warning disable 1591 // Disable "CS1591 Missing XML comment for publicly visible type or member ..."
#pragma warning disable 8073 // Disable "CS8073 The result of the expression is always 'false' since a value of type 'T' is never equal to 'null' of type 'T?'"
#pragma warning disable 3016 // Disable "CS3016 Arrays as attribute arguments is not CLS-compliant"
#pragma warning disable 8603 // Disable "CS8603 Possible null reference return"
#pragma warning disable 8604 // Disable "CS8604 Possible null reference argument for parameter"
#pragma warning disable 8625 // Disable "CS8625 Cannot convert null literal to non-nullable reference type"
#pragma warning disable 8765 // Disable "CS8765 Nullability of type of parameter doesn't match overridden member (possibly because of nullability attributes)."

namespace Infrastructure.Gateways.FlightCacheGateway.Proxies.FlightLiveSearch
{
    using System = global::System;

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial interface IFlightLiveSearchClient
    {
        /// <summary>
        /// Search for flights and retrieve results in portions (packages)
        /// </summary>
        /// <remarks>
        /// **[search-client.html](/search-client-nfsr.html)**
        /// </remarks>
        /// <param name="body">Search request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<PackagedSearchResponse> PackagedSearchAsync(PackagedSearchQuery body);

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Search for flights and retrieve results in portions (packages)
        /// </summary>
        /// <remarks>
        /// **[search-client.html](/search-client-nfsr.html)**
        /// </remarks>
        /// <param name="body">Search request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<PackagedSearchResponse> PackagedSearchAsync(PackagedSearchQuery body, System.Threading.CancellationToken cancellationToken);

        /// <summary>
        /// Fetch fare families for a given flight
        /// </summary>
        /// <param name="body">Request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<FareFamiliesResponse> FetchAsync(FareFamiliesQuery body);

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Fetch fare families for a given flight
        /// </summary>
        /// <param name="body">Request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<FareFamiliesResponse> FetchAsync(FareFamiliesQuery body, System.Threading.CancellationToken cancellationToken);

        /// <summary>
        /// Search for flights and retrieve results based on open search criteria
        /// </summary>
        /// <param name="body">Open search request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<OpenSearchResponse> OpenSearchAsync(OpenSearchQuery body);

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Search for flights and retrieve results based on open search criteria
        /// </summary>
        /// <param name="body">Open search request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<OpenSearchResponse> OpenSearchAsync(OpenSearchQuery body, System.Threading.CancellationToken cancellationToken);

        /// <summary>
        /// Return price charts results based on open search criteria
        /// </summary>
        /// <param name="body">Price charts request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<PriceChartsResponse> PriceChartsAsync(PriceChartsQuery body);

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Return price charts results based on open search criteria
        /// </summary>
        /// <param name="body">Price charts request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<PriceChartsResponse> PriceChartsAsync(PriceChartsQuery body, System.Threading.CancellationToken cancellationToken);

        /// <summary>
        /// Returns best offers based on search criteria
        /// </summary>
        /// <param name="body">Best offers request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<BestOffersResponse> BestOffersAsync(BestOffersQuery body);

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Returns best offers based on search criteria
        /// </summary>
        /// <param name="body">Best offers request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<BestOffersResponse> BestOffersAsync(BestOffersQuery body, System.Threading.CancellationToken cancellationToken);

        /// <summary>
        /// Returns price calendar with cheapest flights prices
        /// </summary>
        /// <param name="body">Price calendar request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<PriceCalendarResponse> PriceCalendarAsync(PriceCalendarQuery body);

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Returns price calendar with cheapest flights prices
        /// </summary>
        /// <param name="body">Price calendar request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<PriceCalendarResponse> PriceCalendarAsync(PriceCalendarQuery body, System.Threading.CancellationToken cancellationToken);

        /// <summary>
        /// Returns price calendar offer
        /// </summary>
        /// <param name="body">Price calendar offer request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<PriceCalendarOfferResponse> OfferAsync(PriceCalendarOfferQuery body);

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Returns price calendar offer
        /// </summary>
        /// <param name="body">Price calendar offer request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<PriceCalendarOfferResponse> OfferAsync(PriceCalendarOfferQuery body, System.Threading.CancellationToken cancellationToken);

        /// <summary>
        /// Returns alternative airports flights
        /// </summary>
        /// <param name="body">Alternative airports request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<AlternativeAirportsResponse> AlternativeAirportsAsync(AlternativeAirportsQuery body);

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Returns alternative airports flights
        /// </summary>
        /// <param name="body">Alternative airports request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<AlternativeAirportsResponse> AlternativeAirportsAsync(AlternativeAirportsQuery body, System.Threading.CancellationToken cancellationToken);

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<GoogleLiveSearchResponse> GFSAPIAsync(GoogleLiveSearchRequest body);

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<GoogleLiveSearchResponse> GFSAPIAsync(GoogleLiveSearchRequest body, System.Threading.CancellationToken cancellationToken);

        /// <summary>
        /// Search for flights from cache and providers
        /// </summary>
        /// <remarks>
        /// **[search-client.html](/search-client.html)**
        /// </remarks>
        /// <param name="body">Search request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<SearchFlightsResponseGrouped> SearchFlightsAsync(SearchFlightsQuery body);

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Search for flights from cache and providers
        /// </summary>
        /// <remarks>
        /// **[search-client.html](/search-client.html)**
        /// </remarks>
        /// <param name="body">Search request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<SearchFlightsResponseGrouped> SearchFlightsAsync(SearchFlightsQuery body, System.Threading.CancellationToken cancellationToken);

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<OfferLiveCheckResponse> ByFlightKeysAsync(OfferLiveCheckRequest body);

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<OfferLiveCheckResponse> ByFlightKeysAsync(OfferLiveCheckRequest body, System.Threading.CancellationToken cancellationToken);

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<OfferLiveCheckResponse> ByFlightKeysWithTFAsync(OfferLiveCheckRequest body);

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<OfferLiveCheckResponse> ByFlightKeysWithTFAsync(OfferLiveCheckRequest body, System.Threading.CancellationToken cancellationToken);

    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightLiveSearchClient : IFlightLiveSearchClient
    {
        private System.Net.Http.HttpClient _httpClient;
        private static System.Lazy<System.Text.Json.JsonSerializerOptions> _settings = new System.Lazy<System.Text.Json.JsonSerializerOptions>(CreateSerializerSettings, true);
        private System.Text.Json.JsonSerializerOptions _instanceSettings;

    #pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public FlightLiveSearchClient(System.Net.Http.HttpClient httpClient)
    #pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        {
            _httpClient = httpClient;
            Initialize();
        }

        private static System.Text.Json.JsonSerializerOptions CreateSerializerSettings()
        {
            var settings = new System.Text.Json.JsonSerializerOptions();
            UpdateJsonSerializerSettings(settings);
            return settings;
        }

        protected System.Text.Json.JsonSerializerOptions JsonSerializerSettings { get { return _instanceSettings ?? _settings.Value; } }

        static partial void UpdateJsonSerializerSettings(System.Text.Json.JsonSerializerOptions settings);

        partial void Initialize();

        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);

        /// <summary>
        /// Search for flights and retrieve results in portions (packages)
        /// </summary>
        /// <remarks>
        /// **[search-client.html](/search-client-nfsr.html)**
        /// </remarks>
        /// <param name="body">Search request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<PackagedSearchResponse> PackagedSearchAsync(PackagedSearchQuery body)
        {
            return PackagedSearchAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Search for flights and retrieve results in portions (packages)
        /// </summary>
        /// <remarks>
        /// **[search-client.html](/search-client-nfsr.html)**
        /// </remarks>
        /// <param name="body">Search request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<PackagedSearchResponse> PackagedSearchAsync(PackagedSearchQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/FlightQuery/PackagedSearch"
                    urlBuilder_.Append("api/v1.0/FlightQuery/PackagedSearch");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<PackagedSearchResponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <summary>
        /// Fetch fare families for a given flight
        /// </summary>
        /// <param name="body">Request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<FareFamiliesResponse> FetchAsync(FareFamiliesQuery body)
        {
            return FetchAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Fetch fare families for a given flight
        /// </summary>
        /// <param name="body">Request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<FareFamiliesResponse> FetchAsync(FareFamiliesQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/FlightQuery/FareFamilies/Fetch"
                    urlBuilder_.Append("api/v1.0/FlightQuery/FareFamilies/Fetch");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<FareFamiliesResponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <summary>
        /// Search for flights and retrieve results based on open search criteria
        /// </summary>
        /// <param name="body">Open search request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<OpenSearchResponse> OpenSearchAsync(OpenSearchQuery body)
        {
            return OpenSearchAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Search for flights and retrieve results based on open search criteria
        /// </summary>
        /// <param name="body">Open search request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<OpenSearchResponse> OpenSearchAsync(OpenSearchQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/FlightQuery/OpenSearch"
                    urlBuilder_.Append("api/v1.0/FlightQuery/OpenSearch");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<OpenSearchResponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <summary>
        /// Return price charts results based on open search criteria
        /// </summary>
        /// <param name="body">Price charts request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<PriceChartsResponse> PriceChartsAsync(PriceChartsQuery body)
        {
            return PriceChartsAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Return price charts results based on open search criteria
        /// </summary>
        /// <param name="body">Price charts request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<PriceChartsResponse> PriceChartsAsync(PriceChartsQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/FlightQuery/PriceCharts"
                    urlBuilder_.Append("api/v1.0/FlightQuery/PriceCharts");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<PriceChartsResponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <summary>
        /// Returns best offers based on search criteria
        /// </summary>
        /// <param name="body">Best offers request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<BestOffersResponse> BestOffersAsync(BestOffersQuery body)
        {
            return BestOffersAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Returns best offers based on search criteria
        /// </summary>
        /// <param name="body">Best offers request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<BestOffersResponse> BestOffersAsync(BestOffersQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/FlightQuery/BestOffers"
                    urlBuilder_.Append("api/v1.0/FlightQuery/BestOffers");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<BestOffersResponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <summary>
        /// Returns price calendar with cheapest flights prices
        /// </summary>
        /// <param name="body">Price calendar request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<PriceCalendarResponse> PriceCalendarAsync(PriceCalendarQuery body)
        {
            return PriceCalendarAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Returns price calendar with cheapest flights prices
        /// </summary>
        /// <param name="body">Price calendar request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<PriceCalendarResponse> PriceCalendarAsync(PriceCalendarQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/FlightQuery/PriceCalendar"
                    urlBuilder_.Append("api/v1.0/FlightQuery/PriceCalendar");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<PriceCalendarResponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <summary>
        /// Returns price calendar offer
        /// </summary>
        /// <param name="body">Price calendar offer request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<PriceCalendarOfferResponse> OfferAsync(PriceCalendarOfferQuery body)
        {
            return OfferAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Returns price calendar offer
        /// </summary>
        /// <param name="body">Price calendar offer request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<PriceCalendarOfferResponse> OfferAsync(PriceCalendarOfferQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/FlightQuery/PriceCalendar/Offer"
                    urlBuilder_.Append("api/v1.0/FlightQuery/PriceCalendar/Offer");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<PriceCalendarOfferResponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <summary>
        /// Returns alternative airports flights
        /// </summary>
        /// <param name="body">Alternative airports request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<AlternativeAirportsResponse> AlternativeAirportsAsync(AlternativeAirportsQuery body)
        {
            return AlternativeAirportsAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Returns alternative airports flights
        /// </summary>
        /// <param name="body">Alternative airports request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<AlternativeAirportsResponse> AlternativeAirportsAsync(AlternativeAirportsQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/FlightQuery/AlternativeAirports"
                    urlBuilder_.Append("api/v1.0/FlightQuery/AlternativeAirports");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<AlternativeAirportsResponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<GoogleLiveSearchResponse> GFSAPIAsync(GoogleLiveSearchRequest body)
        {
            return GFSAPIAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<GoogleLiveSearchResponse> GFSAPIAsync(GoogleLiveSearchRequest body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/GoogleLiveSearch/GFSAPI"
                    urlBuilder_.Append("api/v1.0/GoogleLiveSearch/GFSAPI");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<GoogleLiveSearchResponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <summary>
        /// Search for flights from cache and providers
        /// </summary>
        /// <remarks>
        /// **[search-client.html](/search-client.html)**
        /// </remarks>
        /// <param name="body">Search request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<SearchFlightsResponseGrouped> SearchFlightsAsync(SearchFlightsQuery body)
        {
            return SearchFlightsAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Search for flights from cache and providers
        /// </summary>
        /// <remarks>
        /// **[search-client.html](/search-client.html)**
        /// </remarks>
        /// <param name="body">Search request</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<SearchFlightsResponseGrouped> SearchFlightsAsync(SearchFlightsQuery body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/Legacy/SearchFlights"
                    urlBuilder_.Append("api/v1.0/Legacy/SearchFlights");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<SearchFlightsResponseGrouped>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<OfferLiveCheckResponse> ByFlightKeysAsync(OfferLiveCheckRequest body)
        {
            return ByFlightKeysAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<OfferLiveCheckResponse> ByFlightKeysAsync(OfferLiveCheckRequest body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/OfferLiveCheck/ByFlightKeys"
                    urlBuilder_.Append("api/v1.0/OfferLiveCheck/ByFlightKeys");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<OfferLiveCheckResponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual System.Threading.Tasks.Task<OfferLiveCheckResponse> ByFlightKeysWithTFAsync(OfferLiveCheckRequest body)
        {
            return ByFlightKeysWithTFAsync(body, System.Threading.CancellationToken.None);
        }

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <returns>Success</returns>
        /// <exception cref="ApiException">A server side error occurred.</exception>
        public virtual async System.Threading.Tasks.Task<OfferLiveCheckResponse> ByFlightKeysWithTFAsync(OfferLiveCheckRequest body, System.Threading.CancellationToken cancellationToken)
        {
            var client_ = _httpClient;
            var disposeClient_ = false;
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    var json_ = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(body, JsonSerializerSettings);
                    var content_ = new System.Net.Http.ByteArrayContent(json_);
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json-patch+json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));

                    var urlBuilder_ = new System.Text.StringBuilder();
                
                    // Operation Path: "api/v1.0/OfferLiveCheck/ByFlightKeysWithTF"
                    urlBuilder_.Append("api/v1.0/OfferLiveCheck/ByFlightKeysWithTF");

                    PrepareRequest(client_, request_, urlBuilder_);

                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);

                    PrepareRequest(client_, request_, url_);

                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>>();
                        foreach (var item_ in response_.Headers)
                            headers_[item_.Key] = item_.Value;
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }

                        ProcessResponse(client_, response_);

                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<OfferLiveCheckResponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new ApiException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new ApiException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }

        protected struct ObjectResponseResult<T>
        {
            public ObjectResponseResult(T responseObject, string responseText)
            {
                this.Object = responseObject;
                this.Text = responseText;
            }

            public T Object { get; }

            public string Text { get; }
        }

        public bool ReadResponseAsString { get; set; }

        protected virtual async System.Threading.Tasks.Task<ObjectResponseResult<T>> ReadObjectResponseAsync<T>(System.Net.Http.HttpResponseMessage response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Threading.CancellationToken cancellationToken)
        {
            if (response == null || response.Content == null)
            {
                return new ObjectResponseResult<T>(default(T), string.Empty);
            }

            if (ReadResponseAsString)
            {
                var responseText = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                try
                {
                    var typedBody = System.Text.Json.JsonSerializer.Deserialize<T>(responseText, JsonSerializerSettings);
                    return new ObjectResponseResult<T>(typedBody, responseText);
                }
                catch (System.Text.Json.JsonException exception)
                {
                    var message = "Could not deserialize the response body string as " + typeof(T).FullName + ".";
                    throw new ApiException(message, (int)response.StatusCode, responseText, headers, exception);
                }
            }
            else
            {
                try
                {
                    using (var responseStream = await response.Content.ReadAsStreamAsync().ConfigureAwait(false))
                    {
                        var typedBody = await System.Text.Json.JsonSerializer.DeserializeAsync<T>(responseStream, JsonSerializerSettings, cancellationToken).ConfigureAwait(false);
                        return new ObjectResponseResult<T>(typedBody, string.Empty);
                    }
                }
                catch (System.Text.Json.JsonException exception)
                {
                    var message = "Could not deserialize the response body stream as " + typeof(T).FullName + ".";
                    throw new ApiException(message, (int)response.StatusCode, string.Empty, headers, exception);
                }
            }
        }

        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value == null)
            {
                return "";
            }

            if (value is System.Enum)
            {
                var name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value != null ? attribute.Value : name;
                        }
                    }

                    var converted = System.Convert.ToString(System.Convert.ChangeType(value, System.Enum.GetUnderlyingType(value.GetType()), cultureInfo));
                    return converted == null ? string.Empty : converted;
                }
            }
            else if (value is bool) 
            {
                return System.Convert.ToString((bool)value, cultureInfo).ToLowerInvariant();
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value is string[])
            {
                return string.Join(",", (string[])value);
            }
            else if (value.GetType().IsArray)
            {
                var valueArray = (System.Array)value;
                var valueTextArray = new string[valueArray.Length];
                for (var i = 0; i < valueArray.Length; i++)
                {
                    valueTextArray[i] = ConvertToString(valueArray.GetValue(i), cultureInfo);
                }
                return string.Join(",", valueTextArray);
            }

            var result = System.Convert.ToString(value, cultureInfo);
            return result == null ? "" : result;
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum QueryServiceClass
    {

        _0 = 0,

        _1 = 1,

        _2 = 2,

        _3 = 3,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Leg
    {
        /// <summary>
        /// Departure code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureCode")]
        public string DepartureCode { get; set; }

        /// <summary>
        /// Departure date
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public System.DateTime DepartureDate { get; set; }

        /// <summary>
        /// Arrival code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalCode")]
        public string ArrivalCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Passengers
    {
        /// <summary>
        /// Number of adults
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("adults")]
        public int Adults { get; set; }

        /// <summary>
        /// Number of youths
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("youths")]
        public int Youths { get; set; }

        /// <summary>
        /// Number of children
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("children")]
        public int Children { get; set; }

        /// <summary>
        /// Number of infants
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("infants")]
        public int Infants { get; set; }

        /// <summary>
        /// Each number represents a child age. Array length determines number of children
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("childrenAges")]
        public System.Collections.Generic.ICollection<int> ChildrenAges { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum PriceType
    {

        _0 = 0,

        _1 = 1,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum RuntimeMode
    {

        _0 = 0,

        _1 = 1,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PackagedSearchQuery
    {
        /// <summary>
        /// Partner code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        /// <summary>
        /// Currency code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("serviceClass")]
        public QueryServiceClass ServiceClass { get; set; }

        /// <summary>
        /// Leg collection
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("legs")]
        public System.Collections.Generic.ICollection<Leg> Legs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("passengers")]
        [System.ComponentModel.DataAnnotations.Required]
        public Passengers Passengers { get; set; } = new Passengers();

        /// <summary>
        /// Is price per passenger?
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("isPricePerPax")]
        public bool IsPricePerPax { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("priceType")]
        public PriceType PriceType { get; set; }

        /// <summary>
        /// Search instance identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("searchId")]
        public string SearchId { get; set; }

        /// <summary>
        /// Package number (one-based)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("packageNumber")]
        public int PackageNumber { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("runtimeMode")]
        public RuntimeMode RuntimeMode { get; set; }

        /// <summary>
        /// Session identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("sessionId")]
        public string SessionId { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceModel
    {
        /// <summary>
        /// Flight price amount
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("amount")]
        public decimal Amount { get; set; }

        /// <summary>
        /// Transaction fee amount
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("transactionFee")]
        public decimal TransactionFee { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Stopover
    {

        [System.Text.Json.Serialization.JsonPropertyName("airportCode")]
        public string AirportCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalDate")]
        public System.DateTime? ArrivalDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        public System.DateTime? DepartureDate { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FareDetails
    {

        [System.Text.Json.Serialization.JsonPropertyName("fareCode")]
        public string FareCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("officeId")]
        public string OfficeId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("offerId")]
        public string OfferId { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum FlightServiceClass
    {

        _0 = 0,

        _1 = 1,

        _2 = 2,

        _3 = 3,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum CarrierType
    {

        _0 = 0,

        _1 = 1,

        _2 = 2,

        _3 = 3,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Segment
    {
        /// <summary>
        /// Marketing airline code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airlineCode")]
        public string AirlineCode { get; set; }

        /// <summary>
        /// Operating airline code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("operatedBy")]
        public string OperatedBy { get; set; }

        /// <summary>
        /// Departure location code
        /// <br/>3-letter IATA airport code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureCode")]
        public string DepartureCode { get; set; }

        /// <summary>
        /// Departure date
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public System.DateTime DepartureDate { get; set; }

        /// <summary>
        /// Arrival location code
        /// <br/>3-letter IATA airport code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalCode")]
        public string ArrivalCode { get; set; }

        /// <summary>
        /// Arrival date
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalDate")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public System.DateTime ArrivalDate { get; set; }

        /// <summary>
        /// Journey duration in minutes
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("durationMinutes")]
        public int DurationMinutes { get; set; }

        /// <summary>
        /// Flight number
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("flightNumber")]
        public string FlightNumber { get; set; }

        /// <summary>
        /// Provider code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        public int ProviderCode { get; set; }

        /// <summary>
        /// Stopovers
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("stopovers")]
        public System.Collections.Generic.ICollection<Stopover> Stopovers { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("fareDetails")]
        public FareDetails FareDetails { get; set; }

        /// <summary>
        /// Booking class code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("bookingClass")]
        public string BookingClass { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("serviceClass")]
        public FlightServiceClass ServiceClass { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("carrierType")]
        public CarrierType CarrierType { get; set; }

        /// <summary>
        /// Is self transfer?
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("isSelfTransfer")]
        public bool IsSelfTransfer { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum NearbyAirportsType
    {

        _0 = 0,

        _1 = 1,

        _2 = 2,

        _3 = 3,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Leg2
    {
        /// <summary>
        /// Leg identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public string Id { get; set; }

        /// <summary>
        /// Unique offer identifier in search results scope. Should return the same value across search requests.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("offerId")]
        public string OfferId { get; set; }

        /// <summary>
        /// Segment collection
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("segments")]
        public System.Collections.Generic.ICollection<Segment> Segments { get; set; }

        /// <summary>
        /// Journey duration in minutes
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("durationMinutes")]
        public int DurationMinutes { get; set; }

        /// <summary>
        /// Number of seats available
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("availableSeatsCount")]
        public int? AvailableSeatsCount { get; set; }

        /// <summary>
        /// Encoded leg locator
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("legLocator")]
        public string LegLocator { get; set; }

        /// <summary>
        /// Negative quality score
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("nqs")]
        public decimal Nqs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("nearbyAirports")]
        public NearbyAirportsType NearbyAirports { get; set; }

        /// <summary>
        /// Is the departure date different from requested date (flex result)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("isNearbyDate")]
        public bool IsNearbyDate { get; set; }

        /// <summary>
        /// Fare family name
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fareFamilyName")]
        public string FareFamilyName { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum FlightMixingType
    {

        _0 = 0,

        _1 = 1,

        _2 = 2,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum FlightSource
    {

        _0 = 0,

        _1 = 1,

        _2 = 2,

        _3 = 3,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class DebugEntry
    {
        /// <summary>
        /// Debug data label
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("label")]
        public string Label { get; set; }

        /// <summary>
        /// Debug data value
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("value")]
        public string Value { get; set; }

        /// <summary>
        /// Debug data tooltip
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("tooltip")]
        public string Tooltip { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class TFModel
    {

        [System.Text.Json.Serialization.JsonPropertyName("project")]
        public string Project { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("partner")]
        public string Partner { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("deploymentId")]
        public string DeploymentId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("currency")]
        public string Currency { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("other")]
        public string Other { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("deviceType")]
        public System.Collections.Generic.ICollection<string> DeviceType { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("tfChange")]
        public decimal TfChange { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("jsonWithEncryptedEquation")]
        public object JsonWithEncryptedEquation { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("adsv")]
        public string Adsv { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Flight
    {
        /// <summary>
        /// Flight identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public string Id { get; set; }

        /// <summary>
        /// Provider code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        public int ProviderCode { get; set; }

        /// <summary>
        /// Validating carrier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("validatingCarrier")]
        public string ValidatingCarrier { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("price")]
        [System.ComponentModel.DataAnnotations.Required]
        public PriceModel Price { get; set; } = new PriceModel();

        /// <summary>
        /// Leg collection
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("legs")]
        public System.Collections.Generic.ICollection<Leg2> Legs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("mixingType")]
        public FlightMixingType MixingType { get; set; }

        /// <summary>
        /// Negative quality score
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("nqs")]
        public decimal Nqs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("source")]
        public FlightSource Source { get; set; }

        /// <summary>
        /// Groups flights from duplication aspect. Flights with the same value should be overridden by newer package
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("flightDeduplicationId")]
        public string FlightDeduplicationId { get; set; }

        /// <summary>
        /// Related Flights with different fare family name
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("relatedFlights")]
        public System.Collections.Generic.ICollection<Flight> RelatedFlights { get; set; }

        /// <summary>
        /// Fare family name
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fareFamilyName")]
        public string FareFamilyName { get; set; }

        /// <summary>
        /// Offer identifier, will remain the same for the same flight between searches
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("offerId")]
        public string OfferId { get; set; }

        /// <summary>
        /// Flight debug data
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("debug")]
        public System.Collections.Generic.ICollection<DebugEntry> Debug { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("tfModel")]
        public TFModel TfModel { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class NqsModel
    {

        [System.Text.Json.Serialization.JsonPropertyName("modelId")]
        public string ModelId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("modelVersionId")]
        public string ModelVersionId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("deployedModelId")]
        public string DeployedModelId { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PackagedSearchResponse
    {
        /// <summary>
        /// Flight collection
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("flights")]
        public System.Collections.Generic.ICollection<Flight> Flights { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("nqsModel")]
        public NqsModel NqsModel { get; set; }

        /// <summary>
        /// Determines whether there will be more search results (true) or if the search is finished and all results have been returned (false)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("isLastPackage")]
        public bool IsLastPackage { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum ProviderCodeEnum
    {

        _0 = 0,

        _1 = 1,

        _2 = 2,

        _3 = 3,

        _4 = 4,

        _5 = 5,

        _6 = 6,

        _7 = 7,

        _8 = 8,

        _9 = 9,

        _10 = 10,

        _11 = 11,

        _12 = 12,

        _13 = 13,

        _14 = 14,

        _15 = 15,

        _16 = 16,

        _17 = 17,

        _18 = 18,

        _19 = 19,

        _20 = 20,

        _21 = 21,

        _22 = 22,

        _23 = 23,

        _24 = 24,

        _25 = 25,

        _26 = 26,

        _27 = 27,

        _28 = 28,

        _29 = 29,

        _30 = 30,

        _31 = 31,

        _32 = 32,

        _33 = 33,

        _34 = 34,

        _35 = 35,

        _36 = 36,

        _37 = 37,

        _38 = 38,

        _39 = 39,

        _40 = 40,

        _41 = 41,

        _42 = 42,

        _43 = 43,

        _44 = 44,

        _45 = 45,

        _46 = 46,

        _47 = 47,

        _48 = 48,

        _49 = 49,

        _50 = 50,

        _51 = 51,

        _52 = 52,

        _53 = 53,

        _54 = 54,

        _55 = 55,

        _56 = 56,

        _57 = 57,

        _58 = 58,

        _59 = 59,

        _60 = 60,

        _61 = 61,

        _62 = 62,

        _63 = 63,

        _64 = 64,

        _65 = 65,

        _66 = 66,

        _67 = 67,

        _68 = 68,

        _69 = 69,

        _70 = 70,

        _71 = 71,

        _72 = 72,

        _73 = 73,

        _74 = 74,

        _75 = 75,

        _77 = 77,

        _78 = 78,

        _80 = 80,

        _81 = 81,

        _82 = 82,

        _83 = 83,

        _84 = 84,

        _88 = 88,

        _99 = 99,

        _100 = 100,

        _101 = 101,

        _102 = 102,

        _103 = 103,

        _110 = 110,

        _111 = 111,

        _112 = 112,

        _113 = 113,

        _116 = 116,

        _117 = 117,

        _118 = 118,

        _119 = 119,

        _120 = 120,

        _121 = 121,

        _122 = 122,

        _123 = 123,

        _124 = 124,

        _201 = 201,

    }

    /// <summary>
    /// Fare details
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FareDetails2
    {
        /// <summary>
        /// Fare code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fareCode")]
        public string FareCode { get; set; }

        /// <summary>
        /// Office identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("officeId")]
        public string OfficeId { get; set; }

        /// <summary>
        /// Offer identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("offerId")]
        public string OfferId { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Segment2
    {
        /// <summary>
        /// Departure code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureCode")]
        public string DepartureCode { get; set; }

        /// <summary>
        /// Departure date/time - airport local time
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public System.DateTime DepartureDate { get; set; }

        /// <summary>
        /// Arrival code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalCode")]
        public string ArrivalCode { get; set; }

        /// <summary>
        /// Arrival date/time - airport local time
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalDate")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public System.DateTime ArrivalDate { get; set; }

        /// <summary>
        /// Airline code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airlineCode")]
        public string AirlineCode { get; set; }

        /// <summary>
        /// Flight number
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("flightNumber")]
        public string FlightNumber { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("fareDetails")]
        public FareDetails2 FareDetails { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("serviceClass")]
        public FlightServiceClass ServiceClass { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Leg3
    {
        /// <summary>
        /// Collection of segments
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("segments")]
        public System.Collections.Generic.ICollection<Segment2> Segments { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum SearchKind
    {

        _0 = 0,

        _1 = 1,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum OpenSearchRequestSource
    {

        _0 = 0,

        _1 = 1,

        _2 = 2,

        _3 = 3,

        _4 = 4,

        _5 = 5,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class RequestSourceDetails
    {

        [System.Text.Json.Serialization.JsonPropertyName("searchKind")]
        public SearchKind SearchKind { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("openSearchSource")]
        public OpenSearchRequestSource OpenSearchSource { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FareFamiliesQuery
    {
        /// <summary>
        /// Partner code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        /// <summary>
        /// Currency code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        public ProviderCodeEnum ProviderCode { get; set; }

        /// <summary>
        /// Leg collection
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("legs")]
        public System.Collections.Generic.ICollection<Leg3> Legs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("passengers")]
        [System.ComponentModel.DataAnnotations.Required]
        public Passengers Passengers { get; set; } = new Passengers();

        /// <summary>
        /// Is price per passenger?
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("isPricePerPax")]
        public bool IsPricePerPax { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("priceType")]
        public PriceType PriceType { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("runtimeMode")]
        public RuntimeMode RuntimeMode { get; set; }

        /// <summary>
        /// Session identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("sessionId")]
        public string SessionId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("requestSource")]
        public RequestSourceDetails RequestSource { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FareFamiliesResponse
    {
        /// <summary>
        /// Flight collection
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("flights")]
        public System.Collections.Generic.ICollection<Flight> Flights { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Leg4
    {
        /// <summary>
        /// Departure code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureCode")]
        public string DepartureCode { get; set; }

        /// <summary>
        /// Departure date, `null` if not specified
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        public string DepartureDate { get; set; }

        /// <summary>
        /// Arrival code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalCode")]
        public string ArrivalCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class OpenDatesCriteria
    {
        /// <summary>
        /// List of months for which search is performed
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("months")]
        public System.Collections.Generic.ICollection<string> Months { get; set; }

        /// <summary>
        /// List of days after which return flight is searched
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("stayLengths")]
        public System.Collections.Generic.ICollection<int> StayLengths { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class OpenSearchQuery
    {
        /// <summary>
        /// Partner code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        /// <summary>
        /// Currency code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("serviceClass")]
        public QueryServiceClass ServiceClass { get; set; }

        /// <summary>
        /// Leg collection
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("legs")]
        public System.Collections.Generic.ICollection<Leg4> Legs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("passengers")]
        [System.ComponentModel.DataAnnotations.Required]
        public Passengers Passengers { get; set; } = new Passengers();

        /// <summary>
        /// Is price per passenger?
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("isPricePerPax")]
        public bool IsPricePerPax { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("priceType")]
        public PriceType PriceType { get; set; }

        /// <summary>
        /// Number of days within which flights are searched (+/- departure and return dates)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("flex")]
        public int Flex { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("openDates")]
        [System.ComponentModel.DataAnnotations.Required]
        public OpenDatesCriteria OpenDates { get; set; } = new OpenDatesCriteria();

        [System.Text.Json.Serialization.JsonPropertyName("runtimeMode")]
        public RuntimeMode RuntimeMode { get; set; }

        /// <summary>
        /// Session identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("sessionId")]
        public string SessionId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("source")]
        public OpenSearchRequestSource Source { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class OpenSearchResponse
    {
        /// <summary>
        /// Flight collection
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("flights")]
        public System.Collections.Generic.ICollection<Flight> Flights { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("nqsModel")]
        public NqsModel NqsModel { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Leg5
    {
        /// <summary>
        /// Departure code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureCode")]
        public string DepartureCode { get; set; }

        /// <summary>
        /// Arrival code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalCode")]
        public string ArrivalCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum TransferOption
    {

        _0 = 0,

        _1 = 1,

        _2 = 2,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HourRange
    {

        [System.Text.Json.Serialization.JsonPropertyName("from")]
        public int From { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("to")]
        public int To { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceChartsFilters
    {
        /// <summary>
        /// Filter flights with given airline codes
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airlineCodes")]
        public System.Collections.Generic.ICollection<string> AirlineCodes { get; set; }

        /// <summary>
        /// Filter flights that have corresponding number of transfers
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("transfers")]
        public System.Collections.Generic.ICollection<TransferOption> Transfers { get; set; }

        /// <summary>
        /// Hour ranges for outbound flight arrival
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("travelTimeDepartureOutbound")]
        public System.Collections.Generic.ICollection<HourRange> TravelTimeDepartureOutbound { get; set; }

        /// <summary>
        /// Hour ranges for outbound flight departure
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("travelTimeArrivalOutbound")]
        public System.Collections.Generic.ICollection<HourRange> TravelTimeArrivalOutbound { get; set; }

        /// <summary>
        /// Hour ranges for return flight departure
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("travelTimeDepartureInbound")]
        public System.Collections.Generic.ICollection<HourRange> TravelTimeDepartureInbound { get; set; }

        /// <summary>
        /// Hour ranges for return flight arrival
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("travelTimeArrivalInbound")]
        public System.Collections.Generic.ICollection<HourRange> TravelTimeArrivalInbound { get; set; }

        /// <summary>
        /// Provider code list (for debug mode)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("providerCodes")]
        public System.Collections.Generic.ICollection<int> ProviderCodes { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceChartsQuery
    {
        /// <summary>
        /// Partner code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        /// <summary>
        /// Currency code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        /// <summary>
        /// Leg collection
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("legs")]
        public System.Collections.Generic.ICollection<Leg5> Legs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("passengers")]
        [System.ComponentModel.DataAnnotations.Required]
        public Passengers Passengers { get; set; } = new Passengers();

        /// <summary>
        /// List of days after which return flight is searched
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("stayLengths")]
        public System.Collections.Generic.ICollection<int> StayLengths { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("filters")]
        public PriceChartsFilters Filters { get; set; }

        /// <summary>
        /// Is price per passenger?
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("isPricePerPax")]
        public bool IsPricePerPax { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("priceType")]
        public PriceType PriceType { get; set; }

        /// <summary>
        /// Session identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("sessionId")]
        public string SessionId { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class MonthlyPrice
    {
        /// <summary>
        /// Month to which the price applies
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("month")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Month { get; set; }

        /// <summary>
        /// Price of the flight in a given month
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("amount")]
        public decimal Amount { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceChartsResponse
    {
        /// <summary>
        /// Monthly price model
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("monthlyPrices")]
        public System.Collections.Generic.ICollection<MonthlyPrice> MonthlyPrices { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class BestOffersQuery
    {
        /// <summary>
        /// Partner code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        /// <summary>
        /// Currency code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        /// <summary>
        /// List of codes for origin countries
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("originCountryCodes")]
        public System.Collections.Generic.ICollection<string> OriginCountryCodes { get; set; }

        /// <summary>
        /// List of codes for destination countries
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("destinationCountryCodes")]
        public System.Collections.Generic.ICollection<string> DestinationCountryCodes { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("runtimeMode")]
        public RuntimeMode RuntimeMode { get; set; }

        /// <summary>
        /// Session identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("sessionId")]
        public string SessionId { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Origin
    {
        /// <summary>
        /// Airport code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airportCode")]
        public string AirportCode { get; set; }

        /// <summary>
        /// City code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("cityCode")]
        public string CityCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Destination
    {
        /// <summary>
        /// Airport code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airportCode")]
        public string AirportCode { get; set; }

        /// <summary>
        /// Multiport code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("multiportCode")]
        public string MultiportCode { get; set; }

        /// <summary>
        /// City code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("cityCode")]
        public string CityCode { get; set; }

        /// <summary>
        /// Country code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("countryCode")]
        public string CountryCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Offer
    {

        [System.Text.Json.Serialization.JsonPropertyName("origin")]
        public Origin Origin { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("destination")]
        public Destination Destination { get; set; }

        /// <summary>
        /// Lowest price amount
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("lowestPriceAmount")]
        public decimal LowestPriceAmount { get; set; }

        /// <summary>
        /// Offer identifier of the cheapest flight
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("flightOfferId")]
        public string FlightOfferId { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class BestOffersResponse
    {
        /// <summary>
        /// Currency code, common for all prices
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        /// <summary>
        /// List of best offers
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("offers")]
        public System.Collections.Generic.ICollection<Offer> Offers { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Leg6
    {
        /// <summary>
        /// Departure code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureCode")]
        public string DepartureCode { get; set; }

        /// <summary>
        /// Departure date
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string DepartureDate { get; set; }

        /// <summary>
        /// Arrival code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalCode")]
        public string ArrivalCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceCalendarQuery
    {
        /// <summary>
        /// Partner code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        /// <summary>
        /// Currency code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        /// <summary>
        /// Leg collection
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("legs")]
        public System.Collections.Generic.ICollection<Leg6> Legs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("passengers")]
        [System.ComponentModel.DataAnnotations.Required]
        public Passengers Passengers { get; set; } = new Passengers();

        /// <summary>
        /// Is price per passenger?
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("isPricePerPax")]
        public bool IsPricePerPax { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("priceType")]
        public PriceType PriceType { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("runtimeMode")]
        public RuntimeMode RuntimeMode { get; set; }

        /// <summary>
        /// Session identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("sessionId")]
        public string SessionId { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceCalendarCell
    {
        /// <summary>
        /// Departure date
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string DepartureDate { get; set; }

        /// <summary>
        /// Return date, null for one way flights
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("returnDate")]
        public string ReturnDate { get; set; }

        /// <summary>
        /// Cheapest flight price
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("amount")]
        public decimal Amount { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceCalendar
    {
        /// <summary>
        /// List of calendar cells with the cheapest flight price
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("cells")]
        public System.Collections.Generic.ICollection<PriceCalendarCell> Cells { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceCalendarResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("priceCalendar")]
        [System.ComponentModel.DataAnnotations.Required]
        public PriceCalendar PriceCalendar { get; set; } = new PriceCalendar();

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceCalendarOfferQuery_Leg
    {
        /// <summary>
        /// Departure code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureCode")]
        public string DepartureCode { get; set; }

        /// <summary>
        /// Departure date
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string DepartureDate { get; set; }

        /// <summary>
        /// Arrival code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalCode")]
        public string ArrivalCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceCalendarOfferQuery
    {
        /// <summary>
        /// Partner code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        /// <summary>
        /// Currency code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        /// <summary>
        /// Leg collection
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("legs")]
        public System.Collections.Generic.ICollection<PriceCalendarOfferQuery_Leg> Legs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("passengers")]
        [System.ComponentModel.DataAnnotations.Required]
        public Passengers Passengers { get; set; } = new Passengers();

        /// <summary>
        /// Is price per passenger?
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("isPricePerPax")]
        public bool IsPricePerPax { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("priceType")]
        public PriceType PriceType { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("runtimeMode")]
        public RuntimeMode RuntimeMode { get; set; }

        /// <summary>
        /// Session identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("sessionId")]
        public string SessionId { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PriceCalendarOfferResponse
    {
        /// <summary>
        /// List of calendar offers
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("flights")]
        public System.Collections.Generic.ICollection<Flight> Flights { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("nqsModel")]
        public NqsModel NqsModel { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class AlternativeAirportsQuery_Leg
    {
        /// <summary>
        /// Departure code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureCode")]
        public string DepartureCode { get; set; }

        /// <summary>
        /// Departure date
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string DepartureDate { get; set; }

        /// <summary>
        /// Arrival code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalCode")]
        public string ArrivalCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class AlternativeAirportsQuery
    {
        /// <summary>
        /// Partner code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        /// <summary>
        /// Currency code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        /// <summary>
        /// Leg collection
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("legs")]
        public System.Collections.Generic.ICollection<AlternativeAirportsQuery_Leg> Legs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("passengers")]
        [System.ComponentModel.DataAnnotations.Required]
        public Passengers Passengers { get; set; } = new Passengers();

        /// <summary>
        /// Is price per passenger?
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("isPricePerPax")]
        public bool IsPricePerPax { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("priceType")]
        public PriceType PriceType { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("runtimeMode")]
        public RuntimeMode RuntimeMode { get; set; }

        /// <summary>
        /// Session identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("sessionId")]
        public string SessionId { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Airport
    {
        /// <summary>
        /// Airport code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public string Code { get; set; }

        /// <summary>
        /// City code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("cityCode")]
        public string CityCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Offer2
    {

        [System.Text.Json.Serialization.JsonPropertyName("airport")]
        [System.ComponentModel.DataAnnotations.Required]
        public Airport Airport { get; set; } = new Airport();

        /// <summary>
        /// Distance to origin airport in kilometers
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("originAirportDistanceKm")]
        public int OriginAirportDistanceKm { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flight")]
        [System.ComponentModel.DataAnnotations.Required]
        public Flight Flight { get; set; } = new Flight();

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class AlternativeAirportsResponse
    {
        /// <summary>
        /// Nearby departure airports offers
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("nearbyDepartureOffers")]
        public System.Collections.Generic.ICollection<Offer2> NearbyDepartureOffers { get; set; }

        /// <summary>
        /// Nearby arrival airports offers
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("nearbyArrivalOffers")]
        public System.Collections.Generic.ICollection<Offer2> NearbyArrivalOffers { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("nqsModel")]
        public NqsModel NqsModel { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum CabinCode
    {

        _0 = 0,

        _1 = 1,

        _2 = 2,

        _3 = 3,

        _4 = 4,

    }

    /// <summary>
    /// Trip specification (one-way or round-trip).
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class TripSpecification
    {
        /// <summary>
        /// The list of IATA airport codes to query for. Usually contains a
        /// <br/>single IATA code, but can sometimes contain several IATA codes.
        /// <br/>As an example, a user querying flight from New York might imply
        /// <br/>querying your API for JFK, LGA, EWR.
        /// <br/>In some rare cases, the list can also contain the IATA code for a
        /// <br/>train station.
        /// <br/>For partner API implementation not able to support querying multiple
        /// <br/>IATA codes, it is recommended to take the first IATA code in the
        /// <br/>list.
        /// <br/>It is allowed to return open-jaws in the response in cases of multiple
        /// <br/>departure_airports or arrival_airports.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureAirports")]
        public System.Collections.Generic.ICollection<string> DepartureAirports { get; set; }

        /// <summary>
        /// The list of IATA arrival codes to query for.
        /// <br/>See comment for departure_airports.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalAirports")]
        public System.Collections.Generic.ICollection<string> ArrivalAirports { get; set; }

        /// <summary>
        /// Departure date - "YYYY-MM-DD".
        /// <br/>Required.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string DepartureDate { get; set; }

        /// <summary>
        /// Return date - "YYYY-MM-DD", applies to round-trip queries.
        /// <br/>Optional.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("returnDate")]
        public string ReturnDate { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GoogleLiveSearchRequest
    {
        /// <summary>
        /// The currency selected by the user - ISO 4217 format. Client expects
        /// <br/>this currency to be returned in SearchFlightPricesResponse unless
        /// <br/>it is not supported by the server in which case another currency
        /// <br/>may be provided.
        /// <br/>Providing different currency results in degraded user experience.
        /// <br/>Required.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        /// <summary>
        /// The language selected by the user - ISO 639-2 format.
        /// <br/>Applies in particular to the bookingUrl element in the response,
        /// <br/>selecting the language.
        /// <br/>Required.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("languageCode")]
        public string LanguageCode { get; set; }

        /// <summary>
        /// The country selected by the user - Alpha-2 ISO 3166-1 format.
        /// <br/>Applies in particular to the BookingUrl element in the response,
        /// <br/>typically selecting the domain.
        /// <br/>Required.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("countryCode")]
        public string CountryCode { get; set; }

        /// <summary>
        /// Total number of adults (13+ years old). Mandatory and must be &gt;= 1.
        /// <br/>Required.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("adults")]
        public int Adults { get; set; }

        /// <summary>
        /// Total number of children/infants in seat (2 - 12 years old).
        /// <br/>Optional.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("children")]
        public int Children { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("infants")]
        public int Infants { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("intendedCabin")]
        public CabinCode IntendedCabin { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("tripSpec")]
        [System.ComponentModel.DataAnnotations.Required]
        public TripSpecification TripSpec { get; set; } = new TripSpecification();

        /// <summary>
        /// Version of the API. The API described in this document is version 1.
        /// <br/>Required.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("version")]
        public int Version { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    /// <summary>
    /// Leg, as defined by IATA.
    /// <br/>The smallest unit of air travel which starts with consists of a
    /// <br/>single takeoff followed by a single landing. E.g., a segment with a
    /// <br/>technical stop consists of 2 legs.
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Leg7
    {
        /// <summary>
        /// 3 letter IATA code of the origin airport.
        /// <br/>Required.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureAirport")]
        public string DepartureAirport { get; set; }

        /// <summary>
        /// Scheduled departure time, ISO 8601 format for the time zone of the
        /// <br/>departure airport. For example: "2023-12-31T16:30:00".
        /// <br/>Required.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureDateTime")]
        public System.DateTime DepartureDateTime { get; set; }

        /// <summary>
        /// 3 letter IATA code of the destination airport.
        /// <br/>Required.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalAirport")]
        public string ArrivalAirport { get; set; }

        /// <summary>
        /// Scheduled arrival time, ISO 8601 format for the time zone of the
        /// <br/>arrival airport or station. For example: "2023-12-31T17:30:00".
        /// <br/>Required.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalDateTime")]
        public System.DateTime ArrivalDateTime { get; set; }

        /// <summary>
        /// Marketing carrier of the segment, 2 letter IATA code.
        /// <br/>For example: "LX".
        /// <br/>Required.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("carrier")]
        public string Carrier { get; set; }

        /// <summary>
        /// Flight number. For example: "1624".
        /// <br/>Required.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("flightNumber")]
        public string FlightNumber { get; set; }

        /// <summary>
        /// Operating carrier of the segment, 2 letter IATA code.
        /// <br/>For example: "LH". If flights is operated and marketed by the same
        /// <br/>airline, operating carrier should be set to the same value as
        /// <br/>carrier. If not present, the itinerary might not appear in search
        /// <br/>results.
        /// <br/>Optional.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("operatingCarrier")]
        public string OperatingCarrier { get; set; }

        /// <summary>
        /// Operating flight number.
        /// <br/>For example: "431". If not present, the itinerary might not appear
        /// <br/>in search results.
        /// <br/>Optional.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("operatingFlightNumber")]
        public string OperatingFlightNumber { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    /// <summary>
    /// Description of a baggage allowance restricted by per unit weight.
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Baggage
    {
        /// <summary>
        /// Maximum weight of the baggage per unit, in kg.
        /// <br/>Required.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("maxWeight")]
        public float MaxWeight { get; set; }

        /// <summary>
        /// Maximum number of baggages.
        /// <br/>Required.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("count")]
        public int Count { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    /// <summary>
    /// Description of baggage allowance restricted by total maximum
    /// <br/>weight.
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class TotalBaggage
    {
        /// <summary>
        /// Total maximum weight of all the baggage.
        /// <br/>Required.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("totalMaxWeight")]
        public int TotalMaxWeight { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    /// <summary>
    /// Baggage allowance (carry and checked)
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class BaggageAllowance
    {

        [System.Text.Json.Serialization.JsonPropertyName("carryOnBaggage")]
        public Baggage CarryOnBaggage { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("carryOnTotalBaggage")]
        public TotalBaggage CarryOnTotalBaggage { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("checkedOnBaggage")]
        public Baggage CheckedOnBaggage { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("checkedTotalBaggage")]
        public TotalBaggage CheckedTotalBaggage { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    /// <summary>
    /// Segment, as defined by IATA. One or more consecutive legs on the same
    /// <br/>flight and within the same itinerary variation.
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Segment3
    {
        /// <summary>
        /// Consecutive legs, at least one leg must be defined.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("legs")]
        public System.Collections.Generic.ICollection<Leg7> Legs { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("cabinCode")]
        public CabinCode CabinCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("baggageAllowance")]
        public BaggageAllowance BaggageAllowance { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    /// <summary>
    /// A slice, comprised of multiple segments. Onbound or inbound part of a
    /// <br/>trip.
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Slice
    {
        /// <summary>
        /// Segment, as defined by IATA. One or more consecutive legs on the same
        /// <br/>flight and within the same itinerary variation.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("segments")]
        public System.Collections.Generic.ICollection<Segment3> Segments { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Price
    {
        /// <summary>
        /// Total price of the itinerary with the decimal separated by '.'. Must
        /// <br/>include all fees as displayed on the landing page. For example:
        /// <br/>"345.95".
        /// <br/>Required.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("totalDecimal")]
        public string TotalDecimal { get; set; }

        /// <summary>
        /// The currency of the fare price and that will be displayed
        /// <br/>on your landing page - ISO 4217 format. For example: "USD".
        /// <br/>It is strongly encouraged to use the same currency as the one in the
        /// <br/>request.
        /// <br/>Required.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum VirtualInterlineType
    {

        _0 = 0,

        _1 = 1,

        _2 = 2,

        _3 = 3,

    }

    /// <summary>
    /// An itinerary offered on your website.
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Itinerary
    {

        [System.Text.Json.Serialization.JsonPropertyName("outbound")]
        public Slice Outbound { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("inbound")]
        public Slice Inbound { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("price")]
        public Price Price { get; set; }

        /// <summary>
        /// Booking URL redirecting the user to the flight checkout page.
        /// <br/>Mandatory for OTAs integrating with Google Flights.
        /// <br/>Optional otherwise.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("bookingUrl")]
        public string BookingUrl { get; set; }

        /// <summary>
        /// Time to live of the itinerary in seconds. If not present, assumes
        /// <br/>the itinerary to be bookable using the provided BookingUrl
        /// <br/>for 3600 seconds (1 hour).
        /// <br/>Optional.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("validitySeconds")]
        public int? ValiditySeconds { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("virtualInterlineType")]
        public VirtualInterlineType VirtualInterlineType { get; set; }

        /// <summary>
        /// Link to a help page on partner website that explains the rules
        /// <br/>regarding their virtual interline content.
        /// <br/>Must be set if virtualInterlineType is not DEFAULT_TYPE.
        /// <br/>Optional.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("virtualInterlineDescriptionUrl")]
        public string VirtualInterlineDescriptionUrl { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Log
    {
        /// <summary>
        /// Error or warning code semantic defined by the partner.
        /// <br/>Used to aggregate errors on the Google internal dashboard.
        /// <br/>Should have limited cardinality.
        /// <br/>Example: "ERR_UNSUPPORTED_AIRPORT", "WARN_PARTIAL_RESULT".
        /// <br/>Required.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public string Code { get; set; }

        /// <summary>
        /// Human readable description of the error.
        /// <br/>Example: "Airport XZY is not supported.",
        /// <br/>"Partial result returned due to internal timeout (carrier 6E, LH)"
        /// <br/>Required.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("description")]
        public string Description { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GoogleLiveSearchResponse
    {
        /// <summary>
        /// The list of offered itineraries
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("itineraries")]
        public System.Collections.Generic.ICollection<Itinerary> Itineraries { get; set; }

        /// <summary>
        /// Warnings if any occurred.
        /// <br/>Can be returned along with itineraries.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("warnings")]
        public System.Collections.Generic.ICollection<Log> Warnings { get; set; }

        /// <summary>
        /// Errors if any occurred. Mutually exclusive with itineraries.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("errors")]
        public System.Collections.Generic.ICollection<Log> Errors { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum ServiceClassEnum
    {

        _0 = 0,

        _1 = 1,

        _2 = 2,

        _3 = 3,

        _4 = 4,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SearchFlightsQuery_Leg
    {
        /// <summary>
        /// Departure code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureCode")]
        public string DepartureCode { get; set; }

        /// <summary>
        /// Departure date
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        public System.DateTime DepartureDate { get; set; }

        /// <summary>
        /// Arrival code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalCode")]
        public string ArrivalCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SearchFlightsQuery_PackageSearchConfig
    {
        /// <summary>
        /// Search instance identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("searchId")]
        public string SearchId { get; set; }

        /// <summary>
        /// Number of results portion (package) to return
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("portionId")]
        public int PortionId { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SearchFlightsQuery_FlexSearchConfig
    {
        /// <summary>
        /// Determines if flights cache based flex search is enabled
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("enabled")]
        public bool Enabled { get; set; }

        /// <summary>
        /// Flex range in days
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("range")]
        public int Range { get; set; }

        /// <summary>
        /// Flex flights limit
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("limit")]
        public int Limit { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum RuntimeModeEnum
    {

        _0 = 0,

        _1 = 1,

        _2 = 2,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum PersonTypeEnum
    {

        _0 = 0,

        _1 = 1,

        _2 = 2,

        _3 = 3,

        _4 = 4,

        _5 = 5,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ISearchFlightsRequestPassenger
    {

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public PersonTypeEnum Code { get; set; }

        /// <summary>
        /// Number of passengers of a specified type
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("count")]
        public int Count { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SearchFlightsQuery
    {
        /// <summary>
        /// Requested partner code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        /// <summary>
        /// Requested currency code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        /// <summary>
        /// Language code, determines translation language for dictionary elements
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("languageCode")]
        public string LanguageCode { get; set; }

        /// <summary>
        /// Search session identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("sessionId")]
        public string SessionId { get; set; }

        /// <summary>
        /// Determines whether the response should contain flights organized in leg groups (true) or ungrouped data (false). Default is false.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("packageSearch")]
        public bool? PackageSearch { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("serviceClass")]
        public ServiceClassEnum ServiceClass { get; set; }

        /// <summary>
        /// Price type: 5 - base price, 15 - provider price, otherwise provider price + TF
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("priceType")]
        public int? PriceType { get; set; }

        /// <summary>
        /// Determines if price should be returned per pax (true) or as a total (false). Default is false.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("pricePerPax")]
        public bool? PricePerPax { get; set; }

        /// <summary>
        /// Journey legs
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("legs")]
        public System.Collections.Generic.ICollection<SearchFlightsQuery_Leg> Legs { get; set; }

        /// <summary>
        /// Determines if returned legs should contain deep links
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("deepLink")]
        public bool DeepLink { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("searchConfig")]
        public SearchFlightsQuery_PackageSearchConfig SearchConfig { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flex")]
        public SearchFlightsQuery_FlexSearchConfig Flex { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("runtimeMode")]
        public RuntimeModeEnum RuntimeMode { get; set; }

        /// <summary>
        /// Passenger configuration, defaults to single adult
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("passengers")]
        public System.Collections.Generic.ICollection<ISearchFlightsRequestPassenger> Passengers { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class StopoverItem
    {

        [System.Text.Json.Serialization.JsonPropertyName("airportCode")]
        public string AirportCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalDate")]
        public System.DateTime? ArrivalDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureDate")]
        public System.DateTime? DepartureDate { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrivalTerminal")]
        public string ArrivalTerminal { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("departureTerminal")]
        public string DepartureTerminal { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("cityCode")]
        public string CityCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("countryCode")]
        public string CountryCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FareItem
    {
        /// <summary>
        /// Passenger code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("pc")]
        public int Pc { get; set; }

        /// <summary>
        /// Fare code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fc")]
        public string Fc { get; set; }

        /// <summary>
        /// Additional data
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("addData")]
        public string AddData { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum FlightFacilityType
    {

        _0 = 0,

        _1 = 1,

        _2 = 2,

        _3 = 3,

        _4 = 4,

        _5 = 5,

        _6 = 6,

        _7 = 7,

        _8 = 8,

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightFacility
    {

        [System.Text.Json.Serialization.JsonPropertyName("t")]
        public FlightFacilityType T { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("d")]
        public string D { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("o")]
        public bool O { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightAttribute
    {
        /// <summary>
        /// Flight attribute type
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("t")]
        public string T { get; set; }

        /// <summary>
        /// Flight attribute description
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("d")]
        public string D { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightProperty
    {
        /// <summary>
        /// Flight property type
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("t")]
        public string T { get; set; }

        /// <summary>
        /// Flight property key
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("k")]
        public string K { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightAmenityPrice
    {
        /// <summary>
        /// Flight amenity price currency
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("c")]
        public string C { get; set; }

        /// <summary>
        /// Flight amenity price amount
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("a")]
        public decimal A { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightAmenity
    {
        /// <summary>
        /// Flight amenity type
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("t")]
        public string T { get; set; }

        /// <summary>
        /// Flight amenity key
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("k")]
        public string K { get; set; }

        /// <summary>
        /// Flight amenity is available
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ia")]
        public bool Ia { get; set; }

        /// <summary>
        /// Flight amenity is paid
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ip")]
        public bool Ip { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("p")]
        public FlightAmenityPrice P { get; set; }

        /// <summary>
        /// Flight amenity weight in kgs
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("w")]
        public int W { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SegmentItem
    {
        /// <summary>
        /// Airline code, example values: LO, W6, FR,
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ac")]
        public string Ac { get; set; }

        /// <summary>
        /// Departure location code,
        /// <br/>3-letter IATA airport code, example values: WAW, LTN
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("dac")]
        public string Dac { get; set; }

        /// <summary>
        /// Departure city code, example values: WAW, LON
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("dcc")]
        public string Dcc { get; set; }

        /// <summary>
        /// Departure date, example values: Date(1709099700000), Date(1713870900000)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("dd")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public System.DateTime Dd { get; set; }

        /// <summary>
        /// Arrival location code,
        /// <br/>3-letter IATA airport code, example values: WAW, LTN
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("aac")]
        public string Aac { get; set; }

        /// <summary>
        /// Arrival city code, example values: WAW, LON
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("acc")]
        public string Acc { get; set; }

        /// <summary>
        /// Arrival date, example values: Date(1709099700000), Date(1713962400000)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ad")]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public System.DateTime Ad { get; set; }

        /// <summary>
        /// Flight time in hh:mm format, example values: 2:35
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ft")]
        public string Ft { get; set; }

        /// <summary>
        /// Flight number, example values: 286, 3883
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fn")]
        public string Fn { get; set; }

        /// <summary>
        /// Provider code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("pc")]
        public int Pc { get; set; }

        /// <summary>
        /// Stopovers
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("sac")]
        public System.Collections.Generic.ICollection<StopoverItem> Sac { get; set; }

        /// <summary>
        /// Fares
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fares")]
        public System.Collections.Generic.ICollection<FareItem> Fares { get; set; }

        /// <summary>
        /// Booking class code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("bc")]
        public string Bc { get; set; }

        /// <summary>
        /// Service class code
        /// <br/>Any = 0, Economy = 1, First = 2, Business = 3, EconomyPremium = 4
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("sc")]
        public int Sc { get; set; }

        /// <summary>
        /// Mixed flight identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("mid")]
        public int Mid { get; set; }

        /// <summary>
        /// Flight facilities
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ff")]
        public System.Collections.Generic.ICollection<FlightFacility> Ff { get; set; }

        /// <summary>
        /// Flight attributes
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fa")]
        public System.Collections.Generic.ICollection<FlightAttribute> Fa { get; set; }

        /// <summary>
        /// Flight properties
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fp")]
        public System.Collections.Generic.ICollection<FlightProperty> Fp { get; set; }

        /// <summary>
        /// Flight amenities
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fam")]
        public System.Collections.Generic.ICollection<FlightAmenity> Fam { get; set; }

        /// <summary>
        /// Aircraft code, example values: Boeing737, Embraer195, CanadairRegionalJet900
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("a")]
        public string A { get; set; }

        /// <summary>
        /// Is self transfer
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("iST")]
        public bool IST { get; set; }

        /// <summary>
        /// Is protected
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("iP")]
        public bool? IP { get; set; }

        /// <summary>
        /// Flight is operated by, example values: LO, W6, FR,
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ob")]
        public string Ob { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class LegItem
    {
        /// <summary>
        /// Leg identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fid")]
        public string Fid { get; set; }

        /// <summary>
        /// Unique offer identifier in search results scope. Should return the same value across search requests.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("oid")]
        public string Oid { get; set; }

        /// <summary>
        /// Flight time in hh:mm format, example values: 2:35
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ft")]
        public string Ft { get; set; }

        /// <summary>
        /// Available seats count, max to 9
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("asc")]
        public int Asc { get; set; }

        /// <summary>
        /// Encoded leg locator
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ll")]
        public string Ll { get; set; }

        /// <summary>
        /// Encoded leg locators for bundled flights
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ffll")]
        public System.Collections.Generic.IDictionary<string, string> Ffll { get; set; }

        /// <summary>
        /// Negative quality score
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("nqs")]
        public decimal Nqs { get; set; }

        /// <summary>
        /// DeepLink (pricing indicator) used by metasearch
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("pi")]
        public string Pi { get; set; }

        /// <summary>
        /// Optional reservation (pending airlines)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("or")]
        public bool Or { get; set; }

        /// <summary>
        /// Is the departure airport different from requested airport (nearby airport)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("dn")]
        public bool Dn { get; set; }

        /// <summary>
        /// Is the arrival airport different from requested airport (nearby airport)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("an")]
        public bool An { get; set; }

        /// <summary>
        /// Is the departure date different from requested date (flex result)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ddn")]
        public bool Ddn { get; set; }

        /// <summary>
        /// Leg segments
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("segs")]
        public System.Collections.Generic.ICollection<SegmentItem> Segs { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    /// <summary>
    /// Leg group
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightItemWithLegGroups_LegGroup
    {
        /// <summary>
        /// Flight legs in group
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("legs")]
        public System.Collections.Generic.ICollection<LegItem> Legs { get; set; }

        /// <summary>
        /// Is the departure airport different from requested airport (nearby airport)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("dn")]
        public bool Dn { get; set; }

        /// <summary>
        /// Is the arrival airport different from requested airport (nearby airport)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("an")]
        public bool An { get; set; }

        /// <summary>
        /// Is the departure date different from requested date (flex result)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ddn")]
        public bool Ddn { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightItemWithLegGroups
    {
        /// <summary>
        /// Legs grouped by price
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("lg")]
        public System.Collections.Generic.ICollection<FlightItemWithLegGroups_LegGroup> Lg { get; set; }

        /// <summary>
        /// Negative quality score
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("nqs")]
        public decimal Nqs { get; set; }

        /// <summary>
        /// Offer ids of best legs, according to NQS
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("nqsoid")]
        public System.Collections.Generic.ICollection<string> Nqsoid { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("__type")]
        [System.Obsolete]
        public string __type { get; set; }

        /// <summary>
        /// Currency code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("c")]
        public string C { get; set; }

        /// <summary>
        /// Currency symbol
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("cs")]
        public string Cs { get; set; }

        /// <summary>
        /// DU fee
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("du")]
        public decimal Du { get; set; }

        /// <summary>
        /// Provider code
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("pc")]
        public int Pc { get; set; }

        /// <summary>
        /// Flight price
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("p")]
        public decimal P { get; set; }

        /// <summary>
        /// Total payment price
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("tp")]
        public decimal Tp { get; set; }

        /// <summary>
        /// Price per passenger
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ppx")]
        public decimal Ppx { get; set; }

        /// <summary>
        /// Total base price
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("tbf")]
        public decimal Tbf { get; set; }

        /// <summary>
        /// Total taxes and fees
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("ttf")]
        public decimal Ttf { get; set; }

        /// <summary>
        /// Transaction fee amount
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("tf")]
        public decimal Tf { get; set; }

        /// <summary>
        /// Discount
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("d")]
        public decimal D { get; set; }

        /// <summary>
        /// Flight identifier
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fid")]
        public string Fid { get; set; }

        /// <summary>
        /// Flight mixing type
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("fmt")]
        public string Fmt { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    /// <summary>
    /// Dictionary item
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class DictionaryItem_1OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e
    {
        /// <summary>
        /// Item key
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("key")]
        public string Key { get; set; }

        /// <summary>
        /// Item type
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("type")]
        public string Type { get; set; }

        /// <summary>
        /// Item value
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("value")]
        public string Value { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class KeyValuePair_2OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798eAndOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e
    {

        [System.Text.Json.Serialization.JsonPropertyName("key")]
        public string Key { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("value")]
        public string Value { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    /// <summary>
    /// Price object
    /// </summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Price2
    {
        /// <summary>
        /// Currency
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; }

        /// <summary>
        /// Amount
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("amount")]
        public decimal Amount { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SearchFilters
    {
        /// <summary>
        /// List of departure location codes
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("departureCodes")]
        public System.Collections.Generic.ICollection<string> DepartureCodes { get; set; }

        /// <summary>
        /// List of arrival location codes
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("arrivalCodes")]
        public System.Collections.Generic.ICollection<string> ArrivalCodes { get; set; }

        /// <summary>
        /// List of transfer codes
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("transferCodes")]
        public System.Collections.Generic.ICollection<string> TransferCodes { get; set; }

        /// <summary>
        /// List of airport names
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airportsNames")]
        public System.Collections.Generic.ICollection<DictionaryItem_1OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e> AirportsNames { get; set; }

        /// <summary>
        /// List of airport city mappings
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airportsCityCodes")]
        public System.Collections.Generic.ICollection<DictionaryItem_1OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e> AirportsCityCodes { get; set; }

        /// <summary>
        /// List of airport city mappings
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airportsCountryCodes")]
        public System.Collections.Generic.ICollection<KeyValuePair_2OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798eAndOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e> AirportsCountryCodes { get; set; }

        /// <summary>
        /// List of city names
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("cityNames")]
        public System.Collections.Generic.ICollection<DictionaryItem_1OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e> CityNames { get; set; }

        /// <summary>
        /// List of country names
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("countryNames")]
        public System.Collections.Generic.ICollection<KeyValuePair_2OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798eAndOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e> CountryNames { get; set; }

        /// <summary>
        /// List of airline codes
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airlineCodes")]
        public System.Collections.Generic.ICollection<string> AirlineCodes { get; set; }

        /// <summary>
        /// List of airline names
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airlineNames")]
        public System.Collections.Generic.ICollection<DictionaryItem_1OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e> AirlineNames { get; set; }

        /// <summary>
        /// List of airline types
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airlineTypes")]
        public System.Collections.Generic.ICollection<DictionaryItem_1OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e> AirlineTypes { get; set; }

        /// <summary>
        /// List of provider codes
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("providerCodes")]
        public System.Collections.Generic.ICollection<DictionaryItem_1OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e> ProviderCodes { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airlineAlliances")]
        public System.Collections.Generic.ICollection<DictionaryItem_1OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e> AirlineAlliances { get; set; }

        /// <summary>
        /// Stopovers
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("stopovers")]
        public System.Collections.Generic.ICollection<int> Stopovers { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airlinePopularity")]
        [System.Obsolete]
        public System.Collections.Generic.ICollection<string> AirlinePopularity { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("airportsWebNames")]
        [System.Obsolete]
        public System.Collections.Generic.ICollection<DictionaryItem_1OfOfStringAndCoreLibAnd_0AndCulture_neutralAndPublicKeyToken_7cec85d7bea7798e> AirportsWebNames { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("maxPromotedFlightsForHighlightCategory")]
        [System.Obsolete]
        public int MaxPromotedFlightsForHighlightCategory { get; set; }

        /// <summary>
        /// Obsolete - for backward compatibility only
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("maxPromotedFlightsForRaiseCategory")]
        [System.Obsolete]
        public int MaxPromotedFlightsForRaiseCategory { get; set; }

        /// <summary>
        /// Shortest flight time returned
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("maxPromotedFlightsForTooltipCategory")]
        [System.Obsolete]
        public int MaxPromotedFlightsForTooltipCategory { get; set; }

        /// <summary>
        /// Longest flight time returned
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("maximalFlightTime")]
        [System.Obsolete]
        public string MaximalFlightTime { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("maximalPrice")]
        public Price2 MaximalPrice { get; set; }

        /// <summary>
        /// Shortest flight time returned
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("minimalFlightTime")]
        [System.Obsolete]
        public string MinimalFlightTime { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("minimalPrice")]
        public Price2 MinimalPrice { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class AdditionalInformation
    {

        [System.Text.Json.Serialization.JsonPropertyName("key")]
        public string Key { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("severity")]
        public int Severity { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SearchFlightsResponseGrouped
    {
        /// <summary>
        /// Flight results organized in leg groups
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("items")]
        public System.Collections.Generic.ICollection<FlightItemWithLegGroups> Items { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("searchFilters")]
        public SearchFilters SearchFilters { get; set; }

        /// <summary>
        /// Determines whether there will be more search results (true) or if the search is finished and all results have been returned (false)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("moreResultsAvailable")]
        public bool MoreResultsAvailable { get; set; }

        /// <summary>
        /// Server name - has to be an empty string for compatibility reasons
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("serverName")]
        [System.Obsolete]
        public string ServerName { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("airTrafficRuleId")]
        [System.Obsolete]
        public int AirTrafficRuleId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("airTrafficRuleVersion")]
        [System.Obsolete]
        public string AirTrafficRuleVersion { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("priceType")]
        [System.Obsolete]
        public int PriceType { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("isPricePerPax")]
        [System.Obsolete]
        public bool IsPricePerPax { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("alternativeItems")]
        [System.Obsolete]
        public System.Collections.Generic.ICollection<string> AlternativeItems { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("pagingLocator")]
        [System.Obsolete]
        public string PagingLocator { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("flexDateCalendarPercentCoverage")]
        [System.Obsolete]
        public int FlexDateCalendarPercentCoverage { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("sessionId")]
        [System.Obsolete]
        public string SessionId { get; set; }

        /// <summary>
        /// Variant code for A/B tests
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("abVariant")]
        public string AbVariant { get; set; }

        /// <summary>
        /// Details of quality score ML model, used for NQS calculation
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("nqsm")]
        public string Nqsm { get; set; }

        /// <summary>
        /// Additional messages related to search execution process (e.g. airport redirects)
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("additionalInformation")]
        public System.Collections.Generic.ICollection<AdditionalInformation> AdditionalInformation { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class SearchFlightsRequestPassenger
    {

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public PersonTypeEnum Code { get; set; }

        /// <summary>
        /// Number of passengers of a specified type
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("count")]
        public int Count { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("ages")]
        public System.Collections.Generic.ICollection<int> Ages { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class OfferLiveCheckRequest
    {

        [System.Text.Json.Serialization.JsonPropertyName("flightOfferKeys")]
        public System.Collections.Generic.ICollection<string> FlightOfferKeys { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("passengers")]
        public System.Collections.Generic.ICollection<SearchFlightsRequestPassenger> Passengers { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("partnerCode")]
        public string PartnerCode { get; set; }

        /// <summary>
        /// If true, prices are returned in default partner currency. 
        /// <br/>Otherwise provider currency is returned.
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("usePartnerCurrency")]
        public bool UsePartnerCurrency { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("calculateTF")]
        public bool CalculateTF { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public enum OfferAvailability
    {
        Unconfirmed,
        Available,
        NotAvailable,
    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CacheFlightOfferDetails
    {

        [System.Text.Json.Serialization.JsonPropertyName("price")]
        public decimal Price { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ProviderFlightOfferDetails
    {

        [System.Text.Json.Serialization.JsonPropertyName("legLocators")]
        public System.Collections.Generic.ICollection<string> LegLocators { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("price")]
        public decimal Price { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("tf")]
        public decimal Tf { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FlightOffer
    {
        /// <summary>
        /// Flight offer key from cache
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("flightOfferKey")]
        public string FlightOfferKey { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string Status { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("cacheOfferDetails")]
        public CacheFlightOfferDetails CacheOfferDetails { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("providerOfferDetails")]
        public ProviderFlightOfferDetails ProviderOfferDetails { get; set; }

        /// <summary>
        /// Common currency code for provider and cache offer prices
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("currency")]
        public string Currency { get; set; }

        /// <summary>
        /// Provider code used for search
        /// </summary>

        [System.Text.Json.Serialization.JsonPropertyName("providerCode")]
        public int ProviderCode { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class OfferLiveCheckResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("offers")]
        public System.Collections.Generic.ICollection<FlightOffer> Offers { get; set; }

        private System.Collections.Generic.IDictionary<string, object> _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

    }



    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ApiException : System.Exception
    {
        public int StatusCode { get; private set; }

        public string Response { get; private set; }

        public System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> Headers { get; private set; }

        public ApiException(string message, int statusCode, string response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Exception innerException)
            : base(message + "\n\nStatus: " + statusCode + "\nResponse: \n" + ((response == null) ? "(null)" : response.Substring(0, response.Length >= 512 ? 512 : response.Length)), innerException)
        {
            StatusCode = statusCode;
            Response = response;
            Headers = headers;
        }

        public override string ToString()
        {
            return string.Format("HTTP Response: \n\n{0}\n\n{1}", Response, base.ToString());
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ApiException<TResult> : ApiException
    {
        public TResult Result { get; private set; }

        public ApiException(string message, int statusCode, string response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, TResult result, System.Exception innerException)
            : base(message, statusCode, response, headers, innerException)
        {
            Result = result;
        }
    }

}

#pragma warning restore  108
#pragma warning restore  114
#pragma warning restore  472
#pragma warning restore  612
#pragma warning restore 1573
#pragma warning restore 1591
#pragma warning restore 8073
#pragma warning restore 3016
#pragma warning restore 8603
#pragma warning restore 8604
#pragma warning restore 8625