using Esky.Packages.Application.Abstractions.Gateways.FlightLiveGateway;
using Esky.Packages.Infrastructure.HttpClients.Constants;
using Esky.Packages.Infrastructure.HttpClients.Extensions;
using Esky.Packages.Infrastructure.HttpClients.Options;
using Esky.Packages.Infrastructure.Observability;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.Packages.Infrastructure.Gateways.FlightLiveGateway;

internal static class DependencyInjection
{
    public static IServiceCollection AddFlightLiveGateway(this IServiceCollection services, bool enableLoggingHandlers = false)
    {
        var builder = services.AddHttpClient(ApiHttpClientConsts.FlightLiveClient,
                (sp, client) =>
                {
                    var apiSettings = sp.GetRequiredService<ApiUrlsOptions>().FlightLive;
                    client.BaseAddress = new Uri(apiSettings.Url);
                    client.Timeout = TimeSpan.FromMilliseconds(apiSettings.TimeoutInMiliseconds);
                })
            .AddCompression();
            
        if (enableLoggingHandlers)
        {
            builder.WithLoggingHandler();
        }

        services.AddSingleton<IFlightLiveGateway, FlightLiveGateway>();

        return services;
    }
}