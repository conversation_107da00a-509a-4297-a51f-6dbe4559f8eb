using Esky.Packages.Application.Abstractions.Gateways.FlightGateway;
using Esky.Packages.Infrastructure.HttpClients.Constants;
using Esky.Packages.Infrastructure.HttpClients.Extensions;
using Esky.Packages.Infrastructure.HttpClients.Options;
using Esky.Packages.Infrastructure.Observability;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.Packages.Infrastructure.Gateways.OfferAccuracyMonitorGateway;

internal static class DependencyInjection
{
    public static IServiceCollection AddOfferAccuracyGateway(this IServiceCollection services, bool enableLoggingHandlers = false)
    {
        var builder = services.AddHttpClient(ApiHttpClientConsts.OfferAccuracyClient,
            (sp, client) =>
            {
                var settings = sp.GetRequiredService<ApiUrlsOptions>().OfferAccuracy;
                client.BaseAddress = new Uri(settings.Url);
                client.Timeout = TimeSpan.FromMilliseconds(settings.TimeoutInMiliseconds);
            })
            .AddCompression();

        if (enableLoggingHandlers)
        {
            builder.WithLoggingHandler();
        }

        services.AddSingleton<IOfferAccuracyGateway, OfferAccuracyGateway>();

        return services;
    }
}