{"openapi": "3.0.1", "info": {"title": "Esky.Offer.Accuracy.Monitor.Api | v1", "version": "1.0.0"}, "paths": {"/flights/unavailability": {"post": {"tags": ["Flights"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetUnavailableFlightOffersQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetUnavailableFlightOffersQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetUnavailableFlightOffersQuery"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UnavailableFlightOffersResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UnavailableFlightOffersResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UnavailableFlightOffersResult"}}}}}}}, "/hotels/unavailability": {"post": {"tags": ["Hotels"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetUnavailableHotelRoomOffersQuery"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetUnavailableHotelRoomOffersQuery"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetUnavailableHotelRoomOffersQuery"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UnavailableHotelRoomOffersResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UnavailableHotelRoomOffersResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UnavailableHotelRoomOffersResult"}}}}}}}}, "components": {"schemas": {"Context": {"required": ["partnerCode"], "type": "object", "properties": {"partnerCode": {"type": "string"}, "sessionId": {"type": "string", "nullable": true}}}, "GetUnavailableFlightOffersQuery": {"required": ["context", "flightIds", "paxConfiguration"], "type": "object", "properties": {"context": {"$ref": "#/components/schemas/Context"}, "flightIds": {"type": "array", "items": {"type": "string"}}, "paxConfiguration": {"type": "string"}}}, "GetUnavailableHotelRoomOffersQuery": {"required": ["context", "provider", "hotelMetaCode", "checkinDate", "checkoutDate"], "type": "object", "properties": {"context": {"$ref": "#/components/schemas/Context"}, "provider": {"type": "integer", "format": "int32"}, "hotelMetaCode": {"type": "integer", "format": "int32"}, "checkinDate": {"type": "string", "format": "date"}, "checkoutDate": {"type": "string", "format": "date"}}}, "UnavailableFlightOffersResult": {"required": ["unavailableOffers"], "type": "object", "properties": {"unavailableOffers": {"type": "array", "items": {"type": "string"}}}}, "UnavailableHotelRoomOffer": {"required": ["packageId", "hotelMetaCode", "provider"], "type": "object", "properties": {"packageId": {"type": "string"}, "hotelMetaCode": {"type": "integer", "format": "int32"}, "provider": {"type": "integer", "format": "int32"}}}, "UnavailableHotelRoomOffersResult": {"required": ["unavailableOffers"], "type": "object", "properties": {"unavailableOffers": {"type": "array", "items": {"$ref": "#/components/schemas/UnavailableHotelRoomOffer"}}}}}}, "tags": [{"name": "Flights"}, {"name": "Hotels"}]}