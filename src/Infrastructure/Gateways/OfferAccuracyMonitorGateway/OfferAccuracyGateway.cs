using System.Diagnostics;
using Esky.Packages.Application.Abstractions.Gateways.FlightGateway;
using Esky.Packages.Application.Observability;
using Esky.Packages.Domain.Types;
using Esky.Packages.Infrastructure.Gateways.OfferAccuracyMonitorGateway.Client;
using Esky.Packages.Infrastructure.HttpClients.Constants;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Infrastructure.Gateways.OfferAccuracyMonitorGateway;

public class OfferAccuracyGateway : IOfferAccuracyGateway
{
    private const string Flight = "flight";
    private const string Hotel = "hotel";
    
    private readonly ILogger<OfferAccuracyGateway> _logger;
    private readonly OfferAccuracyClient _offerAccuracyClient;

    public OfferAccuracyGateway(IHttpClientFactory httpClientFactory, ILogger<OfferAccuracyGateway> logger)
    {
        _logger = logger;
        var httpClient = httpClientFactory.CreateClient(ApiHttpClientConsts.OfferAccuracyClient);

        _offerAccuracyClient = new OfferAccuracyClient(httpClient);
    }
    
    public async Task<HashSet<string>> GetUnavailableFlightOffers(string[] flightIds, Occupancy occupancy, 
        CancellationToken cancellationToken = default)
    {
        using var activity = Activities.Source.StartActivity();

        var query = new GetUnavailableFlightOffersQuery
        {
            Context = new Context
            {
                PartnerCode = string.Empty,
                SessionId = string.Empty
            },
            FlightIds = flightIds,
            PaxConfiguration = FlightPassengers.FromOccupancy(occupancy).ToString()
        };
        activity?.AddTag("flightIdsCount", flightIds.Length);
        activity?.AddTag("flightIds", string.Join(',', flightIds));
        activity?.AddTag("paxConfiguration", query.PaxConfiguration);

        try
        {
            OfferAccuracyMetrics.RegisterOfferAccuracyRequest(Flight);
            var response = await _offerAccuracyClient.UnavailabilityAsync(query, cancellationToken);
            
            activity?.AddTag("unavailableFlightOffersCount", response.UnavailableOffers.Count);
            activity?.AddTag("unavailableFlightOffers", string.Join(',', response.UnavailableOffers));
            activity?.SetStatus(ActivityStatusCode.Ok);
            
            var unavailableOffers = response.UnavailableOffers.ToHashSet();
            OfferAccuracyMetrics.RegisterOfferAccuracyExclusions(Flight, unavailableOffers.Count);
            return unavailableOffers;
        }
        catch (TaskCanceledException e)
        {
            OfferAccuracyMetrics.RegisterOfferAccuracyTimeout(Flight);
            activity?.SetStatus(ActivityStatusCode.Error);
            activity?.AddException(e);
            _logger.LogInformation(e, "Task canceled for flight offer accuracy check");
            return [];
        }
        catch (Exception e)
        {
            OfferAccuracyMetrics.RegisterOfferAccuracyError(Flight);
            activity?.SetStatus(ActivityStatusCode.Error);
            activity?.AddException(e);
            _logger.LogWarning(e, "Unhandled exception during flight offer accuracy check");
            return [];
        }
    }
    
    public async Task<HashSet<string>> GetUnavailableHotelOfferIds(int hotelMetaCode, IEnumerable<string> hotelOfferIds,
        DateOnly checkinDate, DateOnly checkoutDate, CancellationToken cancellationToken = default)
    {
        try
        {
            var offerIds = hotelOfferIds.Select(OfferId.FromHotelOfferId).ToLookup(x => x.Provider);
        
            var tasks = offerIds.Select(async p =>
            {
                var unavailableOffers = (await GetUnavailableHotelRoomOffers(p.Key, hotelMetaCode, checkinDate, checkoutDate, cancellationToken)).ToHashSet();
                return p.Where(x => unavailableOffers.Contains(x.HotelPackageId)).Select(x => x.HotelOfferId);
            }).ToArray();
        
            await Task.WhenAll(tasks);
            var unavailableHotelOffers = tasks.SelectMany(t => t.Result).ToHashSet();
            OfferAccuracyMetrics.RegisterOfferAccuracyExclusions(Hotel, unavailableHotelOffers.Count);
            return unavailableHotelOffers;
        }
        catch (Exception e)
        {
            _logger.LogWarning(e, "Unhandled exception during hotel offer accuracy check");
            return [];
        }
    }

    private async Task<IEnumerable<string>> GetUnavailableHotelRoomOffers(int provider, int hotelMetaCode, 
        DateOnly checkinDate, DateOnly checkoutDate, CancellationToken cancellationToken = default)
    {
        using var activity = Activities.Source.StartActivity();
        activity?.AddTag("provider", provider);
        activity?.AddTag("hotelMetaCode", hotelMetaCode);
        activity?.AddTag("checkinDate", checkinDate);
        activity?.AddTag("checkoutDate", checkoutDate);

        var query = new GetUnavailableHotelRoomOffersQuery
        {
            Context = new Context
            {
                PartnerCode = string.Empty,
                SessionId = string.Empty
            },
            Provider = provider,
            HotelMetaCode = hotelMetaCode,
            CheckinDate = checkinDate,
            CheckoutDate = checkoutDate
        };

        try
        {
            OfferAccuracyMetrics.RegisterOfferAccuracyRequest(Hotel);
            var response = await _offerAccuracyClient.Unavailability2Async(query, cancellationToken);

            activity?.AddTag("unavailableHotelOffersCount", response.UnavailableOffers.Count);
            activity?.AddTag("unavailableHotelOffers", string.Join(',', response.UnavailableOffers.Select(o => o.PackageId)));
            activity?.SetStatus(ActivityStatusCode.Ok);

            return response.UnavailableOffers.Select(o => o.PackageId);
        }
        catch (TaskCanceledException e)
        {
            OfferAccuracyMetrics.RegisterOfferAccuracyTimeout(Hotel);
            activity?.SetStatus(ActivityStatusCode.Error);
            activity?.AddException(e);
            _logger.LogInformation(e, "Task canceled for hotel offer accuracy check");
            return [];
        }
        catch (Exception e)
        {
            OfferAccuracyMetrics.RegisterOfferAccuracyError(Hotel);
            activity?.SetStatus(ActivityStatusCode.Error);
            activity?.AddException(e);
            _logger.LogWarning(e, "Unhandled exception during hotel offer accuracy check");
            return [];
        }
    }

    private const string HotelOfferIdSeparator = ":";
    private sealed record OfferId(string HotelOfferId, string HotelPackageId, int Provider)
    {
        public static OfferId FromHotelOfferId(string hotelOfferId)
        {
            var parts = hotelOfferId.Split(HotelOfferIdSeparator);
            var hotelPackageId = parts[5];
            var provider = int.Parse(parts[7]);
            
            return new OfferId(hotelOfferId, hotelPackageId, provider);
        }
    }
}