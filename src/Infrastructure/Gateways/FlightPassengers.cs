using System.Text.RegularExpressions;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Infrastructure.Gateways;

internal class FlightPassengers
{
    private static readonly Regex PaxConfigRegex = new(@"^(?<adults>\d+)\.(?<youths>\d+)\.(?<children>\d+)\.(?<infants>\d+)$", RegexOptions.Compiled);
    
    public int Adults { get; private init; }
    public int Youths { get; private init; }
    public int[] YouthsAges { get; private init; } = [];
    public int Children { get; private init; }
    public int[] ChildrenAges { get; private init; } = [];
    public int Infants { get; private init; }
    public int[] InfantsAges { get; private init; } = [];

    public int TotalSeats => Adults + Youths + Children;
    public override string ToString() => $"{Adults}.{Youths}.{Children}.{Infants}";

    public static FlightPassengers FromOccupancy(Occupancy occupancy)
    {
        var youthsAges = occupancy.ChildrenAges.Where(c => c is > 11 and <= 17).ToArray();
        var childrenAges = occupancy.ChildrenAges.Where(c => c is >= 2 and <= 11).ToArray();
        var infantsAges = occupancy.ChildrenAges.Where(c => c < 2).ToArray();

        return new FlightPassengers
        {
            Adults = occupancy.Adults,
            Youths = youthsAges.Length,
            YouthsAges = youthsAges,
            Children = childrenAges.Length,
            ChildrenAges = childrenAges,
            Infants = infantsAges.Length,
            InfantsAges = infantsAges
        };
    }

    public static FlightPassengers FromPaxConfiguration(string paxConfiguration)
    {
        var match = PaxConfigRegex.Match(paxConfiguration);

        if (!match.Success)
        {
            throw new FormatException($"Invalid pax configuration format {paxConfiguration}");
        }

        var adults = int.Parse(match.Groups["adults"].Value);
        var youths = int.Parse(match.Groups["youths"].Value);
        var children = int.Parse(match.Groups["children"].Value);
        var infants = int.Parse(match.Groups["infants"].Value);

        return new FlightPassengers
        {
            Adults = adults,
            Youths = youths,
            Children = children,
            Infants = infants
        };
    }
}
