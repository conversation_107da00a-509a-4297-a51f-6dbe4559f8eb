using Esky.Packages.Application.Abstractions.Gateways.HotelGateway;
using Esky.Packages.Domain.Model.PackageDefinitions.HotelSelectors;
using Esky.Packages.Infrastructure.Gateways.HotelGateway.Exceptions;
using Esky.Packages.Infrastructure.Gateways.HotelGateway.Options;
using Esky.Packages.Infrastructure.Gateways.HotelGateway.QueryTranslators;
using Esky.Packages.Infrastructure.HttpClients.Constants;
using Infrastructure.Gateways.HotelGateway.Proxies.HotelStatic;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using HotelAirports = Esky.Packages.Application.Abstractions.Gateways.HotelGateway.HotelAirports;

namespace Esky.Packages.Infrastructure.Gateways.HotelGateway;

public class HotelGateway(
    HotelGatewayOptions hotelGatewayOptions,
    IHttpClientFactory httpClientFactory,
    ILogger<HotelGateway> logger) : IHotelGateway
{
    private const int SingleQueryHotelHardLimit = 5_000;

    public async Task<List<Hotel>> SearchHotels(
        HotelSelector selector, 
        int? top = null,
        CancellationToken cancellationToken = default)
    {
        var queryTranslator = new SqlHotelSelectorQueryTranslator();

        top ??= SingleQueryHotelHardLimit;
        top = Math.Min(top.Value, SingleQueryHotelHardLimit) + 1;

        var sql = queryTranslator.GenerateQuery(selector, top.Value);
        logger.LogTrace("Search SQL: \n{sql}", sql.ToString());

        await using var c = new SqlConnection(hotelGatewayOptions.ConnectionString);
        await c.OpenAsync(cancellationToken);

        var cmd = c.CreateCommand();
        cmd.CommandText = sql.Sql;

        foreach (var param in sql.NamedBindings)
            cmd.Parameters.AddWithValue(param.Key, param.Value);

        await using var reader = await cmd.ExecuteReaderAsync(cancellationToken);
        List<Hotel> hotels = [];
        while (await reader.ReadAsync(cancellationToken))
        {
            if (hotels.Count >= top - 1)
            {
                throw new QuotaExceededException(top.Value);
            }

            var metaCode = (int)reader[0];
            hotels.Add(new Hotel(
                MetaCode: metaCode,
                Name: (string)reader[1],
                CityCode: (string)reader[2],
                CountryCode: (string)reader[3]));
        }

        return hotels;
    }

    public async Task<List<HotelAirports>> GetAirportsForMetaCodes(
        IReadOnlyCollection<int> metaCodes,
        CancellationToken cancellationToken = default)
    {
        if (metaCodes.Count == 0) 
            return [];

        using var hotelStaticClient = httpClientFactory.CreateClient(ApiHttpClientConsts.HotelStaticClient);
        var client = new HotelStaticClient(hotelStaticClient);
        var codes = string.Join(',', metaCodes);
        var response = await client.HotelAirportsAsync("2.0", codes, cancellationToken);

        return response
            .Select(MapAirports)
            .ToList();
    }

    private static HotelAirports MapAirports(global::Infrastructure.Gateways.HotelGateway.Proxies.HotelStatic.HotelAirports dto)
    {
        return new HotelAirports
        (
            MetaCode: dto.MetaCode,
            Airports: dto
                .Airports
                .Select(a => new HotelAirport(
                    MetaCode: dto.MetaCode,
                    AirportCode: a.AirportCode))
                .ToList()
        );
    }
}