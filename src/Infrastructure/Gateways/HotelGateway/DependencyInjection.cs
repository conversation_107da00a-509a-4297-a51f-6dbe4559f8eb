using System.Net;
using Esky.Packages.Application.Abstractions.Gateways.HotelGateway;
using Esky.Packages.Infrastructure.Configuration;
using Esky.Packages.Infrastructure.Gateways.HotelGateway.Options;
using Esky.Packages.Infrastructure.HttpClients.Constants;
using Esky.Packages.Infrastructure.HttpClients.Extensions;
using Esky.Packages.Infrastructure.HttpClients.Options;
using Esky.Packages.Infrastructure.Observability;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Polly;

namespace Esky.Packages.Infrastructure.Gateways.HotelGateway;

internal static class DependencyInjection
{
    public static IServiceCollection AddHotelGateway(this IServiceCollection services, IConfiguration configuration, bool enableLoggingHandlers = false)
    {
        services
            .RegisterOptions<HotelGatewayOptions>(configuration, HotelGatewayOptions.ConfigurationSection)
            .AddSingleton<IHotelGateway, HotelGateway>();
        
        var builder = services.AddHttpClient(ApiHttpClientConsts.HotelStaticClient,
                (sp, client) =>
                {
                    var apiSettings = sp.GetRequiredService<ApiUrlsOptions>().HotelStatic;
                    client.BaseAddress = new Uri(apiSettings.Url);
                    client.Timeout = TimeSpan.FromMilliseconds(apiSettings.TimeoutInMiliseconds);
                })
            .AddCompression();
            
        if (enableLoggingHandlers)
        {
            builder.WithLoggingHandler();
        }
        
        builder.AddResilienceHandler("hotelStatic", (b, _) =>
            b.AddRetry(new()
            {
                ShouldHandle = static args => ValueTask.FromResult(args.Outcome.Result?.StatusCode is
                    HttpStatusCode.BadGateway
                    or HttpStatusCode.ServiceUnavailable
                    or HttpStatusCode.GatewayTimeout
                    or HttpStatusCode.InternalServerError
                ),
                BackoffType = DelayBackoffType.Exponential,
                UseJitter = true,
                MaxRetryAttempts = 5,
                MaxDelay = TimeSpan.FromSeconds(30),
                Delay = TimeSpan.FromMilliseconds(1000),
            })
        );

        return services;
    }
}