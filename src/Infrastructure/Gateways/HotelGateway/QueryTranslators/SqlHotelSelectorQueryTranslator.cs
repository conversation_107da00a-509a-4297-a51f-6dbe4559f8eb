using Esky.Packages.Domain.Model.PackageDefinitions.HotelSelectors;
using Esky.Packages.Domain.Model.PackageDefinitions.HotelSelectors.Combine;
using Esky.Packages.Domain.Model.PackageDefinitions.HotelSelectors.Groups;
using Esky.Packages.Domain.Model.PackageDefinitions.HotelSelectors.Locations;
using SqlKata;
using SqlKata.Compilers;

namespace Esky.Packages.Infrastructure.Gateways.HotelGateway.QueryTranslators;

public sealed class SqlHotelSelectorQueryTranslator
{
    readonly Stack<Query> _queries = new();
    private Query CurrentQuery => _queries.Peek();

    const string _forceRecompileOptionClause = " OPTION(RECOMPILE, MAXDOP 1)";

    public SqlResult GenerateQuery(HotelSelector selector, int top)
    {
        _queries.Push(
            new Query("dbo.ActiveHotelsMetadataView")
                .Select("MetaCode", "Name", "CityCode", "CountryCode", "CheckInOut")
                .Take(top)
        );

        _ = Visit(selector);

        var compiler = new SqlServerCompiler();
        var result = compiler.Compile(CurrentQuery);
        result.RawSql += _forceRecompileOptionClause;
        result.Sql += _forceRecompileOptionClause;

        return result;
    }

    private HotelSelector Visit(HotelSelector hotelSelector)
    {
        return hotelSelector switch
        {
            HotelAndSelector hotelAndSelector => VisitAnd(hotelAndSelector),
            HotelCitySelector hotelCitySelector => VisitCity(hotelCitySelector),
            HotelContinentSelector hotelContinentSelector => VisitContinent(hotelContinentSelector),
            HotelCountrySelector hotelCountrySelector => VisitCountry(hotelCountrySelector),
            HotelMetaCodeSelector hotelMetaCodeSelector => VisitMetaCode(hotelMetaCodeSelector),
            HotelNotSelector hotelNotSelector => VisitNot(hotelNotSelector),
            HotelNullSelector hotelNullSelector => VisitNull(hotelNullSelector),
            HotelOrSelector hotelOrSelector => VisitOr(hotelOrSelector),
            HotelRegionSelector hotelRegionSelector => VisitRegion(hotelRegionSelector),
            HotelTagSelector hotelTagSelector => VisitTag(hotelTagSelector),
            _ => throw new ArgumentOutOfRangeException(nameof(hotelSelector))
        };
    }

    private HotelSelector VisitCity(HotelCitySelector selector)
    {
        CurrentQuery.WhereIn("CityCode", selector.CityCodes);

        return selector;
    }

    private HotelSelector VisitCountry(HotelCountrySelector selector)
    {
        CurrentQuery.WhereIn("CountryCode", selector.CountryCodes);

        return selector;
    }

    private HotelSelector VisitContinent(HotelContinentSelector selector)
    {
        CurrentQuery.WhereIn("CountryCode", q => q
            .From("dbo.Countries")
            .Select("Code")
            .WhereIn("ContinentCode", selector.ContinentCodes)
        );

        return selector;
    }

    private HotelSelector VisitMetaCode(HotelMetaCodeSelector selector)
    {
        CurrentQuery.WhereIn("MetaCode", selector.HotelMetaCodes);

        return selector;
    }

    private HotelSelector VisitRegion(HotelRegionSelector selector)
    {
        CurrentQuery.WhereIn("MetaCode", q => q
            .From("dbo.MetaHotelRegion")
            .Select("MetaCode")
            .WhereIn("RegionCode", selector.RegionCodes)
        );

        return selector;
    }

    private HotelSelector VisitTag(HotelTagSelector selector)
    {
        CurrentQuery.WhereIn("MetaCode", q => q
            .From("dbo.MetaHotelTagsMetaHotelsMetadata")
            .Select("MetaCode")
            .WhereIn("TagId", q2 => q2
                .From("dbo.MetaHotelTags")
                .Select("Id")
                .WhereIn("TagName", selector.Tags)
            )
        );

        return selector;
    }

    private HotelSelector VisitNot(HotelNotSelector selector)
    {
        var query = CurrentQuery.NewChild();

        _queries.Push(query);

        Visit(selector.Selector);

        _queries.Pop();

        var clause = new NestedCondition<Query> { Query = query, IsNot = true };
        CurrentQuery.AddComponent("where", clause);

        return selector;
    }

    private HotelSelector VisitAnd(HotelAndSelector selector)
    {
        var query = CurrentQuery.NewChild();

        _queries.Push(query);

        foreach (var s in selector.Selectors)
            Visit(s);

        _queries.Pop();

        var clause = new NestedCondition<Query> { Query = query };
        CurrentQuery.AddComponent("where", clause);

        return selector;
    }

    private HotelSelector VisitOr(HotelOrSelector selector)
    {
        var query = CurrentQuery.NewChild();

        _queries.Push(query);

        foreach (var s in selector.Selectors)
        {
            var orQuery = new Query();
            _queries.Push(orQuery);
            Visit(s);
            _queries.Pop();

            var orClause = new NestedCondition<Query> { Query = orQuery, IsOr = true };
            CurrentQuery.AddComponent("where", orClause);
        }

        _queries.Pop();

        var clause = new NestedCondition<Query> { Query = query };
        CurrentQuery.AddComponent("where", clause);

        return selector;
    }

    private HotelSelector VisitNull(HotelNullSelector nullSelector)
    {
        CurrentQuery.WhereRaw("1 = 1");

        return nullSelector;
    }
}