{"openapi": "3.0.1", "info": {"title": "Hotels Static Api 2.0", "description": "API for static hotels metadata", "version": "2.0"}, "paths": {"/api/v{version}/AirportCodes/InRadiusFromPoint": {"get": {"tags": ["AirportCodes"], "summary": "Gets airport codes for nearest hotels in specified distance from point", "parameters": [{"name": "Longitude", "in": "query", "required": true, "schema": {"maximum": 180, "minimum": -180, "type": "number", "format": "decimal"}}, {"name": "Latitude", "in": "query", "required": true, "schema": {"maximum": 90, "minimum": -90, "type": "number", "format": "decimal"}}, {"name": "Distance", "in": "query", "required": true, "schema": {"maximum": 100000, "minimum": 0, "type": "number", "format": "decimal"}}, {"name": "DestinationCityCode", "in": "query", "schema": {"type": "string"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/api/v{version}/AirportCodes/InRegion": {"get": {"tags": ["AirportCodes"], "summary": "Gets airport codes for nearest hotels in specified region", "parameters": [{"name": "regionCode", "in": "query", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/api/v{version}/CacheLoader/InRadiusFromPoint": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Gets hotel meta codes within a specified distance from point.", "parameters": [{"name": "Longitude", "in": "query", "required": true, "schema": {"maximum": 180, "minimum": -180, "type": "number", "format": "decimal"}}, {"name": "Latitude", "in": "query", "required": true, "schema": {"maximum": 90, "minimum": -90, "type": "number", "format": "decimal"}}, {"name": "Distance", "in": "query", "required": true, "schema": {"maximum": 100000, "minimum": 0, "type": "number", "format": "decimal"}}, {"name": "DestinationCityCode", "in": "query", "schema": {"type": "string"}}, {"name": "hotelCountLimit", "in": "query", "description": "", "schema": {"type": "integer", "format": "int32"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}}}}}, "/api/v{version}/CacheLoader/InRegion": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Gets hotel meta codes in region.", "parameters": [{"name": "regionCode", "in": "query", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "hotelCountLimit", "in": "query", "description": "", "schema": {"type": "integer", "format": "int32"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}}}}}, "/api/v{version}/HotelAirports": {"post": {"tags": ["HotelAirports"], "summary": "Get hotels by metacodes.", "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"metaCodes": {"type": "string"}}}, "encoding": {"metaCodes": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HotelAirports"}}}}}}}}, "/api/v{version}/HotelCodes/InRadiusFromPoint": {"get": {"tags": ["HotelCodes"], "summary": "Gets hotel meta codes within a specified distance from point.", "parameters": [{"name": "Longitude", "in": "query", "required": true, "schema": {"maximum": 180, "minimum": -180, "type": "number", "format": "decimal"}}, {"name": "Latitude", "in": "query", "required": true, "schema": {"maximum": 90, "minimum": -90, "type": "number", "format": "decimal"}}, {"name": "Distance", "in": "query", "required": true, "schema": {"maximum": 100000, "minimum": 0, "type": "number", "format": "decimal"}}, {"name": "DestinationCityCode", "in": "query", "schema": {"type": "string"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}}}}}, "/api/v{version}/HotelCodes/InRegion": {"get": {"tags": ["HotelCodes"], "summary": "Gets hotel meta codes in region.", "parameters": [{"name": "regionCode", "in": "query", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}}}}}, "/api/v{version}/Hotels": {"get": {"tags": ["Hotels"], "summary": "Get hotels by metacodes.", "parameters": [{"name": "metaCodes", "in": "query", "description": "Metacodes of hotels separated by comma.", "schema": {"type": "string"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HotelStaticDataResponse"}}}}}}}, "/api/v{version}/HotelsFilter": {"post": {"tags": ["HotelsFilter"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json;odata.metadata=minimal;odata.streaming=true": {"schema": {"$ref": "#/components/schemas/HotelSearchCriteria"}}, "application/json;odata.metadata=minimal;odata.streaming=false": {"schema": {"$ref": "#/components/schemas/HotelSearchCriteria"}}, "application/json;odata.metadata=minimal": {"schema": {"$ref": "#/components/schemas/HotelSearchCriteria"}}, "application/json;odata.metadata=full;odata.streaming=true": {"schema": {"$ref": "#/components/schemas/HotelSearchCriteria"}}, "application/json;odata.metadata=full;odata.streaming=false": {"schema": {"$ref": "#/components/schemas/HotelSearchCriteria"}}, "application/json;odata.metadata=full": {"schema": {"$ref": "#/components/schemas/HotelSearchCriteria"}}, "application/json;odata.metadata=none;odata.streaming=true": {"schema": {"$ref": "#/components/schemas/HotelSearchCriteria"}}, "application/json;odata.metadata=none;odata.streaming=false": {"schema": {"$ref": "#/components/schemas/HotelSearchCriteria"}}, "application/json;odata.metadata=none": {"schema": {"$ref": "#/components/schemas/HotelSearchCriteria"}}, "application/json;odata.streaming=true": {"schema": {"$ref": "#/components/schemas/HotelSearchCriteria"}}, "application/json;odata.streaming=false": {"schema": {"$ref": "#/components/schemas/HotelSearchCriteria"}}, "application/json": {"schema": {"$ref": "#/components/schemas/HotelSearchCriteria"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HotelSearchCriteria"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/HotelSearchCriteria"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HotelSearchCriteria"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/HotelSearchCriteria"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HotelMetaCodesSearchResult"}}}}}, "deprecated": true}}, "/api/v{version}/HotelsMinimalData": {"post": {"tags": ["HotelsMinimalData"], "summary": "Get hotels minimal data - for sorting and filtering", "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"metaCodes": {"type": "string"}}}, "encoding": {"metaCodes": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StoreHotelMinimalStaticData"}}}}}}}}, "/api/v{version}/LandingPage/DestinationCodes": {"post": {"tags": ["LandingPage"], "summary": "Gets destination hotel and airport codes within a specified destination codes and types.", "parameters": [{"name": "limit", "in": "query", "description": "", "schema": {"type": "integer", "format": "int32"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "", "content": {"application/json;odata.metadata=minimal;odata.streaming=true": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DestinationCodesRequest"}}}, "application/json;odata.metadata=minimal;odata.streaming=false": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DestinationCodesRequest"}}}, "application/json;odata.metadata=minimal": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DestinationCodesRequest"}}}, "application/json;odata.metadata=full;odata.streaming=true": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DestinationCodesRequest"}}}, "application/json;odata.metadata=full;odata.streaming=false": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DestinationCodesRequest"}}}, "application/json;odata.metadata=full": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DestinationCodesRequest"}}}, "application/json;odata.metadata=none;odata.streaming=true": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DestinationCodesRequest"}}}, "application/json;odata.metadata=none;odata.streaming=false": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DestinationCodesRequest"}}}, "application/json;odata.metadata=none": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DestinationCodesRequest"}}}, "application/json;odata.streaming=true": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DestinationCodesRequest"}}}, "application/json;odata.streaming=false": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DestinationCodesRequest"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DestinationCodesRequest"}}}, "application/xml": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DestinationCodesRequest"}}}, "text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DestinationCodesRequest"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DestinationCodesRequest"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DestinationCodesRequest"}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DestinationCodesResponse"}}}}}}}}, "/api/v{version}/PromotedHotels/Filter": {"post": {"tags": ["PromotedHotels"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json;odata.metadata=minimal;odata.streaming=true": {"schema": {"$ref": "#/components/schemas/PromotedHotelsRequest"}}, "application/json;odata.metadata=minimal;odata.streaming=false": {"schema": {"$ref": "#/components/schemas/PromotedHotelsRequest"}}, "application/json;odata.metadata=minimal": {"schema": {"$ref": "#/components/schemas/PromotedHotelsRequest"}}, "application/json;odata.metadata=full;odata.streaming=true": {"schema": {"$ref": "#/components/schemas/PromotedHotelsRequest"}}, "application/json;odata.metadata=full;odata.streaming=false": {"schema": {"$ref": "#/components/schemas/PromotedHotelsRequest"}}, "application/json;odata.metadata=full": {"schema": {"$ref": "#/components/schemas/PromotedHotelsRequest"}}, "application/json;odata.metadata=none;odata.streaming=true": {"schema": {"$ref": "#/components/schemas/PromotedHotelsRequest"}}, "application/json;odata.metadata=none;odata.streaming=false": {"schema": {"$ref": "#/components/schemas/PromotedHotelsRequest"}}, "application/json;odata.metadata=none": {"schema": {"$ref": "#/components/schemas/PromotedHotelsRequest"}}, "application/json;odata.streaming=true": {"schema": {"$ref": "#/components/schemas/PromotedHotelsRequest"}}, "application/json;odata.streaming=false": {"schema": {"$ref": "#/components/schemas/PromotedHotelsRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PromotedHotelsRequest"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/PromotedHotelsRequest"}}, "text/plain": {"schema": {"$ref": "#/components/schemas/PromotedHotelsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PromotedHotelsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PromotedHotelsRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PromotedHotel"}}}}}}}}, "/api/v{version}/TFG": {"get": {"tags": ["TFG"], "parameters": [{"name": "Longitude", "in": "query", "required": true, "schema": {"maximum": 180, "minimum": -180, "type": "number", "format": "decimal"}}, {"name": "Latitude", "in": "query", "required": true, "schema": {"maximum": 90, "minimum": -90, "type": "number", "format": "decimal"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Price"}}}}}}}}, "components": {"schemas": {"Airport": {"type": "object", "properties": {"airportCode": {"type": "string", "nullable": true}, "distanceInStraightLine": {"type": "integer", "format": "int32", "readOnly": true}, "routeDistanceInMeters": {"type": "integer", "format": "int32"}, "routeTimeInSeconds": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CheckFromTo": {"type": "object", "properties": {"from": {"type": "string", "nullable": true}, "to": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CRSType": {"enum": ["Unspecified", "Name", "Link"], "type": "string"}, "DestinationCodesRequest": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/DestinationType"}}, "additionalProperties": false}, "DestinationCodesResponse": {"type": "object", "properties": {"destinationCode": {"type": "string", "nullable": true}, "destinationType": {"$ref": "#/components/schemas/DestinationType"}, "destinationCountryCode": {"type": "string", "nullable": true}, "hotelAirportMap": {"type": "array", "items": {"$ref": "#/components/schemas/HotelAirportMapping"}, "nullable": true}}, "additionalProperties": false}, "DestinationType": {"enum": ["City", "Region", "Country"], "type": "string"}, "GeoJSONObjectType": {"enum": ["Point", "MultiPoint", "LineString", "MultiLineString", "Polygon", "MultiPolygon", "GeometryCollection", "Feature", "FeatureCollection"], "type": "string"}, "HotelAirportMapping": {"type": "object", "properties": {"airports": {"type": "array", "items": {"type": "string"}, "nullable": true}, "hotels": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "HotelAirports": {"type": "object", "properties": {"metaCode": {"type": "integer", "format": "int32"}, "airports": {"type": "array", "items": {"$ref": "#/components/schemas/Airport"}, "nullable": true}}, "additionalProperties": false}, "HotelIdentification": {"type": "object", "properties": {"metaCode": {"type": "integer", "format": "int32"}, "officialName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "HotelMetaCodesSearchResult": {"type": "object", "properties": {"hotelMetaCodes": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "aggregatedFilters": {"$ref": "#/components/schemas/HotelsAggregatedFilters"}}, "additionalProperties": false}, "HotelsAggregatedFilters": {"type": "object", "properties": {"starsRating": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}, "objectType": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}, "guestRating": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}, "amenities": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "HotelSearchCriteria": {"required": ["metaCodes"], "type": "object", "properties": {"metaCodes": {"type": "string"}, "starRatings": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "objectTypes": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "guestRatings": {"type": "array", "items": {"type": "number", "format": "decimal"}, "nullable": true}, "amenities": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "HotelStaticData": {"type": "object", "properties": {"hotelIdentification": {"$ref": "#/components/schemas/HotelIdentification"}, "location": {"$ref": "#/components/schemas/Location"}, "rating": {"$ref": "#/components/schemas/Rating"}, "objectType": {"$ref": "#/components/schemas/ObjectType"}, "mainPhotoUrl": {"type": "string", "nullable": true}, "photos": {"type": "array", "items": {"type": "string"}, "nullable": true}, "airports": {"type": "array", "items": {"$ref": "#/components/schemas/Airport"}, "nullable": true}, "distanceToCityCenter": {"type": "number", "format": "decimal"}, "supportedFilters": {"type": "array", "items": {"type": "string"}, "nullable": true}, "rank": {"type": "integer", "format": "int32"}, "transferType": {"$ref": "#/components/schemas/TransferType"}, "checkIn": {"$ref": "#/components/schemas/CheckFromTo"}, "checkOut": {"$ref": "#/components/schemas/CheckFromTo"}}, "additionalProperties": false}, "HotelStaticDataResponse": {"type": "object", "properties": {"hotelStaticData": {"type": "array", "items": {"$ref": "#/components/schemas/HotelStaticData"}, "nullable": true}}, "additionalProperties": false}, "ICRSObject": {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/CRSType"}}, "additionalProperties": false}, "IPosition": {"type": "object", "properties": {"altitude": {"type": "number", "format": "decimal", "nullable": true, "readOnly": true}, "latitude": {"type": "number", "format": "decimal", "readOnly": true}, "longitude": {"type": "number", "format": "decimal", "readOnly": true}}, "additionalProperties": false}, "Location": {"type": "object", "properties": {"countryCode": {"type": "string", "nullable": true}, "cityCode": {"type": "string", "nullable": true}, "coordinates": {"$ref": "#/components/schemas/Point"}, "street": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ObjectType": {"enum": ["Hotel", "Hostel", "Aparthotel", "Suite", "GuestHouse", "BedAndBreakfast", "InnLodge", "Resort", "Campsite", "HolidayPark", "Unspecified", "Villa", "SummerHouse", "FarmStay", "HomeStay"], "type": "string"}, "Point": {"type": "object", "properties": {"bbox": {"type": "array", "items": {"type": "number", "format": "decimal"}, "nullable": true}, "crs": {"$ref": "#/components/schemas/ICRSObject"}, "type": {"$ref": "#/components/schemas/GeoJSONObjectType"}, "coordinates": {"$ref": "#/components/schemas/IPosition"}}, "additionalProperties": false}, "Price": {"type": "object", "properties": {"amount": {"type": "number", "format": "decimal"}, "currency": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PromotedHotel": {"type": "object", "properties": {"metaCode": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "PromotedHotelsRequest": {"required": ["metaCodes"], "type": "object", "properties": {"metaCodes": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "category": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Rating": {"type": "object", "properties": {"stars": {"type": "integer", "format": "int32"}, "guestRating": {"type": "number", "format": "decimal"}, "guestRatingCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "StoreHotelMinimalStaticData": {"type": "object", "properties": {"metaCode": {"type": "integer", "format": "int32"}, "starsRating": {"type": "integer", "format": "int32", "readOnly": true}, "guestRating": {"type": "number", "format": "decimal"}, "objectType": {"type": "integer", "format": "int32"}, "rank": {"type": "integer", "format": "int32"}, "amenities": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true, "deprecated": true}, "supportedFilters": {"type": "array", "items": {"type": "string"}, "nullable": true}, "name": {"type": "string", "nullable": true}, "longitude": {"type": "number", "format": "decimal"}, "latitude": {"type": "number", "format": "decimal"}, "cityCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TransferType": {"enum": ["None", "Free", "Paid"], "type": "string"}}}}