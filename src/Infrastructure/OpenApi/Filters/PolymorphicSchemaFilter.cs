using System.Text.Json.Serialization;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Esky.Packages.Infrastructure.OpenApi.Filters;

public class PolymorphicSchemaFilter : ISchemaFilter
{
    public void Apply(OpenApiSchema schema, SchemaFilterContext context)
    {
        var schemaRepository = context.SchemaRepository.Schemas;
        var schemaGenerator = context.SchemaGenerator;

        var type = context.Type;

        var attributes = type.GetCustomAttributes(typeof(JsonDerivedTypeAttribute), false);

        if (attributes.Length > 0)
        {
            var types = attributes.Cast<JsonDerivedTypeAttribute>();

            schema.Discriminator = new OpenApiDiscriminator
            {
                PropertyName = "_t",
                Mapping = new Dictionary<string, string>()
            };

            foreach (var t in types)
            {
                if (!schemaRepository.ContainsKey(t.DerivedType.Name))
                    schemaGenerator.GenerateSchema(t.DerivedType, context.SchemaRepository);

                var reference = new OpenApiReference { Id = t.DerivedType.Name, Type = ReferenceType.Schema };
                schema.OneOf.Add(new OpenApiSchema { Reference = reference });

                var discriminatorValue = t.TypeDiscriminator?.ToString() ?? t.DerivedType.Name;

                schema.Discriminator.Mapping.Add(discriminatorValue, reference.ReferenceV3);
            }

            schema.Properties.Clear();
        }

        if (HasJsonDerivedTypeDefinedInBaseClass(type))
        {
            schema.Required.Add("_t");
            schema.Properties.Add("_t", new OpenApiSchema()
            {
                Type = "string",
                Nullable = false,
                ReadOnly = true,
            });
        }
    }

    private static bool HasJsonDerivedTypeDefinedInBaseClass(Type type)
    {
        var typeToCheck = type;

        while (typeToCheck != null)
        {
            var attributes = typeToCheck.GetCustomAttributes(typeof(JsonDerivedTypeAttribute), false);
            var types = attributes.OfType<JsonDerivedTypeAttribute>();

            if (types.Any(t => t.DerivedType == type))
                return true;

            typeToCheck = typeToCheck.BaseType;
        }

        return false;
    }
}