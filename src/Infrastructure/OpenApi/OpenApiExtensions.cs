using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Esky.Packages.Infrastructure.OpenApi;

public static class OpenApiExtensions
{
    public static SwaggerGenOptions MapTypeAsString<T>(this SwaggerGenOptions options)
    {
        options.MapType<T>(() => new OpenApiSchema { Type = "string" });
        return options;
    }

    public static SwaggerGenOptions MapWellKnownTypes(this SwaggerGenOptions options)
    {
        return options
            .MapDateOnly()
            .MapTimeOnly()
            .MapTimeSpan()
            .MapDecimal();
    }

    public static SwaggerGenOptions MapDateOnly(this SwaggerGenOptions options)
    {
        // https://swagger.io/docs/specification/data-models/data-types/
        options.MapType<DateOnly>(() => new OpenApiSchema()
        {
            Type = "string",
            Format = "date"
        });
        return options;
    }

    public static SwaggerGenOptions MapTimeOnly(this SwaggerGenOptions options)
    {
        // https://swagger.io/docs/specification/data-models/data-types/
        // There's no time format in the specification but it's an open value
        options.MapType<TimeOnly>(() => new OpenApiSchema()
        {
            Type = "string",
            Format = "time"
        });
        return options;
    }

    public static SwaggerGenOptions MapTimeSpan(this SwaggerGenOptions options)
    {
        // https://swagger.io/docs/specification/data-models/data-types/
        // There's no time format in the specification but it's an open value
        options.MapType<TimeSpan>(() => new OpenApiSchema()
        {
            Type = "string",
            Format = "timespan"
        });
        return options;
    }

    public static SwaggerGenOptions MapDecimal(this SwaggerGenOptions options)
    {
        // https://swagger.io/docs/specification/data-models/data-types/
        options.MapType<decimal>(() => new OpenApiSchema()
        {
            Type = "number",
            Format = "decimal"
        });
        return options;
    }
}