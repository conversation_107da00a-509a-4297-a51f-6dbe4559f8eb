using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace Esky.Packages.Infrastructure.Configuration;

public static class ConfigurationExtensions
{
    public static IServiceCollection RegisterOptions<T>(this IServiceCollection services, IConfiguration configuration, string sectionName) 
        where T : class
    {
        var section = configuration.GetSection(sectionName);
        services.Configure<T>(section);
        services.AddSingleton(s => s.GetRequiredService<IOptions<T>>().Value);

        return services;
    }
}