using Esky.Packages.Application.Services;
using Esky.Packages.Domain.Factories;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Types;
using Esky.Packages.Infrastructure.Policies;

namespace Esky.Packages.Infrastructure.Factories;

public class PackageFlightFactory(ICurrencyConverterService currencyConverterService) : IPackageFlightFactory
{
    public IEnumerable<PackageFlight> CreateMany(IEnumerable<FlightOffer> flightOffers, string marketId, 
        string definitionId, Currency currency, PackageOccupancy[] occupancies)
    {
        return flightOffers
            .GroupBy(x => (x.CheckIn, x.StayLength, x.ArrivalAirport, x.DepartureAirport))
            .Select(grouping =>
            {
                var packageFlightId = new PackageFlightId(
                    checkIn: grouping.Key.CheckIn,
                    stayLength: grouping.Key.StayLength,
                    arrivalAirport: grouping.Key.ArrivalAirport,
                    departureAirport: grouping.Key.DepartureAirport,
                    marketId: marketId);

                return PackageFlight.Create(packageFlightId, definitionId, currency, occupancies, grouping.ToArray(),
                    ApplyPolicies);
            });
    }
    
    public void ApplyPolicies(PackageFlight packageFlight)
    {
        var currencyConversionPolicy = new CurrencyConversionPolicy(packageFlight.Currency, currencyConverterService);

        packageFlight.ApplyPolicies(currencyConversionPolicy);
    }
}