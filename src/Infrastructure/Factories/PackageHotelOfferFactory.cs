using Esky.Packages.Application.Services; 
using Esky.Packages.Domain.Factories;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Types;
using Esky.Packages.Infrastructure.Policies;

namespace Esky.Packages.Infrastructure.Factories;

public class PackageHotelOfferFactory(ICurrencyConverterService currencyConverterService) : IPackageHotelOfferFactory
{
    public IEnumerable<PackageHotelOffer> CreateMany(IEnumerable<HotelOffer> hotelOffers, string marketId, 
        string definitionId, Currency currency, PackageOccupancy[] occupancies, 
        ProviderConfigurationId[] providerConfigurationIds, 
        Dictionary<int, Dictionary<Airport, Airport[]>> metaCodeByArrivalAirportByDepartureAirport)
    {
        return hotelOffers
            .GroupBy(x => (x.CheckIn, x.StayLength, x.MetaCode))
            .Select(grouping =>
            {
                var airports = metaCodeByArrivalAirportByDepartureAirport.GetValueOrDefault(grouping.Key.MetaCode, 
                    new Dictionary<Airport, Airport[]>());

                var packageHotelOfferId = new PackageHotelOfferId(
                    checkIn: grouping.Key.CheckIn,
                    stayLength: grouping.Key.StayLength,
                    marketId: marketId,
                    metaCode: grouping.Key.MetaCode);

                return PackageHotelOffer.Create(
                    id: packageHotelOfferId,
                    definitionId: definitionId,
                    currency: currency,
                    occupancies: occupancies,
                    providerConfigurationIds: providerConfigurationIds,
                    hotelOffers: grouping.ToArray(),
                    airports: airports,
                    configure: ApplyPolicies);
            });
    }

    public void ApplyPolicies(PackageHotelOffer packageHotelOffer)
    {
        var currencyConversionPolicy = new CurrencyConversionPolicy(packageHotelOffer.Currency, 
            currencyConverterService);

        packageHotelOffer.ApplyPolicies(currencyConversionPolicy);
    }
}
