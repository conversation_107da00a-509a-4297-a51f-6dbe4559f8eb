using Esky.Packages.Application.Services;
using Esky.Packages.Domain.Factories;
using Esky.Packages.Domain.Model.LiveVariants;
using Esky.Packages.Domain.Types;
using Esky.Packages.Infrastructure.Policies;

namespace Esky.Packages.Infrastructure.Factories;

public class HotelOfferVariantFactory(ICurrencyConverterService currencyConverterService) : IHotelOfferVariantFactory
{
    public HotelOfferLiveVariant Create(
        DateOnly checkIn,
        int stayLength,
        string offerId, 
        MealPlan mealPlan, 
        Refundability refundability,
        DateOnly? freeRefundUntil,
        RoomIds roomIds,
        Currency currency, 
        Money price, 
        Money priceAtHotel, 
        int availability)
    {
        var currencyConversionPolicy = new CurrencyConversionPolicy(currency, currencyConverterService);

        return HotelOfferLiveVariant.Create(
            checkIn: checkIn,
            stayLength: stayLength,
            offerId: offerId,
            mealPlan: mealPlan,
            refundability: refundability,
            freeRefundUntil: freeRefundUntil,
            roomIds: roomIds,
            currency: currency,
            price: price,
            priceAtHotel: priceAtHotel,
            availability: availability,
            configure: hotelOfferVariant => hotelOfferVariant.ApplyPolicies(currencyConversionPolicy));
    }
}
