using System.Net;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.Packages.Infrastructure.HttpClients.Extensions;

internal static class HttpClientBuilderExtensions
{
    internal static IHttpClientBuilder AddCompression(this IHttpClientBuilder builder)
    {
        return builder.ConfigureHttpClient(client =>
            {
                client.DefaultRequestHeaders.AcceptEncoding.Add(new("br"));
                client.DefaultRequestHeaders.AcceptEncoding.Add(new("gzip"));
                client.DefaultRequestHeaders.AcceptEncoding.Add(new("deflate"));
            })
            .ConfigurePrimaryHttpMessageHandler(
                _ =>
                {
                    var handler = new HttpClientHandler();
                    if (handler.SupportsAutomaticDecompression)
                        handler.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip | DecompressionMethods.Brotli;

                    return handler;
                });
    }
}