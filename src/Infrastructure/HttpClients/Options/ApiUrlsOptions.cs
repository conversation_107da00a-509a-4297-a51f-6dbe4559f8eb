namespace Esky.Packages.Infrastructure.HttpClients.Options;

internal class ApiUrlsOptions
{
    public const string OptionKeyName = "Apis";

    public ApiSettings FlightCache { get; set; } = default!;
    public ApiSettings FlightLive { get; set; } = default!;
    public ApiSettings HotelStatic { get; set; } = default!;
    public ApiSettings HotelCache { get; set; } = default!;
    public ApiSettings HotelTransaction { get; set; } = default!;
    public ApiSettings HotelApi { get; set; } = default!;
    public ApiSettings CurrencyConverter { get; set; } = default!;
    public ApiSettings OfferAccuracy { get; set; } = default!;
    
    public record ApiSettings(string Url, int TimeoutInMiliseconds = 100_000); // default timeout of HtppClient 100 seconds
}