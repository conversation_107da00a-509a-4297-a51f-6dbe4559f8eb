using Esky.Hotels.Infrastructure.Kafka;
using Esky.Hotels.Infrastructure.Kafka.Producer;
using Esky.Packages.Infrastructure.Configuration;
using Esky.Packages.Infrastructure.Gateways.BloomFilterNotificationGateway;
using Esky.Packages.Infrastructure.Gateways.DataAnalyticsGateway;
using Esky.Packages.Infrastructure.OplogReaders;
using Esky.Packages.Infrastructure.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Esky.Packages.Infrastructure.DependencyInjections;

public interface IInfrastructureOptions
{
    bool EnableHttpClientLoggingHandlers { get; set; }
    
    void AddOplogReaderTokenSaver<T>(string resumeTokenCollectionName,
        int resumeTokenSaveIntervalInMilliseconds = 3000) where T : OplogReader;
    void AddTaskScheduler(bool skipMessageBusConfiguration = false);
    void AddBloomFilterNotificationGateway();
    void AddDataAnalyticsGateway();
}

internal class InfrastructureOptions(IServiceCollection services, IConfiguration configuration) : IInfrastructureOptions
{
    public bool EnableHttpClientLoggingHandlers { get; set; }

    public void AddOplogReaderTokenSaver<T>(string resumeTokenCollectionName, int resumeTokenSaveIntervalInMilliseconds = 3000)
        where T : OplogReader
    {
        resumeTokenCollectionName = resumeTokenCollectionName ?? throw new ArgumentNullException(nameof(resumeTokenCollectionName));

        services.AddOplogReaderTokenSaver<T>(resumeTokenCollectionName, resumeTokenSaveIntervalInMilliseconds);
    }
    
    public void AddTaskScheduler(bool skipMessageBusConfiguration = false)
    {
        services.AddTaskScheduler(configuration, skipMessageBusConfiguration);
    }

    public void AddBloomFilterNotificationGateway()
    { 
        services.TryAddSingleton<KafkaOptions>();
        services.TryAddSingleton<KafkaProducerFactory>();
        
        services.RegisterOptions<BloomFilterNotificationProducerOptions>(configuration, 
            BloomFilterNotificationProducerOptions.ConfigurationSection);
        
        services.AddBloomFilterNotificationGateway();
    }
    
    public void AddDataAnalyticsGateway()
    {
        services.AddDataAnalyticsGateway(configuration);
    }
}