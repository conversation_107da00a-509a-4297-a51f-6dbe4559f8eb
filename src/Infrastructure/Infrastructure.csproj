<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <RootNamespace>Esky.Packages.Infrastructure</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
      <Content Include="nlog-common.config">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Application\Application.csproj" />
    </ItemGroup>

    <ItemGroup>
		<PackageReference Include="Confluent.Kafka" />
		<PackageReference Include="Esky.Hotels.HApi.Protos" />
		<PackageReference Include="Esky.Hotels.Infrastructure.Kafka" />
		<PackageReference Include="Esky.NLog.RabbitMQ.Target" />
		<PackageReference Include="Google.Cloud.BigQuery.V2" />
		<PackageReference Include="Grpc.Net.ClientFactory" />
        <PackageReference Include="BloomFilter.NetCore" />
        <PackageReference Include="MassTransit.RabbitMQ" />
		<PackageReference Include="Microsoft.Data.SqlClient" />
		<PackageReference Include="MongoDB.Driver" />
        <PackageReference Include="Microsoft.Extensions.Http" />
        <PackageReference Include="Microsoft.Extensions.Http.Resilience" />
        <PackageReference Include="MongoDB.Driver.Core.Extensions.DiagnosticSources" />
        <PackageReference Include="NLog.Appsettings.Standard" />
		<PackageReference Include="NLog.Targets.ElasticSearch"/>
		<PackageReference Include="NLog.Web.AspNetCore"/>
		<PackageReference Include="SqlKata.Execution" />
		<PackageReference Include="Swashbuckle.AspNetCore" />
		<PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" />
    </ItemGroup>

	<ItemGroup>
		<InternalsVisibleTo Include="Integration.Tests" />
		<InternalsVisibleTo Include="DynamicProxyGenAssembly2" />
	</ItemGroup>

</Project>
