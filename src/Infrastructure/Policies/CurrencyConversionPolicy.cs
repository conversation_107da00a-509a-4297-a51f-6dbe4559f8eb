using Esky.Packages.Application.Services;
using Esky.Packages.Domain.Policies;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Infrastructure.Policies;

public class CurrencyConversionPolicy(Currency toCurrency, ICurrencyConverterService currencyConverterService) 
    : ICurrencyConversionPolicy
{
    public decimal Convert(decimal amount, Currency fromCurrency)
    {
        return amount == 0 ? 0 : currencyConverterService.Convert(amount, fromCurrency, toCurrency);
    }
}