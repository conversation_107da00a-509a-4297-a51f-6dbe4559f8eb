namespace Esky.Packages.Infrastructure.Pipelines.Logging;

internal class PipelineIdScope : IDisposable
{
    private static readonly AsyncLocal<PipelineIdScope?> _current = new();
    private readonly PipelineIdScope? _previous;

    public string PipelineId { get; private set; }
    public int Indent { get; private set; }

    public PipelineIdScope(string pipelineId)
    {
        PipelineId = pipelineId;
        _previous = Current;
    }

    public PipelineIdScope()
    {
        if (Current == null)
        {
            throw new InvalidOperationException("Child PipelineIdScope can only be created when there is a parent scope with pipeline id");
        }

        PipelineId = Current.PipelineId;
        Indent = Current.Indent + 1;
        _previous = Current;
    }

    public static PipelineIdScope? Current
    {
        get => _current.Value;
        set => _current.Value = value;
    }

    public void Dispose()
    {
        Current = _previous;
    }
}
