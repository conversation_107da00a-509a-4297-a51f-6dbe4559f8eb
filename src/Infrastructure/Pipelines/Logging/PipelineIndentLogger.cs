using Microsoft.Extensions.Logging;
using KeyValue = System.Collections.Generic.KeyValuePair<string, string>;

namespace Esky.Packages.Infrastructure.Pipelines.Logging;

internal class PipelineIndentLogger(ILogger logger) : ILogger
{
    public IDisposable? BeginScope<TState>(TState state) where TState : notnull
    {
        if (state is KeyValue { Key: "pipelineId" } keyValue)
        {
            PipelineIdScope.Current = new PipelineIdScope(keyValue.Value);
        }
        else if (PipelineIdScope.Current != null)
        {
            PipelineIdScope.Current = new PipelineIdScope();
        }

        return PipelineIdScope.Current;
    }

    public void Log<TState>(
        LogLevel logLevel,
        EventId eventId,
        TState state,
        Exception? exception,
        Func<TState, Exception?, string> formatter)
    {
        if (PipelineIdScope.Current != null)
        {
            var indent = new string(' ', PipelineIdScope.Current.Indent * 3);
            var message = formatter(state, exception);

            using (logger.BeginScope(new KeyValue("pipelineId", PipelineIdScope.Current.PipelineId)))
            {
                logger.Log(logLevel, eventId, exception, "{indent}{message}", indent, message);
            }
        }
    }

    public bool IsEnabled(LogLevel logLevel)
    {
        return true;
    }
}
