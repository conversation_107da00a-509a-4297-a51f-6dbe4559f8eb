using Esky.Packages.Application.Abstractions.Tasks;
using Esky.Packages.Infrastructure.Tasks.Options;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.Packages.Infrastructure.Tasks;

internal static class DependencyInjection
{
    public static IServiceCollection AddTaskScheduler(this IServiceCollection services, IConfiguration configuration, 
        bool skipMessageBusConfiguration = false)
    {
        var rabbitMqOptions = new RabbitMqOptions();
        configuration.GetSection(RabbitMqOptions.ConfigurationSection).Bind(rabbitMqOptions);
        
        if (!skipMessageBusConfiguration)
        {
            services.AddMassTransit(x =>
            {
                x.UsingRabbitMq((_, cfg) =>
                {
                    cfg.Host(rabbitMqOptions.Host);
                });
            });
        }
        
        services
            .AddTransient<ITaskScheduler, TaskScheduler>();

        return services;
    }
}