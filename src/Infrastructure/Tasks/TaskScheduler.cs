using Esky.Packages.Application.Abstractions.Tasks;
using MassTransit;

namespace Esky.Packages.Infrastructure.Tasks;

public class TaskScheduler(IPublishEndpoint publishEndpoint) : ITaskScheduler
{
    public Task Publish<TTask>(TTask task, CancellationToken cancellationToken = default) 
        where TTask : class
    {
        return publishEndpoint.Publish(task, cancellationToken);
    }

    public Task Schedule<TTask>(TTask task, TimeSpan delay, CancellationToken cancellationToken = default) 
        where TTask : class
    {
        return publishEndpoint.Publish(task, context => context.Delay = delay, cancellationToken);
    }
}