using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;

namespace Esky.Packages.Infrastructure.OplogReaders;

public abstract class OplogReader : BackgroundService
{
    protected OplogReader() => ReaderName = GetType().Name;
    protected int BatchSize { get; set; } = 400;

    public abstract ResumePoint? GetResumePointToSave();
    public string ReaderName { get; set; }
}

public abstract class OplogReader<T>(
    IMongoDatabase database, 
    ILogger<OplogReader<T>> logger, 
    string collectionName, 
    string resumeTokenCollectionName)
    : OplogReader
{
    private ResumePoint? _resumePointToSave;

    private readonly Queue<ResumePoint> _resumePoints = new();

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var resumeToken = await ReadResumeToken(stoppingToken);

        var collection = database.GetCollection<T>(collectionName);
        var pipeline = ConfigurePipeline(new EmptyPipelineDefinition<ChangeStreamDocument<T>>());
        var options = new ChangeStreamOptions
        {
            BatchSize = BatchSize,
            ResumeAfter = resumeToken?.Token,
        };

        ConfigureOptions(options);

        using var cursor = await collection.WatchAsync(pipeline, options, cancellationToken: stoppingToken);
        while (await cursor.MoveNextAsync(stoppingToken).ConfigureAwait(false))
        {
            var batch = cursor.Current.ToList();
            if (batch.Count == 0)
            {
                AddResumePointAfterBatch(cursor, true);
                ProcessEmptyBatch();
            }
            else
            {
                var clusterTime = batch.Last().ClusterTime;
                var wallTime = batch.Last().WallTime;

                var resumePoint = AddResumePointAfterBatch(cursor, false, clusterTime, wallTime);
                ProcessBatch(batch, () => resumePoint.MarkAsCommitted());
            }

            ProcessCommittedResumePoints();
        }
    }

    private ResumePoint AddResumePointAfterBatch(IChangeStreamCursor<ChangeStreamDocument<T>> cursor,
        bool committed, BsonTimestamp? clusterTime = null, DateTime? wallTime = null)
    {
        var resumeToken = cursor.GetResumeToken();
        var resumePoint = new ResumePoint(committed, resumeToken, clusterTime, wallTime);
        _resumePoints.Enqueue(resumePoint);

        return resumePoint;
    }

    private void ProcessCommittedResumePoints()
    {
        ResumePoint? lastCommittedResumePoint = null;

        if (_resumePoints.Count == 0)
        {
            return;
        }

        while (_resumePoints.Count != 0)
        {
            _resumePoints.TryPeek(out var resumePoint);
            if (resumePoint?.Committed == true)
            {
                _resumePoints.TryDequeue(out lastCommittedResumePoint);
            }
            else
            {
                break;
            }
        }

        if (lastCommittedResumePoint != null)
        {
            Interlocked.Exchange(ref _resumePointToSave, lastCommittedResumePoint);
            logger.LogDebug("OplogReader {name} ready to save resume token {token}", ReaderName,
                lastCommittedResumePoint.ResumeToken);
        }
    }

    protected virtual PipelineDefinition<ChangeStreamDocument<T>, ChangeStreamDocument<T>> ConfigurePipeline(
        EmptyPipelineDefinition<ChangeStreamDocument<T>> pipeline)
        => pipeline;

    protected virtual void ConfigureOptions(ChangeStreamOptions options)
    {
    }

    protected abstract void ProcessBatch(IReadOnlyCollection<ChangeStreamDocument<T>> batch, Action acknowledge);
    protected abstract void ProcessEmptyBatch();

    private async Task<ResumeToken?> ReadResumeToken(CancellationToken stoppingToken)
    {
        var resumeTokens = database.GetCollection<ResumeToken>(resumeTokenCollectionName);

        var resumeToken = await resumeTokens
            .Find(Builders<ResumeToken>.Filter.Eq("_id", ReaderName))
            .FirstOrDefaultAsync(cancellationToken: stoppingToken);

        if (resumeToken == null)
        {
            logger.LogWarning("OplogReader {name} resume token is not present. Starting from the last entry",
                ReaderName);
        }
        else
        {
            if (resumeToken.WallTime.HasValue)
            {
                logger.LogInformation("OplogReader {name} resuming from {time}", ReaderName,
                    resumeToken.WallTime);
            }
            else
            {
                logger.LogInformation("OplogReader {name} resuming from ~{time}", ReaderName, resumeToken.SavedAt);
            }
        }

        return resumeToken;
    }

    public override ResumePoint? GetResumePointToSave()
        => Interlocked.Exchange(ref _resumePointToSave, null);
}