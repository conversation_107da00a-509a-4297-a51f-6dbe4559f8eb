using MongoDB.Bson;

namespace Esky.Packages.Infrastructure.OplogReaders;

public class ResumePoint(
    bool committed,
    BsonDocument resumeToken,
    BsonTimestamp? clusterTime = null,
    DateTime? wallTime = null)
{
    private int _committed = committed ? 1 : 0;
    public bool Committed => _committed != 0;

    public BsonDocument ResumeToken { get; } = resumeToken;
    public BsonTimestamp? ClusterTime { get; } = clusterTime;
    public DateTime? WallTime { get; } = wallTime;

    public void MarkAsCommitted()
        => Interlocked.Exchange(ref _committed, 1);
}