using Esky.Packages.Infrastructure.Database;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;

namespace Esky.Packages.Infrastructure.OplogReaders;

internal static class DependencyInjection
{
    internal static IServiceCollection AddOplogReaderTokenSaver<T>(this IServiceCollection services, 
        string resumeTokenCollectionName, int resumeTokenSaveIntervalInMilliseconds = 3000) where T : OplogReader
    {
        return services
            .AddSingleton<OplogReaderTokenSaver<T>>(s =>
            {
                var oplogReader = s.GetRequiredService<T>() as OplogReader;
                var database = s.GetRequiredService<PackageDatabase>();
                var logger = s.GetRequiredService<ILogger<OplogReaderTokenSaver<T>>>();

                return new OplogReaderTokenSaver<T>(database.Database, logger, oplogReader, resumeTokenCollectionName, 
                    resumeTokenSaveIntervalInMilliseconds);
            })
            .AddHostedService(s => s.GetRequiredService<OplogReaderTokenSaver<T>>());
    }
}