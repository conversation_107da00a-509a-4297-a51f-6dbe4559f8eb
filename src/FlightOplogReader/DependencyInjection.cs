using Esky.Hotels.Infrastructure.Kafka;
using Esky.Hotels.Infrastructure.Kafka.Producer;
using Esky.Packages.FlightOplogReader.Observability;
using Esky.Packages.FlightOplogReader.Options;
using Esky.Packages.Infrastructure.Configuration;
using Esky.Packages.Infrastructure.Database;
using MongoDB.Driver;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;

namespace Esky.Packages.FlightOplogReader;

internal static class DependencyInjection
{
    public static IServiceCollection AddOplogReader(this IServiceCollection services, IConfiguration configuration)
    {
        services.RegisterOptions<OplogReaderOptions>(configuration, OplogReaderOptions.ConfigurationSection);
        services.RegisterOptions<PackageFlightProducerOptions>(configuration,
            PackageFlightProducerOptions.ConfigurationSection);
        
        services.AddKafka(configuration);

        services.AddSingleton<PackageFlightOplogReader>(o =>
        {
            var database = o.GetRequiredService<PackageDatabase>();
            var logger = o.GetRequiredService<ILogger<PackageFlightOplogReader>>();
            var producerFactory = o.GetRequiredService<KafkaProducerFactory>();
            var confirmationQueue = o.GetRequiredService<ConfirmationQueue>();
            var options = o.GetRequiredService<OplogReaderOptions>();
            var producerOptions = o.GetRequiredService<PackageFlightProducerOptions>();
            var collectionName = "packageFlights";
            var resumeTokenCollectionName = "oplogPackageFlightResumeTokens";

            return new PackageFlightOplogReader(database, logger, producerFactory, confirmationQueue, options, 
                producerOptions, collectionName, resumeTokenCollectionName);
        });

        services.AddHostedService(p => p.GetRequiredService<PackageFlightOplogReader>());

        services.AddSingleton<ConfirmationQueue>();
        services.AddHostedService(p => p.GetRequiredService<ConfirmationQueue>());

        return services;
    }
    
    internal static IServiceCollection AddObservability(this IServiceCollection services)
    {
        var appName = "esky-packages-flightoplogreader";

        services.AddOpenTelemetry()
            .WithMetrics(options =>
                options.SetResourceBuilder(ResourceBuilder.CreateDefault().AddService(appName))
                    .AddMeter(Metrics.MeterName)
                    .AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation()
                    .AddRuntimeInstrumentation()
                    .AddProcessInstrumentation()
                    .AddPrometheusExporter()
            );

        return services;
    }
}