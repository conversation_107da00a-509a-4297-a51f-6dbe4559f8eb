using Esky.Hotels.Infrastructure.Kafka.ConfirmationGroup;

namespace Esky.Packages.FlightOplogReader;

public class ConfirmationQueue(ILogger<ConfirmationQueue> logger)
    : BackgroundService
{
    public ConfirmationGroupQueue Queue { get; } = new();
    
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        logger.LogInformation("Starting ConfirmationCommitter service");

        while (!stoppingToken.IsCancellationRequested)
        {
            Queue.TryCommit();

            try
            {
                await Task.Delay(100, stoppingToken);
            }
            catch (TaskCanceledException)
            {
                logger.LogInformation("ConfirmationCommitter shutting down. Saving the last resume token");

                // Kafka wont commit immediately, maybe add Flush?
                Queue.TryCommit();
                break;
            }
        }
    }
}