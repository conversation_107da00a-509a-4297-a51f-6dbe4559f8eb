using Confluent.Kafka;

namespace Esky.Packages.FlightOplogReader.Options;

public class PackageFlightProducerOptions
{
    public const string ConfigurationSection = "PackageFlightProducer";
    public string BootstrapServers { get; set; } = "";
    public string PackageQuoteTopic { get; set; } = "";
    public string PackageFlightVariantTopic { get; set; } = "";
    public CompressionType Compression { get; set; }
}