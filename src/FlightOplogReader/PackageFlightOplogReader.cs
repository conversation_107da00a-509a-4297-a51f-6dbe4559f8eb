using System.Text.Json;
using Confluent.Kafka;
using Esky.Hotels.Infrastructure.Kafka.ConfirmationGroup;
using Esky.Hotels.Infrastructure.Kafka.Producer;
using Esky.Packages.Domain.Events.PackageFlightVariants;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Model.PackageFlightVariants;
using Esky.Packages.FlightOplogReader.Observability;
using Esky.Packages.FlightOplogReader.Options;
using Esky.Packages.Infrastructure.Database;
using Esky.Packages.Infrastructure.OplogReaders;
using Esky.Packages.Infrastructure.Partitioners;
using Esky.Packages.Infrastructure.Serialization.Contexts;
using MongoDB.Bson;
using MongoDB.Driver;

namespace Esky.Packages.FlightOplogReader;

public class PackageFlightOplogReader(
    PackageDatabase database,
    ILogger<OplogReader<PackageFlight>> logger,
    KafkaProducerFactory producerFactory,
    ConfirmationQueue confirmationQueue,
    OplogReaderOptions options,
    PackageFlightProducerOptions producerOptions,
    string collectionName,
    string resumeTokenCollectionName)
    : OplogReader<PackageFlight>(database.Database, logger, collectionName, resumeTokenCollectionName)
{
    private KafkaProducer<string, byte[]> _producer = null!;
    private readonly HotelMetaCodePartitioner _packageQuotePartitioner = new();
    private readonly AirportPartitioner _packageFlightVariantPartitioner = new();

    protected override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _packageQuotePartitioner.Initialize(producerOptions.PackageQuoteTopic, producerOptions.BootstrapServers);
        _packageFlightVariantPartitioner.Initialize(producerOptions.PackageFlightVariantTopic,
            producerOptions.BootstrapServers);

        _producer = producerFactory.Create<string, byte[]>(o => o
            .WithBootstrapServers(producerOptions.BootstrapServers)
            .WithAcks(Acks.All) // TODO: Check if this is the correct setting for the producer
            .WithDeliveryReportFields(ProducerDeliveryReportFields.Status)
            .WithClientId(nameof(PackageFlightOplogReader))
            .WithCompression(producerOptions.Compression)
            .WithQueueBufferingMaxMessages(1_000_000));

        if (options.PartitionCount < 1 || options.PartitionIndex < 0 ||
            options.PartitionIndex > options.PartitionCount - 1)
            throw new InvalidOperationException("Invalid configuration - PartitionCount/PartitionIndex");

        ReaderName = $"{nameof(PackageFlightOplogReader)}-{options.PartitionIndex}";

        return base.ExecuteAsync(stoppingToken);
    }

    protected override void ConfigureOptions(ChangeStreamOptions changeStreamOptions)
    {
        changeStreamOptions.FullDocument = ChangeStreamFullDocumentOption.Required;
        changeStreamOptions.FullDocumentBeforeChange = ChangeStreamFullDocumentBeforeChangeOption.Required;
    }

    protected override PipelineDefinition<ChangeStreamDocument<PackageFlight>, ChangeStreamDocument<PackageFlight>>
        ConfigurePipeline(EmptyPipelineDefinition<ChangeStreamDocument<PackageFlight>> pipeline)
    {
        logger.LogInformation("Partition count: {count}", options.PartitionCount);
        logger.LogInformation("Partition index: {index}", options.PartitionIndex);

        return pipeline.Match(
            BsonDocument.Parse(
                // language=json
                $$"""
                  {
                    "$expr": {
                      "$and": [
                        {
                          "$eq": [
                            {
                              "$mod": [{ "$dayOfYear": "$documentKey._id.c" }, {{options.PartitionCount}}]
                            },
                            {{options.PartitionIndex}}
                          ]
                        },
                        {
                          "$in": ["$operationType", ["insert", "delete", "replace"]]
                        }
                      ]
                    }
                  }
                  """
            )
        );
    }

    protected override void ProcessBatch(IReadOnlyCollection<ChangeStreamDocument<PackageFlight>> batch,
        Action acknowledge)
    {
        Metrics.RegisterLag(DateTimeOffset.FromUnixTimeSeconds(batch.First().ClusterTime.Timestamp).UtcDateTime);

        var variantEvents = new List<PackageFlightVariantEvent>();

        foreach (var change in batch)
        {
            if (MapToVariantEvents(change, logger) is { } e)
            {
                variantEvents.AddRange(e);
            }
        }

        var confirmationCount = variantEvents.Count;
        var confirmationGroup = confirmationQueue.Queue.CreateAndEnqueue(confirmationCount, _ => acknowledge());

        foreach (var e in variantEvents)
        {
            SendVariantEvent(e, confirmationGroup);
        }

        logger.LogDebug("Sent {count} events", confirmationCount);
    }

    protected override void ProcessEmptyBatch()
    {
        Metrics.RegisterLag(null);
    }

    private static List<PackageFlightVariantEvent> MapToVariantEvents(
        ChangeStreamDocument<PackageFlight> change,
        ILogger logger)
    {
        var createdFlights = 0;
        var updatedFlights = 0;
        var deletedFlights = 0;

        var before = change.FullDocumentBeforeChange;
        var after = change.FullDocument;

        var events = new List<PackageFlightVariantEvent>();

        var beforeVariants = before?.GetVariants();
        var afterVariants = after?.GetVariants();

        if (before == null && after == null)
        {
            logger.LogWarning("FullDocument and FullDocumentBeforeChange of the change stream document are null");
        }
        else if (before == null) // added flight
        {
            events.AddRange(afterVariants!.Select(CreateVariantUpdatedEvent));
            createdFlights += afterVariants!.Count;
        }
        else if (after == null) // removed flight
        {
            events.AddRange(beforeVariants!.Select(CreateVariantDeletedEvent));
            deletedFlights += beforeVariants!.Count;
        }
        else // updated flight
        {
            var deletedVariants = beforeVariants!.Where(b => afterVariants!.All(a => a.Id != b.Id)).ToArray();
            var addedVariants = afterVariants!.Where(a => beforeVariants!.All(b => a.Id != b.Id)).ToArray();
            var updatedVariants = afterVariants!.Where(a => beforeVariants!.Any(b =>
                a.Id == b.Id && (a.Price != b.Price || a.DepartureDate != b.DepartureDate ||
                                 a.FlightNumbers != b.FlightNumbers ||
                                 a.ReturnFlightNumbers != b.ReturnFlightNumbers ||
                                 a.BaggageIncluded != b.BaggageIncluded ||
                                 a.ReturnArrivalDate != b.ReturnArrivalDate || 
                                 a.ReturnDepartureDate != b.ReturnDepartureDate))).ToArray();

            events.AddRange(deletedVariants.Select(CreateVariantDeletedEvent));
            events.AddRange(addedVariants.Select(CreateVariantUpdatedEvent));
            events.AddRange(updatedVariants.Select(CreateVariantUpdatedEvent));

            deletedFlights += deletedVariants.Length;
            createdFlights += addedVariants.Length;
            updatedFlights += updatedVariants.Length;
        }

        Metrics.RegisterFlightStatistics(createdFlights, updatedFlights, deletedFlights,
            after?.Id.DepartureAirport ?? before?.Id.DepartureAirport ?? "unknown",
            after?.Id.ArrivalAirport ?? before?.Id.ArrivalAirport ?? "unknown");

        return events;
    }

    private static PackageFlightVariantUpdatedEvent CreateVariantUpdatedEvent(PackageFlightVariant variant) =>
        new()
        {
            Id = variant.Id,
            CheckIn = variant.Id.CheckIn,
            StayLength = variant.Id.StayLength,
            MarketId = variant.Id.MarketId,
            ArrivalAirport = variant.Id.ArrivalAirport,
            DepartureAirport = variant.Id.DepartureAirport,
            Occupancy = variant.Id.Occupancy,
            OfferId = variant.OfferId,
            InboundDeparture = variant.Id.InboundDeparture,
            OutboundDeparture = variant.Id.OutboundDeparture,
            DepartureDate = variant.DepartureDate,
            ReturnArrivalDate = variant.ReturnArrivalDate,
            ReturnDepartureDate = variant.ReturnDepartureDate,
            FlightNumbers = variant.FlightNumbers,
            ReturnFlightNumbers = variant.ReturnFlightNumbers,
            BaggageIncluded = variant.BaggageIncluded,
            Price = variant.Price
        };

    private static PackageFlightVariantDeletedEvent CreateVariantDeletedEvent(PackageFlightVariant variant) =>
        new()
        {
            Id = variant.Id,
            CheckIn = variant.Id.CheckIn,
            StayLength = variant.Id.StayLength,
            MarketId = variant.Id.MarketId,
            ArrivalAirport = variant.Id.ArrivalAirport,
            DepartureAirport = variant.Id.DepartureAirport,
            Occupancy = variant.Id.Occupancy,
            InboundDeparture = variant.Id.InboundDeparture,
            OutboundDeparture = variant.Id.OutboundDeparture,
        };

    private void SendVariantEvent(PackageFlightVariantEvent e, ConfirmationGroup confirmationGroup)
    {
        var partition = _packageFlightVariantPartitioner.GetTopicPartition(e.ArrivalAirport);

        var value = JsonSerializer.SerializeToUtf8Bytes(e,
            PackageFlightVariantEventJsonContext.Default.PackageFlightVariantEvent);

        _producer.Produce(partition, new Message<string, byte[]>
            {
                Key = e.Id,
                Value = value
            },
            report => MarkConfirmationGroupBasedOnDeliveryReport(report, confirmationGroup));
    }

    private void MarkConfirmationGroupBasedOnDeliveryReport<TKey, TValue>(
        DeliveryReport<TKey, TValue> deliveryReport, ConfirmationGroup confirmationGroup)
    {
        if (deliveryReport.Status == PersistenceStatus.Persisted)
        {
            confirmationGroup.ConfirmOne();
        }
        else
        {
            logger.LogError("Cannot deliver message to kafka: {error}", deliveryReport.Error);
            confirmationGroup.MarkAsFailed();
        }
    }
}