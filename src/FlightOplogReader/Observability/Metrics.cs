using System.Diagnostics.Metrics;

namespace Esky.Packages.FlightOplogReader.Observability;

public static class Metrics
{
    public const string MeterName = "packages-flight-oplog-reader";
    private static readonly Meter Meter = new(MeterName);

    private static long _lastEventMillis;
    
    private static readonly ObservableGauge<long> Lag = 
        Meter.CreateObservableGauge($"{MeterName}_lag", () => _lastEventMillis, "milliseconds");
    
    private static readonly Counter<long> CreatedFlights = Meter.CreateCounter<long>($"{MeterName}_created_flight_variants", 
        "count");
    
    private static readonly Counter<long> UpdatedFlights = Meter.CreateCounter<long>($"{MeterName}_updated_flight_variants",
        "count");
    
    private static readonly Counter<long> DeletedFlights = Meter.CreateCounter<long>($"{MeterName}_deleted_flight_variants",
        "count");

    public static void RegisterLag(DateTime? lastEventTime)
    {
        if (lastEventTime == null)
        {
            _lastEventMillis = 0;
        }
        else
        {
            _lastEventMillis = (long) (DateTime.UtcNow - lastEventTime.Value).TotalMilliseconds;
        }
    }
    
    public static void RegisterFlightStatistics(long created, long updated, long deleted, string departureAirport, 
        string arrivalAirport)
    {
        var tags = new KeyValuePair<string, object>[]
        {
            new("departureAirport", departureAirport),
            new("arrivalAirport", arrivalAirport)
        };
        
        CreatedFlights.Add(created, tags!);
        UpdatedFlights.Add(updated, tags!);
        DeletedFlights.Add(deleted, tags!);
    }
    
}