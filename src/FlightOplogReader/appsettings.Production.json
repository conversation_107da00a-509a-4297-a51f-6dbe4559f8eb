{"Logging": {"ServiceBus": {"Clustermembers": "esky-ets-hotels-pro.rabbitmq-logs-k8s.service.gcp-pro.consul", "UserName": "SECRET", "Password": "SECRET"}}, "Mongo": {"ConnectionString": "SECRET"}, "OplogReader": {"PartitionCount": 6, "PartitionIndex": -1}, "PackageFlightProducer": {"BootstrapServers": "esky-ets-hotels-pro.kafka-hotels-pro-kafka-node-0.service.gcp-pro.consul:9092,esky-ets-hotels-pro.kafka-hotels-pro-kafka-node-1.service.gcp-pro.consul:9092,esky-ets-hotels-pro.kafka-hotels-pro-kafka-node-2.service.gcp-pro.consul:9092", "PackageQuoteTopic": "packageQuotes", "PackageFlightVariantTopic": "packageFlightVariants", "Compression": "Lz4"}}