namespace Esky.Packages.Domain.Types;

public readonly struct TimeOfDay : IParsable<TimeOfDay>, IEquatable<TimeOfDay>
{
    private static readonly HashSet<string> Values =
    [
        "Morning",
        "Afternoon",
        "Evening"
    ];

    private readonly string _value;

    private const int MorningHourBoundary = 12;
    private const int AfternoonHourBoundary = 18;
    public static IReadOnlyCollection<int> HourBoundaries { get; } = [MorningHourBoundary, AfternoonHourBoundary, 24];

    public TimeOfDay(string value)
    {
        _value = Parse(value, true) ?? throw new FormatException("Invalid time of day value");
    }

    public static TimeOfDay Morning => new("Morning");
    public static TimeOfDay Afternoon => new("Afternoon");
    public static TimeOfDay Evening => new("Evening");

    private static string? Parse(string value, bool shouldThrow)
    {
        if (!Values.Contains(value))
        {
            if (shouldThrow)
                throw new FormatException("Invalid time of day value");
            return null;
        }

        return value;
    }

    public static TimeOfDay FromDateTime(DateTime dateTime)
    {
        if (dateTime.Hour < MorningHourBoundary)
            return Morning;
        if (dateTime.Hour < AfternoonHourBoundary)
            return Afternoon;
        return Evening;
    }
    
    public static TimeOfDay FromShortString(string timeOfDay)
        => timeOfDay switch
        {
            "M" => Morning,
            "A" => Afternoon,
            "E" => Evening,
            _ => throw new ArgumentOutOfRangeException()
        };

    public string ToShortString() => _value switch
    {
        "Morning" => "M",
        "Afternoon" => "A",
        "Evening" => "E",
        _ => throw new ArgumentOutOfRangeException()
    };
    
    public static implicit operator TimeOfDay(string text) 
        => new(text);
    
    public static implicit operator string(TimeOfDay timeOfDay)
        => timeOfDay._value;
    
    public static bool operator ==(TimeOfDay timeOfDay, TimeOfDay timeOfDay2)
        => timeOfDay._value == timeOfDay2._value;
    
    public static bool operator !=(TimeOfDay timeOfDay, TimeOfDay timeOfDay2)
        => !(timeOfDay == timeOfDay2);
    
    public override bool Equals(object? obj)
        => obj is TimeOfDay other && Equals(other);
    
    public override int GetHashCode()
        => _value.GetHashCode();
    
    public override string ToString()
        => _value;
    
    public static TimeOfDay Parse(string s, IFormatProvider? provider)
        => (TimeOfDay)s;

    public static bool TryParse(string? s, IFormatProvider? provider, out TimeOfDay result)
    {
        if (s != null && Parse(s, false) != null)
        {
            result = new TimeOfDay(s);
            return true;
        }

        result = default;
        return false;
    }

    public bool Equals(TimeOfDay other)
    {
        return _value == other._value;
    }
}