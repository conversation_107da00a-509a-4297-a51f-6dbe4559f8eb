namespace Esky.Packages.Domain.Types;

public readonly struct ProviderConfigurationId : IEquatable<ProviderConfigurationId>, IComparable<ProviderConfigurationId>
{
    public string Value { get; private init; }

    public ProviderConfigurationId(string value)
    {
        Value = value ?? throw new ArgumentNullException(nameof(value));
    }
    
    public static implicit operator ProviderConfigurationId(string text) 
        => new(text);

    public static implicit operator string(ProviderConfigurationId providerConfigurationId)
        => providerConfigurationId.Value;

    public bool Equals(ProviderConfigurationId other)
    {
        return Value == other.Value;
    }

    public int CompareTo(ProviderConfigurationId other)
    {
        return string.Compare(Value, other.Value, StringComparison.Ordinal);
    }

    public override bool Equals(object? obj)
    {
        return obj is ProviderConfigurationId other && Equals(other);
    }

    public override int GetHashCode()
    {
        return Value.GetHashCode();
    }
    
    public override string ToString()
        => Value;
}