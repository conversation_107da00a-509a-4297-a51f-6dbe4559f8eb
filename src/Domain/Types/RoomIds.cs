using System.Diagnostics.CodeAnalysis;

namespace Esky.Packages.Domain.Types;

public readonly struct RoomIds : IEquatable<RoomIds>, IParsable<RoomIds>
{
    private const char Separator = ',';

    public string[] Value { get; private init; } = [];

    public static RoomIds Empty => new([]);

    // TODO: Don't allow empty RoomIds as we should always get them - remove Empty static value as well
    public RoomIds(string[] roomIds)
    {
        Value = roomIds?
            .Where(x => !string.IsNullOrWhiteSpace(x))
            .Select(x => x.Replace(":", string.Empty).Replace(",", string.Empty).Replace("|", string.Empty))
            .OrderBy(x => x, StringComparer.Ordinal)
            .ToArray() ?? [];
    }

    public static RoomIds Merge(RoomIds[] others)
    {
        var allValues = Enumerable.Empty<string>();
        
        foreach (var other in others)
        {
            allValues = allValues.Concat(other.Value);
        }
        
        return new RoomIds(allValues.ToArray());
    }

    public static implicit operator RoomIds(string[] roomIds) 
        => new(roomIds);

    public static implicit operator string[](RoomIds roomIds)
        => roomIds.Value;

    public override string ToString()
    {
        return string.Join(Separator, Value);
    }

    public static RoomIds Parse(string s, IFormatProvider? provider)
    {
        if (string.IsNullOrWhiteSpace(s))
        {
            return Empty;
        }

        var split = s.Split(Separator, StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);

        return new RoomIds(split);
    }

    public static bool TryParse([NotNullWhen(true)] string? s, IFormatProvider? provider, out RoomIds result)
    {
        if (string.IsNullOrWhiteSpace(s))
        {
            result = Empty;
            return true;
        }

        try
        {
            result = Parse(s, provider);
            return true;
        }
        catch
        {
            result = default;
            return false;
        }
    }

    public bool Equals(RoomIds other)
    {
        return ToString() == other.ToString();
    }

    public override bool Equals(object? obj)
    {
        return obj is RoomIds other && Equals(other);
    }

    public override int GetHashCode()
    {
        return ToString().GetHashCode();
    }

    public static bool operator ==(RoomIds left, RoomIds right)
    {
        return left.Equals(right);
    }

    public static bool operator !=(RoomIds left, RoomIds right)
    {
        return !(left == right);
    }
}
