namespace Esky.Packages.Domain.Types;

public readonly struct Currency : IEquatable<Currency>, IParsable<Currency>
{
    private readonly string _code;

    public Currency(string code)
    {
        _code = code;
    }
    
    public static Currency PLN => new("PLN");
    public static Currency EUR => new("EUR");
    public static Currency USD => new("USD");
    public static Currency GBP => new("GBP");
    
    public static implicit operator C<PERSON>rency(string text) 
        => new(text);

    public static implicit operator string(Currency currency)
        => currency._code;
    
    public static bool operator ==(Currency currency, Currency currency2)
        => currency._code == currency2._code;
    
    public static bool operator !=(Currency currency, Currency currency2)
        => !(currency == currency2);

    public override bool Equals(object? obj)
        => obj is Currency other && Equals(other);
    
    public override int GetHashCode()
        => _code.GetHashCode();
    
    public override string ToString()
        => _code;
    
    public static Currency Parse(string s, IFormatProvider? provider)
        => (Currency)s;

    public static bool TryParse(string? s, IFormatProvider? provider, out Currency result)
    {
        if (s != null)
        {
            result = new Currency(s);
            return true;
        }

        result = default;
        return false;
    }

    public bool Equals(Currency other)
    {
        return _code == other._code;
    }
}