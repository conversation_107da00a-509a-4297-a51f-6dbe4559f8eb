namespace Esky.Packages.Domain.Types;

public readonly struct MealPlan : IParsable<MealPlan>, IEquatable<MealPlan>
{
    private static readonly HashSet<string> Values =
    [
        "None",
        "Breakfast",
        "HalfBoard",
        "FullBoard",
        "AllInclusive"
    ];

    private readonly string _value;
    
    public MealPlan(string value)
    {
         _value = Parse(value, true) ?? throw new FormatException("Invalid meal plan value");
    }
    
    private static string? Parse(string value, bool shouldThrow)
    {
        if (!Values.Contains(value))
        {
            if (shouldThrow)
                throw new FormatException("Invalid meal plan value");
            return null;
        }

        return value;
    }
    
    public static MealPlan None => new("None");
    public static MealPlan Breakfast => new("Breakfast");
    public static MealPlan HalfBoard => new("HalfBoard");
    public static MealPlan FullBoard => new("FullBoard");
    public static MealPlan AllInclusive => new("AllInclusive");
    
    public static MealPlan FromShortString(string mealPlan)
        => mealPlan switch
        {
            "N" => None,
            "B" => Breakfast,
            "H" => HalfBoard,
            "F" => FullBoard,
            "A" => AllInclusive,
            _ => throw new ArgumentOutOfRangeException()
        };

    public string ToShortString() => _value switch
    {
        "None" => "N",
        "Breakfast" => "B",
        "HalfBoard" => "H",
        "FullBoard" => "F",
        "AllInclusive" => "A",
        _ => throw new ArgumentOutOfRangeException()
    };
    
    public static implicit operator MealPlan(string text) 
        => new(text);

    public static implicit operator string(MealPlan mealPlan)
        => mealPlan._value;
    
    public static bool operator ==(MealPlan mealPlan, MealPlan mealPlan2)
        => mealPlan._value == mealPlan2._value;
    
    public static bool operator !=(MealPlan mealPlan, MealPlan mealPlan2)
        => !(mealPlan == mealPlan2);

    public override bool Equals(object? obj)
        => obj is MealPlan other && Equals(other);
    
    public override int GetHashCode()
        => _value.GetHashCode();
    
    public override string ToString()
        => _value;
    
    public static MealPlan Parse(string s, IFormatProvider? provider)
        => (MealPlan)s;

    public static bool TryParse(string? s, IFormatProvider? provider, out MealPlan result)
    {
        if (s != null && Parse(s, false) != null)
        {
            result = new MealPlan(s);
            return true;
        }

        result = default;
        return false;
    }

    public bool Equals(MealPlan other)
    {
        return _value == other._value;
    }
}