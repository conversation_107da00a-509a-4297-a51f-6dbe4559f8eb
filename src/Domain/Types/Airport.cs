namespace Esky.Packages.Domain.Types;

public readonly struct Airport : IEquatable<Airport>, IParsable<Airport>
{
    private readonly string _code;

    public Airport(string code)
    {
        if (code is null || code.Length != 3)
        {
            throw new FormatException("Airport code length must be 3.");
        }

        _code = code;
    }
    
    public static Airport WAW => new("WAW");
    
    public static implicit operator Airport(string text) 
        => new(text);

    public static implicit operator string(Airport airport)
        => airport._code;
    
    public static bool operator ==(Airport airport, Airport airport2)
        => airport._code == airport2._code;
    
    public static bool operator !=(Airport airport, Airport airport2)
        => !(airport == airport2);

    public override bool Equals(object? obj)
        => obj is Airport other && Equals(other);
    
    public override int GetHashCode()
        => _code.GetHashCode();
    
    public override string ToString()
        => _code;
    
    public static Airport Parse(string s, IFormatProvider? provider)
        => (Airport)s;

    public static bool TryParse(string? s, IFormatProvider? provider, out Airport result)
    {
        if (s != null)
        {
            result = new Airport(s);
            return true;
        }

        result = default;
        return false;
    }

    public bool Equals(Airport other)
    {
        return _code == other._code;
    }
}