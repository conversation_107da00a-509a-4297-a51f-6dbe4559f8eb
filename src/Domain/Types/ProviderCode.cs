namespace Esky.Packages.Domain.Types;

public readonly struct ProviderCode : IEquatable<ProviderCode>, IParsable<ProviderCode>
{
    private readonly int _code;

    public ProviderCode(int code)
    {
        _code = code;
    }

    public static implicit operator ProviderCode(int providerCode)
        => new(providerCode);

    public static implicit operator string(ProviderCode providerCode)
        => providerCode._code.ToString();

    public static implicit operator int(ProviderCode providerCode)
        => providerCode._code;

    public static bool operator ==(ProviderCode providerCode, ProviderCode providerCode2)
        => providerCode._code == providerCode2._code;

    public static bool operator !=(ProviderCode providerCode, ProviderCode providerCode2)
        => !(providerCode == providerCode2);

    public override bool Equals(object? obj)
        => obj is ProviderCode other && Equals(other);

    public override int GetHashCode()
        => _code.GetHashCode();

    public override string ToString()
        => _code.ToString();

    public static ProviderCode Parse(string s, IFormatProvider? provider)
        => (ProviderCode)int.Parse(s);

    public static bool TryParse(string? s, IFormatProvider? provider, out ProviderCode result)
    {
        if (s != null)
        {
            result = new ProviderCode(int.Parse(s));
            return true;
        }

        result = default;
        return false;
    }

    public bool Equals(ProviderCode other)
    {
        return _code == other._code;
    }
}
