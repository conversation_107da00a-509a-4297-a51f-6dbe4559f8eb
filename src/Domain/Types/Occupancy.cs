using Esky.Packages.Contract.Common;
using System.Text;

namespace Esky.Packages.Domain.Types;

public readonly struct Occupancy : IEquatable<Occupancy>
{
    public int Adults { get; private init; }
    public int[] ChildrenAges { get; private init; }
    
    public Occupancy(int adults, int[] childrenAges)
    {
        Adults = adults;
        ChildrenAges = childrenAges;
    }

    public static Occupancy FromDto(OccupancyDto dto)
    {
        return new(dto.Adults, dto.ChildrenAges);
    }
    
    public static Occupancy[] FromDto(OccupancyDto[] dtos)
    {
        return dtos.Select(dto => FromDto(dto)).ToArray();
    }
    
    public OccupancyDto ToDto()
    {
        return new OccupancyDto(Adults, ChildrenAges);
    }

    public override string ToString()
    {
        var sb = new StringBuilder();

        sb.Append('A');
        sb.Append(Adults);

        foreach (var child in ChildrenAges.OrderBy(c => c))
        {
            sb.Append('C');
            sb.Append(child);
        }

        return sb.ToString();
    }

    public bool Equals(Occupancy other)
    {
        if (Adults != other.Adults)
            return false;

        return ChildrenAges.SequenceEqual(other.ChildrenAges);
    }
    
    public static bool operator ==(Occupancy occupancy, Occupancy occupancy2)
        => occupancy.Equals(occupancy2);

    public static bool operator !=(Occupancy occupancy, Occupancy occupancy2)
        => !occupancy.Equals(occupancy2);
}