namespace Esky.Packages.Domain.Types.Extensions;

public static class DictionaryExtension
{
    public static bool DeepEquals<TKey, TValue>(this Dictionary<TKey, TValue> dict1, Dictionary<TKey, TValue> dict2,
        Func<TValue, TValue, bool>? valueComparer = null)
        where TKey : notnull
        where TValue : notnull
    {
        if (dict1.Count != dict2.Count) return false;

        var comparer = valueComparer ?? ((x, y) =>
        {
            if (x == null || y == null)
            {
                throw new ArgumentException("Cannot compare null values");
            }
            
            return x.Equals(y);
        });

        foreach (var kvp in dict1)
        {
            if (!dict2.TryGetValue(kvp.Key, out var value) || !comparer(kvp.Value, value))
            {
                return false;
            }
        }

        return true;
    }

    public static bool DeepEquals<TKey1, TKey2, TValue>(this Dictionary<TKey1, Dictionary<TKey2, TValue>> dict1,
        Dictionary<TKey1, Dictionary<TKey2, TValue>> dict2, Func<TValue, TValue, bool>? valueComparer = null)
        where TKey1 : notnull
        where TKey2 : notnull
        where TValue : notnull
    {
        if (dict1.Count != dict2.Count) return false;

        foreach (var kvp in dict1)
        {
            if (!dict2.TryGetValue(kvp.Key, out var value) || !kvp.Value.DeepEquals(value, valueComparer))
            {
                return false;
            }
        }

        return true;
    }

    public static bool DeepEquals<TKey1, TKey2, TKey3, TValue>(
        this Dictionary<TKey1, Dictionary<TKey2, Dictionary<TKey3, TValue>>> dict1,
        Dictionary<TKey1, Dictionary<TKey2, Dictionary<TKey3, TValue>>> dict2, 
        Func<TValue, TValue, bool>? valueComparer = null)
        where TKey1 : notnull
        where TKey2 : notnull
        where TKey3 : notnull
        where TValue : notnull
    {
        if (dict1.Count != dict2.Count) return false;

        foreach (var kvp in dict1)
        {
            if (!dict2.TryGetValue(kvp.Key, out var value) || !kvp.Value.DeepEquals(value, valueComparer))
            {
                return false;
            }
        }

        return true;
    }
}