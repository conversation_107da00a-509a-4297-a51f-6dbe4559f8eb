namespace Esky.Packages.Domain.Types.Extensions;

public static class DateExtensions
{
    public static int DifferenceInStayLength(this DateOnly dateOne, DateOnly dateTwo)
        => (int)dateOne.Minus(dateTwo).TotalDays;

    public static TimeSpan Minus(this DateOnly dateOne, DateOnly dateTwo)
        => dateOne.ToDateTime(TimeOnly.MinValue) - dateTwo.ToDateTime(TimeOnly.MinValue);

    public static DateOnly ToDateOnly(this DateTime dateTime)
        => DateOnly.FromDateTime(dateTime);

    public static (DateOnly MinDate, DateOnly MaxDate)[] GetDateChunks(
        DateOnly minCheckIn, 
        DateOnly maxCheckIn,
        int chunks)
    {
        var dateRanges = new List<(DateOnly MinDate, DateOnly MaxDate)>();

        var days = maxCheckIn.DayNumber - minCheckIn.DayNumber;
        var chunkSize = days / chunks;

        for (var i = 0; i < chunks; i++)
        {
            var minDate = minCheckIn.AddDays(i * chunkSize);
            var maxDate = minCheckIn.AddDays((i + 1) * chunkSize);

            dateRanges.Add((minDate, maxDate));
        }

        dateRanges.Add((DateOnly.MinValue, minCheckIn));
        dateRanges.Add((maxCheckIn, DateOnly.MaxValue));

        return dateRanges.OrderBy(x => x.MinDate).ToArray();
    }
}