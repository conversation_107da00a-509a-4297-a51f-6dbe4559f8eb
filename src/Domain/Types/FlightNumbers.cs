using System.Diagnostics.CodeAnalysis;
using System.Text.RegularExpressions;

namespace Esky.Packages.Domain.Types;

public readonly partial struct FlightNumbers : IParsable<FlightNumbers>, IEquatable<FlightNumbers>
{
    private const char Separator = ',';
    [GeneratedRegex(@"^[A-Z0-9]{2}\d{1,4}$", RegexOptions.Compiled)]
    private static partial Regex FlightNumberRegex();

    private List<string> Numbers { get; } = [];

    public static FlightNumbers Empty => new([]);

    public FlightNumbers(string[] numbers)
    {
        var invalid = numbers?.Where(n => string.IsNullOrWhiteSpace(n) || !FlightNumberRegex().IsMatch(n)).ToList() ?? [];
        if (invalid.Count > 0) throw new ArgumentException($"Invalid flight number(s): {string.Join(", ", invalid)}", nameof(numbers));
        Numbers = numbers?.ToList() ?? [];
    }

    public override string ToString()
    {
        return string.Join(Separator, Numbers ?? []);
    }
    
    public static implicit operator FlightNumbers(string[] numbers) 
        => new(numbers);
    
    public static implicit operator string[](FlightNumbers flightNumbers)
        => flightNumbers.Numbers.ToArray();

    public static FlightNumbers Parse(string s, IFormatProvider? provider)
    {
        if (string.IsNullOrWhiteSpace(s)) return Empty;
        var split = s.Split(Separator, StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
        return new FlightNumbers(split);
    }

    public static bool TryParse([NotNullWhen(true)] string? s, IFormatProvider? provider, out FlightNumbers result)
    {
        if (string.IsNullOrWhiteSpace(s))
        {
            result = default;
            return false;
        }
        try
        {
            result = Parse(s, provider);
            return true;
        }
        catch
        {
            result = default;
            return false;
        }
    }

    public bool Equals(FlightNumbers other)
    {
        return ToString() == other.ToString();
    }

    public override bool Equals(object? obj)
    {
        return obj is FlightNumbers other && Equals(other);
    }

    public override int GetHashCode()
    {
        return ToString().GetHashCode();
    }

    public static bool operator ==(FlightNumbers left, FlightNumbers right)
    {
        return left.Equals(right);
    }

    public static bool operator !=(FlightNumbers left, FlightNumbers right)
    {
        return !(left == right);
    }
}
