using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageHotelAirports;


public class PackageHotelAirports
{
    public PackageHotelAirportsId Id { get; private init; }
    public List<Airport> Airports { get; private init; } = [];
    public string DefinitionId { get; private init; } = null!;
    public DateTime UpdatedAt { get; private init; }
    
    public static PackageHotelAirports Create(PackageHotelAirportsId id, List<Airport> airports, string definitionId)
    {
        return new PackageHotelAirports
        {
            Id = id,
            Airports = airports,
            DefinitionId = definitionId,
            UpdatedAt = DateTime.UtcNow
        };
    }
}