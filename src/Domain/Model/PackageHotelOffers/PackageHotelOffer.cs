using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Policies;
using Esky.Packages.Domain.Types;
using Esky.Packages.Domain.Types.Extensions;

namespace Esky.Packages.Domain.Model.PackageHotelOffers;

public class PackageHotelOffer
{
    private const int DaysToCheckInToStoreNotRefundablePrices = 21;
    private const int MaxDaysToCheckInBeforeDepartureDate = 1; // TODO: Use it in generator
    private const int MaxDaysToCheckInAfterDepartureDate = 3; // TODO: Use it in generator

    // List of package occupancies that are collected in HCACHE. It isn't ideal to have this list here, but I don't
    // know how to do it better.
    private static readonly ISet<PackageOccupancy> OccupanciesCollectedInHotelsCache = new HashSet<PackageOccupancy>
    {
        PackageOccupancy.A1,
        PackageOccupancy.A1C1,
        PackageOccupancy.A1Y1,
        PackageOccupancy.A2,
        PackageOccupancy.A2I1,
        PackageOccupancy.A2C1,
        PackageOccupancy.A2Y1,
        PackageOccupancy.A2C2,
        PackageOccupancy.A2Y1C1,
        PackageOccupancy.A2Y2,
        PackageOccupancy.A3,
        PackageOccupancy.A4
    };

    public PackageHotelOfferId Id { get; private init; }
    public string DefinitionId { get; private init; } = null!;
    public Currency Currency { get; private init; }
    public PackageOccupancy[] Occupancies { get; private init; } = null!;
    public ProviderConfigurationId[] ProviderConfigurationIds { get; private init; } = null!;
    public bool OnlyRefundable { get; private set; }
    public Dictionary<ProviderConfigurationId, Dictionary<PackageOccupancy, PackageMealPlanRoomOffers>> RoomOffersByProviderConfigurationIdByOccupancy { get; private init; } = new(); // TODO: set null
    public Dictionary<Airport, Airport[]> Airports { get; private init; } = null!; // <Arrival, Departure[]>
    public DateTime GeneratedAt { get; private init; }
    public DateTime UpdatedAt { get; private set; }

    private ICurrencyConversionPolicy _currencyConversionPolicy = null!;

    public static PackageHotelOffer Create(PackageHotelOfferId id, string definitionId, Currency currency,
        PackageOccupancy[] occupancies, ProviderConfigurationId[] providerConfigurationIds,
        Dictionary<Airport, Airport[]> airports, HotelOffer[] hotelOffers, Action<PackageHotelOffer>? configure = null)
    {
        var onlyRefundable = ShouldBeOnlyRefundable(id.CheckIn);

        var packageHotelOffer = new PackageHotelOffer
        {
            Id = id,
            DefinitionId = definitionId,
            Currency = currency,
            Occupancies = occupancies,
            ProviderConfigurationIds = providerConfigurationIds,
            OnlyRefundable = onlyRefundable,
            RoomOffersByProviderConfigurationIdByOccupancy = [],
            Airports = airports,
            GeneratedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
        };

        configure?.Invoke(packageHotelOffer);
        packageHotelOffer.RequirePolicies();

        if (packageHotelOffer.Id.CheckIn.DayNumber - DateOnly.FromDateTime(DateTime.UtcNow).DayNumber <=
            DaysToCheckInToStoreNotRefundablePrices)
        {
            packageHotelOffer.OnlyRefundable = false;
        }

        if (packageHotelOffer.OnlyRefundable && !HasAnyRefundablePrice(hotelOffers))
        {
            packageHotelOffer.OnlyRefundable = false;
        }

        foreach (var hotelOffer in hotelOffers)
        {
            packageHotelOffer.ApplyHotelOffer(hotelOffer);
        }

        return packageHotelOffer;
    }

    public void ApplyPolicies(ICurrencyConversionPolicy currencyConversionPolicy)
    {
        _currencyConversionPolicy = currencyConversionPolicy;
    }

    public bool Compare(PackageHotelOffer packageHotelOffer)
    {
        return Id == packageHotelOffer.Id &&
               DefinitionId == packageHotelOffer.DefinitionId &&
               Currency == packageHotelOffer.Currency &&
               Occupancies.SequenceEqual(packageHotelOffer.Occupancies) &&
               ProviderConfigurationIds.SequenceEqual(packageHotelOffer.ProviderConfigurationIds) &&
               OnlyRefundable == packageHotelOffer.OnlyRefundable &&
               Airports.DeepEquals(packageHotelOffer.Airports, (current, other) => current.SequenceEqual(other)) &&
               RoomOffersByProviderConfigurationIdByOccupancy.DeepEquals(
                   packageHotelOffer.RoomOffersByProviderConfigurationIdByOccupancy, (a, b) => a.IsUpToDate(b));
    }

    public PackageHotelOfferStayKey GetStayKey()
    {
        return new PackageHotelOfferStayKey(Id.MetaCode, Id.CheckIn, Id.StayLength);
    }
    
    public MealPlan[] GetMealPlans()
    {
        return RoomOffersByProviderConfigurationIdByOccupancy
            .SelectMany(providerConfigurationPair => providerConfigurationPair.Value
                .SelectMany(occupancyPair => occupancyPair.Value.RoomOffersByMealPlan
                    .Select(mealPlanPair => mealPlanPair.Key)))
            .Distinct()
            .ToArray();
    }

    public Airport[] GetDepartureAirports()
    {
        return Airports.Values
            .SelectMany(departureAirports => departureAirports)
            .Distinct()
            .ToArray();
    }

    public PackageOccupancy[] GetOccupancies()
    {
        return RoomOffersByProviderConfigurationIdByOccupancy
            .SelectMany(providerConfigurationPair => providerConfigurationPair.Value
                .Select(occupancyPair => occupancyPair.Key))
            .Distinct()
            .ToArray();
    }

    public static bool ShouldBeOnlyRefundable(DateOnly checkIn)
    {
        return checkIn.DayNumber - DateOnly.FromDateTime(DateTime.UtcNow).DayNumber >
            DaysToCheckInToStoreNotRefundablePrices;
    }

    public static DateOnly GetMinimalCheckInDate(DateOnly departureDate)
    {
        if (departureDate.DayNumber <= MaxDaysToCheckInBeforeDepartureDate)
        {
            return departureDate;
        }
        
        return departureDate.AddDays(-MaxDaysToCheckInBeforeDepartureDate);
    }
    
    public static DateOnly GetMaximalCheckInDate(DateOnly departureDate)
    {
        if (departureDate.DayNumber + MaxDaysToCheckInAfterDepartureDate > DateOnly.MaxValue.DayNumber)
        {
            return DateOnly.MaxValue;
        }
        
        return departureDate.AddDays(MaxDaysToCheckInAfterDepartureDate);
    }

    /// <summary>
    /// Returns a dictionary of prices grouped by occupancy and meal plan. It's for backward compatibility to return
    /// single room offers.
    /// </summary>
    /// <returns></returns>
    public Dictionary<PackageOccupancy, Dictionary<MealPlan, int>> GetPrices()
    {
        return RoomOffersByProviderConfigurationIdByOccupancy
            .SelectMany(providerConfigurationPair => providerConfigurationPair.Value
                .SelectMany(occupancyPair => occupancyPair.Value.RoomOffersByMealPlan
                    .Select(mealPlanPair => (
                        ProviderConfigurationId: providerConfigurationPair.Key,
                        Occupancy: occupancyPair.Key,
                        MealPlan: mealPlanPair.Key,
                        RoomOffers: mealPlanPair.Value))))
            .GroupBy(x => x.Occupancy)
            .ToDictionary(byOccupancy => byOccupancy.Key, byOccupancy => byOccupancy
                .GroupBy(x => x.MealPlan)
                .ToDictionary(byMealPlan => byMealPlan.Key, byMealPlan => byMealPlan
                    .SelectMany(x => x.RoomOffers)
                    .MinBy(x => x.CompensatedPrice)!.CompensatedPrice));
    }

    /// <summary>
    /// Returns a dictionary of prices grouped by meal plan for the given occupancies.
    /// Each occupancy represents a single room offer, and the prices of single rooms are summed up.
    /// Prices always come from the same provider, as they must be booked with a single provider during the booking step.
    /// </summary>
    /// <param name="occupancies">One or more occupanices (room configurations)</param>
    /// <returns>Multi room price for each meal plan</returns>
    public Dictionary<MealPlan, PackageRoomOffer> GetPrices(PackageOccupancy[] occupancies)
    {
        return RoomOffersByProviderConfigurationIdByOccupancy
            .SelectMany(providerConfigurationPair => providerConfigurationPair.Value
                .SelectMany(occupancyPair => occupancyPair.Value.RoomOffersByMealPlan
                    .Select(mealPlanPair => (
                        ProviderConfigurationId: providerConfigurationPair.Key,
                        Occupancy: occupancyPair.Key,
                        MealPlan: mealPlanPair.Key,
                        RoomOffers: mealPlanPair.Value))))
            .GroupBy(x => x.MealPlan)
            .ToDictionary(x => x.Key,
                x => CalculateLowestPrice(
                    x.Select(byMealPlan =>
                        (byMealPlan.ProviderConfigurationId, byMealPlan.Occupancy, byMealPlan.RoomOffers)),
                    occupancies))
            .Where(x => x.Value is not null)
            .ToDictionary(x => x.Key, x => x.Value!);
    }

    private static PackageRoomOffer? CalculateLowestPrice(IEnumerable<(ProviderConfigurationId ProviderConfigurationId, PackageOccupancy Occupancy, PackageRoomOffer[] RoomOffers)> prices,
        PackageOccupancy[] occupancies)
    {
        var uniqueOccupancies = occupancies
            .GroupBy(o => o)
            .Select(group => (Occupancy: group.Key, Count: group.Count()))
            .ToList();

        return prices
            .GroupBy(x => x.ProviderConfigurationId)
            .Select(group =>
            {
                var totalPrice = 0;
                var roomIds = new List<RoomIds>();
                int minimumRoomAvailability = int.MaxValue;

                foreach (var uniqueOccupancy in uniqueOccupancies)
                {
                    var roomOffers = group
                        .Where(p => p.Occupancy == uniqueOccupancy.Occupancy)
                        .SelectMany(p => p.RoomOffers)
                        .OrderBy(p => p.CompensatedPrice)
                        .ToArray();

                    var requestedAvailability = uniqueOccupancy.Count;
                    for (var i = 0; i < roomOffers.Length && requestedAvailability > 0; i++)
                    {
                        var availability = roomOffers[i].Availability;
                        if (availability >= requestedAvailability)
                        {
                            totalPrice += roomOffers[i].CompensatedPrice * requestedAvailability;
                            roomIds.Add(roomOffers[i].RoomIds);
                            minimumRoomAvailability = availability;
                            requestedAvailability = 0; // All requested rooms are satisfied
                            break; // We found a room offer that satisfies the request
                        }

                        totalPrice += roomOffers[i].CompensatedPrice * availability;
                        roomIds.Add(roomOffers[i].RoomIds);
                        if (availability < minimumRoomAvailability)
                        {
                            minimumRoomAvailability = availability;
                        }
                        requestedAvailability -= availability;
                    }
                    
                    if (requestedAvailability > 0)
                    {
                        // Not enough rooms available for this occupancy
                        return null;
                    }
                }

                return new PackageRoomOffer
                {
                    Availability = minimumRoomAvailability,
                    CompensatedPrice = totalPrice,
                    RoomIds = RoomIds.Merge(roomIds.ToArray())
                };
            })
            .Where(x => x is not null)
            .MinBy(x => x!.CompensatedPrice);
    }

    public bool ApplyHotelOffersByStayKey(Dictionary<PackageHotelOfferStayKey, List<HotelOffer>> hotelOffersByStayKey)
    {
        var updated = false;

        if (Id.CheckIn.DayNumber - DateOnly.FromDateTime(DateTime.UtcNow).DayNumber <=
            DaysToCheckInToStoreNotRefundablePrices)
        {
            OnlyRefundable = false;
        }

        if (hotelOffersByStayKey.TryGetValue(new PackageHotelOfferStayKey(Id.MetaCode, Id.CheckIn, Id.StayLength),
                out var hotelOffers))
        {
            foreach (var hotelOffer in hotelOffers)
            {
                updated |= ApplyHotelOffer(hotelOffer);
            }
        }

        if (updated)
        {
            UpdatedAt = DateTime.UtcNow;
        }

        return updated;
    }

    private bool ApplyHotelOffer(HotelOffer hotelOffer)
    {
        // TODO: Get it from market and remove from here
        if (!ProviderConfigurationIds.Contains(hotelOffer.ProviderConfigurationId))
        {
            return false;
        }
        
        var packageOccupancy = PackageOccupancy.FromOccupancy(hotelOffer.Occupancy);

        // TODO: Get it from market and remove from here
        if (!Occupancies.Contains(packageOccupancy))
        {
            return false;
        }

        var currentProviderOffers = RoomOffersByProviderConfigurationIdByOccupancy.GetValueOrDefault(
            hotelOffer.ProviderConfigurationId, new Dictionary<PackageOccupancy, PackageMealPlanRoomOffers>());
        var currentMealPlanRoomOffers =
            currentProviderOffers.GetValueOrDefault(packageOccupancy, PackageMealPlanRoomOffers.Empty);

        var newOccupancyOffers = hotelOffer.RoomOffersByMealPlanByRefundability
            .SelectMany(mealPlanPair => mealPlanPair.Value
                .Select(refundabilityPair => (
                    MealPlan: mealPlanPair.Key,
                    Refundability: refundabilityPair.Key,
                    Prices: refundabilityPair.Value
                        .Select(r => new PackageRoomOffer 
                        {
                            Availability = r.Availability,
                            CompensatedPrice = (int)_currencyConversionPolicy.Convert(r.Price.Value, r.Price.Currency)
                                .RoundAwayFromZero(),
                            RoomIds = r.RoomIds
                        }))))
            .Where(x => !OnlyRefundable || x.Refundability.IsRefundable)
            .GroupBy(x => x.MealPlan)
            .ToDictionary(m => m.Key, m => m
                .SelectMany(p => p.Prices)
                .OrderBy(x => x.CompensatedPrice)
                .ToArray());

        var newMealPlanRoomOffers = new PackageMealPlanRoomOffers(newOccupancyOffers, hotelOffer.UpdatedAt);

        // compare prices with compensation
        if (currentMealPlanRoomOffers.IsUpToDate(newMealPlanRoomOffers))
        {
            return false;
        }

        // update prices
        currentProviderOffers[packageOccupancy] = newMealPlanRoomOffers;
        RoomOffersByProviderConfigurationIdByOccupancy[hotelOffer.ProviderConfigurationId] = currentProviderOffers;

        return true;
    }

    private void RequirePolicies()
    {
        if (_currencyConversionPolicy == null)
        {
            throw new InvalidOperationException("Currency conversion policy is required.");
        }
    }

    private static bool HasAnyRefundablePrice(HotelOffer[] quotes)
    {
        return quotes.Any(quote =>
            quote.RoomOffersByMealPlanByRefundability
                .Any(price => price.Value.Any(refundability => refundability.Key.IsRefundable)));
    }

    public List<PackageFlightId> GetPackageFlightIds()
    {
        return Airports
            .SelectMany(airports => airports.Value.Select(departureAirport =>
                new PackageFlightId(
                    checkIn: Id.CheckIn,
                    stayLength: Id.StayLength,
                    marketId: Id.MarketId,
                    arrivalAirport: airports.Key,
                    departureAirport: departureAirport)))
            .ToList();
    }
    
    public static bool AvailableInHotelsCache(Occupancy[] occupancies)
    {
        var packageOccupancies = occupancies.Select(PackageOccupancy.FromOccupancy).ToArray();
        return packageOccupancies.All(occupancy => OccupanciesCollectedInHotelsCache.Contains(occupancy));
    }
}