using Esky.Packages.Domain.Types;
using Esky.Packages.Domain.Types.Extensions;

namespace Esky.Packages.Domain.Model.PackageHotelOffers;

public class PackageMealPlanRoomOffers(Dictionary<MealPlan, PackageRoomOffer[]> roomOffersByMealPlan, DateTime updatedAt)
{
    public Dictionary<MealPlan, PackageRoomOffer[]> RoomOffersByMealPlan { get; private init; } = roomOffersByMealPlan;
    public DateTime UpdatedAt { get; private init; } = updatedAt;


    public static PackageMealPlanRoomOffers Empty => new(new Dictionary<MealPlan, PackageRoomOffer[]>(), DateTime.MinValue);

    public bool IsUpToDate(PackageMealPlanRoomOffers other)
    {
        return RoomOffersByMealPlan.DeepEquals(other.RoomOffersByMealPlan, (a, b) =>
        {
            if (a.Length != b.Length)
            {
                return false;
            }

            for (var i = 0; i < a.Length; i++)
            {
                if (!a[i].IsUpToDate(b[i]))
                {
                    return false;
                }
            }

            return true;
        }) || UpdatedAt > other.UpdatedAt;
    }
}