using System.Diagnostics.CodeAnalysis;

namespace Esky.Packages.Domain.Model.PackageHotelOffers;

public readonly struct PackageHotelOfferId : IParsable<PackageHotelOfferId>, IEquatable<PackageHotelOfferId>
{
    private const char KeySeparator = ':';
    
    public DateOnly CheckIn { get; private init; }
    public int StayLength { get; private init; }
    public string MarketId { get; private init;}
    public int MetaCode { get; private init; }
    
    public PackageHotelOfferId(string id)
    {
        (CheckIn, StayLength, MarketId, MetaCode) =
            Parse(id, shouldThrow: true) ?? throw new FormatException("Invalid package hotel offer id");
    }

    public PackageHotelOfferId(DateOnly checkIn, int stayLength, string marketId, int metaCode)
    {
        CheckIn = checkIn;
        StayLength = stayLength;
        MarketId = marketId;
        MetaCode = metaCode;
    }

    private static (DateOnly CheckIn, int StayLength, string MarketId, int MetaCode)? Parse(string id, 
        bool shouldThrow)
    {
        var t = id.AsSpan();

        var separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("CheckIn/StayLength date separator not found");
            return null;
        }

        if (!DateOnly.TryParseExact(t.Slice(0, separatorPosition), "yyMMdd", out var checkIn))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(checkIn)} date");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow) 
                throw new FormatException("StayLength/MarketId date separator not found");
        }

        if (!int.TryParse(t.Slice(0, separatorPosition), out var stayLength))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid stay length");
            return null;
        }

        t = t.Slice(separatorPosition + 1);
       
        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("MarketId/MetaCode separator not found");
            return null;
        }
        
        var marketId = t.Slice(0, separatorPosition).ToString();
        
        if (!int.TryParse(t.Slice(separatorPosition + 1), out var metaCode))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(metaCode)}");
            return null;
        }
        
        return (checkIn, stayLength, marketId, metaCode);
    }
    
    public static implicit operator PackageHotelOfferId(string id) 
        => new(id);

    public static implicit operator string(PackageHotelOfferId packageHotelOfferId)
        => packageHotelOfferId.ToString();

    public static bool operator ==(PackageHotelOfferId packageHotelOfferId, PackageHotelOfferId packageHotelOfferId2)
        => packageHotelOfferId.Equals(packageHotelOfferId2);

    public static bool operator !=(PackageHotelOfferId packageHotelOfferId, PackageHotelOfferId packageHotelOfferId2)
        => !(packageHotelOfferId == packageHotelOfferId2);

    public override bool Equals([NotNullWhen(true)] object? obj)
        => obj is PackageHotelOfferId && Equals(obj);

    public override int GetHashCode() 
        => ToString().GetHashCode();

    public override string ToString()
        => $"{CheckIn.ToString("yyMMdd")}{KeySeparator}{StayLength}{KeySeparator}{MarketId}{KeySeparator}{MetaCode}";

    public static PackageHotelOfferId Parse(string s, IFormatProvider? provider)
        => (PackageHotelOfferId)s;

    public static bool TryParse([NotNullWhen(true)] string? s, IFormatProvider? provider, out PackageHotelOfferId result)
    {
        if (s != null && Parse(s, false) != null)
        {
            result = new PackageHotelOfferId(s);
            return true;
        }

        result = default;
        return false;
    }

    public bool Equals(PackageHotelOfferId other)
    {
        return CheckIn.Equals(other.CheckIn) && StayLength == other.StayLength &&
               MetaCode == other.MetaCode && MarketId == other.MarketId;
    }
}