using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageHotelOffers;

public class PackageRoomOffer
{
    private const decimal CompensationPercentage = 0.01M;

    public int Availability { get; init; }
    public int CompensatedPrice { get; init; }
    public RoomIds RoomIds { get; init; } = RoomIds.Empty;
    
    public bool IsUpToDate(PackageRoomOffer other)
    {
        return Availability == other.Availability &&
               IsCompensatedPriceEqual(other.CompensatedPrice) &&
               RoomIds == other.RoomIds;
    }
    
    private bool IsCompensatedPriceEqual(decimal newPrice)
    {
        if (CompensatedPrice == 0 && newPrice == 0)
        {
            return true;
        }

        if (CompensatedPrice == 0 || newPrice == 0)
        {
            return false;
        }

        return Math.Abs(CompensatedPrice - newPrice) / CompensatedPrice <= CompensationPercentage;
    }
}