using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageHotelOffers;

public class HotelOffer
{
    public required int MetaCode { get; init; }
    public required DateOnly CheckIn { get; init; }
    public required int StayLength { get; init; }
    public required ProviderConfigurationId ProviderConfigurationId { get; init; }
    public required Occupancy Occupancy { get; init; }
    public required Dictionary<MealPlan, Dictionary<Refundability, RoomOffer[]>> RoomOffersByMealPlanByRefundability { get; init; }
    public required DateTime UpdatedAt { get; init; }
}

public class RoomOffer
{
    public required int Availability { get; init; }
    public required Money Price { get; init; }
    public RoomIds RoomIds { get; init; } = RoomIds.Empty;
}