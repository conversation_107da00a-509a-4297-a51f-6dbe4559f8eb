using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageFlightVariants;

public readonly struct PackageFlightVariantId : IParsable<PackageFlightVariantId>, IEquatable<PackageFlightVariantId>
{
    private const char KeySeparator = ':';

    public DateOnly CheckIn { get; private init; }
    public int StayLength { get; private init; }
    public string MarketId { get; private init; }
    public Airport ArrivalAirport { get; private init; }
    public Airport DepartureAirport { get; private init; }
    public PackageOccupancy Occupancy { get; private init; }
    public TimeOfDay InboundDeparture { get; private init; }
    public TimeOfDay OutboundDeparture { get; private init; }

    public PackageFlightVariantId(string id)
    {
        (
            CheckIn, 
            StayLength, 
            MarketId, 
            ArrivalAirport, 
            DepartureAirport, 
            Occupancy, 
            InboundDeparture,
            OutboundDeparture
        ) = Parse(id, shouldThrow: true) ?? throw new FormatException("Invalid package flight id");
    }

    public PackageFlightVariantId(DateOnly checkIn, int stayLength, string marketId, Airport arrivalAirport,
        Airport departureAirport, PackageOccupancy occupancy, TimeOfDay inboundDeparture, TimeOfDay outboundDeparture)
    {
        CheckIn = checkIn;
        StayLength = stayLength;
        MarketId = marketId;
        ArrivalAirport = arrivalAirport;
        DepartureAirport = departureAirport;
        Occupancy = occupancy;
        InboundDeparture = inboundDeparture;
        OutboundDeparture = outboundDeparture;
    }

    private static (DateOnly CheckIn, int StayLength, string MarketId, Airport ArrivalAirport, Airport DepartureAirport,
        PackageOccupancy Occupancy, TimeOfDay InboundDeparture, TimeOfDay OutboundDeparture)? Parse(string id,
            bool shouldThrow)
    {
        var t = id.AsSpan();

        var separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("CheckIn/StayLength date separator not found");
            return null;
        }

        if (!DateOnly.TryParseExact(t.Slice(0, separatorPosition), "yyMMdd", out var checkIn))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(checkIn)} date");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("StayLength/MarketId date separator not found");
        }

        if (!int.TryParse(t.Slice(0, separatorPosition), out var stayLength))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid stay length");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("MarketId/DepartureAirport date separator not found");
        }

        var marketId = t.Slice(0, separatorPosition).ToString();

        t = t.Slice(separatorPosition + 1);

        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("ArrivalAirport/DepartureAirport date separator not found");
        }

        var arrivalAirport = new Airport(t.Slice(0, separatorPosition).ToString());

        t = t.Slice(separatorPosition + 1);

        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("DepartureAirport/Occupancy date separator not found");
        }

        var departureAirport = new Airport(t.Slice(0, separatorPosition).ToString());

        t = t.Slice(separatorPosition + 1);

        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("Occupancy/InboundDeparture date separator not found");
        }

        var occupancy = PackageOccupancy.Parse(t.Slice(0, separatorPosition).ToString(), null);

        t = t.Slice(separatorPosition + 1);

        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("InboundDeparture/OutboundDeparture date separator not found");
        }

        var inboundDeparture = TimeOfDay.FromShortString(t.Slice(0, separatorPosition).ToString());

        t = t.Slice(separatorPosition + 1);

        var outboundDeparture = TimeOfDay.FromShortString(t.ToString());

        return (CheckIn: checkIn, StayLength: stayLength, MarketId: marketId, ArrivalAirport: arrivalAirport,
            DepartureAirport: departureAirport, Occupancy: occupancy, InboundDeparture: inboundDeparture,
            OutboundDeparture: outboundDeparture);
    }

    public static implicit operator PackageFlightVariantId(string text)
        => new(text);

    public static implicit operator string(PackageFlightVariantId packageFlightVariantId)
        => packageFlightVariantId.ToString();

    public static bool operator ==(PackageFlightVariantId packageFlightVariantId,
        PackageFlightVariantId packageFlightVariantId2)
        => packageFlightVariantId.Equals(packageFlightVariantId2);

    public static bool operator !=(PackageFlightVariantId packageFlightVariantId,
        PackageFlightVariantId packageFlightVariantId2)
        => !(packageFlightVariantId == packageFlightVariantId2);

    public override bool Equals(object? obj)
        => obj is PackageFlightVariantId other && Equals(other);

    public override int GetHashCode()
        => ToString().GetHashCode();

    public override string ToString()
        => $"{CheckIn.ToString("yyMMdd")}{KeySeparator}{StayLength}{KeySeparator}{MarketId}{KeySeparator}" +
           $"{ArrivalAirport}{KeySeparator}{DepartureAirport}{KeySeparator}{Occupancy}{KeySeparator}" +
           $"{InboundDeparture.ToShortString()}{KeySeparator}{OutboundDeparture.ToShortString()}";

    public static PackageFlightVariantId Parse(string s, IFormatProvider? provider)
        => (PackageFlightVariantId)s;

    public static bool TryParse(string? s, IFormatProvider? provider, out PackageFlightVariantId result)
    {
        if (s != null && Parse(s, false) != null)
        {
            result = new PackageFlightVariantId(s);
            return true;
        }

        result = default;
        return false;
    }

    public bool Equals(PackageFlightVariantId other)
    {
        return CheckIn == other.CheckIn &&
               StayLength == other.StayLength &&
               MarketId == other.MarketId &&
               DepartureAirport == other.DepartureAirport &&
               ArrivalAirport == other.ArrivalAirport &&
               Occupancy == other.Occupancy &&
               InboundDeparture == other.InboundDeparture &&
               OutboundDeparture == other.OutboundDeparture;
    }
}