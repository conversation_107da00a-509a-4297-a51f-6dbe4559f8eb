using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageFlightVariants;

public class PackageFlightVariant
{
    public PackageFlightVariantId Id { get; private init; }
    public string OfferId { get; private init; } = null!;
    public DateOnly DepartureDate { get; private init; }
    public DateOnly ReturnDepartureDate { get; private init; }
    public DateOnly ReturnArrivalDate { get; private init; }
    public FlightNumbers FlightNumbers { get; private init; }
    public FlightNumbers ReturnFlightNumbers { get; private init; }
    public bool BaggageIncluded { get; private init; }
    public int Price { get; private init; }

    public static PackageFlightVariant Create(DateOnly checkIn, int stayLength, string marketId, Airport arrivalAirport,
        Airport departureAirport, PackageOccupancy packageOccupancy, TimeOfDay inboundDeparture,
        TimeOfDay outboundDeparture, string offerId, DateOnly departureDate,
        DateOnly returnDepartureDate, DateOnly returnArrivalDate, 
        FlightNumbers flightNumbers, FlightNumbers returnFlightNumbers, bool baggageIncluded, int price)
    {
        return new PackageFlightVariant
        {
            Id = new PackageFlightVariantId(checkIn, stayLength, marketId, arrivalAirport, departureAirport,
                packageOccupancy, inboundDeparture, outboundDeparture),
            OfferId = offerId,
            DepartureDate = departureDate,
            ReturnDepartureDate = returnDepartureDate,
            ReturnArrivalDate = returnArrivalDate,
            FlightNumbers = flightNumbers,
            ReturnFlightNumbers = returnFlightNumbers,
            BaggageIncluded = baggageIncluded,
            Price = price
        };
    }
}