using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.Markets;

public class Market
{
    public string Id { get; private init; } = null!;
    public Currency Currency { get; private init; }
    public string PartnerCode { get; private set; } = null!;
    public Airport[] DepartureAirports { get; private set; } = null!; // TODO: Remove
    public ProviderConfigurationId[] HotelOfferProviderConfigurationIds { get; private set; } = null!;
    public bool EnableInboundOutboundFlightDepartureHours { get; set; } = false;
    public bool EmitPriceHistoryEvents { get; set; } = false;
    public TimeZoneInfo TimeZone { get; private set; } = null!;
    
    public static Market Create(string id, Currency currency, string partnerCode, Airport[] departureAirports, 
        ProviderConfigurationId[] providerConfigurationIds, bool enableInboundOutboundFlightDepartureHours, bool emitPriceHistoryEvents, TimeZoneInfo timeZone)
    {
        return new Market
        {
            Id = id,
            Currency = currency,
            PartnerCode = partnerCode,
            DepartureAirports = departureAirports, 
            HotelOfferProviderConfigurationIds = providerConfigurationIds,
            EnableInboundOutboundFlightDepartureHours = enableInboundOutboundFlightDepartureHours,
            EmitPriceHistoryEvents = emitPriceHistoryEvents,
            TimeZone = timeZone
        };
    }

    public void Update(Market market)
    {
        if (Id != market.Id)
        {
            throw new InvalidOperationException("Cannot update market with different Id");
        }
        
        if (Currency != market.Currency) 
        {
            throw new InvalidOperationException("Cannot update market with different currency");
        }
        
        PartnerCode = market.PartnerCode;
        DepartureAirports = market.DepartureAirports;
        HotelOfferProviderConfigurationIds = market.HotelOfferProviderConfigurationIds;
        EnableInboundOutboundFlightDepartureHours = market.EnableInboundOutboundFlightDepartureHours;
        EmitPriceHistoryEvents = market.EmitPriceHistoryEvents;
        TimeZone = market.TimeZone;
    }

    public DateOnly GetMarketDateOnlyForUtc(DateTime date)
    {
        if (date.Kind != DateTimeKind.Utc)
        {
            throw new ArgumentException("Date must be in UTC", nameof(date));
        }

        var localTime = TimeZoneInfo.ConvertTimeFromUtc(date, TimeZone);
        return DateOnly.FromDateTime(localTime);
    }
}