using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.LiveVariants;

public class FlightLiveVariants
{
    public List<FlightLiveVariant> Flights { get; private set; } = [];

    private FlightLiveVariants()
    {
    }

    public static FlightLiveVariants Create(IEnumerable<FlightLiveVariant> flights)
    {
        return new FlightLiveVariants
        {
            Flights = flights
                .DistinctBy(x => x.Key)
                .ToList()
        };
    }

    public FlightLiveVariant? SelectFlight(Airport[]? selectedDepartureAirports, string? selectedFlightOptionId,
        Airport? preferredDepartureAirport, TimeOfDay[]? inboundDepartures, TimeOfDay[]? outboundDepartures)
    {
        FlightLiveVariant? flightVariant;

        if (selectedFlightOptionId != null)
        {
            flightVariant = Flights.FirstOrDefault(f => f.Key == selectedFlightOptionId);
            if (flightVariant != null)
            {
                return flightVariant;
            }
        }

        if (preferredDepartureAirport != null)
        {
            flightVariant = Flights
                .Where(f => f.DepartureAirport == preferredDepartureAirport)
                .Where(f => inboundDepartures == null || inboundDepartures.Length == 0 || inboundDepartures.Contains(f.InboundDeparture))
                .Where(f => outboundDepartures == null || outboundDepartures.Length == 0 || outboundDepartures.Contains(f.OutboundDeparture))
                .OrderBy(f => f.Price)
                .FirstOrDefault();

            if (flightVariant != null)
            {
                return flightVariant;
            }
        }

        if (selectedDepartureAirports != null)
        {
            flightVariant = Flights
                .Where(f => selectedDepartureAirports.Contains(f.DepartureAirport))
                .Where(f => inboundDepartures == null || inboundDepartures.Length == 0 || inboundDepartures.Contains(f.InboundDeparture))
                .Where(f => outboundDepartures == null || outboundDepartures.Length == 0 || outboundDepartures.Contains(f.OutboundDeparture))
                .OrderBy(f => f.Price)
                .FirstOrDefault();

            if (flightVariant != null)
            {
                return flightVariant;
            }
        }

        return Flights.OrderBy(f => f.Price).FirstOrDefault();
    }

    public void RemoveSoldOutFlight(FlightLiveVariant flightLiveVariant)
    {
        if (!Flights.Contains(flightLiveVariant))
        {
            throw new InvalidOperationException($"Alternative flight with key {flightLiveVariant.Key} is not availabile in flight list.");
        }

        Flights.Remove(flightLiveVariant);
    }

    public Dictionary<(Airport DepartureAirport, TimeOfDay InboundDeparture, TimeOfDay OutboundDeparture), List<FlightLiveVariant>> GetFlightsByDepartureAirportByInboundDepartureByOutboundDeparture()
    {
        return Flights
            .GroupBy(f => (f.DepartureAirport, f.InboundDeparture, f.OutboundDeparture))
            .ToDictionary(
                g => g.Key,
                g => g.OrderBy(v => v.Price).ToList());
    }
}
