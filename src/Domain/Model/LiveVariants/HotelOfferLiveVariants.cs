using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.LiveVariants;

public class HotelOfferLiveVariants
{
    public List<HotelOfferLiveVariant> HotelOffers { get; private set; } = [];
    public bool OnlyRefundable { get; private set; }
    public MealPlan[]? PreferredMealPlans { get; private set; }

    private HotelOfferLiveVariants()
    {
    }

    public static HotelOfferLiveVariants CreateCheapestHotelOfferVariantsByRooms(
        IEnumerable<Dictionary<string, IEnumerable<HotelOfferLiveVariant>>> hotelOfferVariantsGroupedByRooms,
        bool onlyRefundable,
        MealPlan[]? preferredMealPlans)
    {
        var hotelOfferVariants = new List<HotelOfferLiveVariant>();

        var shouldApplyPreferredMealPlanFiltering = preferredMealPlans != null && preferredMealPlans.Length > 0 &&
            hotelOfferVariantsGroupedByRooms.Any(hotelOfferVariantsByRoom =>
                hotelOfferVariantsByRoom.Any(g => g.Value.Any(v =>
                    ApplyRefundFilter(v, onlyRefundable) && preferredMealPlans.Contains(v.MealPlan))));

        foreach (var hotelOfferVariantsByRoom in hotelOfferVariantsGroupedByRooms)
        {
            var cheapestRoom = hotelOfferVariantsByRoom
                .SelectMany(g => g.Value
                    .Where(v => ApplyRefundFilter(v, onlyRefundable))
                    .Where(v => ApplyMealPlanFilter(v, shouldApplyPreferredMealPlanFiltering, preferredMealPlans))
                    .Select(v => (Room: g.Key, Variant: v)))
                .OrderBy(t => t.Variant.Price)
                .FirstOrDefault();

            if (cheapestRoom != default)
            {
                hotelOfferVariants.AddRange(hotelOfferVariantsByRoom[cheapestRoom.Room]
                    .Where(v => ApplyRefundFilter(v, onlyRefundable))
                    .Where(v => ApplyMealPlanFilter(v, shouldApplyPreferredMealPlanFiltering, preferredMealPlans)));
            }
        }

        return new HotelOfferLiveVariants
        {
            HotelOffers = hotelOfferVariants,
            OnlyRefundable = onlyRefundable,
            PreferredMealPlans = preferredMealPlans
        };

        static bool ApplyMealPlanFilter(HotelOfferLiveVariant variant, bool shouldApplyPreferredMealPlanFiltering, MealPlan[]? preferredMealPlans)
        {
            return !shouldApplyPreferredMealPlanFiltering || preferredMealPlans == null || preferredMealPlans.Contains(variant.MealPlan);
        }

        static bool ApplyRefundFilter(HotelOfferLiveVariant variant, bool onlyRefundable)
        {
            return !onlyRefundable || variant.Refundability.IsRefundable;
        }
    }

    public Dictionary<MealPlan, List<HotelOfferLiveVariant>> GetHotelOffersByMealPlan()
    {
        return HotelOffers
            .GroupBy(f => f.MealPlan)
            .ToDictionary(
                g => g.Key,
                g => g.OrderBy(v => v.Price).ToList());
    }
}