using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageHotelOfferVariants;
using Esky.Packages.Domain.Policies;
using Esky.Packages.Domain.Types;
using Esky.Packages.Domain.Types.Extensions;

namespace Esky.Packages.Domain.Model.LiveVariants;

public class HotelOfferLiveVariant
{
    private const decimal CompensationPercentage = 0.1M;

    public DateOnly CheckIn { get; private set; }
    public int StayLength { get; private set; }
    public string OfferId { get; private set; } = null!;
    public MealPlan MealPlan { get; private set; }
    public Refundability Refundability { get; private set; }
    public DateOnly? FreeRefundUntil { get; private set; }
    public RoomIds RoomIds { get; private set; } = RoomIds.Empty;
    public Currency Currency { get; private set; }
    public decimal Price { get; private set; }
    public decimal PriceCompensated { get; private set; }
    public Money PriceAtHotel { get; private set; }
    public int Availability { get; private set; }

    private ICurrencyConversionPolicy _currencyConversionPolicy = null!;

    private HotelOfferLiveVariant()
    {
    }

    public static HotelOfferLiveVariant Create(
        DateOnly checkIn,
        int stayLength,
        string offerId,
        MealPlan mealPlan,
        Refundability refundability,
        DateOnly? freeRefundUntil,
        RoomIds roomIds,
        Currency currency,
        Money price,
        Money priceAtHotel,
        int availability,
        Action<HotelOfferLiveVariant>? configure = null)
    {
        var hotelOfferVariant = new HotelOfferLiveVariant
        {
            CheckIn = checkIn,
            StayLength = stayLength,
            OfferId = offerId,
            MealPlan = mealPlan,
            Refundability = refundability,
            RoomIds = roomIds,
            FreeRefundUntil = freeRefundUntil,
            Currency = currency,
            PriceAtHotel = priceAtHotel,
            Availability = availability,
        };

        configure?.Invoke(hotelOfferVariant);
        hotelOfferVariant.RequirePolicies();

        hotelOfferVariant.ApplyPrice(price);

        return hotelOfferVariant;
    }

    private void RequirePolicies()
    {
        if (_currencyConversionPolicy == null)
        {
            throw new InvalidOperationException("Currency conversion policy is required.");
        }
    }

    private void ApplyPrice(Money price)
    {
        Price = _currencyConversionPolicy.Convert(price.Value, price.Currency).RoundAwayFromZero();
        PriceCompensated = Price;
    }

    public void ApplyPolicies(ICurrencyConversionPolicy currencyConversionPolicy)
    {
        _currencyConversionPolicy = currencyConversionPolicy;
    }

    public decimal Compensate(
        decimal? lastCompensatedPrice,
        bool onlyUp = false)
    {
        if (lastCompensatedPrice is null)
        {
            return Price;
        }

        if (Price <= lastCompensatedPrice)
        {
            return lastCompensatedPrice.Value;
        }

        var lowerPercent = onlyUp ? 1m : 1m - CompensationPercentage;
        var upperPercent = 1m + CompensationPercentage;

        return Compensate(lastCompensatedPrice.Value, lowerPercent, upperPercent);
    }
    
    // TODO: When live-variants endpoint is removed merge this method with Compensate to set price inside this class
    // TODO: Refactor these "live" types to reduce duplication
    public void SetPriceCompensated(decimal priceCompensated)
    {
        PriceCompensated = priceCompensated;
    }

    public PackageHotelOfferVariant ToPackageHotelOfferVariant(string marketId, int metaCode, PackageOccupancy occupancy)
    {
        return PackageHotelOfferVariant.Create(
            checkIn: CheckIn,
            stayLength: StayLength,
            marketId: marketId,
            metaCode: metaCode,
            occupancy: occupancy,
            mealPlan: MealPlan,
            refundability: Refundability,
            roomIds: RoomIds,
            price: (int)Price);
    }

    private decimal Compensate(
        decimal lastCompensatedPrice,
        decimal lowerPercent,
        decimal upperPercent)
    {
        if (!IsInRange(
                target: lastCompensatedPrice,
                lower: Price * lowerPercent,
                upper: Price * upperPercent))
        {
            return Price;
        }

        return lastCompensatedPrice;
    }

    private static bool IsInRange(decimal target, decimal lower, decimal upper)
    {
        if (target < lower)
        {
            return false;
        }

        if (target > upper)
        {
            return false;
        }

        return true;
    }
}