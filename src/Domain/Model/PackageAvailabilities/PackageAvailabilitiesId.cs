using System.Diagnostics.CodeAnalysis;

namespace Esky.Packages.Domain.Model.PackageAvailabilities;

public readonly struct PackageAvailabilitiesId : IParsable<PackageAvailabilitiesId>, IEquatable<PackageAvailabilitiesId>
{
    private const char KeySeparator = ':';

    public string DefinitionId { get; private init; }
    public int Partition { get; private init; }

    public PackageAvailabilitiesId(string id)
    {
        (DefinitionId, Partition) =
            Parse(id, shouldThrow: true) ?? throw new FormatException("Invalid package availability id");
    }

    public PackageAvailabilitiesId(string definitionId, int partition)
    {
        DefinitionId = definitionId;
        Partition = partition;
    }

    private static (string DefinitionId, int Partition)? Parse(string id, bool shouldThrow)
    {
        var t = id.AsSpan();

        var separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("DefinitionId/Partition separator not found");
            return null;
        }

        var definitionId = t.Slice(0, separatorPosition).ToString();

        if (!int.TryParse(t.Slice(separatorPosition + 1), out var partition))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid partition");
            return null;
        }

        return (definitionId, partition);
    }

    public static implicit operator PackageAvailabilitiesId(string id)
        => new(id);

    public static implicit operator string(PackageAvailabilitiesId packageAvailabilitiesId)
        => packageAvailabilitiesId.ToString();

    public static bool operator ==(PackageAvailabilitiesId packageAvailabilitiesId,
        PackageAvailabilitiesId packageAvailabilitiesId2)
        => packageAvailabilitiesId.Equals(packageAvailabilitiesId2);

    public static bool operator !=(PackageAvailabilitiesId packageAvailabilitiesId,
        PackageAvailabilitiesId packageAvailabilitiesId2)
        => !(packageAvailabilitiesId == packageAvailabilitiesId2);

    public override bool Equals([NotNullWhen(true)] object? obj)
        => obj is PackageAvailabilitiesId && Equals(obj);

    public override int GetHashCode()
        => ToString().GetHashCode();

    public override string ToString()
        => $"{DefinitionId}{KeySeparator}{Partition}";

    public static PackageAvailabilitiesId Parse(string s, IFormatProvider? provider)
        => (PackageAvailabilitiesId)s;

    public static bool TryParse([NotNullWhen(true)] string? s, IFormatProvider? provider,
        out PackageAvailabilitiesId result)
    {
        if (s != null && Parse(s, false) != null)
        {
            result = new PackageAvailabilitiesId(s);
            return true;
        }

        result = default;
        return false;
    }

    public bool Equals(PackageAvailabilitiesId other)
    {
        return DefinitionId == other.DefinitionId && Partition == other.Partition;
    }
}