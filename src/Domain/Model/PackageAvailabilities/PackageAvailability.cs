using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageAvailabilities;

public class PackageAvailability
{
    public required int MetaCode { get; init; }
    public required PackageOccupancy[] Occupancies { get; set; } = [];
    public required MealPlan[] MealPlans { get; set; } = [];
    public required Airport[] DepartureAirports { get; set; } = [];
    public required Airport[] ArrivalAirports { get; set; } = [];
    public required int[] StayLengths { get; set; } = [];
    public required decimal LowestTotalPrice { get; set; }
    public required Dictionary<int, decimal> LowestPricesPerStayLength { get; set; } = [];
    public required DateOnly MinCheckIn { get; set; }
    public required DateOnly MaxCheckIn { get; set; }

    public void Merge(PackageAvailability other)
    {
        if (MetaCode != other.MetaCode)
        {
            throw new InvalidOperationException("Can't merge PackageAvailability with different MetaCodes");
        }

        Occupancies = Occupancies.Union(other.Occupancies).ToArray();
        MealPlans = MealPlans.Union(other.MealPlans).ToArray();
        DepartureAirports = DepartureAirports.Union(other.DepartureAirports).ToArray();
        ArrivalAirports = ArrivalAirports.Union(other.ArrivalAirports).ToArray();
        StayLengths = StayLengths.Union(other.StayLengths).ToArray();
        LowestTotalPrice = Math.Min(LowestTotalPrice, other.LowestTotalPrice);
        LowestPricesPerStayLength = MergeLowestPricesPerStayLength(other.LowestPricesPerStayLength);
        MinCheckIn = MinCheckIn < other.MinCheckIn ? MinCheckIn : other.MinCheckIn;
        MaxCheckIn = MaxCheckIn > other.MaxCheckIn ? MaxCheckIn : other.MaxCheckIn;
    }

    private Dictionary<int, decimal> MergeLowestPricesPerStayLength(
        Dictionary<int, decimal> otherPrices)
    {
        return LowestPricesPerStayLength
            .Concat(otherPrices)
            .GroupBy(kvp => kvp.Key)
            .ToDictionary(
                g => g.Key,
                g => g.Min(kvp => kvp.Value));
    }
}