using System.Diagnostics.CodeAnalysis;
using Esky.Packages.Domain.Model.PackageFlights;

namespace Esky.Packages.Domain.Model.PackageFlightVariantPrices;

public class PackageFlightVariantPrice
{
    public PackageFlightVariantPriceId Id { get; private init; }
    
    public PackageFlightPartitionKey PartitionKey { get; private init; }

    public Dictionary<PackageFlightVariantPriceKey, int> Prices { get; private init; } = [];
    
    public static PackageFlightVariantPrice Create(
        PackageFlightVariantPriceId id,
        Dictionary<PackageFlightVariantPriceKey, int> prices)
    {
        return new PackageFlightVariantPrice
        {
            Id = id,
            PartitionKey = new PackageFlightPartitionKey(id.ArrivalAirport, id.DepartureAirport),
            Prices = prices
        };
    }

    public bool ApplyPrices(Dictionary<PackageFlightVariantPriceKey, int> newPrices)
    {
        var changed = false;
        
        foreach (var (key, newValue) in newPrices)
        {
            if (Prices.TryGetValue(key, out var current))
            {
                var minPrice = Math.Min(current, newValue);
                if (current == minPrice) continue;
                
                Prices[key] = minPrice;
            }
            else
            {
                Prices[key] = newValue;
            }
            
            changed = true;
        }
        
        return changed;
    }
}

public readonly record struct PackageFlightVariantPriceKey : IParsable<PackageFlightVariantPriceKey>
{
    public bool BaggageIncluded { get; init; }

    public static PackageFlightVariantPriceKey Parse(string s, IFormatProvider? provider)
    {
        try
        {
            if (s is not { Length: 1 }) throw new FormatException("Invalid format for PackageFlightVariantPriceKey");

            return new PackageFlightVariantPriceKey { BaggageIncluded = s[0] == 'B' };
        }
        catch (Exception)
        {
            return new PackageFlightVariantPriceKey { BaggageIncluded = false };
        }
    }

    public static bool TryParse([NotNullWhen(true)] string? s, IFormatProvider? provider, out PackageFlightVariantPriceKey result)
    {
        try
        {
            result = Parse(s ?? throw new ArgumentNullException(nameof(s)), provider);
            return true;
        }
        catch (FormatException)
        {
            result = default;
            return false;
        }
    }

    public override string ToString()
    {
        return BaggageIncluded ? "B" : "N";
    }
}
