using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageVariants;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageFlightVariantPrices;

public readonly record struct PackageFlightVariantPriceId
{
    public DateOnly DepartureDate { get; private init; }
    public DateOnly ReturnDepartureDate { get; private init; }
    public string MarketId { get; private init; }
    public Airport ArrivalAirport { get; private init; }
    public Airport DepartureAirport { get; private init; }
    public FlightNumbers FlightNumbers { get; private init; }
    public FlightNumbers ReturnFlightNumbers { get; private init; }
    public PackageOccupancy Occupancy { get; private init; }
    public DateOnly PriceDate { get; private init; }

    public static PackageFlightVariantPriceId Create(
        DateOnly departureDate,
        DateOnly returnDepartureDate,
        string marketId,
        Airport arrivalAirport,
        Airport departureAirport,
        FlightNumbers flightNumbers,
        FlightNumbers returnFlightNumbers,
        PackageOccupancy occupancy,
        DateOnly priceDate)
    {
        return new PackageFlightVariantPriceId
        {
            DepartureDate = departureDate,
            ReturnDepartureDate = returnDepartureDate,
            MarketId = marketId,
            ArrivalAirport = arrivalAirport,
            DepartureAirport = departureAirport,
            FlightNumbers = flightNumbers,
            ReturnFlightNumbers = returnFlightNumbers,
            Occupancy = occupancy,
            PriceDate = priceDate
        };
    }

    public bool IsMatching(PackageVariantId packageVariantId)
    {
        return DepartureDate == packageVariantId.DepartureDate &&
               ReturnDepartureDate == packageVariantId.ReturnDepartureDate &&
               MarketId == packageVariantId.MarketId &&
               ArrivalAirport.Equals(packageVariantId.ArrivalAirport) &&
               DepartureAirport.Equals(packageVariantId.DepartureAirport) &&
               FlightNumbers.Equals(packageVariantId.FlightNumbers) &&
               ReturnFlightNumbers.Equals(packageVariantId.ReturnFlightNumbers) &&
               Occupancy.Equals(packageVariantId.Occupancy);
    }

    public bool Equals(PackageFlightVariantPriceId other)
    {
        return DepartureDate == other.DepartureDate &&
               ReturnDepartureDate == other.ReturnDepartureDate &&
               MarketId == other.MarketId &&
               ArrivalAirport.Equals(other.ArrivalAirport) &&
               DepartureAirport.Equals(other.DepartureAirport) &&
               FlightNumbers.Equals(other.FlightNumbers) &&
               ReturnFlightNumbers.Equals(other.ReturnFlightNumbers) &&
               Occupancy.Equals(other.Occupancy) &&
               PriceDate == other.PriceDate;
    }

    public override int GetHashCode()
    {
        var hashCode = new HashCode();
        hashCode.Add(DepartureDate);
        hashCode.Add(ReturnDepartureDate);
        hashCode.Add(MarketId);
        hashCode.Add(ArrivalAirport.ToString());
        hashCode.Add(DepartureAirport.ToString());
        hashCode.Add(FlightNumbers.ToString());
        hashCode.Add(ReturnFlightNumbers.ToString());
        hashCode.Add(Occupancy.ToString());
        hashCode.Add(PriceDate);
        return hashCode.ToHashCode();
    }
}