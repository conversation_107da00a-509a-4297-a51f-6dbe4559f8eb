namespace Esky.Packages.Domain.Model.Common.Extensions;

public static class PackageOccupancyExtension
{
    public static PackageOccupancy Merge(this ICollection<PackageOccupancy> occupancies)
    {
        return new PackageOccupancy(
            occupancies.Sum(c => c.Adults),
            occupancies.Sum(c => c.Youths),
            occupancies.Sum(c => c.Children),
            occupancies.Sum(c => c.Infants));
    }
}