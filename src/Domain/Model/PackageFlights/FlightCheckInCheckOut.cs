using Esky.Packages.Domain.Model.PackageFlights.Exceptions;
using Esky.Packages.Domain.Types.Extensions;

namespace Esky.Packages.Domain.Model.PackageFlights;

public class FlightCheckInCheckOut
{
    private static readonly TimeSpan MaxFlightArrivalTimeAfterMidnightToCheckInYesterday = TimeSpan.FromHours(4);
    private static readonly TimeSpan MaxFlightReturnDepartureTimeAfterMidnightToCheckOutYesterday = TimeSpan.FromHours(2);
        
    public DateOnly CheckIn { get; }
    public DateOnly CheckOut { get; }
    public int StayLength { get; }

    public FlightCheckInCheckOut(DateTime arrivalDate, DateTime returnDepartureDate)
    {
        if (returnDepartureDate < arrivalDate)
        {
            throw new ReturnDepartureDateBeforeArrivalException(arrivalDate, returnDepartureDate);
        }

        var checkIn = (arrivalDate - MaxFlightArrivalTimeAfterMidnightToCheckInYesterday).ToDateOnly();
        var checkOut = (returnDepartureDate - MaxFlightReturnDepartureTimeAfterMidnightToCheckOutYesterday).ToDateOnly();

        CheckIn = checkIn;
        CheckOut = checkOut;
        StayLength = checkOut.DifferenceInStayLength(checkIn);
    }
}
