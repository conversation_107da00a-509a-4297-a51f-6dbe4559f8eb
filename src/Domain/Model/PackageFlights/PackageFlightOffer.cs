using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageFlights;

public class PackageFlightOffer
{
    public string Id { get; set; } = "";
    public DateTime DepartureDate { get; set; }
    public DateTime ReturnDepartureDate { get; set; }
    public DateTime ReturnArrivalDate { get; set; }
    public Flight[] Flights { get; set; } = default!;
    public FlightNumbers FlightNumbers { get; set; }
    public FlightNumbers ReturnFlightNumbers { get; set; }
    public bool BaggageIncluded { get; set; }

    public TimeOfDay InboundDeparture => TimeOfDay.FromDateTime(ReturnDepartureDate);
    public TimeOfDay OutboundDeparture => TimeOfDay.FromDateTime(DepartureDate);

    public int? GetPrice(PackageOccupancy occupancy)
    {
        if (Flights.Any(f => !f.Prices.ContainsKey(occupancy)))
        {
            return null;
        }
        
        return (int?)Flights.Sum(f => f.Prices[occupancy]);
    }
}