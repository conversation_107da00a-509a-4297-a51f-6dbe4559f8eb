using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlightVariants;
using Esky.Packages.Domain.Policies;
using Esky.Packages.Domain.Types;
using Esky.Packages.Domain.Types.Extensions;

namespace Esky.Packages.Domain.Model.PackageFlights;

public class PackageFlight
{
    // Maximum number of seats supported by the package flight (domain rule).
    public const int MaxSupportedSeats = 9;

    // Supported stay lengths for package flights (domain rule).
    public static readonly HashSet<int> SupportedStayLengths = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 
        17, 21, 22, 24, 28];

    private const decimal CompensationPercentage = 0.03M;
    private const int MaxFlightOffersPerOccupancy = 3;

    public PackageFlightId Id { get; private init; }
    public string DefinitionId { get; private init; } = null!;
    public PackageFlightPartitionKey PartitionKey { get; private init; }
    public Currency Currency { get; private init; }
    public PackageOccupancy[] Occupancies { get; private set; } = null!;
    public PackageFlightOffer[] FlightOffers { get; private set; } = null!;
    public DateTime GeneratedAt { get; private init; }
    public DateTime UpdatedAt { get; private set; }

    private ICurrencyConversionPolicy _currencyConversionPolicy = null!;

    public static PackageFlight Create(PackageFlightId id, string definitionId, Currency currency,
        PackageOccupancy[] occupancies, FlightOffer[] flightOffers, Action<PackageFlight>? configure = null)
    {
        if (flightOffers.Length == 0)
        {
            throw new ArgumentException("FlightOffers must not be empty.");
        }

        var partitionKey = new PackageFlightPartitionKey(id.ArrivalAirport, id.DepartureAirport);

        var packageFlightOffers = flightOffers.Select(x => new PackageFlightOffer
            {
                Id = x.Id,
                DepartureDate = x.DepartureDate,
                ReturnDepartureDate = x.ReturnDepartureDate,
                ReturnArrivalDate = x.ReturnArrivalDate,
                FlightNumbers = x.FlightNumbers,
                ReturnFlightNumbers = x.ReturnFlightNumbers,
                BaggageIncluded = x.BaggageIncluded,
                Flights = x.Prices
                    .Select(p => new Flight
                    {
                        Id = p.Key,
                        Prices = []
                    })
                    .ToArray()
            })
            .ToArray();

        var packageFlight = new PackageFlight
        {
            Id = id,
            DefinitionId = definitionId,
            PartitionKey = partitionKey,
            Currency = currency,
            Occupancies = occupancies,
            FlightOffers = packageFlightOffers,
            GeneratedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
        };

        var flightQuotes = flightOffers
            .SelectMany(x => x.Prices)
            .Select(p => new FlightQuote
            {
                FlightId = p.Key,
                Prices = p.Value.Prices.ToDictionary(
                    y => new PackageOccupancy(y.Key.Adults, y.Key.Youths, y.Key.Children, y.Key.Infants), y => y.Value),
                Currency = p.Value.Currency,
                UpdateTime = p.Value.UpdatedAt
            })
            .ToArray();

        configure?.Invoke(packageFlight);
        packageFlight.RequirePolicies();

        packageFlight.ApplyQuotes(flightQuotes);
        packageFlight.LimitFlightOffers();

        return packageFlight;
    }

    public void ApplyPolicies(ICurrencyConversionPolicy currencyConversionPolicy)
    {
        _currencyConversionPolicy = currencyConversionPolicy;
    }

    // TODO: Unit test
    public bool Compare(PackageFlight packageFlight)
    {
        return Id == packageFlight.Id &&
               DefinitionId == packageFlight.DefinitionId &&
               Occupancies.SequenceEqual(packageFlight.Occupancies) &&
               Currency == packageFlight.Currency &&
               CompareFlightOffers(packageFlight.FlightOffers);
    }

    public Dictionary<PackageOccupancy, FlightPriceEntry> GetLowestPricesByOccupancy()
    {
        var prices = GetLowestPricesByOccupancyInboundOutbound();

        return prices
            .GroupBy(p => p.Key.Occupancy)
            .ToDictionary(x => x.Key, x => x.MinBy(f => f.Value.Price).Value);
    }


    public Dictionary<(TimeOfDay InboundDeparture, TimeOfDay OutboundDeparture), FlightPriceEntry> 
        GetLowestPricesByOccupancyInboundOutbound(PackageOccupancy occupancy)
    {
        return GetLowestPricesByOccupancyInboundOutbound()
            .Where(kvp => kvp.Key.Occupancy == occupancy)
            .ToDictionary(kvp => (kvp.Key.InboundDeparture, kvp.Key.OutboundDeparture), kvp => kvp.Value);
    }

    private Dictionary<(PackageOccupancy Occupancy, TimeOfDay InboundDeparture, TimeOfDay OutboundDeparture),
        FlightPriceEntry> GetLowestPricesByOccupancyInboundOutbound()
    {
        var prices = new Dictionary<(PackageOccupancy, TimeOfDay, TimeOfDay), FlightPriceEntry>();

        foreach (var occupancy in Occupancies)
        {
            foreach (var flightOffer in FlightOffers)
            {
                var inboundDeparture = flightOffer.InboundDeparture;
                var outboundDeparture = flightOffer.OutboundDeparture;

                var flightPrices = flightOffer.Flights
                    .SelectMany(f => f.Prices)
                    .Where(p => p.Key == occupancy)
                    .Select(p => p.Value)
                    .ToList();

                if (flightPrices.Count == flightOffer.Flights.Length)
                {
                    var price = flightPrices.Sum();
                    if (!prices.TryGetValue((occupancy, inboundDeparture, outboundDeparture), out var currentPrice) ||
                        currentPrice.Price > price)
                    {
                        prices[(occupancy, inboundDeparture, outboundDeparture)] = new FlightPriceEntry
                        {
                            Id = flightOffer.Id,
                            FlightIds = flightOffer.Flights.Select(f => f.Id).ToArray(),
                            Price = price,
                            DepartureDate = flightOffer.DepartureDate.ToDateOnly(),
                            ReturnDepartureDate = flightOffer.ReturnDepartureDate.ToDateOnly(),
                            ReturnArrivalDate = flightOffer.ReturnArrivalDate.ToDateOnly(),
                            FlightNumbers = flightOffer.FlightNumbers,
                            ReturnFlightNumbers = flightOffer.ReturnFlightNumbers,
                            BaggageIncluded = flightOffer.BaggageIncluded
                        };
                    }
                }
            }
        }

        return prices;
    }

    public List<PackageFlightVariant> GetVariants()
    {
        var prices = GetLowestPricesByOccupancyInboundOutbound();

        return prices
            .Select(kvp => PackageFlightVariant.Create(
                checkIn: Id.CheckIn,
                stayLength: Id.StayLength,
                marketId: Id.MarketId,
                arrivalAirport: Id.ArrivalAirport,
                departureAirport: Id.DepartureAirport,
                packageOccupancy: kvp.Key.Occupancy,
                inboundDeparture: kvp.Key.InboundDeparture,
                outboundDeparture: kvp.Key.OutboundDeparture,
                offerId: kvp.Value.Id,
                departureDate: kvp.Value.DepartureDate,
                returnDepartureDate: kvp.Value.ReturnDepartureDate,
                returnArrivalDate: kvp.Value.ReturnArrivalDate,
                flightNumbers: kvp.Value.FlightNumbers,
                returnFlightNumbers: kvp.Value.ReturnFlightNumbers,
                baggageIncluded: kvp.Value.BaggageIncluded,
                price: (int)kvp.Value.Price))
            .ToList();
    }

    public List<PackageFlightVariant> GetVariants(PackageOccupancy occupancy)
    {
        return GetVariants().Where(v => v.Id.Occupancy.Equals(occupancy)).ToList();
    }

    public IEnumerable<string> GetFlightIds()
    {
        return FlightOffers
            .SelectMany(x => x.Flights)
            .Select(x => x.Id)
            .Distinct();
    }

    public IEnumerable<string> GetFlightOfferIds()
    {
        return FlightOffers
            .Select(x => x.Id)
            .Distinct();
    }

    public bool ApplyQuotes(Dictionary<string, FlightQuote> quotesByFlightId)
    {
        var updated = false;
        var flightsToUpdate = new List<Flight>();

        foreach (var flightOffer in FlightOffers)
        {
            foreach (var flight in flightOffer.Flights)
            {
                if (quotesByFlightId.ContainsKey(flight.Id))
                {
                    flightsToUpdate.Add(flight);
                }
            }
        }

        foreach (var flight in flightsToUpdate)
        {
            updated |= ApplyFlightQuote(flight, quotesByFlightId[flight.Id]);
        }

        if (updated)
        {
            UpdatedAt = DateTime.UtcNow;
        }

        return updated;
    }

    private void ApplyQuotes(FlightQuote[] quotes)
    {
        var quotesByFlightId = quotes
            .DistinctBy(x => x.FlightId)
            .ToDictionary(x => x.FlightId);

        ApplyQuotes(quotesByFlightId);
    }

    private bool ApplyFlightQuote(Flight flight, FlightQuote quote)
    {
        var updated = false;

        foreach (var occupancy in Occupancies)
        {
            var currentPrice = flight.Prices.TryGetValue(occupancy, out var cp) ? cp : (decimal?)null;
            var quotePrice = quote.Prices.TryGetValue(occupancy, out var qp) ? qp : (decimal?)null;
            var newPrice = quotePrice != null
                ? _currencyConversionPolicy.Convert((decimal)quotePrice, quote.Currency).RoundAwayFromZero()
                : (decimal?)null;

            if (!IsCompensatedPriceEqual(currentPrice, newPrice) &&
                (flight.UpdatedAt == null || flight.UpdatedAt <= quote.UpdateTime))
            {
                if (newPrice != null)
                {
                    flight.Prices[occupancy] = (decimal)newPrice;
                }
                else
                {
                    flight.Prices.Remove(occupancy);
                }

                updated = true;
            }
        }

        if (updated)
        {
            flight.UpdatedAt = quote.UpdateTime;
        }

        return updated;
    }

    private void RequirePolicies()
    {
        if (_currencyConversionPolicy == null)
        {
            throw new InvalidOperationException("Currency conversion policy is required.");
        }
    }

    private void LimitFlightOffers()
    {
        if (FlightOffers.Length <= MaxFlightOffersPerOccupancy)
        {
            return;
        }

        var selectedFlightOfferIds = new HashSet<string>();
        var selectedFlightOffers = new List<PackageFlightOffer>();

        foreach (var occupancy in Occupancies)
        {
            var offersWithPrices = FlightOffers
                .Select(offer => (
                    FlightOffer: offer,
                    Price: offer.Flights
                        .Where(f => f.Prices.ContainsKey(occupancy))
                        .Sum(f => f.Prices[occupancy])
                ))
                .Where(x => x.Price > 0)
                .GroupBy(x => (x.FlightOffer.InboundDeparture, x.FlightOffer.OutboundDeparture))
                .SelectMany(group => group
                    .OrderBy(offer => offer.Price)
                    .Take(MaxFlightOffersPerOccupancy))
                .ToList();

            foreach (var (flightOffer, _) in offersWithPrices)
            {
                if (selectedFlightOfferIds.Add(flightOffer.Id))
                {
                    selectedFlightOffers.Add(flightOffer);
                }
            }
        }

        FlightOffers = selectedFlightOffers.ToArray();
    }

    private static bool IsCompensatedPriceEqual(decimal? currentPrice, decimal? newPrice)
    {
        if (currentPrice == null && newPrice == null)
        {
            return true;
        }

        if (currentPrice == null || newPrice == null)
        {
            return false;
        }
        
        if (currentPrice == 0m)
        {
            return newPrice == 0m;
        }

        return Math.Abs((decimal)currentPrice - (decimal)newPrice) / currentPrice <= CompensationPercentage;
    }


    private bool CompareFlightOffers(PackageFlightOffer[] flightOffers)
    {
        return FlightOffers.Length == flightOffers.Length &&
               flightOffers.All(flightOffer => FlightOffers.Any(x => CompareFlightOffer(x, flightOffer)));

        static bool CompareFlightOffer(PackageFlightOffer packageFlightOfferA, PackageFlightOffer packageFlightOfferB) =>
            packageFlightOfferA.Id == packageFlightOfferB.Id &&
            packageFlightOfferA.DepartureDate == packageFlightOfferB.DepartureDate &&
            packageFlightOfferA.ReturnArrivalDate == packageFlightOfferB.ReturnArrivalDate &&
            packageFlightOfferA.ReturnDepartureDate == packageFlightOfferB.ReturnDepartureDate &&
            packageFlightOfferA.BaggageIncluded == packageFlightOfferB.BaggageIncluded &&
            packageFlightOfferA.FlightNumbers == packageFlightOfferB.FlightNumbers &&
            packageFlightOfferA.ReturnFlightNumbers == packageFlightOfferB.ReturnFlightNumbers &&
            packageFlightOfferA.Flights.Length == packageFlightOfferB.Flights.Length &&
            CompareFlights(packageFlightOfferA.Flights, packageFlightOfferB.Flights);

        static bool CompareFlights(Flight[] flightsA, Flight[] flightsB) =>
            flightsB.All(flightB => flightsA.Any(flightA => CompareFlight(flightA, flightB)));

        static bool CompareFlight(Flight flightA, Flight flightB) =>
            flightA.Id == flightB.Id &&
            flightA.Prices.DeepEquals(flightB.Prices, (priceA, priceB) => IsCompensatedPriceEqual(priceA, priceB));
    }

    public void AddOccupancy(PackageOccupancy packageOccupancy)
    {
        var occupancies = new HashSet<PackageOccupancy>(Occupancies);
        occupancies.Add(packageOccupancy);

        Occupancies = occupancies.ToArray();
    }

    public PackageFlightOffer? GetCheapestFlightOffer(PackageOccupancy packageOccupancy, TimeOfDay[] inboundDepartures, 
        TimeOfDay[] outboundDepartures)
    {
        var flightOccupancy = packageOccupancy.ToFlightSeatsOccupancy();

        return FlightOffers
            .Where(flightOffer => flightOffer.Flights.All(f => f.Prices.ContainsKey(flightOccupancy)))
            .Where(flightOffer => inboundDepartures.Length == 0 || inboundDepartures.Contains(flightOffer.InboundDeparture))
            .Where(flightOffer => outboundDepartures.Length == 0 || outboundDepartures.Contains(flightOffer.OutboundDeparture))
            .MinBy(flightOffer => flightOffer.Flights.Sum(f => f.Prices[flightOccupancy]));
    }
}