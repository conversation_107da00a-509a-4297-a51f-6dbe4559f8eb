using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageFlights;

public readonly struct PackageFlightId : IParsable<PackageFlightId>, IEquatable<PackageFlightId>
{
    private const char KeySeparator = ':';

    public DateOnly CheckIn { get; private init; }
    public int StayLength { get; private init; }
    public string MarketId { get; private init; }
    public Airport ArrivalAirport { get; private init; }
    public Airport DepartureAirport { get; private init; }

    public PackageFlightId(string id)
    {
        (CheckIn, StayLength, MarketId, ArrivalAirport, DepartureAirport) =
            Parse(id, shouldThrow: true) ?? throw new FormatException("Invalid package flight id");
    }

    public PackageFlightId(DateOnly checkIn, int stayLength, string marketId, Airport arrivalAirport,
        Airport departureAirport)
    {
        CheckIn = checkIn;
        StayLength = stayLength;
        MarketId = marketId;
        ArrivalAirport = arrivalAirport;
        DepartureAirport = departureAirport;
    }

    private static (DateOnly CheckIn, int StayLength, string MarketId, Airport ArrivalAirport, 
        Airport DepartureAirport)? Parse(string id, bool shouldThrow)
    {
        var t = id.AsSpan();

        var separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("CheckIn/StayLength date separator not found");
            return null;
        }

        if (!DateOnly.TryParseExact(t.Slice(0, separatorPosition), "yyMMdd", out var checkIn))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(checkIn)} date");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("StayLength/MarketId date separator not found");
        }

        if (!int.TryParse(t.Slice(0, separatorPosition), out var stayLength))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid stay length");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("MarketId/DepartureAirport date separator not found");
        }

        var marketId = t.Slice(0, separatorPosition).ToString();

        t = t.Slice(separatorPosition + 1);

        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("ArrivalAirport/DepartureAirport date separator not found");
        }

        var arrivalAirport = new Airport(t.Slice(0, separatorPosition).ToString());

        t = t.Slice(separatorPosition + 1);

        var departureAirport = new Airport(t.ToString());

        return (CheckIn: checkIn, StayLength: stayLength, MarketId: marketId, ArrivalAirport: arrivalAirport,
            DepartureAirport: departureAirport);
    }

    public static implicit operator PackageFlightId(string text)
        => new(text);

    public static implicit operator string(PackageFlightId packageFlightId)
        => packageFlightId.ToString();

    public static bool operator ==(PackageFlightId packageFlightId, PackageFlightId packageFlightId2)
        => packageFlightId.Equals(packageFlightId2);

    public static bool operator !=(PackageFlightId packageFlightId, PackageFlightId packageFlightId2)
        => !(packageFlightId == packageFlightId2);

    public override bool Equals(object? obj)
        => obj is PackageFlightId other && Equals(other);

    public override int GetHashCode()
        => ToString().GetHashCode();

    public override string ToString()
        => $"{CheckIn.ToString("yyMMdd")}{KeySeparator}{StayLength}{KeySeparator}{MarketId}{KeySeparator}" +
           $"{ArrivalAirport}{KeySeparator}{DepartureAirport}";

    public static PackageFlightId Parse(string s, IFormatProvider? provider)
        => (PackageFlightId)s;

    public static bool TryParse(string? s, IFormatProvider? provider, out PackageFlightId result)
    {
        if (s != null && Parse(s, false) != null)
        {
            result = new PackageFlightId(s);
            return true;
        }

        result = default;
        return false;
    }

    public bool Equals(PackageFlightId other)
    {
        return CheckIn == other.CheckIn &&
               StayLength == other.StayLength &&
               MarketId == other.MarketId &&
               DepartureAirport == other.DepartureAirport &&
               ArrivalAirport == other.ArrivalAirport;
    }
}