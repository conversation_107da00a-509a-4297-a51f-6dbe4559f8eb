using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageFlights;

public record FlightPriceEntry
{
    public required string Id { get; init; }
    public required string[] FlightIds { get; init; }
    public required decimal Price { get; init; }
    public required DateOnly DepartureDate { get; init; }
    public required DateOnly ReturnDepartureDate { get; init; }
    public required DateOnly ReturnArrivalDate { get; init; }
    public required FlightNumbers FlightNumbers { get; init; }
    public required FlightNumbers ReturnFlightNumbers { get; init; }
    public required bool BaggageIncluded { get; init; }
}