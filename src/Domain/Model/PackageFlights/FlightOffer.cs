using Esky.Packages.Domain.Types;
using Esky.Packages.Domain.Types.Extensions;

namespace Esky.Packages.Domain.Model.PackageFlights;

public class FlightOffer
{
    private static readonly TimeSpan MaxFlightArrivalTimeAfterMidnightToCheckInYesterday = TimeSpan.FromHours(4);
    private static readonly TimeSpan MaxFlightReturnDepartureTimeAfterMidnightToCheckOutYesterday = TimeSpan.FromHours(2);
    
    public required string Id { get; init; }
    public required Airport DepartureAirport { get; init; }
    public required Airport ArrivalAirport { get; init; }
    public required DateTime DepartureDate { get; init; }
    public required DateTime ArrivalDate { get; init; }
    public required DateTime ReturnDepartureDate { get; init; }
    public required DateTime ReturnArrivalDate { get; init; }
    public required int ProviderCode { get; init; }
    public required int Stops { get; init; }
    public required string[] AirlineCodes { get; init; }
    public required string[] FlightIds { get; init; }
    public required FlightNumbers FlightNumbers { get; init; }
    public required FlightNumbers ReturnFlightNumbers { get; init; }
    public required IReadOnlyDictionary<string, FlightPrices> Prices { get; init; }
    public required bool BaggageIncluded  { get; init; }
    public string[]? LegLocators { get; init; }
    
    public DateOnly CheckIn => (ArrivalDate - MaxFlightArrivalTimeAfterMidnightToCheckInYesterday).ToDateOnly();
    public DateOnly CheckOut => (ReturnDepartureDate - MaxFlightReturnDepartureTimeAfterMidnightToCheckOutYesterday).ToDateOnly();
    public int StayLength => CheckOut.DifferenceInStayLength(CheckIn);
}

public class FlightPrices
{
    public required Dictionary<FlightOccupancy, decimal> Prices { get; init; }
    public required string Currency { get; init; }
    public required DateTime UpdatedAt { get; init; }
}

public record FlightOccupancy(int Adults, int Youths, int Children, int Infants);