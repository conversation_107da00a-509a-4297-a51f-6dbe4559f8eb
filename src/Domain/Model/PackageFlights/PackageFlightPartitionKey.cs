using System.Diagnostics.CodeAnalysis;

namespace Esky.Packages.Domain.Model.PackageFlights;

public readonly struct PackageFlightPartitionKey : IParsable<PackageFlightPartitionKey>,
    IEquatable<PackageFlightPartitionKey>
{
    public string[] AirportPair { get; }

    public PackageFlightPartitionKey(string key)
    {
        AirportPair = Parse(key, shouldThrow: true) ?? throw new FormatException("Invalid key format");
    }

    public PackageFlightPartitionKey(params string[] airportPair)
    {
        AirportPair = airportPair;
    }

    private static string[]? Parse(string s, bool shouldThrow)
    {
        var t = s.AsSpan();

        if (s.Length != 6)
        {
            if (shouldThrow)
            {
                throw new FormatException("Invalid key format");
            }

            return null;
        }

        var firstAirport = t[..3].ToString();
        var secondAirport = t.Slice(3, 3).ToString();

        return
        [
            firstAirport,
            secondAirport
        ];
    }
    
    // TODO: Move this logic to FCACHE - this should included in events
    public static PackageFlightPartitionKey FromFlightId(string flightId)
    {
        var t = flightId.AsSpan();

        var firstAirport = t[..3].ToString();
        var secondAirport = t.Slice(3, 3).ToString();

        return new PackageFlightPartitionKey(firstAirport, secondAirport);
    }
    
    public static implicit operator PackageFlightPartitionKey(string text)
        => new(text);

    public static implicit operator string(PackageFlightPartitionKey key)
        => key.ToString();

    public static bool operator ==(PackageFlightPartitionKey key, PackageFlightPartitionKey key2)
        => key.Equals(key2);

    public static bool operator !=(PackageFlightPartitionKey key, PackageFlightPartitionKey key2)
        => !(key == key2);

    public override bool Equals(object? obj)
        => obj is PackageFlightId other && Equals(other);

    public override int GetHashCode()
        => ToString().GetHashCode();

    public override string ToString()
    {
        return AirportPair.Order().Aggregate("", (acc, airport) => acc + airport);
    }

    public static PackageFlightPartitionKey Parse(string s, IFormatProvider? provider)
        => (PackageFlightPartitionKey)s;

    public static bool TryParse([NotNullWhen(true)] string? s, IFormatProvider? provider, 
        out PackageFlightPartitionKey result)
    {
        if (s != null && Parse(s, false) != null)
        {
            result = new PackageFlightPartitionKey(s);
            return true;
        }

        result = default;
        return false;
    }

    public bool Equals(PackageFlightPartitionKey other)
    {
        return AirportPair.SequenceEqual(other.AirportPair);
    }
}