using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlightVariants;
using Esky.Packages.Domain.Model.PackageHotelOfferVariants;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageVariants;

public record PackageVariant
{
    public PackageVariantId Id { get; private init; }
    public DateOnly ReturnArrivalDate { get; private init; }
    public string FlightOfferId { get; private init; } = null!;
    public PackageVariantPriceBreakdown PriceBreakdown { get; private init; } = null!;
    public int Price { get; private init; }

    public static PackageVariant Create(DateOnly checkIn, int stayLength, string marketId, int metaCode,
        Airport departureAirport, Airport arrivalAirport, PackageOccupancy occupancy, MealPlan mealPlan, DateOnly departureDate,
        DateOnly returnDepartureDate, DateOnly returnArrivalDate, Refundability refundability, RoomIds roomIds, FlightNumbers flightNumbers,
        FlightNumbers returnFlightNumbers, bool baggageIncluded, string flightOfferId, int flightVariantPrice, int hotelOfferVariantPrice)
    {
        var id = new PackageVariantId(
            checkIn: checkIn, 
            stayLength: stayLength, 
            marketId: marketId,
            metaCode: metaCode, 
            occupancy: occupancy, 
            departureAirport: departureAirport, 
            mealPlan: mealPlan,
            arrivalAirport: arrivalAirport,
            departureDate:  departureDate,
            returnDepartureDate: returnDepartureDate,
            refundability: refundability,
            roomIds: roomIds,
            flightNumbers: flightNumbers,
            returnFlightNumbers: returnFlightNumbers,
            baggageIncluded: baggageIncluded);

        var price = (long)flightVariantPrice + hotelOfferVariantPrice;

        return new PackageVariant
        {
            Id = id,
            ReturnArrivalDate = returnArrivalDate,
            FlightOfferId = flightOfferId,
            PriceBreakdown = new PackageVariantPriceBreakdown
            {
                FlightVariantPrice = flightVariantPrice,
                HotelOfferVariantPrice = hotelOfferVariantPrice
            },
            Price = price > int.MaxValue ? int.MaxValue : (int)price
        };
    }

    public static List<PackageVariant> CreateMany(ICollection<PackageFlightVariant> packageFlightVariants,
        ICollection<PackageHotelOfferVariant> packageHotelOfferVariants, 
        ICollection<PackageHotelAirports.PackageHotelAirports> packageHotelAirports)
    {
        var packageVariants = new List<PackageVariant>();

        var airportsPerMetaCode = packageHotelOfferVariants
            .Join(packageHotelAirports, h => h.Id.MetaCode, a => a.Id.MetaCode, (h, a) => new { H = h, A = a })
            .GroupBy(x => x.H.Id.MetaCode)
            .ToDictionary(x => x.Key, x => x.SelectMany(g => g.A.Airports).Distinct().ToList());

        var groupedPackageHotelOfferVariants = packageHotelOfferVariants
            .GroupBy(h => h.Id.MetaCode)
            .ToDictionary(g => g.Key, g => g.ToList());

        var groupedPackageFlightVariants = packageFlightVariants
            .GroupBy(f => f.Id.ArrivalAirport)
            .ToDictionary(g => g.Key, g => g.ToList());

        foreach (var (metaCode, arrivalAirports) in airportsPerMetaCode)
        {
            var hotelOfferVariants = groupedPackageHotelOfferVariants
                .GetValueOrDefault(metaCode, new List<PackageHotelOfferVariant>());

            var flightVariants = arrivalAirports
                .SelectMany(a => groupedPackageFlightVariants.GetValueOrDefault(a, new List<PackageFlightVariant>()))
                .ToList();

            var variants = hotelOfferVariants.Join(flightVariants,
                h => new { h.Id.CheckIn, h.Id.StayLength, h.Id.Occupancy, h.Id.MarketId },
                f => new { f.Id.CheckIn, f.Id.StayLength, f.Id.Occupancy, f.Id.MarketId },
                (h, f) => Create(
                    checkIn: f.Id.CheckIn,
                    stayLength: f.Id.StayLength,
                    marketId: f.Id.MarketId,
                    metaCode: metaCode,
                    departureAirport: f.Id.DepartureAirport,
                    arrivalAirport: f.Id.ArrivalAirport,
                    occupancy: f.Id.Occupancy,
                    mealPlan: h.Id.MealPlan,
                    departureDate: f.DepartureDate,
                    returnDepartureDate: f.ReturnDepartureDate,
                    returnArrivalDate: f.ReturnArrivalDate,
                    refundability: h.Refundability,
                    roomIds: h.RoomIds,
                    flightNumbers: f.FlightNumbers,
                    returnFlightNumbers: f.ReturnFlightNumbers,
                    baggageIncluded: f.BaggageIncluded,
                    flightOfferId: f.OfferId,
                    flightVariantPrice: f.Price,
                    hotelOfferVariantPrice: h.Price));

            packageVariants.AddRange(variants);
        }
        
        return packageVariants;
    }

    public static List<PackageVariant> CreateManyPerMetaCode(ICollection<PackageFlightVariant> packageFlightVariants,
        ICollection<PackageHotelOfferVariant> packageHotelOfferVariants, 
        ICollection<PackageHotelAirports.PackageHotelAirports> packageHotelAirports)
    {
        var packageVariants = CreateMany(packageFlightVariants, packageHotelOfferVariants, packageHotelAirports);
        
        return packageVariants
            .GroupBy(v => v.Id.MetaCode)
            .Select(g => g.OrderBy(v => v.Price).First())
            .ToList();
    }
}