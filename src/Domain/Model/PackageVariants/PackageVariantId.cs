using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageVariants;

public readonly struct PackageVariantId : IParsable<PackageVariantId>, IEquatable<PackageVariantId>
{
    private const char KeySeparator = ':';

    private readonly string _id;

    public DateOnly CheckIn { get; }
    public int StayLength { get; }
    public string MarketId { get; }
    public int MetaCode { get; }
    public PackageOccupancy Occupancy { get; }
    public MealPlan MealPlan { get; }
    public Refundability Refundability { get; }
    public RoomIds RoomIds { get; } = RoomIds.Empty;
    public Airport ArrivalAirport { get; }
    public Airport DepartureAirport { get; }
    public DateOnly DepartureDate { get; }
    public DateOnly ReturnDepartureDate { get; }
    public FlightNumbers FlightNumbers { get; }
    public FlightNumbers ReturnFlightNumbers { get; }
    public bool BaggageIncluded { get; }

    public PackageVariantId(string id)
    {
        _id = id;

        (CheckIn, StayLength, MarketId, MetaCode, Occupancy, MealPlan, Refundability,
         RoomIds, ArrivalAirport, DepartureAirport, DepartureDate,
         ReturnDepartureDate, FlightNumbers,
         ReturnFlightNumbers, BaggageIncluded) =
            Parse(id, shouldThrow: true) ?? throw new FormatException("Invalid package variant id");
    }

    public PackageVariantId(DateOnly checkIn, int stayLength, string marketId, int metaCode, PackageOccupancy occupancy,
        MealPlan mealPlan, Refundability refundability, RoomIds roomIds, Airport arrivalAirport, Airport departureAirport,
        DateOnly departureDate, DateOnly returnDepartureDate, FlightNumbers flightNumbers, FlightNumbers returnFlightNumbers, 
        bool baggageIncluded)
    {
        CheckIn = checkIn;
        StayLength = stayLength;
        MarketId = marketId;
        MetaCode = metaCode;
        Occupancy = occupancy;
        MealPlan = mealPlan;
        Refundability = refundability;
        RoomIds = roomIds;
        ArrivalAirport = arrivalAirport;
        DepartureAirport = departureAirport;
        DepartureDate = departureDate;
        ReturnDepartureDate = returnDepartureDate;
        FlightNumbers = flightNumbers;
        ReturnFlightNumbers = returnFlightNumbers;
        BaggageIncluded = baggageIncluded;

        _id = string.Format(
            "{0}{1}{2}{3}{4}{5}{6}{7}{8}{9}{10}{11}{12}{13}{14}{15}{16}{17}{18}{19}{20}{21}{22}{23}{24}{25}{26}{27}{28}",
            checkIn.ToString("yyMMdd"), KeySeparator,
            stayLength, KeySeparator,
            marketId, KeySeparator,
            metaCode, KeySeparator,
            occupancy, KeySeparator,
            mealPlan.ToShortString(), KeySeparator,
            refundability.ToShortString(), KeySeparator,
            roomIds.ToString(), KeySeparator,
            arrivalAirport, KeySeparator,
            departureAirport, KeySeparator,
            departureDate.ToString("yyMMdd"), KeySeparator,
            returnDepartureDate.ToString("yyMMdd"), KeySeparator,
            flightNumbers.ToString(), KeySeparator,
            returnFlightNumbers.ToString(), KeySeparator,
            baggageIncluded);
    }

    private static (DateOnly CheckIn, int StayLength, string MarketId, int MetaCode, PackageOccupancy Occupancy,
        MealPlan MealPlan, Refundability Refundability, RoomIds RoomIds, Airport ArrivalAirport, Airport DepartureAirport,
        DateOnly DepartureDate, DateOnly ReturnDepartureDate, FlightNumbers FlightNumbers, FlightNumbers ReturnFlightNumbers,
        bool BaggageIncluded)? Parse(string id, bool shouldThrow)
    {
        var t = id.AsSpan();

        // Parse CheckIn
        var separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("CheckIn separator not found");
            return null;
        }

        if (!DateOnly.TryParseExact(t.Slice(0, separatorPosition), "yyMMdd", out var checkIn))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(checkIn)} date");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        // Parse StayLength
        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("StayLength separator not found");
            return null;
        }

        if (!int.TryParse(t.Slice(0, separatorPosition), out var stayLength))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(stayLength)}");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        // Parse MarketId
        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("MarketId separator not found");
            return null;
        }

        var marketId = t.Slice(0, separatorPosition).ToString();

        t = t.Slice(separatorPosition + 1);

        // Parse MetaCode
        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("MetaCode separator not found");
            return null;
        }

        if (!int.TryParse(t.Slice(0, separatorPosition), out var metaCode))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(metaCode)}");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        // Parse Occupancy
        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("Occupancy separator not found");
            return null;
        }

        if (!PackageOccupancy.TryParse(t.Slice(0, separatorPosition).ToString(), null, out var occupancy))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(occupancy)}");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        // Parse MealPlan
        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("MealPlan separator not found");
            return null;
        }

        var mealPlan = MealPlan.FromShortString(t.Slice(0, separatorPosition).ToString());

        t = t.Slice(separatorPosition + 1);

        // Parse Refundability
        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("Refundability separator not found");
            return null;
        }

        var refundability = Refundability.FromShortString(t.Slice(0, separatorPosition).ToString());

        t = t.Slice(separatorPosition + 1);

        // Parse RoomIds
        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("RoomIds separator not found");
            return null;
        }

        var roomIdsStr = t.Slice(0, separatorPosition).ToString();
        var roomIdsArray = string.IsNullOrEmpty(roomIdsStr) ? [] : roomIdsStr.Split(',');
        var roomIds = new RoomIds(roomIdsArray);

        t = t.Slice(separatorPosition + 1);

        // Parse ArrivalAirport
        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("ArrivalAirport separator not found");
            return null;
        }

        if (!Airport.TryParse(t.Slice(0, separatorPosition).ToString(), null, out var arrivalAirport))
        {
            if (shouldThrow)
                throw new FormatException("Invalid arrival airport");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        // Parse DepartureAirport
        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("DepartureAirport separator not found");
            return null;
        }

        if (!Airport.TryParse(t.Slice(0, separatorPosition).ToString(), null, out var departureAirport))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(departureAirport)}");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        // Parse DepartureDate
        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("DepartureDate separator not found");
            return null;
        }

        if (!DateOnly.TryParseExact(t.Slice(0, separatorPosition), "yyMMdd", out var departureDate))
        {
            if (shouldThrow)
                throw new FormatException("Invalid departure date");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        // Parse ReturnDepartureDate
        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("ReturnDepartureDate separator not found");
            return null;
        }

        if (!DateOnly.TryParseExact(t.Slice(0, separatorPosition), "yyMMdd", out var returnDepartureDate))
        {
            if (shouldThrow)
                throw new FormatException("Invalid return departure date");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        // Parse FlightNumbers
        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("FlightNumbers separator not found");
            return null;
        }

        var flightNumbersStr = t.Slice(0, separatorPosition).ToString();
        var flightNumbersArray = string.IsNullOrEmpty(flightNumbersStr) ? [] : flightNumbersStr.Split(',');
        var flightNumbers = new FlightNumbers(flightNumbersArray);

        t = t.Slice(separatorPosition + 1);

        // Parse ReturnFlightNumbers
        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("ReturnFlightNumbers separator not found");
            return null;
        }

        var returnFlightNumbersStr = t.Slice(0, separatorPosition).ToString();
        var returnFlightNumbersArray = string.IsNullOrEmpty(returnFlightNumbersStr) ? [] : returnFlightNumbersStr.Split(',');
        var returnFlightNumbers = new FlightNumbers(returnFlightNumbersArray);

        t = t.Slice(separatorPosition + 1);

        // Parse BaggageIncluded (last property)
        if (!bool.TryParse(t, out var baggageIncluded))
        {
            if (shouldThrow)
                throw new FormatException("Invalid baggage included");
            return null;
        }

        return (checkIn, stayLength, marketId, metaCode, occupancy, mealPlan, refundability,
                roomIds, arrivalAirport, departureAirport, departureDate,
                returnDepartureDate, flightNumbers, returnFlightNumbers, baggageIncluded);
    }

    public static implicit operator PackageVariantId(string text)
        => new(text);

    public static implicit operator string(PackageVariantId packageVariantId)
        => packageVariantId._id;

    public static bool operator ==(PackageVariantId packageVariantId, PackageVariantId packageVariantId2)
        => packageVariantId._id == packageVariantId2._id;

    public static bool operator !=(PackageVariantId packageVariantId, PackageVariantId packageVariantId2)
        => !(packageVariantId == packageVariantId2);

    public override bool Equals(object? obj)
        => obj is PackageVariantId other && Equals(other);

    public override int GetHashCode()
        => _id.GetHashCode();

    public override string ToString()
        => _id;

    public static PackageVariantId Parse(string s, IFormatProvider? provider)
        => (PackageVariantId)s;

    public static bool TryParse(string? s, IFormatProvider? provider, out PackageVariantId result)
    {
        if (s != null && Parse(s, false) != null)
        {
            result = new PackageVariantId(s);
            return true;
        }

        result = default;
        return false;
    }

    public bool Equals(PackageVariantId other)
    {
        return _id == other._id &&
               CheckIn == other.CheckIn &&
               StayLength == other.StayLength &&
               MarketId == other.MarketId &&
               MetaCode == other.MetaCode &&
               Occupancy == other.Occupancy &&
               DepartureAirport == other.DepartureAirport &&
               MealPlan == other.MealPlan &&
               Refundability == other.Refundability &&
               RoomIds == other.RoomIds &&
               ArrivalAirport == other.ArrivalAirport &&
               DepartureDate == other.DepartureDate &&
               ReturnDepartureDate == other.ReturnDepartureDate &&
               FlightNumbers == other.FlightNumbers &&
               ReturnFlightNumbers == other.ReturnFlightNumbers &&
               BaggageIncluded == other.BaggageIncluded;
    }
}