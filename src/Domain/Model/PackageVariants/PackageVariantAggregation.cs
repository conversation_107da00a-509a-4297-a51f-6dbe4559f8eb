using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageVariants;

public record PackageVariantAggregation(
    List<MealPlan> MealPlans, 
    List<Airport> DepartureAirports, 
    List<PackageOccupancy> Occupancies)
{
    public static PackageVariantAggregation Empty => new(
        MealPlans: new List<MealPlan>(),
        DepartureAirports: new List<Airport>(),
        Occupancies: new List<PackageOccupancy>());
    
    public bool IsEmpty() => 
        MealPlans.Count == 0 || 
        DepartureAirports.Count == 0 || 
        Occupancies.Count == 0;
}