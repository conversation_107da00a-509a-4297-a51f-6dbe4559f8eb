using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageHotelOfferVariants;

public readonly struct PackageHotelOfferVariantId(DateOnly checkIn, int stayLength, string marketId, int metaCode,
    PackageOccupancy occupancy, MealPlan mealPlan) : IEquatable<PackageHotelOfferVariantId>
{
    public DateOnly CheckIn { get; private init; } = checkIn;
    public int StayLength { get; private init; } = stayLength;
    public string MarketId { get; private init;} = marketId;
    public int MetaCode { get; private init; } = metaCode;
    public PackageOccupancy Occupancy { get; private init; } = occupancy;
    public MealPlan MealPlan { get; private init; } = mealPlan;

    public bool Equals(PackageHotelOfferVariantId other)
    {
        return CheckIn == other.CheckIn &&
               StayLength == other.StayLength &&
               MarketId == other.MarketId &&
               MetaCode == other.MetaCode &&
               Occupancy == other.Occupancy &&
               MealPlan == other.MealPlan;
    }
}