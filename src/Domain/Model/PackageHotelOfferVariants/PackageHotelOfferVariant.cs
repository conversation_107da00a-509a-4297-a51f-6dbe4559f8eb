using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.Common.Extensions;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageHotelOfferVariants;

public class PackageHotelOfferVariant
{
    public PackageHotelOfferVariantId Id { get; private init; }
    public Refundability Refundability { get; private init; }
    public RoomIds RoomIds { get; private init; } = RoomIds.Empty;
    public int Price { get; private init; }

    public static PackageHotelOfferVariant Create(DateOnly checkIn, int stayLength, string marketId, int metaCode,
        PackageOccupancy occupancy, MealPlan mealPlan, Refundability refundability, RoomIds roomIds, int price)
    {
        var id = new PackageHotelOfferVariantId(checkIn, stayLength, marketId, metaCode, occupancy, mealPlan);
        return new PackageHotelOfferVariant
        {
            Id = id,
            Refundability = refundability,
            RoomIds = roomIds,
            Price = price
        };
    }

    public static PackageHotelOfferVariant[] CreateMany(PackageHotelOffer packageHotelOffer, 
        PackageOccupancy[] occupancies, MealPlan[]? mealPlans = null)
    {
        var prices = packageHotelOffer.GetPrices(occupancies);
        var occupancy = occupancies.Merge();

        return prices
            .Select(p => Create(
                checkIn: packageHotelOffer.Id.CheckIn, 
                stayLength: packageHotelOffer.Id.StayLength,
                marketId: packageHotelOffer.Id.MarketId, 
                metaCode: packageHotelOffer.Id.MetaCode, 
                occupancy: occupancy, 
                mealPlan: p.Key, 
                refundability: new Refundability(packageHotelOffer.OnlyRefundable),
                roomIds: p.Value.RoomIds,
                price: p.Value.CompensatedPrice))
            .Where(v => mealPlans is not { Length: > 0 } || mealPlans.Contains(v.Id.MealPlan))
            .ToArray();
    }
}