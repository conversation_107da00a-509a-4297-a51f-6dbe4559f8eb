using System.Diagnostics.CodeAnalysis;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageHotelOfferVariantPrices;

public class PackageHotelOfferVariantPrice
{
    public PackageHotelOfferVariantPriceId Id { get; private init; }

    public Dictionary<PackageHotelOfferVariantPriceKey, int> Prices { get; private init; } = [];

    public static PackageHotelOfferVariantPrice Create(
        PackageHotelOfferVariantPriceId id,
        Dictionary<PackageHotelOfferVariantPriceKey, int> prices)
    {
        return new PackageHotelOfferVariantPrice
        {
            Id = id,
            Prices = prices
        };
    }

    public bool ApplyPrices(Dictionary<PackageHotelOfferVariantPriceKey, int> newPrices)
    {
        var changed = false;

        foreach (var (key, newValue) in newPrices)
        {
            if (Prices.TryGetValue(key, out var current))
            {
                var minPrice = Math.Min(current, newValue);
                if (current == minPrice) continue;

                Prices[key] = minPrice;
            }
            else
            {
                Prices[key] = newValue;
            }

            changed = true;
        }

        return changed;
    }
}

public readonly record struct PackageHotelOfferVariantPriceKey : IParsable<PackageHotelOfferVariantPriceKey>
{
    public Refundability Refundability { get; init; }
    public RoomIds RoomIds { get; init; }

    public static PackageHotelOfferVariantPriceKey Parse(string s, IFormatProvider? provider)
    {
        if (string.IsNullOrEmpty(s)) throw new FormatException("Invalid format for PackageHotelOfferVariantPriceKey");

        var parts = s.Split('|');
        if (parts.Length != 2) throw new FormatException("Invalid format for PackageHotelOfferVariantPriceKey");

        // TODO: Parse only from short string
        if (!Refundability.TryParse(parts[0], provider: null, out var refundability))
        {
            refundability = Refundability.FromShortString(parts[0]);
        }
        var roomIds = RoomIds.Parse(parts[1], provider);

        return new PackageHotelOfferVariantPriceKey { Refundability = refundability, RoomIds = roomIds };
    }

    public static bool TryParse([NotNullWhen(true)] string? s, IFormatProvider? provider, out PackageHotelOfferVariantPriceKey result)
    {
        try
        {
            result = Parse(s ?? throw new ArgumentNullException(nameof(s)), provider);
            return true;
        }
        catch (Exception)
        {
            result = default;
            return false;
        }
    }

    public override string ToString()
    {
        return $"{Refundability.ToShortString()}|{RoomIds}";
    }
}
