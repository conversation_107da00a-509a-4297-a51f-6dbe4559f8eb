using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageVariants;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Model.PackageHotelOfferVariantPrices;

public readonly record struct PackageHotelOfferVariantPriceId
{
    public DateOnly CheckIn { get; private init; }
    public int StayLength { get; private init; }
    public string MarketId { get; private init;}
    public int MetaCode { get; private init; }
    public MealPlan MealPlan { get; private init; }
    public PackageOccupancy Occupancy { get; private init; }
    public DateOnly PriceDate { get; private init; }

    public static PackageHotelOfferVariantPriceId Create(
        DateOnly checkIn,
        int stayLength,
        string marketId,
        int metaCode,
        MealPlan mealPlan,
        PackageOccupancy occupancy,
        DateOnly priceDate)
    {
        return new PackageHotelOfferVariantPriceId
        {
            CheckIn = checkIn,
            StayLength = stayLength,
            MarketId = marketId,
            MetaCode = metaCode,
            MealPlan = mealPlan,
            Occupancy = occupancy,
            PriceDate = priceDate
        };
    }

    public bool IsMatching(PackageVariantId packageVariantId)
    {
        return CheckIn == packageVariantId.CheckIn &&
               StayLength == packageVariantId.StayLength &&
               MarketId == packageVariantId.MarketId &&
               MetaCode == packageVariantId.MetaCode &&
               MealPlan.Equals(packageVariantId.MealPlan) &&
               Occupancy.Equals(packageVariantId.Occupancy);
    }
}