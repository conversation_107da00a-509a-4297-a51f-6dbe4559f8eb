namespace Esky.Packages.Domain.Model.Packages;

public readonly struct PackageId : IParsable<PackageId>, IEquatable<PackageId>
{
    private const char KeySeparator = ':';
    
    public DateOnly CheckIn { get; private init; }
    public int StayLength { get; private init; }
    public string MarketId { get; private init; }
    public int MetaCode { get; private init; }

    public PackageId(string id)
    {
        (CheckIn, StayLength, MarketId, MetaCode) =
            Parse(id, shouldThrow: true) ?? throw new FormatException("Invalid package id");
    }
    
    public PackageId(DateOnly checkIn, int stayLength, string marketId, int metaCode)
    {
        CheckIn = checkIn;
        StayLength = stayLength;
        MarketId = marketId;
        MetaCode = metaCode;
    }
    
    private static (DateOnly CheckIn, int StayLength, string MarketId, int MetaCode)? Parse(string id, 
        bool shouldThrow)
    {
        var t = id.AsSpan();

        var separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("CheckIn/StayLength date separator not found");
            return null;
        }

        if (!DateOnly.TryParseExact(t.Slice(0, separatorPosition), "yyMMdd", out var checkIn))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(checkIn)} date");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow) 
                throw new FormatException("StayLength/MarketId date separator not found");
        }

        if (!int.TryParse(t.Slice(0, separatorPosition), out var stayLength))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid stay length");
            return null;
        }

        t = t.Slice(separatorPosition + 1);
       
        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("MarketId/MetaCode separator not found");
            return null;
        }
        
        var marketId = t.Slice(0, separatorPosition).ToString();
        
        if (!int.TryParse(t.Slice(separatorPosition + 1), out var metaCode))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(metaCode)}");
            return null;
        }
        
        return (checkIn, stayLength, marketId, metaCode);
    }

    public static bool IsOldPackageId(string packageId)
    {
        return !string.IsNullOrWhiteSpace(packageId) && packageId.Contains('-');
    }

    public static PackageId ParseFromOldPackageId(string packageId)
    {
        if (string.IsNullOrWhiteSpace(packageId))
        {
            throw new ArgumentException("PackageId cannot be null or empty", nameof(packageId));
        }

        var parts = packageId.Split(KeySeparator);
        var checkIn = DateOnly.ParseExact(parts[0], "ddMMyy");
        var stayLength = int.Parse(parts[1]);
        var metaCode = int.Parse(parts[2]);
        var marketId = parts[3].Split('-')[0];

        return new PackageId(checkIn: checkIn, stayLength: stayLength, marketId: marketId, metaCode: metaCode);
    }

    public static implicit operator PackageId(string text) 
        => new(text);

    public static implicit operator string(PackageId packageId)
        => packageId.ToString();

    public static bool operator ==(PackageId packageId, PackageId packageId2)
        => packageId.ToString() == packageId2.ToString();

    public static bool operator !=(PackageId packageId, PackageId packageId2)
        => !(packageId == packageId2);

    public override bool Equals(object? obj)
        => obj is PackageId other && Equals(other);

    public override int GetHashCode()
        => ToString().GetHashCode();
    
    public override string ToString()
        => string.Format(
            "{0}{1}{2}{3}{4}{5}{6}",
            CheckIn.ToString("yyMMdd"), KeySeparator,
            StayLength, KeySeparator,
            MarketId, KeySeparator,
            MetaCode);

    public static PackageId Parse(string s, IFormatProvider? provider)
        => (PackageId)s;

    public static bool TryParse(string? s, IFormatProvider? provider, out PackageId result)
    {
        if (s != null && Parse(s, false) != null)
        {
            result = new PackageId(s);
            return true;
        }

        result = default;
        return false;
    }

    public bool Equals(PackageId other)
    {
        return CheckIn.Equals(other.CheckIn) && StayLength == other.StayLength &&
               MetaCode == other.MetaCode && MarketId == other.MarketId;
    }
}