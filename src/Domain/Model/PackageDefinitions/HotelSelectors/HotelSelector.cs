using System.Text.Json.Serialization;
using Esky.Packages.Domain.Model.PackageDefinitions.HotelSelectors.Combine;
using Esky.Packages.Domain.Model.PackageDefinitions.HotelSelectors.Groups;
using Esky.Packages.Domain.Model.PackageDefinitions.HotelSelectors.Locations;

namespace Esky.Packages.Domain.Model.PackageDefinitions.HotelSelectors;

[JsonDerivedType(typeof(HotelCitySelector), "city")]
[JsonDerivedType(typeof(HotelRegionSelector), "region")]
[JsonDerivedType(typeof(HotelCountrySelector), "country")]
[JsonDerivedType(typeof(HotelContinentSelector), "continent")]
[JsonDerivedType(typeof(HotelMetaCodeSelector), "meta")]
[JsonDerivedType(typeof(HotelTagSelector), "tag")]

[JsonDerivedType(typeof(HotelNullSelector), "null")]
[JsonDerivedType(typeof(HotelAndSelector), "and")]
[JsonDerivedType(typeof(HotelOrSelector), "or")]
[JsonDerivedType(typeof(HotelNotSelector), "not")]
[JsonPolymorphic(TypeDiscriminatorPropertyName = "_t", UnknownDerivedTypeHandling = JsonUnknownDerivedTypeHandling.FailSerialization)]
public abstract record HotelSelector 
{
    public T AsDerivedOrThrow<T>() where T : HotelSelector
    {
        if (this is not T casted)
        {
            throw new InvalidCastException($"Cannot cast {GetType().Name} to {typeof(T).Name}");
        }

        return casted;
    }
}