namespace Esky.Packages.Domain.Model.PackageDefinitions.TimeCriterias.DurationStrategies;

public class CheckInDurationStrategy : DurationStrategy
{
    public required DateOnly MinCheckIn { get; set; }
    public required DateOnly MaxCheckIn { get; set; }

    public (<PERSON>O<PERSON><PERSON>, DateOnly <PERSON>) GetCheckInRange()
    {
        return (MinCheckIn, MaxCheckIn);
    }

    public int GetTotalPartitionsByDate()
    {
        return OneDayPartitioner.GetTotalPartitions(MinCheckIn, MaxCheckIn);
    }
}