namespace Esky.Packages.Domain.Model.PackageDefinitions;

public class PackageDefinition
{
    public required string Id { get; set; } = null!;
    public required PackageDefinitionParameters Parameters { get; set; } = new();
    public required PackageDefinitionMetadata Metadata { get; set; } = new();

    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public int Version { get; set; }
}