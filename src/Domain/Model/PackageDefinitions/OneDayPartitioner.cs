namespace Esky.Packages.Domain.Model.PackageDefinitions;

public class OneDayPartitioner
{
    private static readonly DateTime DeterministicBoundary = DateTime.Parse("2024-01-01");
    private const int DaysPerPartition = 1;

    public static (DateOnly Start, DateOnly End)? GetPartitionDates(DateOnly minCheckIn, DateOnly maxCheckIn, int partition)
    {
        ArgumentOutOfRangeException.ThrowIfLessThan(partition, 0);

        var shift = Math.Abs((int)(DeterministicBoundary - minCheckIn.ToDateTime(TimeOnly.MinValue)).TotalDays) % DaysPerPartition;

        var partitionStart = minCheckIn.AddDays(partition * DaysPerPartition - shift);
        var partitionEnd = minCheckIn.AddDays(partition * DaysPerPartition + DaysPerPartition - 1 - shift);

        if (partitionStart > maxCheckIn)
        {
            return null;
        }

        if (partitionStart < minCheckIn)
        {
            partitionStart = minCheckIn;
        }

        if (partitionEnd > maxCheckIn)
        {
            partitionEnd = maxCheckIn;
        }

        return (partitionStart, partitionEnd);
    }

    public static int GetTotalPartitions(DateOnly minCheckIn, DateOnly maxCheckIn)
    {
        var totalDays = maxCheckIn.DayNumber - minCheckIn.DayNumber;

        return (int)Math.Ceiling(totalDays / (double)DaysPerPartition);
    }
}