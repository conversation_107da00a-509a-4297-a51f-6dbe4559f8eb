using Esky.Packages.Domain.Model.LiveVariants;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Factories;

public interface IFlightVariantFactory
{
    FlightLiveVariant Create(
        string key,
        Airport departureAirport,
        Airport arrivalAirport,
        DateTime departureDate,
        DateTime arrivalDate,
        DateTime returnDepartureDate,
        DateTime returnArrivalDate, 
        FlightNumbers flightNumbers, 
        FlightNumbers returnFlightNumbers,
        int stops,
        int providerCode,
        string[] airlineCodes,
        string[] flightIds,
        string[] legLocators,
        Currency currency,
        Money[] prices, 
        bool registeredBaggageIncluded);
}
