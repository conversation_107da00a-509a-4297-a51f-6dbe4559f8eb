using Esky.Packages.Domain.Model.LiveVariants;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Factories;

public interface IHotelOfferVariantFactory
{
    HotelOfferLiveVariant Create(
        DateOnly checkIn,
        int stayLength,
        string offerId,
        MealPlan mealPlan,
        Refundability refundability,
        DateOnly? freeRefundUntil,
        RoomIds roomIds,
        Currency currency,
        Money price,
        Money priceAtHotel,
        int availability);
}
