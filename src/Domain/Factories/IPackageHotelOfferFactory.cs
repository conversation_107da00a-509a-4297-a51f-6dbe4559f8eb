using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Factories;

public interface IPackageHotelOfferFactory
{
    IEnumerable<PackageHotelOffer> CreateMany(IEnumerable<HotelOffer> hotelOffers, string marketId, string definitionId, 
        Currency currency, PackageOccupancy[] occupancies, ProviderConfigurationId[] providerConfigurationIds, 
        Dictionary<int, Dictionary<Airport, Airport[]>> metaCodeByArrivalAirportByDepartureAirport);

    void ApplyPolicies(PackageHotelOffer packageFlight);
}
