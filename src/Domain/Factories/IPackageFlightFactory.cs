using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Factories;

public interface IPackageFlightFactory
{
    IEnumerable<PackageFlight> CreateMany(IEnumerable<FlightOffer> flightOffers, string marketId, string definitionId,
        Currency currency, PackageOccupancy[] occupancies);
    
    void ApplyPolicies(PackageFlight packageFlight);
}