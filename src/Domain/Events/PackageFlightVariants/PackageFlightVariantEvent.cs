using System.Text.Json.Serialization;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Events.PackageFlightVariants;

[JsonDerivedType(typeof(PackageFlightVariantUpdatedEvent), "packageFlightVariantUpdated")]
[JsonDerivedType(typeof(PackageFlightVariantDeletedEvent), "packageFlightVariantDeleted")]
public class PackageFlightVariantEvent
{
    public required string Id { get; init; }
    public required DateOnly CheckIn { get; init; }
    public required int StayLength { get; init; }
    public required string MarketId { get; init; }
    public required Airport ArrivalAirport { get; init; }
    public required Airport DepartureAirport { get; init; }
    public required PackageOccupancy Occupancy { get; init; }
    public required TimeOfDay InboundDeparture { get; init; }
    public required TimeOfDay OutboundDeparture { get; init; }
}