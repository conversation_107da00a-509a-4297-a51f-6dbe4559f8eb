using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Events.PackageFlightVariants;

public class PackageFlightVariantUpdatedEvent : PackageFlightVariantEvent
{
    public string OfferId { get; init; } = null!;
    public DateOnly DepartureDate { get; init; }
    public DateOnly ReturnDepartureDate { get; set; }
    public DateOnly ReturnArrivalDate { get; set; }
    public int Price { get; init; }
    public FlightNumbers FlightNumbers { get; set; }
    public FlightNumbers ReturnFlightNumbers { get; set; }
    public bool BaggageIncluded { get; set; }
}