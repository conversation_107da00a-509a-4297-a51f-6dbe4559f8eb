using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Events.PackageFlightVariants;

public record PackageFlightVariantPriceEvent(
    DateOnly DepartureDate, 
    DateOnly ReturnDepartureDate, 
    string MarketId, 
    Airport ArrivalAirport, 
    Airport DepartureAirport, 
    FlightNumbers FlightNumbers, 
    FlightNumbers ReturnFlightNumbers, 
    PackageOccupancy Occupancy, 
    DateOnly PriceDate, 
    int Price, 
    bool BaggageIncluded);
