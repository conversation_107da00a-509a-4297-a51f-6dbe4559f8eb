using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageHotelOffers;

namespace Esky.Packages.Domain.Repositories;

// TODO: Remove when we have a proper mapping

public interface IPackageHotelOfferRepository
{
    Task<PackageHotelOffer?> GetById(PackageHotelOfferId id, CancellationToken cancellationToken = default);
    IAsyncEnumerable<PackageHotelOffer> EnumerateByIds(List<PackageHotelOfferId> ids, int batchSize = 1000, 
        int maxDegreeOfParallelism = 2, CancellationToken cancellationToken = default);
    Task<List<PackageHotelOffer>> ListByIds(IEnumerable<PackageHotelOfferId> ids, CancellationToken cancellationToken = default);
    Task<List<PackageHotelOffer>> ListByStayKeys(List<PackageHotelOfferStayKey> stayKeys, CancellationToken cancellationToken = default);
    Task<List<PackageHotelOffer>> ListByMarketAndMetaCode(string marketId, int metaCode, CancellationToken cancellationToken = default);
    Task<List<PackageHotelOffer>> ListByMarketAndMetaCodeAndCheckInRange(string marketId, int metaCode, DateOnly checkInFrom, DateOnly checkInTo, CancellationToken cancellationToken = default);
    Task UpsertWithoutConcurrency(List<PackageHotelOffer> packageHotelOffers, CancellationToken cancellationToken = default);
    Task Update(List<PackageHotelOffer> packageHotelOffers, CancellationToken cancellationToken = default);
    IAsyncEnumerable<PackageHotelOfferStayKey> EnumerateStayKeys(int chunks = 32, int maxDegreeOfParallelism = 4, 
        CancellationToken cancellationToken = default);
    Task<int> RemoveExceptSpecificTimeRanges(string definitionId, DateOnly minCheckIn, DateOnly maxCheckIn, List<(DateOnly CheckIn, int StayLength)> timeRanges, CancellationToken cancellationToken = default);
    Task<int> RemoveFromTimeRangesExceptSpecificIds(string definitionId, List<(DateOnly CheckIn, int StayLength)> timeRanges, IEnumerable<PackageHotelOfferId> ids, CancellationToken cancellationToken = default);
}