using Esky.Packages.Domain.Model.PackageAvailabilities;

namespace Esky.Packages.Domain.Repositories;

public interface IPackageAvailabilitiesRepository
{
    Task<List<PackageAvailabilities>> GetByDefinitionId(string definitionId, CancellationToken cancellationToken = default);
    Task UpsertWithoutConcurrency(PackageAvailabilities packageAvailabilities, CancellationToken cancellationToken = default);
    Task<int> RemoveByPartitionsGreaterThan(string definitionId, int partition, CancellationToken cancellationToken = default);
}