using Esky.Packages.Domain.Model.PackageDefinitions;

namespace Esky.Packages.Domain.Repositories;

public interface IPackageDefinitionRepository
{
    Task Add(PackageDefinition packageDefinition, CancellationToken cancellationToken = default);
    Task<PackageDefinition> Update(PackageDefinition packageDefinition, CancellationToken cancellationToken = default);
    Task<PackageDefinition?> GetById(string id, CancellationToken cancellationToken = default);
    Task<List<PackageDefinition>> ListByIdsOrTags(string[] ids, string[] tags, CancellationToken cancellationToken = default);
    Task<bool> Remove(string id, CancellationToken cancellationToken = default);
    Task<IReadOnlyCollection<PackageDefinition>> GetAll(CancellationToken cancellationToken = default);
}