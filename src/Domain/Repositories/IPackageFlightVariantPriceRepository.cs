using Esky.Packages.Domain.Model.PackageFlightVariantPrices;
using Esky.Packages.Domain.Model.PackageVariants;

namespace Esky.Packages.Domain.Repositories;

public interface IPackageFlightVariantPriceRepository
{
    Task<IReadOnlyCollection<PackageFlightVariantPrice>> GetByIds(IReadOnlyCollection<PackageFlightVariantPriceId> ids, CancellationToken cancellationToken);
    Task<IReadOnlyCollection<PackageFlightVariantPrice>> GetByMultiplePackageVariantsAndDateRange(
        IReadOnlyCollection<(PackageVariantId Id, DateOnly FromPriceDate, DateOnly ToPriceDate)> packageVariantDateRanges,
        CancellationToken cancellationToken);
    Task Upsert(IReadOnlyCollection<PackageFlightVariantPrice> prices, CancellationToken cancellationToken);
}
