using Esky.Packages.Domain.Model.PackageFlights;

namespace Esky.Packages.Domain.Repositories;

public interface IPackageFlightRepository
{
    Task<PackageFlight?> GetById(PackageFlightId id, CancellationToken cancellationToken = default);
    Task<List<PackageFlight>> ListByIds(ICollection<PackageFlightId> packageFlightsIds,
        CancellationToken cancellationToken = default);
    IAsyncEnumerable<PackageFlight> EnumerateByIds(List<PackageFlightId> ids, int batchSize = 200, 
        int maxDegreeOfParallelism = 2, CancellationToken cancellationToken = default);
    Task<List<PackageFlight>> ListByFlightIdAndPartitionKeyPairs(List<(string FlightId, PackageFlightPartitionKey PartitionKey)> flightIdAndPartitionKeyPairs, CancellationToken cancellationToken = default);
    Task UpsertWithoutConcurrency(List<PackageFlight> packageFlights, CancellationToken cancellationToken = default);
    Task Update(List<PackageFlight> packageFlights, CancellationToken cancellationToken = default);
    IAsyncEnumerable<string> EnumerateFlightIds(int chunks = 32, int maxDegreeOfParallelism = 4, CancellationToken cancellationToken = default);
    Task<int> RemoveExceptSpecificTimeRanges(string definitionId, DateOnly minCheckIn, DateOnly maxCheckIn, List<(DateOnly CheckIn, int StayLength)> timeRanges, CancellationToken cancellationToken = default);
    Task<int> RemoveFromTimeRangesExceptSpecificIds(string definitionId, List<(DateOnly CheckIn, int[] StayLengths)> timeRanges, IEnumerable<PackageFlightId> ids, CancellationToken cancellationToken = default);
}