using Esky.Packages.Domain.Model.PackageHotelOfferVariantPrices;
using Esky.Packages.Domain.Model.PackageVariants;

namespace Esky.Packages.Domain.Repositories;

public interface IPackageHotelOfferVariantPriceRepository
{
    Task<IReadOnlyCollection<PackageHotelOfferVariantPrice>> GetByIds(IReadOnlyCollection<PackageHotelOfferVariantPriceId> ids, CancellationToken cancellationToken);
    Task<IReadOnlyCollection<PackageHotelOfferVariantPrice>> GetByMultiplePackageVariantsAndDateRange(
        IReadOnlyCollection<(PackageVariantId Id, DateOnly FromPriceDate, DateOnly ToPriceDate)> packageVariantDateRanges,
        CancellationToken cancellationToken);
    Task Upsert(IReadOnlyCollection<PackageHotelOfferVariantPrice> prices, CancellationToken cancellationToken);
}
