using Esky.Packages.Domain.Events.PackageFlightVariants;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlightVariants;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Repositories;

public interface IPackageFlightVariantRepository
{
    Task UpdateVariants(List<PackageFlightVariantEvent> events, CancellationToken cancellationToken);
    
    Task<PackageFlightVariant[]> GetCheapestsPerAirports(string marketId, int[] stayLengths,
        Airport[] departureAirports, Airport[] arrivalAirports, DateOnly departureDateFrom,
        DateOnly departureDateTo, PackageOccupancy occupancy, TimeOfDay[] inboundDepartures,
        TimeOfDay[] outboundDepartures, CancellationToken cancellationToken);

    Task<IEnumerable<DateOnly>> GetAvailableDepartureDates(string marketId, int stayLength, Airport[] departureAirports, 
        Airport[] arrivalAirports, PackageOccupancy occupancy, CancellationToken cancellationToken);

    Task<PackageFlightVariant[]> GetCheapestsPerDates(string marketId, DateOnly departureDateFrom, 
        DateOnly departureDateTo, int[] stayLengths, Airport[] departureAirports, Airport[] arrivalAirports, PackageOccupancy occupancy, 
        TimeOfDay[] inboundDepartures, TimeOfDay[] outboundDepartures, CancellationToken cancellationToken);
}