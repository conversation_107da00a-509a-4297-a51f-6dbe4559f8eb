using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageHotelAirports;

namespace Esky.Packages.Domain.Repositories;

public interface IPackageHotelAirportsRepository
{
    Task<List<PackageHotelAirports>> ListByMarketIdAndMetaCodes(string marketId, IEnumerable<int> metaCodes,
        CancellationToken cancellationToken = default);
    
    Task<PackageHotelAirports> GetByMarketIdAndMetaCode(string marketId, int metaCode, 
        CancellationToken cancellationToken = default);

    Task<int> RemoveExceptMetaCodes(string marketId, int[] metaCodes, string definitionId,
        CancellationToken cancellationToken = default);

    Task Upsert(List<PackageHotelAirports> packageHotelAirports, CancellationToken cancellationToken = default);
}