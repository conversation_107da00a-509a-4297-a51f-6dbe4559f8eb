using Esky.Packages.Domain.Model.Markets;

namespace Esky.Packages.Domain.Repositories;

public interface IMarketRepository
{
    Task Add(Market market, CancellationToken cancellationToken = default);
    Task Upsert(Market market, CancellationToken cancellationToken = default);
    Task<Market?> GetById(string id, CancellationToken cancellationToken = default);
    Task<bool> Remove(string id, CancellationToken cancellationToken = default);
    Task<IReadOnlyCollection<Market>> GetAll(CancellationToken cancellationToken = default);
}