using Confluent.Kafka;

namespace Esky.Packages.Repartitioner.Partitioners;

public class FlightQuotePartitioner
{
    private TopicPartition[] _partitions = default!;
    
    public void Initialize(string topic, string bootstrapServers)
    {
        using var adminClient = new AdminClientBuilder(new AdminClientConfig 
            { BootstrapServers = bootstrapServers }).Build();
        
        var meta = adminClient.GetMetadata(topic, TimeSpan.FromSeconds(10));

        var t = meta.Topics.Single(x => x.Topic == topic);
        var count = t.Partitions.Count;

        _partitions = new TopicPartition[count];
        for (var i = 0; i < count; i++)
            _partitions[i] = new TopicPartition(topic, new Partition(i));
    }
    
    public TopicPartition GetTopicPartition(string flightId)
    {
        // exaample flightId: TFSLGW250210226.3||U28038. take first 6 characters which are airport codes and order
        // them (airports) alphabetically
        // result: ["TFS", "LGW"] => "LGWTFS"
        
        var departure = flightId[..3];
        var arrival = flightId.Substring(3, 3);

        var airports = string.Join("", new List<string> { departure, arrival }.Order());

        var hash = (uint)airports.GetHashCode();
        return _partitions[hash % _partitions.Length];
    }
}