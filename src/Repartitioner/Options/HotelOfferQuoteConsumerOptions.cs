using Confluent.Kafka;

namespace Esky.Packages.Repartitioner.Options;

public class HotelOfferQuoteConsumerOptions
{
    public const string ConfigurationSection = "HotelOfferQuoteConsumer";
    
    public string BootstrapServers { get; set; } = default!;
    public string ConsumerGroup { get; set; } = default!;
    public AutoOffsetReset AutoOffsetReset { get; set; } = AutoOffsetReset.Latest;
    public string Topic { get; set; } = default!;

    public int MaxBatchSize { get; set; } = 5000;
    public int MaxProcessedMessagesPerBatch { get; set; } = 100_000;
}