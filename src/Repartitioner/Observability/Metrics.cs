using System.Diagnostics.Metrics;

namespace Esky.Packages.Repartitioner.Observability;

public static class Metrics
{
    public const string MeterName = "packages-repartitioner";
    private static readonly Meter Meter = new(MeterName);

    private static long _flightBloomFilterSize;
    private static long _hotelOfferBloomFilterSize;
    
    private static long _flightBloomFilterMaxSize;
    private static long _hotelOfferBloomFilterMaxSize;

    static Metrics()
    {
        FlightBatches.Add(0);
        FlightMessages.Add(0);
        FlightFilteredMessages.Add(0);
        
        HotelOfferBatches.Add(0);
        HotelOfferMessages.Add(0);
        HotelOfferFilteredMessages.Add(0);
    }
    
    public static void RecordFlightBloomFilterSize(long flightBloomFilterSize)
    {
        _flightBloomFilterSize = flightBloomFilterSize;
    }
    
    public static void RecordHotelOfferBloomFilterSize(long hotelOfferBloomFilterSize)
    {
        _hotelOfferBloomFilterSize = hotelOfferBloomFilterSize;
    }
    
    public static void RecordFlightBloomFilterMaxSize(long flightBloomFilterMaxSize)
    {
        _flightBloomFilterMaxSize = flightBloomFilterMaxSize;
    }
    
    public static void RecordHotelOfferBloomFilterMaxSize(long hotelOfferBloomFilterMaxSize)
    {
        _hotelOfferBloomFilterMaxSize = hotelOfferBloomFilterMaxSize;
    }

    // Flight metrics
    internal static readonly Counter<long> FlightBatches = 
        Meter.CreateCounter<long>($"{MeterName}_flight_batches", "count");
    
    internal static readonly Counter<long> FlightMessages = 
        Meter.CreateCounter<long>($"{MeterName}_flight_messages", "count");
    
    internal static readonly Counter<long> FlightFilteredMessages = 
        Meter.CreateCounter<long>($"{MeterName}_flight_filtered_messages", "count");
    
    internal static readonly ObservableGauge<long> FlightBloomFilterSize = 
        Meter.CreateObservableGauge($"{MeterName}_flight_bloom_filter_size", () => _flightBloomFilterSize);
    
    internal static readonly ObservableGauge<long> FlightBloomFilterMaxSize = 
        Meter.CreateObservableGauge<long>($"{MeterName}_flight_bloom_filter_max_size", () => _flightBloomFilterMaxSize);
    
    // Hotel offer metrics
    internal static readonly Counter<long> HotelOfferBatches = 
        Meter.CreateCounter<long>($"{MeterName}_hotel_offer_batches", "count");
    
    internal static readonly Counter<long> HotelOfferMessages = 
        Meter.CreateCounter<long>($"{MeterName}_hotel_offer_messages", "count");
    
    internal static readonly Counter<long> HotelOfferFilteredMessages = 
        Meter.CreateCounter<long>($"{MeterName}_hotel_offer_filtered_messages", "count");
    
    internal static readonly ObservableGauge<long> HotelOfferBloomFilterSize =
        Meter.CreateObservableGauge($"{MeterName}_hotel_offer_bloom_filter_size", () => _hotelOfferBloomFilterSize);
    
    internal static readonly ObservableGauge<long> HotelOfferBloomFilterMaxSize =
        Meter.CreateObservableGauge<long>($"{MeterName}_hotel_offer_bloom_filter_max_size", () => _hotelOfferBloomFilterMaxSize);
}