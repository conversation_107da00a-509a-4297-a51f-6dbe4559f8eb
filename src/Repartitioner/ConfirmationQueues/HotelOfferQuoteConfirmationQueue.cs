using Esky.Hotels.Infrastructure.Kafka.ConfirmationGroup;

namespace Esky.Packages.Repartitioner.ConfirmationQueues;

public class HotelOfferQuoteConfirmationQueue(ILogger<FlightQuoteConfirmationQueue> logger)
    : BackgroundService
{
    public ConfirmationGroupQueue Queue { get; } = new();
    
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        logger.LogInformation("Starting FlightQuoteConfirmationQueue service");

        while (!stoppingToken.IsCancellationRequested)
        {
            Queue.TryCommit();

            try
            {
                await Task.Delay(100, stoppingToken);
            }
            catch (TaskCanceledException)
            {
                logger.LogInformation("FlightQuoteConfirmationQueue shutting down. Saving the last resume token");

                // Ka<PERSON>ka wont commit immediately, maybe add Flush?
                Queue.TryCommit();
                break;
            }
        }
    }
}