using Esky.Hotels.Infrastructure.Kafka;
using Esky.Packages.Infrastructure.Configuration;
using Esky.Packages.Repartitioner.BloomFilters;
using Esky.Packages.Repartitioner.ConfirmationQueues;
using Esky.Packages.Repartitioner.Consumers;
using Esky.Packages.Repartitioner.Observability;
using Esky.Packages.Repartitioner.Options;
using Esky.Packages.Repartitioner.Refreshers;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;

namespace Esky.Packages.Repartitioner;

public static class DependencyInjection
{
    internal static IServiceCollection AddBloomFilters(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton<FlightBloomFilter>();
        services.AddSingleton<HotelOfferBloomFilter>();
        
        services.AddHostedService<FlightBloomFilterRefresher>();
        services.AddHostedService<HotelOfferBloomFilterRefresher>();

        return services;
    }
    
    internal static IServiceCollection AddKafkaConsumers(this IServiceCollection services, IConfiguration configuration)
    {
        services.RegisterOptions<BloomFilterNotificationConsumerOptions>(configuration, BloomFilterNotificationConsumerOptions.ConfigurationSection);
        services.RegisterOptions<HotelOfferQuoteConsumerOptions>(configuration, HotelOfferQuoteConsumerOptions.ConfigurationSection);
        services.RegisterOptions<FlightQuoteConsumerOptions>(configuration, FlightQuoteConsumerOptions.ConfigurationSection);
        services.RegisterOptions<RepartitionedFlightQuoteProducerOptions>(configuration, RepartitionedFlightQuoteProducerOptions.ConfigurationSection);
        services.RegisterOptions<RepartitionedHotelOfferQuoteProducerOptions>(configuration, RepartitionedHotelOfferQuoteProducerOptions.ConfigurationSection);
        services.RegisterOptions<FlightBloomFilterOptions>(configuration, FlightBloomFilterOptions.ConfigurationSection);
        services.RegisterOptions<HotelOfferBloomFilterOptions>(configuration, HotelOfferBloomFilterOptions.ConfigurationSection);

        services.AddKafka(configuration);

        services.AddHostedService<BloomFilterNotificationConsumer>();
        services.AddHostedService<HotelOfferQuoteConsumer>();
        services.AddHostedService<FlightQuoteConsumer>();

        services.AddSingleton<FlightQuoteConfirmationQueue>();
        services.AddSingleton<HotelOfferQuoteConfirmationQueue>();
        services.AddHostedService(p => p.GetRequiredService<FlightQuoteConfirmationQueue>());
        services.AddHostedService(p => p.GetRequiredService<HotelOfferQuoteConfirmationQueue>());

        return services;
    }
    
    internal static IServiceCollection AddObservability(this IServiceCollection services)
    {
        var appName = "esky-packages-repartitioner";

        services.AddOpenTelemetry()
            .WithMetrics(options =>
                options.SetResourceBuilder(ResourceBuilder.CreateDefault().AddService(appName))
                    .AddMeter(Metrics.MeterName)
                    .AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation()
                    .AddRuntimeInstrumentation()
                    .AddProcessInstrumentation()
                    .AddPrometheusExporter()
            );

        return services;
    }
}