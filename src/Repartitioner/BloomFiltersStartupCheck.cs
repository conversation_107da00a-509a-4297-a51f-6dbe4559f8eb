using Esky.Packages.Repartitioner.BloomFilters;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Esky.Packages.Repartitioner;

public class BloomFiltersStartupCheck(FlightBloomFilter flightBloomFilter, HotelOfferBloomFilter hotelOfferBloomFilter) 
    : IHealthCheck
{
    public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken)
    {
        if (flightBloomFilter.IsInitialized && hotelOfferBloomFilter.IsInitialized)
        {
            return Task.FromResult(HealthCheckResult.Healthy());
        }

        return Task.FromResult(HealthCheckResult.Unhealthy("Bloom filters are not initialized"));
    }
}