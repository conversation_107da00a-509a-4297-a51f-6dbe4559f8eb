using Confluent.Kafka;
using Esky.Hotels.Infrastructure.Kafka.ConfirmationGroup;
using Esky.Hotels.Infrastructure.Kafka.Consumer;
using Esky.Hotels.Infrastructure.Kafka.Producer;
using Esky.Packages.Repartitioner.BloomFilters;
using Esky.Packages.Repartitioner.ConfirmationQueues;
using Esky.Packages.Repartitioner.Observability;
using Esky.Packages.Repartitioner.Options;
using Esky.Packages.Repartitioner.Partitioners;

namespace Esky.Packages.Repartitioner.Consumers;

public class FlightQuoteConsumer(
    KafkaConsumerFactory kafkaConsumerFactory,
    KafkaProducerFactory kafkaProducerFactory,
    FlightQuoteConsumerOptions flightQuoteConsumerOptions,
    RepartitionedFlightQuoteProducerOptions repartitionedFlightQuoteProducerOptions,
    FlightBloomFilter bloomFilter,
    FlightQuoteConfirmationQueue confirmationQueue,
    ILogger<FlightQuoteConsumer> logger) : BackgroundService
{
    private KafkaConsumer<string, byte[]> _flightQuoteConsumer = null!;
    private KafkaProducer<string, byte[]> _repartitionedFlightQuotesProducer = null!;
    
    private readonly FlightQuotePartitioner _flightQuotePartitioner = new();

    private void InitializeKafka()
    {
        _flightQuotePartitioner.Initialize(repartitionedFlightQuoteProducerOptions.Topic, 
            repartitionedFlightQuoteProducerOptions.BootstrapServers);
        
        _repartitionedFlightQuotesProducer = kafkaProducerFactory.Create<string, byte[]>(o => o
            .WithBootstrapServers(repartitionedFlightQuoteProducerOptions.BootstrapServers)
            .WithCompression(repartitionedFlightQuoteProducerOptions.Compression)
            .WithDeliveryReportFields(ProducerDeliveryReportFields.Status)
        );

        _flightQuoteConsumer = kafkaConsumerFactory.Create<string, byte[]>(o => o
            .WithBootstrapServers(flightQuoteConsumerOptions.BootstrapServers)
            .WithDefaultTopics(flightQuoteConsumerOptions.Topic)
            .WithConsumerGroupId(flightQuoteConsumerOptions.ConsumerGroup)
            .WithAutoOffsetReset(flightQuoteConsumerOptions.AutoOffsetReset)
            .WithAutoCommitIntervalMs(200)
            .WithLogPartitionRebalance()
        );

        _flightQuoteConsumer.Subscribe();
    }


    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (true)
        {
            if (bloomFilter.IsInitialized || stoppingToken.IsCancellationRequested)
            {
                break;
            }

            try
            {
                await Task.Delay(TimeSpan.FromSeconds(1), stoppingToken);
            }
            catch (TaskCanceledException)
            {
                break;
            }
        }
        
        InitializeKafka();

        while (!stoppingToken.IsCancellationRequested)
        {
            var batch = await _flightQuoteConsumer.ConsumeBatchAsync(new KafkaBatchOptions<string, byte[]>
            {
                Filter = Filter,
                MaxBatchSize = flightQuoteConsumerOptions.MaxBatchSize,
                MaxProcessedMessages = flightQuoteConsumerOptions.MaxProcessedMessagesPerBatch,
                StoppingToken = stoppingToken
            });
            
            logger.LogDebug("Received batch with {receivedMessageCount} messages. Processing " +
                            "{interestingMessageCount} interesting messages.", 
                batch.ReceivedMessageCount, batch.InterestingMessageCount);
            
            Metrics.FlightBatches.Add(1);
            Metrics.FlightMessages.Add(batch.ReceivedMessageCount);
            Metrics.FlightFilteredMessages.Add(batch.InterestingMessageCount);

            ProcessBatch(batch);
        }
    }

    private bool Filter(ConsumeResult<string, byte[]> result)
    {
        var flightId = result.Message.Key;
        return flightId != null && bloomFilter.ContainsFlight(flightId);
    }

    private void ProcessBatch(KafkaBatch<string, byte[]> batch)
    {
        var confirmationGroup = confirmationQueue.Queue
            .CreateAndEnqueue(batch.InterestingResults.Count, _ => batch.StoreOffsetsAndIgnoreLostPartitions());

        foreach (var interestingResult in batch.InterestingResults)
        {
            var flightId = interestingResult.Message.Key;

            var message = new Message<string, byte[]>
            {
                Key = flightId,
                Value = interestingResult.Message.Value
            };

            var partition = _flightQuotePartitioner.GetTopicPartition(flightId);

            _repartitionedFlightQuotesProducer.Produce(partition, message, 
                deliveryReport => ConfirmOne(deliveryReport, confirmationGroup));
        }
    }

    private void ConfirmOne(DeliveryReport<string, byte[]> deliveryReport, ConfirmationGroup confirmationGroup)
    {
        if (deliveryReport.Status == PersistenceStatus.Persisted)
        {
            confirmationGroup.ConfirmOne();
        }
        else
        {
            logger.LogError("MarkAsFailed: {error}", deliveryReport.Error);
            confirmationGroup.MarkAsFailed();
        }
    }
}