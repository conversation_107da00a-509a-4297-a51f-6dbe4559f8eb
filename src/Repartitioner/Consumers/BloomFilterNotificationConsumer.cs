using System.Text.Json;
using Confluent.Kafka;
using Esky.Hotels.Infrastructure.Kafka.Consumer;
using Esky.Packages.Application.Dtos.BloomFilterNotifications;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Infrastructure.Serialization.Contexts;
using Esky.Packages.Repartitioner.BloomFilters;
using Esky.Packages.Repartitioner.Options;

namespace Esky.Packages.Repartitioner.Consumers;

public class BloomFilterNotificationConsumer(KafkaConsumerFactory kafkaConsumerFactory, 
    BloomFilterNotificationConsumerOptions options, FlightBloomFilter flightBloomFilter, 
    HotelOfferBloomFilter hotelOfferBloomFilter, ILogger<BloomFilterNotificationConsumer> logger) 
    : BackgroundService
{
    private KafkaConsumer<string, byte[]> _consumer = null!;
    
    private void InitializeKafka()
    {
        _consumer = kafkaConsumerFactory.Create<string, byte[]>(o => o
            .WithBootstrapServers(options.BootstrapServers)
            .WithDefaultTopics(options.Topic)
            .WithIgnoreConsumerGroupId()
            .WithLogPartitionRebalance()
        );

        _consumer.StartConsumingFrom(Offset.End);
    }
    
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        InitializeKafka();
        
        while (!stoppingToken.IsCancellationRequested)
        {
            var batch = await _consumer.ConsumeBatchAsync(new KafkaBatchOptions<string, byte[]>
            {
                MaxBatchSize = options.MaxBatchSize,
                MaxProcessedMessages = options.MaxProcessedMessagesPerBatch,
                StoppingToken = stoppingToken
            });

            ProcessBatch(batch);
        }
    }

    private void ProcessBatch(KafkaBatch<string, byte[]> batch)
    {
        foreach (var interestingResult in batch.InterestingResults)
        {
            var notification = JsonSerializer.Deserialize<BloomFilterNotificationDto>(interestingResult.Message.Value, 
                BloomFilterNotificationDtoJsonContext.Default.BloomFilterNotificationDto);

            if (notification is BloomFilterFlightUpsertedNotificationDto flightUpserted)
            {
                flightBloomFilter.Add(flightUpserted.FlightId);
            }
            else if (notification is BloomFilterHotelOfferUpsertedNotificationDto hotelOfferUpserted)
            {
                hotelOfferBloomFilter.Add(new PackageHotelOfferStayKey(hotelOfferUpserted.MetaCode, 
                    hotelOfferUpserted.CheckIn, hotelOfferUpserted.StayLength));
            }
            else if (notification is BloomFilterFlightFalsePositiveNotificationDto flightFalsePositive)
            {
                flightBloomFilter.RegisterFalsePositivesPerSecond(flightFalsePositive.Rate);
            }
            else if (notification is BloomFilterHotelOfferFalsePositiveNotificationDto hotelOfferFalsePositive)
            {
                hotelOfferBloomFilter.RegisterFalsePositivesPerSecond(hotelOfferFalsePositive.Rate);
            }
            else
            {
                logger.LogError("Unknown notification type: {NotificationType}", notification?.GetType().Name);
            }
        }
    }
}