using Confluent.Kafka;
using Esky.Hotels.Infrastructure.Kafka.ConfirmationGroup;
using Esky.Hotels.Infrastructure.Kafka.Consumer;
using Esky.Hotels.Infrastructure.Kafka.Producer;
using Esky.Packages.Application.Dtos.HotelOffers;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Infrastructure.Partitioners;
using Esky.Packages.Repartitioner.BloomFilters;
using Esky.Packages.Repartitioner.ConfirmationQueues;
using Esky.Packages.Repartitioner.Observability;
using Esky.Packages.Repartitioner.Options;

namespace Esky.Packages.Repartitioner.Consumers;

public class HotelOfferQuoteConsumer(
    KafkaConsumerFactory kafkaConsumerFactory,
    KafkaProducerFactory kafkaProducerFactory,
    HotelOfferQuoteConsumerOptions hotelOfferQuoteConsumerOptions,
    RepartitionedHotelOfferQuoteProducerOptions repartitionedHotelOfferQuoteProducerOptions,
    HotelOfferBloomFilter bloomFilter,
    HotelOfferQuoteConfirmationQueue confirmationQueue,
    ILogger<HotelOfferQuoteConsumer> logger) : BackgroundService
{
    private KafkaConsumer<string, byte[]> _hotelOfferQuoteConsumer = null!;
    private KafkaProducer<string, byte[]> _repartitionedHotelOfferQuotesProducer = null!;
    
    private readonly HotelMetaCodePartitioner _hotelMetaCodePartitioner = new();

    private void InitializeKafka()
    {
        _hotelMetaCodePartitioner.Initialize(repartitionedHotelOfferQuoteProducerOptions.Topic, 
            repartitionedHotelOfferQuoteProducerOptions.BootstrapServers);
        
        _repartitionedHotelOfferQuotesProducer = kafkaProducerFactory.Create<string, byte[]>(o => o
            .WithBootstrapServers(repartitionedHotelOfferQuoteProducerOptions.BootstrapServers)
            .WithCompression(repartitionedHotelOfferQuoteProducerOptions.Compression)
            .WithDeliveryReportFields(ProducerDeliveryReportFields.Status)
        );

        _hotelOfferQuoteConsumer = kafkaConsumerFactory.Create<string, byte[]>(o => o
            .WithBootstrapServers(hotelOfferQuoteConsumerOptions.BootstrapServers)
            .WithDefaultTopics(hotelOfferQuoteConsumerOptions.Topic)
            .WithConsumerGroupId(hotelOfferQuoteConsumerOptions.ConsumerGroup)
            .WithAutoOffsetReset(hotelOfferQuoteConsumerOptions.AutoOffsetReset)
            .WithAutoCommitIntervalMs(200)
            .WithLogPartitionRebalance()
        );

        _hotelOfferQuoteConsumer.Subscribe();
    }


    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (true)
        {
            if (bloomFilter.IsInitialized || stoppingToken.IsCancellationRequested)
            {
                break;
            }

            try
            {
                await Task.Delay(TimeSpan.FromSeconds(1), stoppingToken);
            }
            catch (TaskCanceledException)
            {
                break;
            }
        }
        
        InitializeKafka();

        while (!stoppingToken.IsCancellationRequested)
        {
            var batch = await _hotelOfferQuoteConsumer.ConsumeBatchAsync(new KafkaBatchOptions<string, byte[]>
            {
                Filter = Filter,
                MaxBatchSize = hotelOfferQuoteConsumerOptions.MaxBatchSize,
                MaxProcessedMessages = hotelOfferQuoteConsumerOptions.MaxProcessedMessagesPerBatch,
                StoppingToken = stoppingToken
            });

            logger.LogDebug("Received batch with {receivedMessageCount} messages. Processing " +
                            "{interestingMessageCount} interesting messages.", 
                batch.ReceivedMessageCount, batch.InterestingMessageCount);
            
            Metrics.HotelOfferBatches.Add(1);
            Metrics.HotelOfferMessages.Add(batch.ReceivedMessageCount);
            Metrics.HotelOfferFilteredMessages.Add(batch.InterestingMessageCount);
            
            ProcessBatch(batch);
        }
    }

    private bool Filter(ConsumeResult<string, byte[]> result)
    {
        var hotelOfferId = new HotelOfferIdDto(result.Message.Key);

        if (!hotelOfferId.HasSupportedOccupancy())
        {
            return false;
        }

        var stayKey = new PackageHotelOfferStayKey(hotelOfferId.MetaCode, hotelOfferId.CheckIn, hotelOfferId.StayLength);
        
        return bloomFilter.ContainsHotelOffer(stayKey);
    }

    private void ProcessBatch(KafkaBatch<string, byte[]> batch)
    {
        var confirmationGroup = confirmationQueue.Queue
            .CreateAndEnqueue(batch.InterestingResults.Count, _ => batch.StoreOffsetsAndIgnoreLostPartitions());

        foreach (var interestingResult in batch.InterestingResults)
        {
            var hotelOfferId = new HotelOfferIdDto(interestingResult.Message.Key);
            
            var message = new Message<string, byte[]>
            {
                Key = interestingResult.Message.Key,
                Value = interestingResult.Message.Value
            };

            var partition = _hotelMetaCodePartitioner.GetTopicPartition(hotelOfferId.MetaCode);

            _repartitionedHotelOfferQuotesProducer.Produce(partition, message, 
                deliveryReport => ConfirmOne(deliveryReport, confirmationGroup));
        }
    }
    
    private void ConfirmOne(DeliveryReport<string, byte[]> deliveryReport, ConfirmationGroup confirmationGroup)
    {
        if (deliveryReport.Status == PersistenceStatus.Persisted)
        {
            confirmationGroup.ConfirmOne();
        }
        else
        {
            logger.LogError("MarkAsFailed: {error}", deliveryReport.Error);
            confirmationGroup.MarkAsFailed();
        }
    }
}