using Esky.Packages.Repartitioner.BloomFilters;
using Esky.Packages.Repartitioner.Observability;

namespace Esky.Packages.Repartitioner.Refreshers;

public class FlightBloomFilterRefresher(
    FlightBloomFilter flightBloomFilter, 
    ILogger<FlightBloomFilterRefresher> logger) 
    : BackgroundService
{
    private static readonly TimeSpan MinimumRefreshInterval = TimeSpan.FromMinutes(30);
    private static readonly float MaximumFalsePositivesPerSecond = 30;
    
    protected override async Task ExecuteAsync(CancellationToken cancellationToken)
    {
        await Refresh(cancellationToken);
        
        while (!cancellationToken.IsCancellationRequested)
        {
            Metrics.RecordFlightBloomFilterSize(flightBloomFilter.Size);
            
            try
            {
                await Task.Delay(TimeSpan.FromSeconds(5), cancellationToken);
            }
            catch (TaskCanceledException)
            {
                break;
            } 
            
            if (DateTime.UtcNow - flightBloomFilter.LastRefresh <= MinimumRefreshInterval)
            {
                continue;
            }

            if (flightBloomFilter.FalsePositivesPerSecond > MaximumFalsePositivesPerSecond)
            {
                await Refresh(cancellationToken);
            }
        }
    }
    
    private async Task Refresh(CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Flight bloom filter refreshing started");
            
            await flightBloomFilter.Refresh(cancellationToken);
            
            logger.LogInformation("Flight bloom filter refreshed. Filter size: {FlightSize} ", flightBloomFilter.Size);
        } 
        catch (TaskCanceledException)
        {
            // ignore
        }
    }
}