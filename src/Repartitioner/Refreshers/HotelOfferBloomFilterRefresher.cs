using Esky.Packages.Repartitioner.BloomFilters;
using Esky.Packages.Repartitioner.Observability;

namespace Esky.Packages.Repartitioner.Refreshers;

public class HotelOfferBloomFilterRefresher(
    HotelOfferBloomFilter hotelOfferBloomFilter, 
    ILogger<FlightBloomFilterRefresher> logger) 
    : BackgroundService
{
    private static readonly TimeSpan MinimumRefreshInterval = TimeSpan.FromMinutes(30);
    private static readonly float MaximumFalsePositivesPerSecond = 30;
    
    protected override async Task ExecuteAsync(CancellationToken cancellationToken)
    {
        await Refresh(cancellationToken);
        
        while (!cancellationToken.IsCancellationRequested)
        {
            Metrics.RecordHotelOfferBloomFilterSize(hotelOfferBloomFilter.Size);
            
            try
            {
                await Task.Delay(TimeSpan.FromSeconds(5), cancellationToken);
            }
            catch (TaskCanceledException)
            {
                break;
            } 
            
            if (DateTime.UtcNow - hotelOfferBloomFilter.LastRefresh <= MinimumRefreshInterval)
            {
                continue;
            }

            if (hotelOfferBloomFilter.FalsePositivesPerSecond > MaximumFalsePositivesPerSecond)
            {
                await Refresh(cancellationToken);
            }
        }
    }
    
    private async Task Refresh(CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Hotel offer bloom filter refreshing started");
            
            await hotelOfferBloomFilter.Refresh(cancellationToken);
            
            logger.LogInformation("Hotel offer bloom filter refreshed. Filter size: {HotelOfferSize} ", 
                hotelOfferBloomFilter.Size);
        } 
        catch (TaskCanceledException)
        {
            // ignore
        }
    }
}