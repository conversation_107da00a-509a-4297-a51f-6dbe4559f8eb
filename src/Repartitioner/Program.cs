using Esky.Packages.Application.DependencyInjections;
using Esky.Packages.Infrastructure.DependencyInjections;
using Esky.Packages.Repartitioner;
using NLog.Web;

var builder = WebApplication.CreateBuilder(args);

var services = builder.Services;
var configuration = builder.Configuration;

if (!builder.Environment.IsDevelopment())
{
    builder.Logging.ClearProviders();
    builder.Host.UseNLog();
}

services
    .AddApplication()
    .AddInfrastructure(configuration);
    
services
    .AddHealthChecks()
    .AddCheck<BloomFiltersStartupCheck>("BloomFilters", tags: ["startup"]);

services
    .AddBloomFilters(configuration)
    .AddKafkaConsumers(configuration)
    .AddObservability();

var app = builder.Build();

app.MapHealthChecks("/healthz");
app.UseOpenTelemetryPrometheusScrapingEndpoint();

app.Run();

namespace Esky.Packages.Repartitioner
{
    // Required for WebApplicationFactory in tests
    public partial class Program { }
}