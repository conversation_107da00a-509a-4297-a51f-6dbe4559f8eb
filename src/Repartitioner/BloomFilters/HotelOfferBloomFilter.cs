using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Repartitioner.Observability;
using Esky.Packages.Repartitioner.Options;

namespace Esky.Packages.Repartitioner.BloomFilters;

public class HotelOfferBloomFilter(HotelOfferBloomFilterOptions options, IPackageHotelOfferRepository packageHotelOfferRepository)
{
    private static readonly float FalsePositiveRate = 0.001f;
    
    private readonly Lock _lock = new();
    
    private Infrastructure.BloomFilters.BloomFilter _filter = new(options.Size, FalsePositiveRate);
    private Infrastructure.BloomFilters.BloomFilter? _newFilter;

    public int Size => _filter.UniqueHashes;
    
    public bool IsInitialized { get; private set; }
    public float FalsePositivesPerSecond { get; private set; }
    public DateTime LastRefresh { get; private set; }

    public async Task Refresh(CancellationToken cancellationToken)
    {
        Metrics.RecordHotelOfferBloomFilterMaxSize(options.Size);
        
        lock (_lock)
        {
            _newFilter = new Infrastructure.BloomFilters.BloomFilter(options.Size, FalsePositiveRate);
        }

        await foreach (var stayKey in packageHotelOfferRepository.EnumerateStayKeys(cancellationToken: cancellationToken))
        {
            _newFilter.Add(StayKeyToString(stayKey));
        }
        
        lock (_lock)
        {
            _filter = _newFilter;
            _newFilter = null;
            FalsePositivesPerSecond = 0;
            LastRefresh = DateTime.UtcNow;
        }
        
        IsInitialized = true;
    }
    
    public bool ContainsHotelOffer(PackageHotelOfferStayKey stayKey)
    {
        if (_filter == null)
        {
            throw new InvalidOperationException("Bloom filter is not initialized");
        }

        var stayKeyString = StayKeyToString(stayKey);
        
        lock (_lock)
        {
            return _filter.Contains(stayKeyString);
        }
    }
    
    public void Add(PackageHotelOfferStayKey stayKey)
    {
        lock (_lock)
        {
            _filter.Add(StayKeyToString(stayKey));
            _newFilter?.Add(StayKeyToString(stayKey));
        }
    }
    
    public void RegisterFalsePositivesPerSecond(float falsePositivesPerSecond)
    {
        lock (_lock)
        {
            FalsePositivesPerSecond = falsePositivesPerSecond;
        }
    }
    
    private static string StayKeyToString(PackageHotelOfferStayKey stayKey)
    {
        return $"{stayKey.CheckIn.ToString("yyMMdd")}-{stayKey.StayLength}-{stayKey.MetaCode}";
    }
}