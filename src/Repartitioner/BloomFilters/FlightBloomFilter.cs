using Esky.Packages.Domain.Repositories;
using Esky.Packages.Repartitioner.Observability;
using Esky.Packages.Repartitioner.Options;

namespace Esky.Packages.Repartitioner.BloomFilters;

public class FlightBloomFilter(FlightBloomFilterOptions options, IPackageFlightRepository packageFlightRepository)
{
    private const float FalsePositiveRate = 0.001f;

    private readonly Lock _lock = new();

    private Infrastructure.BloomFilters.BloomFilter _filter = new(options.Size, FalsePositiveRate);
    private Infrastructure.BloomFilters.BloomFilter? _newFilter;

    public int Size => _filter.UniqueHashes;
    
    public bool IsInitialized { get; private set; }
    public float FalsePositivesPerSecond { get; private set; }
    public DateTime LastRefresh { get; private set; }

    public async Task Refresh(CancellationToken cancellationToken)
    {
        Metrics.RecordFlightBloomFilterMaxSize(options.Size);
        
        lock (_lock)
        {
            _newFilter = new Infrastructure.BloomFilters.BloomFilter(options.Size, FalsePositiveRate);
        }

        await foreach (var flightId in packageFlightRepository.EnumerateFlightIds(cancellationToken: cancellationToken))
        {
            lock (_lock)
            {
                _newFilter.Add(flightId);
            }
        }
        
        lock (_lock)
        {
            _filter = _newFilter;
            _newFilter = null;
            FalsePositivesPerSecond = 0;
            LastRefresh = DateTime.UtcNow;
        }
        
        IsInitialized = true;
    }
    
    public bool ContainsFlight(string flightId)
    {
        if (_filter == null)
        {
            throw new InvalidOperationException("Bloom filter is not initialized");
        }

        lock (_lock)
        {
            return _filter.Contains(flightId);
        }
    }

    public void Add(string flightId)
    {
        lock (_lock)
        {
            _filter.Add(flightId);
            _newFilter?.Add(flightId);
        }
    }
    
    public void RegisterFalsePositivesPerSecond(float falsePositivesPerSecond)
    {
        lock (_lock)
        {
            FalsePositivesPerSecond = falsePositivesPerSecond;
        }
    }
}