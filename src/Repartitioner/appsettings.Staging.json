{"Mongo": {"ConnectionString": "SECRET"}, "FlightQuoteConsumer": {"ConsumerGroup": "packageRepartitioner-staging", "BootstrapServers": "esky-ets-flightscontent-ci.kafka-flightscontent-ci-kafka-node-0.service.gcp-staging.consul:9092,esky-ets-flightscontent-ci.kafka-flightscontent-ci-kafka-node-1.service.gcp-staging.consul:9092,esky-ets-flightscontent-ci.kafka-flightscontent-ci-kafka-node-2.service.gcp-staging.consul:9092", "Topic": "flightQuotes", "AutoOffsetReset": "Latest"}, "BloomFilterNotificationConsumer": {"BootstrapServers": "esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-0.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-1.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-2.service.gcp-staging.consul:9092", "Topic": "bloomFilterNotifications"}, "HotelOfferQuoteConsumer": {"ConsumerGroup": "packageRepartitioner-staging", "BootstrapServers": "esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-0.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-1.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-2.service.gcp-staging.consul:9092", "Topic": "hotelOfferCacheQuotes", "AutoOffsetReset": "Latest"}, "RepartitionedFlightQuoteProducer": {"BootstrapServers": "esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-0.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-1.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-2.service.gcp-staging.consul:9092", "Topic": "repartitionedFlightQuotes", "Compression": "Lz4"}, "RepartitionedHotelOfferQuoteProducer": {"BootstrapServers": "esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-0.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-1.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-2.service.gcp-staging.consul:9092", "Topic": "repartitionedHotelOfferQuotes", "Compression": "Lz4"}}