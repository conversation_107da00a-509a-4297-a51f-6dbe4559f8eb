namespace Esky.Packages.Contract.Packages;

public record PackageVariantsPriceHistoryResponseDto(
    IReadOnlyList<PackageVariantPriceHistoryDto> PackageVariants,
    string TimeZone,
    string Currency);

public record PackageVariantPriceHistoryDto(
    string PackageVariantId,
    IReadOnlyList<PriceHistoryPointDto> HistoryPoints);

public record PriceHistoryPointDto(
    DateOnly Date,
    int Price);
