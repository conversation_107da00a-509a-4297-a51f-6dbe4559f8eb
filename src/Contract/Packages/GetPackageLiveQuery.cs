using Esky.Packages.Contract.Common;

namespace Esky.Packages.Contract.Packages;

public record GetPackageLiveQuery(
    OccupancyDto[] Occupancies,
    string? FlightOptionId,
    string? PreferredDepartureAirport,
    string[]? SelectedDepartureAirports,
    string[]? FallbackDepartureAirports,
    TimeOfDayDto[]? InboundDepartures,
    TimeOfDayDto[]? OutboundDepartures,
    MealPlanDto[]? PreferredMealPlans);