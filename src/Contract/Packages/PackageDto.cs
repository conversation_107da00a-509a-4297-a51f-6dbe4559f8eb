namespace Esky.Packages.Contract.Packages;

public record PackageDto(
    string Id, 
    DateOnly CheckIn, 
    int StayLength, 
    int MetaCode, 
    string MarketId, 
    string Currency, 
    FlightOfferDto[] FlightOffers,
    HotelOfferDto[] HotelOffers);
    
public record PackageMetaSearchDto(
    string Id, 
    DateOnly CheckIn, 
    int StayLength, 
    int MetaCode, 
    string MarketId, 
    string Currency, 
    FlightOfferMetaSearchDto[] FlightOffers,
    Dictionary<string, int> HotelOffers);