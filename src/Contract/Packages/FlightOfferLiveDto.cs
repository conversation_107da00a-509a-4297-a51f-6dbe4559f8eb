namespace Esky.Packages.Contract.Packages;

public record FlightOfferLiveDto(
    string OfferId,
    string DepartureAirport,
    string ArrivalAirport,
    DateOnly DepartureDate,
    DateOnly ArrivalDate,
    DateOnly ReturnDepartureDate,
    DateOnly ReturnArrivalDate,
    string[] FlightNumbers,
    string[] ReturnFlightNumbers,
    int Stops,
    string[] FlightIds,
    string[] LegLocators,
    bool RegisteredBaggageIncluded);