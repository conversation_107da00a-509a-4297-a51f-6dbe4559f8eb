using Esky.Packages.Contract.Common;

namespace Esky.Packages.Contract.Search;

public record PackageVariantDto(
    string PackageId,
    string PackageVariantId,
    string FlightOfferId,
    int MetaCode,
    int StayLength,
    DateOnly CheckIn,
    string DepartureAirport,
    DateOnly DepartureDate,
    DateOnly ReturnArrivalDate,
    MealPlanDto MealPlan,
    int Price,
    PackageVariantSourceDto Source);