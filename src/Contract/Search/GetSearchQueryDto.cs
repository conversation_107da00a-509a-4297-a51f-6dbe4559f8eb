using Esky.Packages.Contract.Common;

namespace Esky.Packages.Contract.Search;

public record GetSearchQueryDto(
    string MarketId,
    int[] MetaCodes,
    int[] StayLengths,
    DateOnly DepartureDateFrom,
    DateOnly DepartureDateTo,
    string[]? DepartureAirports,
    MealPlanDto[]? MealPlans,
    OccupancyDto[] Occupancies,
    TimeOfDayDto[]? InboundDepartures,
    TimeOfDayDto[]? OutboundDepartures,
    int? MaxPrice,
    bool? UseDynamicSearchFallback);