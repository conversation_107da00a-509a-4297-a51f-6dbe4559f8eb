using Esky.Packages.Contract.Common;

namespace Esky.Packages.Contract.PackageAvailabilities;

public record PackageAvailabilityDto(
    int MetaCode,
    OccupancyDto[] Occupancies,
    MealPlanDto[] MealPlans,
    string[] DepartureAirports,
    string[] ArrivalAirports,
    int[] StayLengths,
    decimal LowestTotalPrice,
    Dictionary<int, decimal> LowestPricesPerStayLength,
    <PERSON><PERSON><PERSON><PERSON>,
    DateOnly <PERSON>heckIn);
