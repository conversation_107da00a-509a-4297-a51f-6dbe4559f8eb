using Esky.Packages.Contract.Common;

namespace Esky.Packages.Contract.Calendars;

public record GetCalendarQuery(
    int MetaCode,
    OccupancyDto? Occupancy,
    OccupancyDto[]? Occupancies,
    DateOnly? DepartureDateFrom,
    DateOnly? DepartureDateTo,
    string MarketId,
    MealPlanDto[]? MealPlans,
    string[]? DepartureAirports,
    TimeOfDayDto[]? InboundDepartures,
    TimeOfDayDto[]? OutboundDepartures);