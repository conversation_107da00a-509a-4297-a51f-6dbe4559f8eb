using Esky.Packages.Contract.Common;

namespace Esky.Packages.Contract.PackageVariants;

public record GetPackageLiveVariantsQuery(
    OccupancyDto[] Occupancies,
    string[]? DepartureAirports,
    string? FlightOptionId,
    string? PreferredDepartureAirport,
    TimeOfDayDto[]? InboundDepartures,
    TimeOfDayDto[]? OutboundDepartures,
    MealPlanDto[]? PreferredMealPlans,
    bool PreferSelectedFlight = false);
