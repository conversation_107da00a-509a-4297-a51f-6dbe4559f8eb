using Esky.Packages.Application.Services;
using Microsoft.Extensions.Hosting;

namespace Esky.Packages.Application.Workers;

public class CurrencyConverterRefresher(ICurrencyConverterService currencyConverterService) : BackgroundService
{
    public override async Task StartAsync(CancellationToken cancellationToken)
    {
        await currencyConverterService.RefreshRates(cancellationToken);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            await Task.Delay(TimeSpan.FromMinutes(10), stoppingToken);
            await currencyConverterService.RefreshRates(stoppingToken);
        }
    }
}