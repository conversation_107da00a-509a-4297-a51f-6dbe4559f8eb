using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.Stages;
using Esky.Packages.Application.PackagesGeneration.State;

namespace Esky.Packages.Application.PackagesGeneration.Factories;

public interface IPackagePipelineFactory
{
    PackagePipeline Create(string definitionId, TransformedPackageDefinitionParameters parameters, 
        PackagePipelineOptions options);
}

public class PackagePipelineFactory(
    IPipelineLoggerFactory loggerFactory,
    IPipelineRepository pipelineRepository,
    SelectFlightDestinationsTimeRangesStage selectFlightDestinationsTimeRangesStage,
    FindFlightsStage findFlightsStage,
    CreatePackageFlightsStage createPackageFlightsStage,
    FilterChangedPackageFlightsToUpsertStage filterChangedPackageFlightsToUpsertStage,
    SavePackageFlightsStage savePackageFlightsStage,
    CreatePackageFlightsHotelDetailsStage createPackageFlightsHotelDetailsStage,
    SelectHotelStayTimeRangesStage selectHotelStayTimeRangesStage,
    RemoveOutdatedPackageFlightsStage removeOutdatedPackageFlightsStage,
    RemoveOutdatedPackageFlightsTimeRangesStage removeOutdatedPackageFlightsTimeRangesStage,
    RemoveOutdatedPackageHotelOffersTimeRangesStage removeOutdatedPackageHotelOffersTimeRangesStage,
    FindHotelOffersStage findHotelOffersStage,
    GetMarketStage getMarketStage,
    SavePackageHotelAirportsStage savePackageHotelAirportsStage,
    PublishBloomFilterFlightNotificationsStage publishBloomFilterFlightNotificationsStage,
    PublishBloomFilterHotelOfferNotificationsStage publishBloomFilterHotelOfferNotificationsStage,
    CreatePackageHotelOffersStage createPackageHotelOffersStage,
    FilterChangedPackageHotelOffersToUpsertStage filterChangedPackageHotelOffersToUpsertStage,
    SavePackageHotelOffersStage savePackageHotelOffersStage,
    RemoveOutdatedPackageHotelOffersStage removeOutdatedPackageHotelOffersStage,
    CreatePackageHotelRouteOffersStage createPackageHotelRouteOffersStage,
    CreatePackageAvailabilitiesStage createPackageAvailabilitiesStage,
    LogPackagesStage logPackagesStage,
    SavePackageAvailabilitiesStage savePackageAvailabilitiesStage,
    RemoveOutdatedPackageAvailabilitiesStage removeOutdatedPackageAvailabilitiesStage,
    SelectFlightOccupanciesStage selectFlightOccupanciesStage,
    SelectFlightStayLengthsStage selectFlightStayLengthsStage,
    CreateMetaCodeByArrivalAirportByDepartureAirportStage createMetaCodeByArrivalAirportByDepartureAirportStage)
    : IPackagePipelineFactory
{
    public PackagePipeline Create(string definitionId, TransformedPackageDefinitionParameters parameters, 
        PackagePipelineOptions options)
    {
        var context = new PackagePipelineContext(
            options.PipelineId,
            loggerFactory.Create(),
            new PackagePipelineState
            {
                TargetDefinitionId = definitionId,
                StartedAt = DateTime.UtcNow
            },
            options,
            parameters);

        return new PackagePipeline(CreateStages(options), context, pipelineRepository);
    }

    private SequenceStage<PackagePipelineContext> CreateStages(PackagePipelineOptions options)
    {
        var stages = new List<PipelineStageBase<PackagePipelineContext>>()
        {
            getMarketStage,
            savePackageHotelAirportsStage,
        };

        stages.AddRange(FlightsStages());

        if (options.GenerationMode != GenerationMode.FlightsOnly)
        {
            stages.AddRange(HotelsStages());
        }

        return new SequenceStage<PackagePipelineContext>(stages.ToArray());
    }

    private List<PipelineStageBase<PackagePipelineContext>> FlightsStages()
    {
        return 
        [
            selectFlightStayLengthsStage,
            selectFlightDestinationsTimeRangesStage,
            new ChunkFlightDestinationsTimeRangesStage(
                new SequenceStage<PackagePipelineContext>(
                    selectFlightOccupanciesStage,
                    findFlightsStage,
                    createPackageFlightsStage,
                    filterChangedPackageFlightsToUpsertStage,
                    savePackageFlightsStage,
                    publishBloomFilterFlightNotificationsStage,
                    createPackageFlightsHotelDetailsStage)),
            selectHotelStayTimeRangesStage,
            removeOutdatedPackageFlightsStage,
            removeOutdatedPackageFlightsTimeRangesStage
        ];
    }

    private List<PipelineStageBase<PackagePipelineContext>> HotelsStages()
    {
        return
        [
            removeOutdatedPackageHotelOffersTimeRangesStage,
            createMetaCodeByArrivalAirportByDepartureAirportStage,
            new ChunkHotelStayTimeRangesStage(
                new SequenceStage<PackagePipelineContext>(
                    findHotelOffersStage,
                    createPackageHotelOffersStage,
                    filterChangedPackageHotelOffersToUpsertStage,
                    savePackageHotelOffersStage,
                    publishBloomFilterHotelOfferNotificationsStage,
                    removeOutdatedPackageHotelOffersStage,
                    createPackageHotelRouteOffersStage,
                    createPackageAvailabilitiesStage,
                    logPackagesStage)),
            savePackageAvailabilitiesStage,
            removeOutdatedPackageAvailabilitiesStage
        ];
    }
}