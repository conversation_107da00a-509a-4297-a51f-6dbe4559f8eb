using Esky.Packages.Application.PackagesGeneration.Transformers;
using Esky.Packages.Application.PackagesGeneration.Transformers.Steps;

namespace Esky.Packages.Application.PackagesGeneration.Factories;

public interface IPackageDefinitionParametersTransformerFactory
{
    PackageDefinitionParametersTransformer Create();
    PackageDefinitionParametersTransformer CreateForPartition(int partition);
}

public class PackageDefinitionParametersTransformerFactory(
    AddDefaultOccupanciesTransformer addDefaultOccupanciesTransformer,
    EvaluateRollingDurationStrategiesTransformer evaluateRollingDurationStrategiesTransformer,
    EvaluateHotelMetaCodeSelectorTransformer evaluateHotelMetaCodeSelectorTransformer,
    MapAirportsTransformer mapAirportsTransformer) 
    : IPackageDefinitionParametersTransformerFactory
{
    public PackageDefinitionParametersTransformer Create()
    {
        return new SequenceTransformer(
            addDefaultOccupanciesTransformer,
            evaluateRollingDurationStrategiesTransformer,
            evaluateHotelMetaCodeSelectorTransformer,
            mapAirportsTransformer);
    }

    public PackageDefinitionParametersTransformer CreateForPartition(int partition)
    {
        return new PartitionByDateTransformer(partition);
    }
}