using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageDefinitions;
using Esky.Packages.Domain.Model.PackageDefinitions.FlightCriterias;
using Esky.Packages.Domain.Model.PackageDefinitions.HotelSelectors;
using Esky.Packages.Domain.Model.PackageDefinitions.TimeCriterias;

namespace Esky.Packages.Application.PackagesGeneration.State;

public record TransformedPackageDefinitionParameters
{
    public string MarketId { get; init; } = "pl";
    public string PartnerCode { get; init; } = "ESKYPLPACKAGES";
    
    public TimeCriteria TimeCriteria { get; init; } = new();
    public FlightCriteria FlightCriteria { get; init; } = new();
    public PackageOccupancy[] Occupancies { get; init; } = [];
    public string[] AllArrivalAirportCodes { get; set; } = [];
    public Dictionary<string, int[]> ArrivalAirportCodeToMetaCodesMap { get; set; } = [];
    public int[] AllMetaCodes { get; set; } = [];

    public static TransformedPackageDefinitionParameters FromDefinition(PackageDefinition definition)
    {
        var parameters = definition.Parameters;

        return new TransformedPackageDefinitionParameters
        {
            MarketId = parameters.MarketId,
            PartnerCode = parameters.PartnerCode,
            TimeCriteria = parameters.TimeCriteria,
            FlightCriteria = parameters.FlightCriteria,
            Occupancies = parameters.Occupancies,
            AllMetaCodes = [],
            AllArrivalAirportCodes = [],
            ArrivalAirportCodeToMetaCodesMap = []
        };
    }
}