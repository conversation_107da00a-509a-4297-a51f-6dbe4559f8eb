using Esky.Packages.Application.Abstractions.Pipelines;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.State;

public class PackagePipelineContext(
    string pipelineId,
    ILogger logger,
    PackagePipelineState state,
    PackagePipelineOptions options,
    TransformedPackageDefinitionParameters definitionParameters)
    : PipelineContext(pipelineId, logger)
{
    public PackagePipelineState State { get; } = state;
    public PackagePipelineOptions Options { get; } = options;
    public TransformedPackageDefinitionParameters DefinitionParameters { get; } = definitionParameters;
}