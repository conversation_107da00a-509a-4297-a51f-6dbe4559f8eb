using Esky.Packages.Domain.Model.PackageHotelOffers;

namespace Esky.Packages.Application.PackagesGeneration.State.Components;

public class ChunkHotelStayTimeRangeState(List<HotelStayTimeRange> currentChunk)
{
    public static ChunkHotelStayTimeRangeState Empty { get; } = new([]);

    public List<HotelStayTimeRange> CurrentChunk { get; } = currentChunk;

    // GetHotelOfferGroupsStage
    public List<HotelOffer> HotelOffers { get; set; } = [];

    // CreatePackageHotelOffersStage
    public List<PackageHotelOffer> PackageHotelOffers { get; set; } = [];

    // FilterChangedPackageHotelOffersToUpsertStage
    public List<PackageHotelOffer> PackageHotelOffersToUpsert { get; set; } = [];

    // CreatePackageHotelRouteOffersStage
    public List<PackageHotelRouteOffers> PackageHotelRouteOffers { get; set; } = [];
}
