using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Application.PackagesGeneration.State.Components;

public record struct PackageFlightHotelDetails(
    PackageFlightId Id,
    DateOnly CheckIn,
    int StayLength,
    Airport DepartureAirport,
    Airport ArrivalAirport,
    int[] MetaCodes,
    Dictionary<PackageOccupancy, FlightPriceEntry> Prices);
