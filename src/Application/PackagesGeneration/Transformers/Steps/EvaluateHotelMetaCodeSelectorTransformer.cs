using Esky.Packages.Application.Abstractions.Gateways.HotelGateway;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Model.PackageDefinitions;
using Esky.Packages.Domain.Model.PackageDefinitions.HotelSelectors.Combine;
using Esky.Packages.Domain.Model.PackageDefinitions.HotelSelectors.Locations;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Transformers.Steps;

public class EvaluateHotelMetaCodeSelectorTransformer(
    IHotelGateway hotelGateway,
    ILogger<EvaluateHotelMetaCodeSelectorTransformer> logger)
    : PackageDefinitionParametersTransformer
{
    private const int DefaultMaxHotelLimit = 5000;

    public async override Task<TransformedPackageDefinitionParameters> Transform(
        PackageDefinitionParameters parameters,
        TransformedPackageDefinitionParameters transformedParameters, CancellationToken cancellationToken)
    {
        logger.LogWarning("Taking top {maxHotelLimit} hotels for HotelSelector", DefaultMaxHotelLimit);
        var mainHotels = await hotelGateway.SearchHotels(parameters.HotelSelector, DefaultMaxHotelLimit, cancellationToken);
        logger.LogInformation("HotelSelector returned: {hotelCount} items", mainHotels.Count);

        transformedParameters = transformedParameters with
        {
            AllMetaCodes = mainHotels.Select(x => x.MetaCode).ToArray(),
        };

        return transformedParameters;
    }
}
