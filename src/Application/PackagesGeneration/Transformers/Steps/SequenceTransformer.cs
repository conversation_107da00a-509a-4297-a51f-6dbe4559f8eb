using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Model.PackageDefinitions;

namespace Esky.Packages.Application.PackagesGeneration.Transformers.Steps;

public class SequenceTransformer(IEnumerable<PackageDefinitionParametersTransformer> transformers)
    : PackageDefinitionParametersTransformer
{
    public SequenceTransformer(params PackageDefinitionParametersTransformer[] transformers)
        : this((IEnumerable<PackageDefinitionParametersTransformer>)transformers)
    {
    }

    public async override Task<TransformedPackageDefinitionParameters> Transform(
        PackageDefinitionParameters parameters,
        TransformedPackageDefinitionParameters transformedParameters, CancellationToken cancellationToken)
    {
        foreach (var transformer in transformers)
        {
            transformedParameters = await transformer.Transform(parameters, transformedParameters, cancellationToken);
        }

        return transformedParameters;
    }
}