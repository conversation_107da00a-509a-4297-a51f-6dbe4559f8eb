using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Model.PackageDefinitions;
using Esky.Packages.Domain.Model.PackageDefinitions.TimeCriterias.DurationStrategies;
using Esky.Packages.Domain.Types.Extensions;

namespace Esky.Packages.Application.PackagesGeneration.Transformers.Steps;

public class EvaluateRollingDurationStrategiesTransformer : PackageDefinitionParametersTransformer
{
    public override Task<TransformedPackageDefinitionParameters> Transform(
        PackageDefinitionParameters parameters,
        TransformedPackageDefinitionParameters transformedParameters, 
        CancellationToken cancellationToken)
    {
        var durationStrategy = SimplifyAndEvaluateDurationStrategies(parameters.TimeCriteria.DurationStrategy);

        transformedParameters = transformedParameters with
        {
            TimeCriteria = transformedParameters.TimeCriteria with
            {
                DurationStrategy = durationStrategy
            }
        };

        return Task.FromResult(transformedParameters);
    }

    private static DurationStrategy SimplifyAndEvaluateDurationStrategies(DurationStrategy durationStrategy)
    {
        return durationStrategy switch
        {
            CheckInDurationStrategy checkIn => checkIn,
            RollingCheckInDurationStrategy rollingCheckIn => SimplifyRollingCheckInStrategy(rollingCheckIn),
            _ => throw new ArgumentOutOfRangeException(nameof(durationStrategy))
        };
    }

    private static CheckInDurationStrategy SimplifyRollingCheckInStrategy(RollingCheckInDurationStrategy rollingCheckIn)
    {
        if (rollingCheckIn.MinCheckInDaysFromGeneration <= 0)
            throw new ArgumentOutOfRangeException(nameof(rollingCheckIn.MinCheckInDaysFromGeneration),
                "MinCheckInDaysFromGeneration should be grater than 0.");

        if (rollingCheckIn.MaxCheckInDaysFromGeneration <= 0)
            throw new ArgumentOutOfRangeException(nameof(rollingCheckIn.MaxCheckInDaysFromGeneration),
                "MaxCheckInDaysFromGeneration should be grater than 0.");

        if (rollingCheckIn.MinCheckInDaysFromGeneration > rollingCheckIn.MaxCheckInDaysFromGeneration)
            throw new ArgumentOutOfRangeException(nameof(rollingCheckIn.MaxCheckInDaysFromGeneration),
                "MaxCheckInDaysFromGeneration should be grater than MinCheckInDaysFromGeneration.");

        var now = DateTime.UtcNow.Date;

        var minCheckInDate = now.AddDays(rollingCheckIn.MinCheckInDaysFromGeneration).ToDateOnly();
        var maxCheckInDate = now.AddDays(rollingCheckIn.MaxCheckInDaysFromGeneration).ToDateOnly();

        return new CheckInDurationStrategy
        {
            MinCheckIn = minCheckInDate,
            MaxCheckIn = maxCheckInDate,
            StayLengths = rollingCheckIn.StayLengths
        };
    }
}