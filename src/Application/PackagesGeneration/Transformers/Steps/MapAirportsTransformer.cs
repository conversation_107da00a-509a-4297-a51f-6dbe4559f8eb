using Esky.Packages.Application.Abstractions.Gateways.HotelGateway;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Model.PackageDefinitions;
using Esky.Packages.Domain.Model.PackageDefinitions.HotelSelectors.Locations;
using Esky.Packages.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Transformers.Steps;

public class MapAirportsTransformer(
    IHotelGateway hotelGateway,
    ILogger<MapAirportsTransformer> logger)
    : PackageDefinitionParametersTransformer
{
    public async override Task<TransformedPackageDefinitionParameters> Transform(
        PackageDefinitionParameters parameters,
        TransformedPackageDefinitionParameters transformedParameters, CancellationToken cancellationToken)
    {
        var hotelAirports = await hotelGateway
            .GetAirportsForMetaCodes(transformedParameters.AllMetaCodes, cancellationToken);
        var hotelAirportsFlat = hotelAirports
            .SelectMany(x => x.Airports)
            .ToList();

        var allArrivalAirportCodes = hotelAirportsFlat
            .Select(x => x.AirportCode)
            .Distinct()
            .ToArray();
        var arrivalAirportCodeToMetaCodesMap = hotelAirportsFlat
            .GroupBy(x => x.AirportCode)
            .ToDictionary(
                x => x.Key,
                x => x.Select(x => x.MetaCode).ToArray());

        transformedParameters = transformedParameters with
        {
            AllArrivalAirportCodes = allArrivalAirportCodes,
            ArrivalAirportCodeToMetaCodesMap = arrivalAirportCodeToMetaCodesMap
        };

        logger.LogInformation("All arrival airports distinct {allArrivalAirportCodes}", allArrivalAirportCodes.Length);

        return transformedParameters;
    }
}
