using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Application.PackagesGeneration.Transformers.Exceptions;
using Esky.Packages.Domain.Model.PackageDefinitions;
using Esky.Packages.Domain.Model.PackageDefinitions.TimeCriterias.DurationStrategies;

namespace Esky.Packages.Application.PackagesGeneration.Transformers.Steps;

public class PartitionByDateTransformer(int partition) : PackageDefinitionParametersTransformer
{
    public override Task<TransformedPackageDefinitionParameters> Transform(
        PackageDefinitionParameters parameters,
        TransformedPackageDefinitionParameters transformedParameters, 
        CancellationToken cancellationToken)
    {
        var checkInDurationStrategy = transformedParameters
            .TimeCriteria
            .DurationStrategy
            .AsDerivedOrThrow<CheckInDurationStrategy>();

        return Task.FromResult(PartitionCheckIn(transformedParameters, checkInDurationStrategy));
    }

    private TransformedPackageDefinitionParameters PartitionCheckIn(
        TransformedPackageDefinitionParameters parameters,
        CheckInDurationStrategy checkInDurationStrategy)
    {
        var (minCheckIn, maxCheckIn) = checkInDurationStrategy.GetCheckInRange();
        var (partitionStart, partitionEnd) = OneDayPartitioner.GetPartitionDates(minCheckIn, maxCheckIn, partition) 
            ?? throw new PartitionOutOfBoundException();

        parameters = parameters with
        {
            TimeCriteria = parameters.TimeCriteria with
            {
                DurationStrategy = new CheckInDurationStrategy
                {
                    MinCheckIn = partitionStart,
                    MaxCheckIn = partitionEnd,
                    StayLengths = checkInDurationStrategy.StayLengths
                }
            }
        };

        return parameters;
    }
}