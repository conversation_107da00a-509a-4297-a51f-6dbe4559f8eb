using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageDefinitions;

namespace Esky.Packages.Application.PackagesGeneration.Transformers.Steps;

public class AddDefaultOccupanciesTransformer : PackageDefinitionParametersTransformer
{
    public override Task<TransformedPackageDefinitionParameters> Transform(
        PackageDefinitionParameters parameters,
        TransformedPackageDefinitionParameters transformedParameters, 
        CancellationToken cancellationToken)
    {
        if (parameters.Occupancies is { Length: > 0 })
        {
            return Task.FromResult(transformedParameters);
        }

        PackageOccupancy[] occupancies =
        [
            new(adults: 2, youths: 0, children: 0, infants: 0),
            new(adults: 2, youths: 0, children: 1, infants: 0)
        ];

        transformedParameters = transformedParameters with
        {
            Occupancies = occupancies
        };

        return Task.FromResult(transformedParameters);
    }
}