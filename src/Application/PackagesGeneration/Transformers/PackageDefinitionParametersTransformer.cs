using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Model.PackageDefinitions;

namespace Esky.Packages.Application.PackagesGeneration.Transformers;

public abstract class PackageDefinitionParametersTransformer
{
    public abstract Task<TransformedPackageDefinitionParameters> Transform(
        PackageDefinitionParameters parameters,
        TransformedPackageDefinitionParameters transformedParameters, 
        CancellationToken cancellationToken);
}