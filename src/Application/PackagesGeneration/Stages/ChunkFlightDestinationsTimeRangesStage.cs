using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Application.PackagesGeneration.State.Components;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

/// <summary>
/// 1. Process flight destinations time ranges in chunks.
/// </summary>
public class ChunkFlightDestinationsTimeRangesStage(PipelineStageBase<PackagePipelineContext> stage) : PipelineStageBase<PackagePipelineContext>
{
    protected override async Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var chunks = context.State.FlightDestinationsTimeRanges;
        context.Logger.LogInformation("Flight destinations time ranges chunks: {total}", chunks.Count);

        var chunk = 1;
        foreach (var flightChunk in chunks)
        {
            context.State.ChunkFlightDestinationsTimeRangeState = new ChunkFlightDestinationsTimeRangeState(flightChunk);

            context.Logger.LogInformation("Processing flight destinations time ranges chunk: {chunk}", chunk++);
            await stage.Run(context, cancellationToken);
        }
    }
}