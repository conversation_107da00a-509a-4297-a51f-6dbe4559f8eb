using Esky.Packages.Application.Abstractions.Gateways.BloomFilterNotificationGateway;
using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.Dtos.BloomFilterNotifications;
using Esky.Packages.Application.PackagesGeneration.State;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

public class PublishBloomFilterFlightNotificationsStage(IBloomFilterNotificationGateway bloomFilterNotificationGateway) 
    : PipelineStageBase<PackagePipelineContext>
{
    protected override async Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var notifications = context.State.ChunkFlightDestinationsTimeRangeState.PackageFlightsToUpsert
            .SelectMany(f => f.GetFlightIds())
            .Distinct()
            .Select(f => new BloomFilterFlightUpsertedNotificationDto
            {
                FlightId = f
            })
            .ToList();

        if (notifications.Count > 0)
        {
            await bloomFilterNotificationGateway.PublishNotifications(notifications, cancellationToken);
        }

        CleanUpPreviousStepState(context);
    }

    private static void CleanUpPreviousStepState(PackagePipelineContext context)
    {
        context.State.ChunkFlightDestinationsTimeRangeState.PackageFlightsToUpsert.Clear();
    }
}