using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Model.PackageAvailabilities;
using Esky.Packages.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

public class SavePackageAvailabilitiesStage(IPackageAvailabilitiesRepository packageAvailabilitiesRepository) : PipelineStageBase<PackagePipelineContext>
{
    protected override async Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var packageAvailabilities = new PackageAvailabilities
        {
            Id = new PackageAvailabilitiesId(
                definitionId: context.State.TargetDefinitionId,
                partition: context.Options.Partition),
            Availabilities = context.State.PackageAvailabilities.Values.ToArray()
        };

        await packageAvailabilitiesRepository.UpsertWithoutConcurrency(packageAvailabilities, cancellationToken);

        context.Logger.LogInformation("Upserted {count} package availabilities.", packageAvailabilities.Availabilities.Length);
    }
}