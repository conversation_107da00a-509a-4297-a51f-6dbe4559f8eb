using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

public class CreateMetaCodeByArrivalAirportByDepartureAirportStage : PipelineStageBase<PackagePipelineContext>
{
    protected override Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        context.State.MetaCodeByArrivalAirportByDepartureAirport = context.State.PackageFlightsHotelDetails
            .SelectMany(x => x.MetaCodes.Select(metaCode => new
            {
                x.ArrivalAirport,
                x.DepartureAirport,
                MetaCode = metaCode
            }))
            .GroupBy(x => x.MetaCode)
            .ToDictionary(byMetaCode => byMetaCode.Key, byMetaCode => byMetaCode
                .GroupBy(x => x.ArrivalAirport)
                .ToDictionary(byMetaCodeByArrivalAirport => byMetaCodeByArrivalAirport.Key,
                    byMetaCodeByArrivalAirport => byMetaCodeByArrivalAirport
                        .Select(x => x.DepartureAirport)
                        .Distinct()
                        .ToArray()));
        
        return Task.CompletedTask;
    }
}