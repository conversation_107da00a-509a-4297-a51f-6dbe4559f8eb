using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Application.PackagesGeneration.State.Components;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

public class CreatePackageHotelRouteOffersStage : PipelineStageBase<PackagePipelineContext>
{
    protected override Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var hotels = context.State.ChunkHotelStayTimeRangeState.PackageHotelOffers
            .GroupBy(x => (x.Id.CheckIn, x.Id.StayLength, x.Id.MetaCode));

        var flights = context.State.PackageFlightsHotelDetails
            .SelectMany(x => x.MetaCodes.Select(y => (x.CheckIn, x.Stay<PERSON>ength, MetaCode: y, FlightOffer: x))
            .GroupBy(x => (x.CheckIn, x.<PERSON>, x.MetaCode)));

        context.State.ChunkHotelStayTimeRangeState.PackageHotelRouteOffers = hotels
            .Join(flights,
                x => (x.Key.CheckIn, x.Key.StayLength, x.Key.MetaCode),
                y => (y.Key.CheckIn, y.Key.StayLength, y.Key.MetaCode),
                (hotel, flight) => new PackageHotelRouteOffers(
                    CheckIn: hotel.Key.CheckIn,
                    StayLength: hotel.Key.StayLength,
                    MetaCode: hotel.Key.MetaCode,
                    HotelOffers: hotel.ToArray(),
                    PackageFlightHotelDetails: flight.Select(x => x.FlightOffer).ToArray()))
            .ToList();

        return Task.CompletedTask;
    }
}
