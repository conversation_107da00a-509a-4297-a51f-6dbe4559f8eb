using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Factories;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

/// <summary>
/// 1. Create package flights from flight offers.
/// 2. Filter out package flights that don't match the definition stay lengths.
/// </summary>
public class CreatePackageFlightsStage(IPackageFlightFactory packageFlightFactory) : PipelineStageBase<PackagePipelineContext>
{
    protected override Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var flightOffers = context.State.ChunkFlightDestinationsTimeRangeState.FlightOffers;

        context.State.ChunkFlightDestinationsTimeRangeState.PackageFlights = packageFlightFactory
            .CreateMany(flightOffers, context.State.Market.Id, context.State.TargetDefinitionId, 
                context.State.Market.Currency, context.State.FlightOccupancies)
            // Not sure if it's still necessary to filter but previously FCACHE returned more flights than needed.
            .Where(p => context.State.FlightStayLengths.Contains(p.Id.StayLength))
            .ToList();

        // Clear the flight offers as they are no longer needed.
        context.State.ChunkFlightDestinationsTimeRangeState.FlightOffers.Clear();

        return Task.CompletedTask;
    }
}