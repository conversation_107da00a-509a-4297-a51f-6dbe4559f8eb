using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Model.PackageDefinitions.TimeCriterias.DurationStrategies;
using Esky.Packages.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

/// <summary>
/// 1. Remove outdated package hotel offers from time ranges outside of min/max check in range.
/// </summary>
public class RemoveOutdatedPackageHotelOffersTimeRangesStage(
    IPackageHotelOfferRepository packageHotelOfferRepository)
    : PipelineStageBase<PackagePipelineContext>
{
    protected override async Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var timeRanges = context.State.HotelStayTimeRanges.Select(x => (x.CheckIn, x.StayLength)).ToList();
        var partition = context.Options.Partition;
        var totalPartitions = context.Options.TotalPartitions;
        var checkInDurationStrategy = context
            .DefinitionParameters
            .TimeCriteria
            .DurationStrategy
            .AsDerivedOrThrow<CheckInDurationStrategy>();
        var minCheckIn = partition == 0 ? DateOnly.MinValue : checkInDurationStrategy.MinCheckIn;
        var maxCheckIn = partition + 1 == totalPartitions ? DateOnly.MaxValue : checkInDurationStrategy.MaxCheckIn;

        var removedPackageHotelOffers = await packageHotelOfferRepository.RemoveExceptSpecificTimeRanges(
            definitionId: context.State.TargetDefinitionId, 
            minCheckIn: minCheckIn, 
            maxCheckIn: maxCheckIn, 
            timeRanges: timeRanges, 
            cancellationToken: cancellationToken);

        context.Logger.LogInformation("Removed {count} package hotel offers from outdated time ranges", removedPackageHotelOffers);
    }
}