using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Model.PackageDefinitions.TimeCriterias.DurationStrategies;
using Esky.Packages.Domain.Model.PackageFlights;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

public class SelectFlightStayLengthsStage : PipelineStageBase<PackagePipelineContext>
{
    protected override Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var checkInDurationStrategy = context
            .DefinitionParameters
            .TimeCriteria
            .DurationStrategy
            .AsDerivedOrThrow<CheckInDurationStrategy>();
        
        context.State.FlightStayLengths = PackageFlight.SupportedStayLengths
            .Union(checkInDurationStrategy.StayLengths)
            .Distinct()
            .ToArray();
        
        return Task.CompletedTask;
    }
}