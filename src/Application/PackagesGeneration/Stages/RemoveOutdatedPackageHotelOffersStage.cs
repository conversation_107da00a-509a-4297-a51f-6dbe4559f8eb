using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

/// <summary>
/// 1. Remove outdated package hotel offers (which are not existing in current chunk) from the current time ranges.
/// </summary>
public class RemoveOutdatedPackageHotelOffersStage(IPackageHotelOfferRepository packageHotelOfferRepository) : PipelineStageBase<PackagePipelineContext>
{
    protected override async Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var timeRanges = context.State.ChunkHotelStayTimeRangeState.CurrentChunk
            .Select(x => (x.CheckIn, x.StayLength))
            .Distinct()
            .ToList();
        
        var packageHotelOffersIds = context.State.ChunkHotelStayTimeRangeState.PackageHotelOffers
            .Select(x => x.Id);

        var removedPackageHotelOffers = await packageHotelOfferRepository.RemoveFromTimeRangesExceptSpecificIds(
            definitionId: context.State.TargetDefinitionId,
            timeRanges: timeRanges,
            ids: packageHotelOffersIds,
            cancellationToken: cancellationToken);

        context.Logger.LogInformation("Removed {count} package hotel offers from current time ranges", removedPackageHotelOffers);
    }
}