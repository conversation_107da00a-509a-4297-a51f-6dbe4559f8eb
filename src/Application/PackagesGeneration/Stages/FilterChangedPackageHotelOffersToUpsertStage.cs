using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

/// <summary>
/// 1. Filter hotel offers to upsert in database that:
/// - are not stored in the database
/// - prices have been updated and we can't compensate the price change
/// Oplog reader will have less changes to handle.
/// </summary>
public class FilterChangedPackageHotelOffersToUpsertStage(IPackageHotelOfferRepository repository) 
    : PipelineStageBase<PackagePipelineContext>
{
    private const int BatchSize = 500;

    protected override async Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var packageHotelOffers = context.State.ChunkHotelStayTimeRangeState.PackageHotelOffers;
        var totalPackageHotelOffers = packageHotelOffers.Count;
        
        context.Logger.LogInformation("Processing {total} package hotel offers in batches of {batchSize}", totalPackageHotelOffers, BatchSize);
        
        var packageHotelOffersToUpsert = new List<PackageHotelOffer>();
        var processedCount = 0;
        
        foreach (var batch in packageHotelOffers.Chunk(BatchSize))
        {
            cancellationToken.ThrowIfCancellationRequested();
            
            var storedPackageHotelOffers = await GetStoredPackageHotelOffers(batch, cancellationToken);
            
            var notStoredPackageHotelOffers = batch
                .Where(x => storedPackageHotelOffers.All(f => f.Id != x.Id))
                .ToList();
            packageHotelOffersToUpsert.AddRange(notStoredPackageHotelOffers);
            
            var updatedPackageHotelOffers = GetUpdatedPackageHotelOffers(batch, storedPackageHotelOffers);
            packageHotelOffersToUpsert.AddRange(updatedPackageHotelOffers);
            
            processedCount += batch.Length;
            context.Logger.LogInformation("Batch processed: not stored: {notStored}, updated: {updated}, progress: {processed}/{total}", 
                notStoredPackageHotelOffers.Count, updatedPackageHotelOffers.Count, processedCount, totalPackageHotelOffers);
        }
        
        context.State.ChunkHotelStayTimeRangeState.PackageHotelOffersToUpsert = packageHotelOffersToUpsert;
        context.Logger.LogInformation("Total package hotel offers to upsert: {count}", packageHotelOffersToUpsert.Count);
    }

    private async Task<List<PackageHotelOffer>> GetStoredPackageHotelOffers(
        PackageHotelOffer[] packageHotelOffers, 
        CancellationToken cancellationToken)
    {
        var storedPackageHotelOffers = new List<PackageHotelOffer>();
        
        var packageHotelOffersIds = packageHotelOffers
            .Select(x => x.Id)
            .ToList();
        
        await foreach(var packageHotelOffer in repository.EnumerateByIds(packageHotelOffersIds, 
                          cancellationToken: cancellationToken))
        {
            storedPackageHotelOffers.Add(packageHotelOffer);
        }

        return storedPackageHotelOffers;
    }

    private static List<PackageHotelOffer> GetUpdatedPackageHotelOffers(
        PackageHotelOffer[] packageHotelOffers, 
        List<PackageHotelOffer> storedPackageHotelOffers)
    {
        var packageHotelOffersToUpdate = new List<PackageHotelOffer>();

        foreach (var storedPackageHotelOffer in storedPackageHotelOffers)
        {
            var packageHotelOffer = packageHotelOffers.FirstOrDefault(x => x.Id == storedPackageHotelOffer.Id);
            
            if (packageHotelOffer != null && !storedPackageHotelOffer.Compare(packageHotelOffer))
            {
                packageHotelOffersToUpdate.Add(packageHotelOffer);
            }
            // TODO: Check if definitionId has changed and add metrics to track this. It means that package definitions are configured incorrectly across single market.
        }

        return packageHotelOffersToUpdate;
    }
}
