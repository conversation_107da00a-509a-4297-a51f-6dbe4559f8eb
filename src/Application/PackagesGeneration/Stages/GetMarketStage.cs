using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.Exceptions;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Repositories;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

public class GetMarketStage(IMarketRepository marketRepository) : PipelineStageBase<PackagePipelineContext>
{
    protected override async Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var market = await marketRepository.GetById(context.DefinitionParameters.MarketId, cancellationToken);

        if (market == null)
        {
            throw new NoMarketException();
        }
        
        context.State.Market = market;
    }
}