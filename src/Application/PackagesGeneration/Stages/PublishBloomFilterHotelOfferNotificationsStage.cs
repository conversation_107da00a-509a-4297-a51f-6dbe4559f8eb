using Esky.Packages.Application.Abstractions.Gateways.BloomFilterNotificationGateway;
using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.Dtos.BloomFilterNotifications;
using Esky.Packages.Application.PackagesGeneration.State;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

public class PublishBloomFilterHotelOfferNotificationsStage(IBloomFilterNotificationGateway bloomFilterNotificationGateway) 
    : PipelineStageBase<PackagePipelineContext>
{
    protected override async Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var notifications = context.State.ChunkHotelStayTimeRangeState.PackageHotelOffersToUpsert
            .Select(o => (o.Id.CheckIn, o.Id.StayLength, o.Id.MetaCode))
            .Distinct()
            .Select(o => new BloomFilterHotelOfferUpsertedNotificationDto
            {
                CheckIn = o.CheckIn,
                StayLength = o.Stay<PERSON>ength,
                MetaCode = o.MetaCode
            })
            .ToList();

        if (notifications.Count > 0)
        { 
            await bloomFilterNotificationGateway.PublishNotifications(notifications, cancellationToken);
        }

        CleanUpPreviousStepState(context);
    }

    private static void CleanUpPreviousStepState(PackagePipelineContext context)
    {
        context.State.ChunkHotelStayTimeRangeState.PackageHotelOffersToUpsert.Clear();
    }
}