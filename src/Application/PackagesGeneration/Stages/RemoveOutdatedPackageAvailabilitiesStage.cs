using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

public class RemoveOutdatedPackageAvailabilitiesStage(IPackageAvailabilitiesRepository packageAvailabilitiesRepository) : PipelineStageBase<PackagePipelineContext>
{
    protected override async Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var lastPartition = context.Options.TotalPartitions! - 1;

        var removedPackageAvailabilities = await packageAvailabilitiesRepository.RemoveByPartitionsGreaterThan(
            definitionId: context.State.TargetDefinitionId,
            partition: lastPartition,
            cancellationToken: cancellationToken);

        context.Logger.LogInformation("Removed outdated {count} package availabilities", removedPackageAvailabilities);
    }
}