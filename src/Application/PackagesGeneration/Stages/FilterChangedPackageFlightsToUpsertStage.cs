using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

/// <summary>
/// 1. Filter flights to upsert in database that:
/// - are not stored in the database
/// - prices have been updated and we can't compensate the price change
/// Oplog reader will have less changes to handle.
/// </summary>
public class FilterChangedPackageFlightsToUpsertStage(IPackageFlightRepository repository) 
    : PipelineStageBase<PackagePipelineContext>
{
    protected override async Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var packageFlights = context.State.ChunkFlightDestinationsTimeRangeState.PackageFlights;

        var storedPackageFlights = await GetStoredPackageFlights(packageFlights, cancellationToken);
        context.Logger.LogInformation("Stored package flights: {count}", storedPackageFlights.Count);

        var notStoredPackageFlights = packageFlights
            .Where(x => storedPackageFlights.All(f => f.Id != x.Id))
            .ToList();
        context.Logger.LogInformation("Not stored package flights: {count}", notStoredPackageFlights.Count);

        var updatedPackageFlights = GetUpdatedPackageFlights(packageFlights, storedPackageFlights);
        context.Logger.LogInformation("Updated package flights: {count}", updatedPackageFlights.Count);

        context.State.ChunkFlightDestinationsTimeRangeState.PackageFlightsToUpsert = notStoredPackageFlights
            .Union(updatedPackageFlights)
            .ToList();
    }

    private async Task<List<PackageFlight>> GetStoredPackageFlights(
        List<PackageFlight> packageFlights, 
        CancellationToken cancellationToken)
    {
        var storedPackageFlights = new List<PackageFlight>();
        
        var packageFlightIds = packageFlights
            .Select(x => x.Id)
            .ToList();

        await foreach (var packageFlight in repository.EnumerateByIds(packageFlightIds, 
                           cancellationToken: cancellationToken))
        {
            storedPackageFlights.Add(packageFlight);
        }

        return storedPackageFlights;
    }

    private static List<PackageFlight> GetUpdatedPackageFlights(
        List<PackageFlight> packageFlights, 
        List<PackageFlight> storedPackageFlights)
    {
        var packageFlightsToUpdate = new List<PackageFlight>();

        foreach (var storedPackageFlight in storedPackageFlights)
        {
            var packageFlight = packageFlights.First(x => x.Id == storedPackageFlight.Id);

            if (!storedPackageFlight.Compare(packageFlight))
            {
                packageFlightsToUpdate.Add(packageFlight);
            }
            // TODO: Check if definitionId has changed and add metrics to track this. It means that package definitions are configured incorrectly across single market.
        }

        return packageFlightsToUpdate;
    }
}
