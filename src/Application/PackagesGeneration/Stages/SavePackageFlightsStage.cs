using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.Observability;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

/// <summary>
/// 1. Save package flights to the database.
/// </summary>
public class SavePackageFlightsStage(IPackageFlightRepository repository) : PipelineStageBase<PackagePipelineContext>
{
    protected override async Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var allPackagesCount = context.State.ChunkFlightDestinationsTimeRangeState.PackageFlights.Count;
        var packagesCount = context.State.ChunkFlightDestinationsTimeRangeState.PackageFlightsToUpsert.Count;
        if (packagesCount > 0)
        {
            await repository.UpsertWithoutConcurrency(
                context.State.ChunkFlightDestinationsTimeRangeState.PackageFlightsToUpsert, cancellationToken);
        }
        
        ApplicationMetrics.RegisterGeneratedPackageFlights(allPackagesCount, packagesCount);

        context.Logger.LogInformation("Upserted {packagesCount} package flights, skipped {unchangedPackagesCount}", 
            packagesCount, allPackagesCount - packagesCount);
    }
}
