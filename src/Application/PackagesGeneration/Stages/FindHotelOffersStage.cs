using Esky.Packages.Application.Abstractions.Gateways.DataAnalyticsGateway;
using Esky.Packages.Application.Abstractions.Gateways.HotelOfferGateway;
using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.Dtos.HotelOffers;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Application.PackagesGeneration.State.Components;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

/// <summary>
/// 1. Chunk current hotel stay time ranges by hotel meta codes limit per chunk.
/// 2. Fetch hotel offers for each chunk.
/// </summary>
public class FindHotelOffersStage(IHotelOfferGateway hotelOfferGateway) : PipelineStageBase<PackagePipelineContext>
{
    private const int MaxDegreeOfParallelism = 8;
    private const int MetaCodeCountPerChunk = 250;

    protected override async Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var hotelStayTimeRanges = context.State.ChunkHotelStayTimeRangeState.CurrentChunk;
        context.Logger.LogInformation("Hotel stay time ranges: {count}", hotelStayTimeRanges.Count);

        context.Logger.LogInformation("Chunking time ranges per {metaCodeCountPerChunk} meta codes", MetaCodeCountPerChunk);

        var timeRangesWithChunks = hotelStayTimeRanges
            .SelectMany(timeRange => timeRange.MetaCodes
                .Chunk(MetaCodeCountPerChunk)
                .Select(chunk => (timeRange, chunk)))
            .ToArray();
        context.Logger.LogInformation("Hotel stay time ranges chunked: {count}", timeRangesWithChunks.Length);

        int nextChunk = 0;
        var locker = new object();

        var hotelOffersOccupancies = CreateHotelOffersOccupancies(context);

        await Parallel.ForEachAsync(timeRangesWithChunks,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = MaxDegreeOfParallelism,
                CancellationToken = cancellationToken
            },
            async (timeRangeWithChunk, token) =>
            {
                var (timeRange, chunk) = timeRangeWithChunk;
                var currentChunkIndex = Interlocked.Increment(ref nextChunk);

                context.Logger.LogInformation("Chunk {chunk}/{totalChunks} start, ({min})",
                    currentChunkIndex, timeRangesWithChunks.Length, timeRange.CheckIn);

                var hotelOffers = await GetHotelOffers(context, hotelOffersOccupancies, timeRange, chunk, token);

                var hotelOffersCount = 0;
                lock (locker)
                {
                    foreach (var hotelOffer in hotelOffers)
                    {
                        context.State.ChunkHotelStayTimeRangeState.HotelOffers.Add(hotelOffer);
                        hotelOffersCount++;
                    }
                }
                
                context.Logger.LogInformation("Chunk {chunk}/{totalChunks} completed, ({min}), offers: {offers}",
                    currentChunkIndex, timeRangesWithChunks.Length, timeRange.CheckIn, hotelOffersCount);
            });

        context.Logger.LogInformation("HotelOffers: {total}", context.State.ChunkHotelStayTimeRangeState.HotelOffers.Count);
    }

    private static HotelOfferOccupancy[] CreateHotelOffersOccupancies(PackagePipelineContext context)
    {
        return context
            .DefinitionParameters
            .Occupancies
            .Select(x =>
            {
                var occupancy = x.ToOccupancy();
                return new HotelOfferOccupancy(adults: occupancy.Adults, children: occupancy.ChildrenAges);
            })
            .ToArray();
    }

    private async Task<IEnumerable<HotelOffer>> GetHotelOffers(PackagePipelineContext context, 
        HotelOfferOccupancy[] occupancies, HotelStayTimeRange timeRange, int[] metaCodes, CancellationToken token)
    {
        var providerConfigurationIds = context.State.Market.HotelOfferProviderConfigurationIds
            .Select(p => p.ToString())
            .ToArray();
        
        if (metaCodes.Length == 0 || occupancies.Length == 0 || providerConfigurationIds.Length == 0)
        {
            return [];
        }
        
        var searchParameters = new HotelOfferSearchCriteria(
            CheckIn: timeRange.CheckIn,
            StayLength: timeRange.StayLength,
            ProviderConfigurationIds: providerConfigurationIds,
            MetaCodes: metaCodes,
            Occupancies: occupancies);

        return await hotelOfferGateway.Search(searchParameters, token);
    }
}