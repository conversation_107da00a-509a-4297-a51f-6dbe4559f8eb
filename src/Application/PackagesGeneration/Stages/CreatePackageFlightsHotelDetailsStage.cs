using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

/// <summary>
/// 1. Create package flight hotel details which contains meta codes for each package flight. 
///    It will be used to create PackageHotelOffers.
/// </summary>
public class CreatePackageFlightsHotelDetailsStage : PipelineStageBase<PackagePipelineContext>
{
    protected override Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var packageFlightsHotelDetails = context
            .State
            .ChunkFlightDestinationsTimeRangeState
            .PackageFlights
            .Select(x =>
            {
                var metaCodes = context.DefinitionParameters.ArrivalAirportCodeToMetaCodesMap
                    .TryGetValue(x.Id.ArrivalAirport, out var result) ? result : [];
                
                return new State.Components.PackageFlightHotelDetails(
                    Id: x.Id,
                    CheckIn: x.Id.CheckIn,
                    StayLength: x.Id.StayLength,
                    DepartureAirport: x.Id.DepartureAirport,
                    ArrivalAirport: x.Id.ArrivalAirport,
                    MetaCodes: metaCodes,
                    Prices: x.GetLowestPricesByOccupancy());
            })
            .ToList();

        context.Logger.LogInformation("Created packages flight hotel details: {total}", packageFlightsHotelDetails.Count);

        context.State.PackageFlightsHotelDetails.AddRange(packageFlightsHotelDetails);

        return Task.CompletedTask;
    }
}
