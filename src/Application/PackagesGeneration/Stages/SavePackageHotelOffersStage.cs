using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.Observability;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

/// <summary>
/// 1. Save package hotel offers to the database from current chunk range.
/// </summary>
public class SavePackageHotelOffersStage(IPackageHotelOfferRepository repository) : PipelineStageBase<PackagePipelineContext>
{
    protected override async Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var allPackagesCount = context.State.ChunkHotelStayTimeRangeState.PackageHotelOffers.Count;
        var packagesCount = context.State.ChunkHotelStayTimeRangeState.PackageHotelOffersToUpsert.Count;
        if (packagesCount > 0)
        {
            await repository.UpsertWithoutConcurrency(
                context.State.ChunkHotelStayTimeRangeState.PackageHotelOffersToUpsert, cancellationToken);
        }

        ApplicationMetrics.RegisterGeneratedPackageHotelOffers(allPackagesCount, packagesCount);

        context.Logger.LogInformation("Upserted {packagesCount} package hotels, skipped {unchangedPackagesCount}",
            packagesCount, allPackagesCount - packagesCount);
    }
}
