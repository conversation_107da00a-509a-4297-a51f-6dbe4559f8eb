using Esky.Packages.Application.Abstractions.Gateways.FlightGateway;
using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.Dtos.FlightOffers;
using Esky.Packages.Application.PackagesGeneration.Exceptions;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Application.PackagesGeneration.State.Components;
using Esky.Packages.Domain.Model.PackageFlights;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

/// <summary>
/// 1. Fetch flight offers for one check in date and stay length for multiple departure airports and one arrival airport
/// </summary>
public class FindFlightsStage(IFlightGateway flightGateway) : PipelineStageBase<PackagePipelineContext>
{
    protected override async Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var chunk = context.State.ChunkFlightDestinationsTimeRangeState.CurrentChunk;

        var flights = await GetFlightOffers(context, chunk, cancellationToken);
        
        context.State.ChunkFlightDestinationsTimeRangeState.FlightOffers = flights.ToList();
        
        context.Logger.LogInformation("Chunk completed, flights: {flightCount}", 
            context.State.ChunkFlightDestinationsTimeRangeState.FlightOffers.Count);
    }

    private async Task<IEnumerable<FlightOffer>> GetFlightOffers(
        PackagePipelineContext context,
        FlightDestinationsTimeRange currentChunk,
        CancellationToken token)
    {
        var flightCriteria = new FlightSearchCriteria
        {
            PartnerCode = context.DefinitionParameters.PartnerCode,
            BaseCurrency = context.State.Market.Currency,
            Destinations = BuildDestinations(currentChunk),
            MaxStop = context.DefinitionParameters.FlightCriteria.MaxStop,
            MaxLegDuration = context.DefinitionParameters.FlightCriteria.MaxLegDuration,
            CheckIn = currentChunk.CheckIn,
            StayLengths = currentChunk.StayLengths,
            EnableInboundOutboundDepartureHours = context.State.Market.EnableInboundOutboundFlightDepartureHours
        };

        return await flightGateway.GetFlightOffers(flightCriteria, token);
    }

    private static FlightSearchCriteria.Destination[] BuildDestinations(
        FlightDestinationsTimeRange currentChunk)
    {
        var destinations = currentChunk
            .DepartureAirportCodes
            .Join(currentChunk.ArrivalAirportCodes, _ => true, _ => true, (d, a) => new FlightSearchCriteria.Destination
            {
                DepartureAirport = d,
                ArrivalAirport = a
            })
            .Where(x => !string.IsNullOrEmpty(x.ArrivalAirport) && !string.IsNullOrEmpty(x.DepartureAirport))
            .ToArray();

        if (destinations.Length == 0)
            throw new NoDestinationsException();

        return destinations;
    }
}