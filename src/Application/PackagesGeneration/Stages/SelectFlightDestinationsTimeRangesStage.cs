using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Application.PackagesGeneration.State.Components;
using Esky.Packages.Domain.Model.PackageDefinitions.TimeCriterias.DurationStrategies;
using Esky.Packages.Domain.Model.PackageFlights;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

/// <summary>
/// 1. Calculate flight destinations time ranges, so we can fetch flight offers for one check in date and one stay length for multiple departures.
/// </summary>
public class SelectFlightDestinationsTimeRangesStage : PipelineStageBase<PackagePipelineContext>
{
    protected override Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var checkInDurationStrategy = context
            .DefinitionParameters
            .TimeCriteria
            .DurationStrategy
            .AsDerivedOrThrow<CheckInDurationStrategy>();

        var flightDestinationsTimeRanges = CreateDestinationTimeRanges(checkInDurationStrategy, context).ToList();
        context.State.FlightDestinationsTimeRanges = flightDestinationsTimeRanges;
        context.Logger.LogInformation("Created flight destinations time ranges: {count}",
            flightDestinationsTimeRanges.Count);

        return Task.CompletedTask;
    }

    private static IEnumerable<FlightDestinationsTimeRange> CreateDestinationTimeRanges(
        CheckInDurationStrategy checkInDurationStrategy,
        PackagePipelineContext context)
    {
        var arrivalAirports = context.DefinitionParameters.FlightCriteria.ArrivalAirports.Length > 0
            ? context.DefinitionParameters.FlightCriteria.ArrivalAirports
            : context.DefinitionParameters.AllArrivalAirportCodes;
        
        for (var checkIn = checkInDurationStrategy.MinCheckIn;
             checkIn <= checkInDurationStrategy.MaxCheckIn;
             checkIn = checkIn.AddDays(1))
        {
            foreach (var arrivalAirportCodes in arrivalAirports.Chunk(1))
            {
                yield return new FlightDestinationsTimeRange(
                    CheckIn: checkIn,
                    StayLengths: context.State.FlightStayLengths,
                    DepartureAirportCodes: context.DefinitionParameters.FlightCriteria.DepartureAirportCodes,
                    ArrivalAirportCodes: arrivalAirportCodes);
            }
        }
    }
}