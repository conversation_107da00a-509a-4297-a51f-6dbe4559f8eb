using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Model.PackageHotelAirports;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

public class SavePackageHotelAirportsStage(IPackageHotelAirportsRepository packageHotelAirportsRepository)
    : PipelineStageBase<PackagePipelineContext>
{
    protected override async Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var marketId = context.DefinitionParameters.MarketId;
        var definitionId = context.State.TargetDefinitionId;

        var airportHotelTuples = context.DefinitionParameters.ArrivalAirportCodeToMetaCodesMap
            .SelectMany(x => x.Value.Select(y => (Airport: x.Key, HotelMetaCode: y)));

        if (context.DefinitionParameters.FlightCriteria.ArrivalAirports.Length > 0)
        {
            var flightCriteriaArrivalAirports = context.DefinitionParameters.FlightCriteria.ArrivalAirports.ToHashSet();
            airportHotelTuples = airportHotelTuples.Where(x => flightCriteriaArrivalAirports.Contains(x.Airport));
        }

        var packageHotelAirports = airportHotelTuples
            .GroupBy(x => x.HotelMetaCode, x => x.Airport)
            .ToDictionary(x => x.Key, x => x.Select(a => new Airport(a)).ToList())
            .Select(x => PackageHotelAirports.Create(new PackageHotelAirportsId(marketId, x.Key), x.Value, 
                definitionId))
            .ToList();

        if (packageHotelAirports.Count > 0)
        {
            await packageHotelAirportsRepository.Upsert(packageHotelAirports, cancellationToken);
        }

        await packageHotelAirportsRepository.RemoveExceptMetaCodes(marketId,
            packageHotelAirports.Select(a => a.Id.MetaCode).ToArray(), definitionId, cancellationToken);
    }
}