using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Factories;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

/// <summary>
/// 1. Group hotel offers by check in, stay length and meta code.
/// 2. For each group.
///     - Calculate airports based on previous stages for each meta code.
///     - Create hotel quotes (prices) per providerConfigurationId and occupancy
/// </summary>
public class CreatePackageHotelOffersStage(IPackageHotelOfferFactory packageHotelOfferFactory)
    : PipelineStageBase<PackagePipelineContext>
{
    protected override Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var packageHotelOffers = packageHotelOfferFactory
            .CreateMany(
                hotelOffers: context.State.ChunkHotelStayTimeRangeState.HotelOffers,
                marketId: context.State.Market.Id,
                definitionId: context.State.TargetDefinitionId,
                currency: context.State.Market.Currency,
                occupancies: context.DefinitionParameters.Occupancies,
                providerConfigurationIds: context.State.Market.HotelOfferProviderConfigurationIds,
                metaCodeByArrivalAirportByDepartureAirport: context.State.MetaCodeByArrivalAirportByDepartureAirport)
            .ToList();

        context.Logger.LogInformation("Created package hotel offers: {count}", packageHotelOffers.Count);
        context.State.ChunkHotelStayTimeRangeState.PackageHotelOffers = packageHotelOffers;

        CleanUpPreviousStepState(context);

        return Task.CompletedTask;
    }

    private static void CleanUpPreviousStepState(PackagePipelineContext context)
    {
        context.State.ChunkHotelStayTimeRangeState.HotelOffers.Clear();
    }
}