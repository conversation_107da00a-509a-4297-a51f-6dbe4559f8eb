using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

/// <summary>
/// 1. Remove outdated package flights (which are not existing in a state) from the partition time ranges.
/// </summary>
public class RemoveOutdatedPackageFlightsStage(IPackageFlightRepository packageFlightRepository) : PipelineStageBase<PackagePipelineContext>
{
    protected override async Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var timeRanges = context.State.FlightDestinationsTimeRanges
            .Select(x => (x.CheckIn, x.StayLengths))
            .Distinct()
            .ToList();
        
        var packageFlightsIds = context.State.PackageFlightsHotelDetails
            .Select(x => x.Id);

        var removedPackageFlights = await packageFlightRepository.RemoveFromTimeRangesExceptSpecificIds(
            definitionId: context.State.TargetDefinitionId,
            timeRanges: timeRanges,
            ids: packageFlightsIds,
            cancellationToken: cancellationToken);

        context.Logger.LogInformation("Removed {count} package flights from current time ranges", removedPackageFlights);
    }
}