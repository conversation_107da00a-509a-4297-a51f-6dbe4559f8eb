using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.PackagesGeneration.State;
using Esky.Packages.Application.PackagesGeneration.State.Components;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.PackagesGeneration.Stages;

/// <summary>
/// 1. Process hotel stay time ranges in chunks.
/// </summary>
public class ChunkHotelStayTimeRangesStage(PipelineStageBase<PackagePipelineContext> stage) : PipelineStageBase<PackagePipelineContext>
{
    protected override async Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        foreach (var timeRange in context.State.HotelStayTimeRanges)
        {
            context.State.ChunkHotelStayTimeRangeState = new ChunkHotelStayTimeRangeState([timeRange]);
            await stage.Run(context, cancellationToken);
        }
    }
}