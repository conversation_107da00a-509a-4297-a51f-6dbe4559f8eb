namespace Esky.Packages.Application.PackagesGeneration;

public record PackagePipelineOptions
{
    public string PipelineId { get; init; } = null!;
    public string StartedByUserId { get; init; } = null!;
    public string PipelineGroupId { get; init; } = null!;
    public int TotalPartitions { get; init; }
    public int Partition { get; init; }
    public GenerationMode GenerationMode { get; init; }
}