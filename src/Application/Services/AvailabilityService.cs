using Esky.Packages.Contract.Availabilities;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;
using Esky.Packages.Domain.Types.Extensions;

namespace Esky.Packages.Application.Services;

public interface IAvailabilityService
{
    Task<DepartureDatesDto> GetDepartureDates(string marketId, int stayLength, Airport[] departureAirports,
        Airport[] arrivalAirports, Occupancy[] occupancies, CancellationToken cancellationToken = default);
}

internal class AvailabilityService(IPackageFlightVariantRepository packageFlightVariantRepository) : IAvailabilityService
{
    public async Task<DepartureDatesDto> GetDepartureDates(string marketId, int stayLength, Airport[] departureAirports,
        Airport[] arrivalAirports, Occupancy[] occupancies, CancellationToken cancellationToken = default)
    {
        var combinedOccupancy = occupancies.Merge();
        var flightOccupancy = PackageOccupancy.FromOccupancy(combinedOccupancy).ToFlightSeatsOccupancy();

        var departureDates = await packageFlightVariantRepository.GetAvailableDepartureDates(
            marketId, stayLength, departureAirports, arrivalAirports, flightOccupancy, cancellationToken);

        return new DepartureDatesDto(departureDates.ToArray());
    }
}
