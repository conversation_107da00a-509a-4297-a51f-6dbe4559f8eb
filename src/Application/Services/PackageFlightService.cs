using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.Services;

public interface IPackageFlightService
{
    Task<(int TruePositives, int FalsePositives, int Updated)> ApplyQuotes(List<FlightQuote> quotes, 
        CancellationToken cancellationToken = default);
}

internal class PackageFlightService(IPackageFlightRepository repository, ILogger<PackageFlightService> logger) 
    : IPackageFlightService
{
    public async Task<(int TruePositives, int FalsePositives, int Updated)> ApplyQuotes(List<FlightQuote> quotes, 
        CancellationToken cancellationToken)
    {
        var foundFlightIds = new HashSet<string>();
        
        var quotesByFlightId = quotes
            .GroupBy(q => q.FlightId)
            .ToDictionary(g => g.Key, g => g.MaxBy(q => q.UpdateTime)!);
        
        var flightIds = quotesByFlightId.Keys.ToList();
        var flightIdAndPartitionKeyPairs = flightIds
            .Select(f => (f, PackageFlightPartitionKey.FromFlightId(f)))
            .ToList();
        
        var packageFlights = await repository.ListByFlightIdAndPartitionKeyPairs(flightIdAndPartitionKeyPairs, cancellationToken);
        
        var updatedPackageFlights = new List<PackageFlight>();
        
        foreach (var packageFlight in packageFlights)
        {
            var updated = packageFlight.ApplyQuotes(quotesByFlightId);
            if (updated)
            {
                updatedPackageFlights.Add(packageFlight);
            }

            foreach (var flightId in packageFlight.GetFlightIds())
            {
                foundFlightIds.Add(flightId);
            }
        }

        if (updatedPackageFlights.Count > 0)
        {
            await repository.Update(updatedPackageFlights, cancellationToken);
            logger.LogDebug("Updated {count} package flights", updatedPackageFlights.Count);
        }

        var falsePositives = flightIds.Except(foundFlightIds).Count();
        var truePositives = flightIds.Count - falsePositives;
        
        logger.LogDebug("Applied {count} quotes, true positives: {truePositives}, false positives: {falsePositives}", 
            quotes.Count, truePositives, falsePositives);
        
        return (truePositives, falsePositives, updatedPackageFlights.Count);
    }
}