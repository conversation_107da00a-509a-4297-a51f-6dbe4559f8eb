using Esky.Packages.Application.Abstractions.Gateways.CurrencyConverter;

namespace Esky.Packages.Application.Services;

public interface ICurrencyConverterService
{
    decimal Convert(decimal amount, string fromCurrency, string toCurrency);
    Task RefreshRates(CancellationToken cancellationToken = default);
}

internal class CurrencyConverterService(ICurrencyConverterGateway gateway) : ICurrencyConverterService
{
    private readonly Dictionary<(string FromCurrency, string ToCurrency), decimal> _rates = new();

    public decimal Convert(decimal amount, string fromCurrency, string toCurrency)
    {
        if (string.IsNullOrEmpty(fromCurrency))
            throw new ArgumentNullException(nameof(fromCurrency));
        if (string.IsNullOrEmpty(toCurrency))
            throw new ArgumentNullException(nameof(toCurrency));
        
        var fromCurrencyNormalized = NormalizeCurrency(fromCurrency);
        var toCurrencyNormalized = NormalizeCurrency(toCurrency);
        
        if (fromCurrencyNormalized == toCurrencyNormalized)
            return amount;

        lock (_rates)
        {
            if (!_rates.TryGetValue((fromCurrencyNormalized, toCurrencyNormalized), out var rate))
                throw new InvalidOperationException($"Rate from {fromCurrency} to {toCurrency} not found");
            
            return Math.Round(amount * rate, 2, MidpointRounding.ToEven);
        }
    }

    public async Task RefreshRates(CancellationToken cancellationToken)
    {
        var rates = await gateway.GetRates(cancellationToken);
        
        lock (_rates)
        {
            _rates.Clear();
            foreach (var rate in rates)
            {
                _rates[(NormalizeCurrency(rate.FromCurrency), NormalizeCurrency(rate.ToCurrency))] = rate.Rate;
            }
        }
    }
    
    private static string NormalizeCurrency(string currency)
    {
        return currency.ToUpper();
    }
}