using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Contract.PackagesGeneration.GenerationAggregatedStatistics;
using Esky.Packages.Contract.PackagesGeneration.GenerationDetailedStatistics;

namespace Esky.Packages.Application.Services;

public interface IPackageGenerationStatisticsService
{
    Task<GenerationsAggregatedStatisticsDto> GenerationsAggregatedStatistics(CancellationToken cancellationToken = default);
    Task<GenerationDetailedStatisticsDto?> GenerationDetailedStatistics(string definitionId, CancellationToken cancellationToken);
}

internal class PackageGenerationStatisticsService(IPipelineRepository pipelineRepository) 
    : IPackageGenerationStatisticsService
{
    public async Task<GenerationsAggregatedStatisticsDto> GenerationsAggregatedStatistics(CancellationToken cancellationToken)
    {
        var statistics = await pipelineRepository.GenerationsAggregatedStatistics(cancellationToken);

        var latestRunsDtos = statistics
            .Select(x => new GenerationAggregatedStatisticsDto(
                DefinitionId: x.DefinitionId,
                PipelineGroupId: x.PipelineGroupId,
                LatestFinishedAt: x.LatestFinishedAt,
                Status: MapStatus(x.GetStatus()),
                CompletedSuccessRate: x.GetCompletedSuccessRate()))
            .ToArray();

        return new GenerationsAggregatedStatisticsDto(LatestRuns: latestRunsDtos);
    }

    private static GenerationAggregatedStatisticsStatusDto MapStatus(GenerationAggregatedStatisticsStatus status)
    {
        return status switch
        {
            GenerationAggregatedStatisticsStatus.Waiting => GenerationAggregatedStatisticsStatusDto.Waiting,
            GenerationAggregatedStatisticsStatus.Running => GenerationAggregatedStatisticsStatusDto.Running,
            GenerationAggregatedStatisticsStatus.RunningWithFailures => GenerationAggregatedStatisticsStatusDto.RunningWithFailures,
            GenerationAggregatedStatisticsStatus.CompletedWithSuccess => GenerationAggregatedStatisticsStatusDto.CompletedWithSuccess,
            GenerationAggregatedStatisticsStatus.CompletedWithFailures => GenerationAggregatedStatisticsStatusDto.CompletedWithFailures,
            _ => throw new ArgumentOutOfRangeException(nameof(status), status, null)
        };
    }

    public async Task<GenerationDetailedStatisticsDto?> GenerationDetailedStatistics(string definitionId, CancellationToken cancellationToken)
    {
        var statistics = await pipelineRepository.GenerationDetailedStatistics(definitionId, cancellationToken);
        if (statistics is null)
        {
            return null;
        }

        var failedPipelinesDtos = statistics
            .FailedPipelines
            .Select(x => new GenerationDetailedStatisticsFailedPipelineDto(
                PipelineId: x.PipelineId,
                ErrorMessage: x.ErrorMessage))
            .ToArray();

        return new GenerationDetailedStatisticsDto(
            DefinitionId: statistics.DefinitionId,
            PipelineGroupId: statistics.PipelineGroupId,
            FailedPipelines: failedPipelinesDtos,
            Completed: statistics.Completed,
            Running: statistics.Running,
            Failed: statistics.Failed,
            Waiting: statistics.Waiting,
            Total: statistics.Total,
            LatestFinishedAt: statistics.LatestFinishedAt);
    }
}
