using Esky.Packages.Domain.Model.PackageDefinitions;
using Esky.Packages.Domain.Repositories;

namespace Esky.Packages.Application.Services;

public interface IPackageDefinitionService
{
    Task<PackageDefinition> Update(string packageDefinitionId, PackageDefinition packageDefinition, CancellationToken cancellationToken = default);
}

internal class PackageDefinitionService(IPackageDefinitionRepository repository) : IPackageDefinitionService
{
    public async Task<PackageDefinition> Update(string packageDefinitionId, PackageDefinition packageDefinition, CancellationToken cancellationToken)
    {
        if (packageDefinition.Id != packageDefinitionId)
        {
            throw new InvalidOperationException("Package definition id cannot be changed.");
        }

        return await repository.Update(packageDefinition, cancellationToken);
    }
}
