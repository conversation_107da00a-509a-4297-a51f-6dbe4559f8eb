using Esky.Packages.Application.Abstractions.Gateways.FlightGateway;
using Esky.Packages.Application.Dtos.Common;
using Esky.Packages.Application.Exceptions;
using Esky.Packages.Contract.Calendars;
using Esky.Packages.Domain.Factories;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.Markets;
using Esky.Packages.Domain.Model.PackageFlightVariants;
using Esky.Packages.Domain.Model.Packages;
using Esky.Packages.Domain.Model.PackageVariants;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;
using Esky.Packages.Domain.Types.Extensions;

namespace Esky.Packages.Application.Services;

public record CalendarsParameters(
    string MarketId,
    int MetaCode,
    DateOnly DepartureDateFrom,
    DateOnly DepartureDateTo,
    Airport[] DepartureAirports,
    MealPlan[] MealPlans,
    TimeOfDay[] InboundDepartures,
    TimeOfDay[] OutboundDepartures,
    Occupancy[] Occupancies);

public record CalendarAggregationParameters(
    string MarketId,
    int MetaCode);

public interface ICalendarService
{
    Task<CalendarsDto> GetCalendars(CalendarsParameters parameters, CancellationToken cancellationToken = default);

    Task<CalendarAggregationDto> GetCalendarAggregation(CalendarAggregationParameters parameters,
        CancellationToken cancellationToken = default);
}

internal class CalendarService(
    IMarketRepository marketRepository,
    IPackageFlightRepository packageFlightRepository,
    IPackageFlightVariantRepository packageFlightVariantRepository,
    IPackageHotelOfferRepository packageHotelOfferRepository,
    IPackageHotelAirportsRepository packageHotelAirportsRepository,
    IPackageDefinitionRepository packageDefinitionRepository,
    IPackageFlightFactory packageFlightFactory,
    IPackageHotelOfferService packageHotelOfferService,
    IFlightGateway flightGateway,
    IPriceTrackerService priceTrackerService)
    : ICalendarService
{
    public async Task<CalendarsDto> GetCalendars(CalendarsParameters parameters,
        CancellationToken cancellationToken = default)
    {
        var market = await marketRepository.GetById(parameters.MarketId, cancellationToken);
        if (market == null)
        {
            throw new MarketNotFoundException($"Market with id {parameters.MarketId} not found.");
        }

        var packageHotelAirports = (await packageHotelAirportsRepository.ListByMarketIdAndMetaCodes(market.Id,
            [parameters.MetaCode], cancellationToken)).ToArray();
        var arrivalAirports = packageHotelAirports.SelectMany(m => m.Airports).Distinct().ToArray();

        if (arrivalAirports.Length == 0)
        {
            throw new PackageFlightNotFoundException(
                $"No package flight variants found for market {market.Id} and meta code {parameters.MetaCode}.");
        }

        var definitionId = packageHotelAirports.Select(m => m.DefinitionId).Distinct().First();
        var definition = await packageDefinitionRepository.GetById(definitionId, cancellationToken);
        if (definition == null)
        {
            throw new PackageDefinitionNotFoundException(
                $"Package definition with id {definitionId} not found for market {market.Id} and meta code {parameters.MetaCode}.");
        }

        var stayLengths = definition.Parameters.TimeCriteria.DurationStrategy.StayLengths;
        
        var packageFlightVariantsTask = GetPackageFlightVariants(market, parameters.Occupancies,
            parameters.DepartureDateFrom, parameters.DepartureDateTo, stayLengths, parameters.DepartureAirports, 
            arrivalAirports, parameters.InboundDepartures, parameters.OutboundDepartures, cancellationToken);

        var packageHotelOffersTask = packageHotelOfferService.GetPackageHotelOffers(market,
            parameters.MetaCode, parameters.DepartureDateFrom, parameters.DepartureDateTo, cancellationToken);

        await Task.WhenAll(packageFlightVariantsTask, packageHotelOffersTask);

        var packageFlightVariants = await packageFlightVariantsTask;
        var packageHotelOffers = await packageHotelOffersTask;

        var packageHotelOfferVariants = packageHotelOfferService
            .GetPackageHotelOfferVariants(packageHotelOffers, parameters.Occupancies, parameters.MealPlans);

        var packageVariantsToEmitPriceHistoryEvents = new List<PackageVariant>();
        
        var calendars = PackageVariant
            .CreateMany(packageFlightVariants, packageHotelOfferVariants, packageHotelAirports)
            .GroupBy(c => (c.Id.DepartureDate.Year, c.Id.DepartureDate.Month))
            .OrderBy(c => (c.Key.Year, c.Key.Month))
            .Select(g => new CalendarDto
            (
                Year: g.Key.Year,
                Month: g.Key.Month,
                Days: g
                    .GroupBy(c => c.Id.DepartureDate.Day)
                    .OrderBy(c => c.Key)
                    .ToDictionary(g2 => g2.Key, g2 => g2
                        .GroupBy(c => c.Id.StayLength)
                        .OrderBy(c => c.Key)
                        .ToDictionary(g3 => g3.Key, g3 =>
                        {
                            var cheapestVariant = g3.MinBy(c => c.Price)!;
                            if (market.EmitPriceHistoryEvents)
                            {
                                packageVariantsToEmitPriceHistoryEvents.Add(cheapestVariant);
                            }

                            return new CalendarEntryDto
                            (
                                PackageId: new PackageId(cheapestVariant.Id.CheckIn, cheapestVariant.Id.StayLength, 
                                    cheapestVariant.Id.MarketId, cheapestVariant.Id.MetaCode),
                                PackageVariantId: cheapestVariant.Id,
                                FlightOfferId: cheapestVariant.FlightOfferId,
                                DepartureAirport: cheapestVariant.Id.DepartureAirport,
                                CheckIn: cheapestVariant.Id.CheckIn,
                                ReturnArriveDate: cheapestVariant.ReturnArrivalDate,
                                MealPlan: cheapestVariant.Id.MealPlan.ToDto(),
                                Price: cheapestVariant.Price
                            );
                        }))
            )).ToList();

        if (market.EmitPriceHistoryEvents)
        {
            await priceTrackerService.EmitPriceHistoryEvents(packageVariantsToEmitPriceHistoryEvents, market, cancellationToken);
        }

        return new CalendarsDto(calendars);
    }

    public async Task<CalendarAggregationDto> GetCalendarAggregation(CalendarAggregationParameters parameters,
        CancellationToken cancellationToken = default)
    {
        var packageHotelOffers = await packageHotelOfferRepository.ListByMarketAndMetaCode(parameters.MarketId,
            parameters.MetaCode, cancellationToken);

        if (packageHotelOffers.Count == 0)
        {
            return new CalendarAggregationDto([], [], [], DateOnly.MaxValue, DateOnly.MinValue);
        }

        var mealPlans = packageHotelOffers
            .SelectMany(o => o.GetMealPlans())
            .Distinct()
            .ToList();

        var occupancies = packageHotelOffers
            .SelectMany(o => o.GetOccupancies())
            .Distinct()
            .ToList();

        var departureAirports = packageHotelOffers
            .SelectMany(o => o.GetDepartureAirports())
            .Distinct()
            .ToList();
        
        // TODO: Use in the future departure dates from package flights
        var minDepartureDate = packageHotelOffers.Min(o => o.Id.CheckIn);
        var maxDepartureDate = packageHotelOffers.Max(o => o.Id.CheckIn);

        return new CalendarAggregationDto(
            departureAirports.Select(a => a.ToString()).ToList(),
            mealPlans.Select(m => m.ToDto()).ToList(),
            occupancies.Select(o => o.ToOccupancy().ToDto()).ToList(),
            minDepartureDate, 
            maxDepartureDate);
    }

    private async Task<PackageFlightVariant[]> GetPackageFlightVariants(Market market, Occupancy[] occupancies,
        DateOnly departureDateFrom, DateOnly departureDateTo, int[] stayLenghts, Airport[] departureAirports, 
        Airport[] arrivalAirports, TimeOfDay[] inboundDepartures, TimeOfDay[] outboundDepartures, 
        CancellationToken cancellationToken)
    {
        var mergedOccupancy = GetMergedOccupancy(occupancies);
        var flightOccupancy = GetFlightSeatsOccupancy(occupancies);

        var packageFlightVariants = await packageFlightVariantRepository.GetCheapestsPerDates(
            marketId: market.Id,
            departureDateFrom: departureDateFrom,
            departureDateTo: departureDateTo,
            stayLengths: stayLenghts,
            departureAirports: departureAirports,
            arrivalAirports: arrivalAirports,
            occupancy: flightOccupancy,
            inboundDepartures: inboundDepartures,
            outboundDepartures: outboundDepartures,
            cancellationToken);
     
        packageFlightVariants = await UpdateFlightVariantPrices(market, packageFlightVariants, mergedOccupancy,
            cancellationToken);
        
        return packageFlightVariants;
    }

    // TODO: Move this method from here and SearchService to some common place
    private async Task<PackageFlightVariant[]> UpdateFlightVariantPrices(Market market,
        PackageFlightVariant[] packageFlightVariants, PackageOccupancy packageOccupancy, 
        CancellationToken cancellationToken)
    {
        var flightOfferIds = packageFlightVariants
            .Select(f => f.OfferId)
            .ToHashSet();

        var flightOffers = await flightGateway.GetFlightsByOfferKeyAsync(
            new FlightByOfferKeySearchCriteria(flightOfferIds, packageOccupancy.ToOccupancy(), market.PartnerCode,
                true, false), cancellationToken);

        var updatedPackageFlights = packageFlightFactory.CreateMany(flightOffers, market.Id, $"{market.Id}-dynamic",
            market.Currency, [packageOccupancy]);

        return updatedPackageFlights.SelectMany(f => f.GetVariants(packageOccupancy)).ToArray();
    }

    // TODO: Move to domain or refactor
    private static PackageOccupancy GetMergedOccupancy(Occupancy[] occupancies)
    {
        var mergedOccupancy = occupancies.Merge();
        var packageOccupancy = PackageOccupancy.FromOccupancy(mergedOccupancy);

        return packageOccupancy;
    }

    // TODO: Move to domain or refactor
    private static PackageOccupancy GetFlightSeatsOccupancy(Occupancy[] occupancies)
    {
        var packageOccupancy = GetMergedOccupancy(occupancies);

        return packageOccupancy.ToFlightSeatsOccupancy();
    }
}