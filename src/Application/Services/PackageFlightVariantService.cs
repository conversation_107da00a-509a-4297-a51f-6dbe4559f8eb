using Esky.Packages.Domain.Events.PackageFlightVariants;
using Esky.Packages.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.Services;

public interface IPackageFlightVariantService
{
    Task UpdateVariants(List<PackageFlightVariantEvent> packageVariants, CancellationToken cancellationToken = default);
}

internal class PackageFlightVariantService(
    IPackageFlightVariantRepository repository, 
    ILogger<PackageFlightVariantService> logger) 
    : IPackageFlightVariantService
{
    public async Task UpdateVariants(List<PackageFlightVariantEvent> packageVariants, CancellationToken cancellationToken)
    {
        await repository.UpdateVariants(packageVariants, cancellationToken);
    }
}