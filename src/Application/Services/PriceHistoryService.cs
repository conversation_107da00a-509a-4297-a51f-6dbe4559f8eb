using Esky.Packages.Application.Exceptions;
using Esky.Packages.Contract.Packages;
using Esky.Packages.Domain.Model.Markets;
using Esky.Packages.Domain.Model.PackageVariants;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Application.Services;

public interface IPriceHistoryService
{
    Task<PackageVariantsPriceHistoryResponseDto> GetHistoryAsync(
        IReadOnlyCollection<(PackageVariantId Id, DateOnly FromPriceDate, DateOnly ToPriceDate)> packageVariantDateRanges,
        CancellationToken cancellationToken);
}

public class PriceHistoryService(
    IPackageFlightVariantPriceRepository flightPriceRepository,
    IPackageHotelOfferVariantPriceRepository hotelPriceRepository,
    IMarketRepository marketRepository) 
    : IPriceHistoryService
{
    public async Task<PackageVariantsPriceHistoryResponseDto> GetHistoryAsync(
        IReadOnlyCollection<(PackageVariantId Id, DateOnly FromPriceDate, DateOnly To<PERSON>rice<PERSON>ate)> packageVariantDateRanges,
        CancellationToken cancellationToken)
    {
        var market = await GetMarket(packageVariantDateRanges, cancellationToken);

        var flightPricesTask = flightPriceRepository.GetByMultiplePackageVariantsAndDateRange(
            packageVariantDateRanges: packageVariantDateRanges, 
            cancellationToken: cancellationToken);
        var hotelPricesTask = hotelPriceRepository.GetByMultiplePackageVariantsAndDateRange(
            packageVariantDateRanges: packageVariantDateRanges,
            cancellationToken: cancellationToken);

        await Task.WhenAll(flightPricesTask, hotelPricesTask);

        var flightPrices = await flightPricesTask;
        var hotelPrices = await hotelPricesTask;

        var packageVariants = new List<PackageVariantPriceHistoryDto>();

        foreach (var packageVariantDateRange in packageVariantDateRanges)
        {
            var packageVariantFlightPrices = flightPrices.Where(x => x.Id.IsMatching(packageVariantDateRange.Id)).ToList();
            var packageVariantHotelPrices = hotelPrices.Where(x => x.Id.IsMatching(packageVariantDateRange.Id)).ToList();

            var combinedPrices = CombinePrices(packageVariantFlightPrices, packageVariantHotelPrices, packageVariantDateRange.Id);

            packageVariants.Add(new PackageVariantPriceHistoryDto(packageVariantDateRange.Id.ToString(), combinedPrices));
        }

        return new PackageVariantsPriceHistoryResponseDto(
            PackageVariants: packageVariants,
            TimeZone: market.TimeZone.Id,
            Currency: market.Currency);
    }

    private async Task<Market> GetMarket(
        IReadOnlyCollection<(PackageVariantId Id, DateOnly FromPriceDate, DateOnly ToPriceDate)> packageVariantDateRanges, 
        CancellationToken cancellationToken)
    {
        var marketIds = packageVariantDateRanges
            .Select(v => v.Id.MarketId)
            .Distinct()
            .ToList();
        if (marketIds.Count > 1)
        {
            throw new InvalidOperationException("Price history for multiple markets is not supported.");
        }

        var marketId = marketIds.First();
        var market = await marketRepository.GetById(marketId, cancellationToken);
        if (market is null)
        {
            throw new MarketNotFoundException(marketId);
        }

        return market;
    }

    private static List<PriceHistoryPointDto> CombinePrices(
        List<Domain.Model.PackageFlightVariantPrices.PackageFlightVariantPrice> flightPrices,
        List<Domain.Model.PackageHotelOfferVariantPrices.PackageHotelOfferVariantPrice> hotelPrices,
        PackageVariantId packageVariant)
    {
        var flightPricesByDate = BuildFlightPricesByDate(flightPrices, packageVariant.BaggageIncluded);
        var hotelPricesByDate = BuildHotelPricesByDate(hotelPrices, packageVariant.Refundability, packageVariant.RoomIds);

        var combinedPrices = new List<PriceHistoryPointDto>();

        foreach (var date in flightPricesByDate.Keys.Intersect(hotelPricesByDate.Keys))
        {
            var totalPrice = flightPricesByDate[date] + hotelPricesByDate[date];
            combinedPrices.Add(new PriceHistoryPointDto(date, totalPrice));
        }

        return combinedPrices
            .OrderBy(p => p.Date)
            .ToList();
    }

    private static Dictionary<DateOnly, int> BuildFlightPricesByDate(
        List<Domain.Model.PackageFlightVariantPrices.PackageFlightVariantPrice> flightPrices,
        bool baggageIncluded)
    {
        return flightPrices
            .GroupBy(fp => fp.Id.PriceDate)
            .Where(g => g.Any(fp => fp.Prices.Any(kvp => kvp.Key.BaggageIncluded == baggageIncluded)))
            .ToDictionary(
                g => g.Key,
                g => g.SelectMany(fp => fp.Prices
                        .Where(kvp => kvp.Key.BaggageIncluded == baggageIncluded)
                        .Select(kvp => kvp.Value))
                      .Min());
    }

    private static Dictionary<DateOnly, int> BuildHotelPricesByDate(
        List<Domain.Model.PackageHotelOfferVariantPrices.PackageHotelOfferVariantPrice> hotelPrices,
        Refundability refundability,
        RoomIds roomIds)
    {
        return hotelPrices
            .GroupBy(hp => hp.Id.PriceDate)
            .Where(g => g.Any(hp => hp.Prices.Any(kvp => kvp.Key.Refundability == refundability && kvp.Key.RoomIds.Equals(roomIds))))
            .ToDictionary(
                g => g.Key,
                g => g.SelectMany(hp => hp.Prices
                        .Where(kvp => kvp.Key.Refundability == refundability && kvp.Key.RoomIds.Equals(roomIds))
                        .Select(kvp => kvp.Value))
                      .Min());
    }
}
