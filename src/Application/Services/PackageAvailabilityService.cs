using Esky.Packages.Application.Dtos.PackageAvailabilities;
using Esky.Packages.Contract.PackageAvailabilities;
using Esky.Packages.Domain.Model.PackageAvailabilities;
using Esky.Packages.Domain.Repositories;

namespace Esky.Packages.Application.Services;

public interface IPackageAvailabilityService
{
    Task<PackageAvailabilitiesDto> GetByDefinitionId(string definitionId, CancellationToken cancellationToken = default);
}

internal class PackageAvailabilityService(IPackageAvailabilitiesRepository packageAvailabilitiesRepository) : IPackageAvailabilityService
{
    public async Task<PackageAvailabilitiesDto> GetByDefinitionId(string definitionId, CancellationToken cancellationToken = default)
    {
        var result = new Dictionary<int, PackageAvailability>();
        var availabilities = await packageAvailabilitiesRepository.GetByDefinitionId(definitionId, cancellationToken);

        foreach (var packagesAvailability in availabilities)
        {
            foreach (var packageAvailability in packagesAvailability.Availabilities)
            {
                if (result.TryGetValue(packageAvailability.MetaCode, out var availability))
                {
                    availability.Merge(packageAvailability);
                }
                else
                {
                    result[packageAvailability.MetaCode] = packageAvailability;
                }
            }
        }

        var dtos = result
            .Values
            .Select(x => x.ToDto())
            .ToArray();

        return new PackageAvailabilitiesDto(dtos);
    }
}