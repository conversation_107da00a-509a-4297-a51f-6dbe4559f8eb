using Esky.Packages.Application.Abstractions.Gateways.PriceTrackerGateway;
using Esky.Packages.Domain.Model.PackageVariants;
using Esky.Packages.Domain.Model.Markets;
using Esky.Packages.Domain.Events.PackageFlightVariants;
using Esky.Packages.Domain.Events;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.Services;

public interface IPriceTrackerService
{
    Task EmitPriceHistoryEvents(
        IEnumerable<PackageVariant> packageVariants,
        Market market,
        CancellationToken cancellationToken);
}

internal class PriceTrackerService(
    IPriceTrackerGateway priceTrackerGateway,
    TimeProvider timeProvider,
    ILogger<PriceTrackerService> logger)
    : IPriceTrackerService
{
    public async Task EmitPriceHistoryEvents(
        IEnumerable<PackageVariant> packageVariants,
        Market market,
        CancellationToken cancellationToken)
    {
        var priceEventData = packageVariants
            .Select(variant => CreatePriceEventData(variant, market))
            .ToArray();
        var flightPriceEvents = priceEventData
            .Select(data => data.FlightEvent)
            .Distinct()
            .ToArray();
        var hotelPriceEvents = priceEventData
            .Select(data => data.HotelEvent)
            .Distinct()
            .ToArray();

        try
        {
            await Task.WhenAll(
                priceTrackerGateway.PublishEvents(flightPriceEvents, cancellationToken),
                priceTrackerGateway.PublishEvents(hotelPriceEvents, cancellationToken));
        }
        catch (Exception e)
        {
            logger.LogError(e, "Failed to emit price history events for package variants");

            // TODO: rethrow?
        }
    }

    private PriceEventData CreatePriceEventData(PackageVariant variant, Market market)
    {
        var utcDateTime = timeProvider.GetUtcNow().UtcDateTime;
        var priceDate = market.GetMarketDateOnlyForUtc(utcDateTime);

        var flightEvent = new PackageFlightVariantPriceEvent(
            DepartureDate: variant.Id.DepartureDate, 
            ReturnDepartureDate: variant.Id.ReturnDepartureDate, 
            MarketId: variant.Id.MarketId, 
            ArrivalAirport: variant.Id.ArrivalAirport, 
            DepartureAirport: variant.Id.DepartureAirport, 
            FlightNumbers: variant.Id.FlightNumbers, 
            ReturnFlightNumbers: variant.Id.ReturnFlightNumbers, 
            Occupancy: variant.Id.Occupancy, 
            PriceDate: priceDate, 
            Price: variant.PriceBreakdown.FlightVariantPrice, 
            BaggageIncluded: variant.Id.BaggageIncluded);

        var hotelEvent = new PackageHotelOfferVariantPriceEvent(
            CheckIn: variant.Id.CheckIn,
            StayLength: variant.Id.StayLength,
            MarketId: variant.Id.MarketId,
            MetaCode: variant.Id.MetaCode,
            Occupancy: variant.Id.Occupancy,
            MealPlan: variant.Id.MealPlan,
            Refundability: variant.Id.Refundability,
            RoomIds: variant.Id.RoomIds,
            PriceDate: priceDate,
            Price: variant.PriceBreakdown.HotelOfferVariantPrice);

        return new PriceEventData(flightEvent, hotelEvent);
    }

    internal record PriceEventData(
        PackageFlightVariantPriceEvent FlightEvent,
        PackageHotelOfferVariantPriceEvent HotelEvent);
}
