using Esky.Packages.Application.Exceptions;
using Esky.Packages.Contract.Markets;
using Esky.Packages.Domain.Model.Markets;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Application.Services;

public interface IMarketsService
{
    Task<MarketDto?> GetById(string id, CancellationToken cancellationToken = default);
    Task<IReadOnlyCollection<MarketDto>> GetAll(CancellationToken cancellationToken = default);
    Task<MarketDto> Create(MarketDto marketDto, CancellationToken cancellationToken = default);
    Task<MarketDto> Upsert(string marketId, MarketDto marketDto, CancellationToken cancellationToken = default);
    Task<bool> Delete(string id, CancellationToken cancellationToken = default);
}

internal class MarketsService(IMarketRepository marketRepository) : IMarketsService
{
    public async Task<MarketDto?> GetById(string id, CancellationToken cancellationToken = default)
    {
        var market = await marketRepository.GetById(id, cancellationToken);
        return market is null ? null : MapToDto(market);
    }

    public async Task<IReadOnlyCollection<MarketDto>> GetAll(CancellationToken cancellationToken = default)
    {
        var markets = await marketRepository.GetAll(cancellationToken);
        return markets.Select(MapToDto).ToList();
    }

    public async Task<MarketDto> Create(MarketDto marketDto, CancellationToken cancellationToken = default)
    {
        var timeZone = TimeZoneInfo.FindSystemTimeZoneById(marketDto.TimeZone);
        
        var market = Market.Create(
            marketDto.Id,
            new Currency(marketDto.Currency),
            marketDto.PartnerCode,
            marketDto.DepartureAirports.Select(a => new Airport(a)).ToArray(),
            marketDto.HotelOfferProviderConfigurationIds.Select(p => new ProviderConfigurationId(p)).ToArray(),
            marketDto.EnableInboundOutboundFlightDepartureHours,
            marketDto.EmitPriceHistoryEvents,
            timeZone
        );

        await marketRepository.Add(market, cancellationToken);
        
        return MapToDto(market);
    }

    public async Task<MarketDto> Upsert(string marketId, MarketDto marketDto, CancellationToken cancellationToken = default)
    {
        if (marketId != marketDto.Id)
        {
            throw new InvalidOperationException("Market id cannot be changed.");
        }

        var timeZone = TimeZoneInfo.FindSystemTimeZoneById(marketDto.TimeZone);

        var newMarket = Market.Create(
            marketDto.Id,
            new Currency(marketDto.Currency),
            marketDto.PartnerCode,
            marketDto.DepartureAirports.Select(a => new Airport(a)).ToArray(),
            marketDto.HotelOfferProviderConfigurationIds.Select(p => new ProviderConfigurationId(p)).ToArray(),
            marketDto.EnableInboundOutboundFlightDepartureHours,
            marketDto.EmitPriceHistoryEvents,
            timeZone
        );
        
        var market = await marketRepository.GetById(marketId, cancellationToken);
        if (market is null)
        {
            market = newMarket;
        }
        else
        {
            market.Update(newMarket);
        }

        await marketRepository.Upsert(market, cancellationToken);
        
        return MapToDto(market);
    }

    public async Task<bool> Delete(string id, CancellationToken cancellationToken = default)
    {
        // TODO: Check if there are any package definitions related to this market
        return await marketRepository.Remove(id, cancellationToken);
    }

    private static MarketDto MapToDto(Market market)
    {
        return new MarketDto(
            market.Id,
            market.Currency.ToString(),
            market.PartnerCode,
            market.DepartureAirports.Select(a => a.ToString()).ToList(),
            market.HotelOfferProviderConfigurationIds.Select(p => p.ToString()).ToList(),
            market.EnableInboundOutboundFlightDepartureHours,
            market.EmitPriceHistoryEvents,
            market.TimeZone.Id
        );
    }
}