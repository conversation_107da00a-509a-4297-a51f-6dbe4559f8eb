using Esky.Packages.Domain.Model.PackageFlightVariantPrices;
using Esky.Packages.Domain.Repositories;

namespace Esky.Packages.Application.Services;

public interface IPackageFlightVariantPriceService
{
    Task<int> ApplyPrices(List<PackageFlightVariantPrice> prices, CancellationToken cancellationToken);
}

public class PackageFlightVariantPriceService(IPackageFlightVariantPriceRepository repository) : IPackageFlightVariantPriceService
{
    public async Task<int> ApplyPrices(List<PackageFlightVariantPrice> newPrices, CancellationToken cancellationToken)
    {
        var dict = new Dictionary<PackageFlightVariantPriceId, PackageFlightVariantPrice>();

        var ids = newPrices.Select(p => p.Id).Distinct().ToList();
        var existingPrices = (await repository.GetByIds(ids, cancellationToken)).ToDictionary(x => x.Id);

        foreach (var newPrice in newPrices)
        {
            if (dict.TryGetValue(newPrice.Id, out var value))
            {
                value.ApplyPrices(newPrice.Prices);
                continue;
            }
            
            if (existingPrices.TryGetValue(newPrice.Id, out var existingPrice))
            {
                var changed = existingPrice.ApplyPrices(newPrice.Prices);
                if (changed) dict.Add(existingPrice.Id, existingPrice);
                continue;
            }
            
            dict.Add(newPrice.Id, newPrice);
        }

        var toUpsert = dict.Select(x => x.Value).ToList();

        if (toUpsert.Count > 0)
        {
            await repository.Upsert(toUpsert, cancellationToken);
        }

        return toUpsert.Count;
    }
}