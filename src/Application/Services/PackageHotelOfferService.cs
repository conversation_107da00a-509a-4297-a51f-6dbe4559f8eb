using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.Markets;
using Esky.Packages.Domain.Model.PackageFlightVariants;
using Esky.Packages.Domain.Model.PackageHotelAirports;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Model.PackageHotelOfferVariants;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.Services;

public interface IPackageHotelOfferService
{
    Task<(int TruePositives, int FalsePositives, int Updated)> ApplyQuotes(List<HotelOffer> quotes, 
        CancellationToken cancellationToken = default);

    Task<PackageHotelOfferVariant[]> GetPackageHotelOfferVariants(
        PackageFlightVariant[] packageFlightVariants, PackageHotelAirports[] packageHotelAirports,
        Occupancy[] occupancies, MealPlan[] mealPlans, CancellationToken cancellationToken = default);

    Task<ICollection<PackageHotelOffer>> GetPackageHotelOffers(Market market, int metaCode, DateOnly departureDateFrom, 
        DateOnly departureDateTo, CancellationToken cancellationToken = default);

    ICollection<PackageHotelOfferVariant> GetPackageHotelOfferVariants(
        IEnumerable<PackageHotelOffer> packageHotelOffers, Occupancy[] occupancies, MealPlan[] mealPlans);
}

internal class PackageHotelOfferService(IPackageHotelOfferRepository packageHotelOfferRepository, 
    ILogger<PackageHotelOfferService> logger) 
    : IPackageHotelOfferService
{
    public async Task<(int TruePositives, int FalsePositives, int Updated)> ApplyQuotes(List<HotelOffer> quotes, 
        CancellationToken cancellationToken)
    {
        var foundStayKeys = new HashSet<PackageHotelOfferStayKey>();
        
        var quotesByStayKey = quotes
            .GroupBy(q => new PackageHotelOfferStayKey(q.MetaCode, q.CheckIn, q.StayLength))
            .ToDictionary(g => g.Key, g => g.ToList());
        
        var stayKeys = quotesByStayKey.Keys.ToList();
        var packageHotelOffers = await packageHotelOfferRepository.ListByStayKeys(stayKeys, cancellationToken);
        
        var updatedPackageHotelOffers = new List<PackageHotelOffer>();
        
        foreach (var packageHotelOffer in packageHotelOffers)
        {
            var updated = packageHotelOffer.ApplyHotelOffersByStayKey(quotesByStayKey);
            if (updated)
            {
                updatedPackageHotelOffers.Add(packageHotelOffer);
            }
            
            foundStayKeys.Add(packageHotelOffer.GetStayKey());
        }
        
        if (updatedPackageHotelOffers.Count > 0)
        {
            await packageHotelOfferRepository.Update(updatedPackageHotelOffers, cancellationToken);
            logger.LogDebug("Updated {count} package hotel offers", updatedPackageHotelOffers.Count);
        }
        
        var falsePositives = stayKeys.Except(foundStayKeys).Count();
        var truePositives = stayKeys.Count - falsePositives;
        
        logger.LogDebug("Applied {count} quotes, true positives: {truePositives}, false positives: {falsePositives}", 
            quotes.Count, truePositives, falsePositives);
        
        return (truePositives, falsePositives, updatedPackageHotelOffers.Count);
    }
    
    public async Task<PackageHotelOfferVariant[]> GetPackageHotelOfferVariants(
        PackageFlightVariant[] packageFlightVariants, PackageHotelAirports[] packageHotelAirports,
        Occupancy[] occupancies, MealPlan[] mealPlans, CancellationToken cancellationToken)
    {
        var packageOccupancies = occupancies
            .Select(PackageOccupancy.FromOccupancy)
            .ToArray();

        // If no supported package occupancy found, return empty array
        if (packageOccupancies.Length != occupancies.Length)
        {
            return [];
        }

        var packageHotelOfferIds = GetHotelOfferIds(packageFlightVariants, packageHotelAirports);
        if (packageHotelOfferIds.Length == 0)
        {
            return [];
        }

        var packageHotelOffers = await packageHotelOfferRepository.ListByIds(packageHotelOfferIds, cancellationToken);
        if (packageHotelOffers.Count == 0)
        {
            return [];
        }

        var packageHotelOfferVariants = packageHotelOffers
            .SelectMany(o => PackageHotelOfferVariant.CreateMany(o, packageOccupancies, mealPlans))
            .ToArray();
        
        return packageHotelOfferVariants;
    }
    
    public async Task<ICollection<PackageHotelOffer>> GetPackageHotelOffers(Market market, int metaCode, 
        DateOnly departureDateFrom, DateOnly departureDateTo, CancellationToken cancellationToken)
    {
        var checkInFrom = PackageHotelOffer.GetMinimalCheckInDate(departureDateFrom);
        var checkInTo = PackageHotelOffer.GetMaximalCheckInDate(departureDateTo);

        var packageHotelOffers = await packageHotelOfferRepository.ListByMarketAndMetaCodeAndCheckInRange(market.Id, 
            metaCode, checkInFrom, checkInTo, cancellationToken);

        return packageHotelOffers;
    }

    public ICollection<PackageHotelOfferVariant> GetPackageHotelOfferVariants(
        IEnumerable<PackageHotelOffer> packageHotelOffers, Occupancy[] occupancies, MealPlan[] mealPlans)
    {
        var packageOccupancies = occupancies
            .Select(PackageOccupancy.FromOccupancy)
            .ToArray();

        if (packageOccupancies.Length != occupancies.Length)
        {
            return [];
        }

        var packageHotelOfferVariants = packageHotelOffers
            .SelectMany(o => PackageHotelOfferVariant.CreateMany(o, packageOccupancies, mealPlans))
            .ToArray();

        return packageHotelOfferVariants;
    }

    // TODO: Move to domain
    private PackageHotelOfferId[] GetHotelOfferIds(PackageFlightVariant[] packageFlightVariant, 
        PackageHotelAirports[] airportMappings)
    {
        var airportsToMetaCodes = airportMappings
            .SelectMany(m => m.Airports.Select(a => (ArrivalAirport: a, m.Id.MetaCode)))
            .GroupBy(g => g.ArrivalAirport)
            .ToDictionary(g => g.Key, g => g.Select(x => x.MetaCode).Distinct().ToList());

        return packageFlightVariant
            .SelectMany(f => airportsToMetaCodes.GetValueOrDefault(f.Id.ArrivalAirport, []).Select(metaCode =>
                new PackageHotelOfferId(
                    checkIn: f.Id.CheckIn,
                    stayLength: f.Id.StayLength,
                    marketId: f.Id.MarketId,
                    metaCode: metaCode)))
            .ToArray();
    }
}