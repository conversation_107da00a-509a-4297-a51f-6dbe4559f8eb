using Esky.Packages.Contract.Packages;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Model.Packages;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;
using Esky.Packages.Application.Abstractions.Gateways.FlightGateway;
using Esky.Packages.Application.Abstractions.Gateways.FlightLiveGateway;
using Esky.Packages.Application.Abstractions.Gateways.HotelTransactionGateway;
using Esky.Packages.Domain.Factories;
using Esky.Packages.Domain.Model.LiveVariants;
using Esky.Packages.Domain.Model.Markets;
using Esky.Packages.Domain.Model.PackageDefinitions.FlightCriterias;
using Esky.Packages.Domain.Model.PackageVariants;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Types.Extensions;
using Esky.Packages.Contract.Common;
using Esky.Packages.Application.Dtos.HotelOfferVariants;
using Esky.Packages.Application.Dtos.Common;
using Esky.Packages.Application.Exceptions;
using Esky.Packages.Domain.Model.PackageFlightVariants;
using Esky.Packages.Domain.Model.PackageHotelAirports;

namespace Esky.Packages.Application.Services;

public record PackageLiveParameters(
    PackageId PackageId,
    Occupancy[] Occupancies,
    string? FlightOptionId,
    Airport? PreferredDepartureAirport,
    Airport[]? SelectedDepartureAirports,
    Airport[]? FallbackDepartureAirports,
    TimeOfDay[]? InboundDepartures,
    TimeOfDay[]? OutboundDepartures,
    MealPlan[]? PreferredMealPlans);

public record PackageLiveAlternativesParameters(
    PackageId PackageId,
    string? HotelOfferId,
    Occupancy[] Occupancies,
    MealPlan[]? PreferredMealPlans);

public interface IPackageService
{
    Task<PackageDto?> GetById(string packageId, CancellationToken cancellationToken);

    Task<PackageMetaSearchDto[]> GetByIds(string[] packageIds, Occupancy[] occupancies,
        CancellationToken cancellationToken);

    Task<PackageLiveDto> GetLive(PackageLiveParameters parameters, CancellationToken cancellationToken);

    Task<PackageLiveAlternativesDto> GetLiveAlternatives(PackageLiveAlternativesParameters parameters,
        CancellationToken cancellationToken);
}

internal class PackageService(
    IPackageHotelOfferRepository packageHotelOfferRepository,
    IPackageFlightRepository packageFlightRepository,
    IMarketRepository marketRepository,
    IPackageHotelAirportsRepository packageHotelAirportsRepository,
    IPackageDefinitionRepository packageDefinitionRepository,
    IFlightGateway flightGateway,
    IFlightLiveGateway flightLiveGateway,
    IHotelTransactionGateway hotelTransactionGateway,
    IOfferAccuracyGateway offerAccuracyGateway,
    IFlightVariantFactory flightVariantFactory,
    IHotelOfferVariantFactory hotelOfferVariantFactory,
    IPackageFlightFactory packageFlightFactory,
    IPriceTrackerService priceTrackerService)
    : IPackageService
{
    public async Task<PackageDto?> GetById(string packageId, CancellationToken cancellationToken)
    {
        var pid = new PackageId(packageId);
        var phoId = new PackageHotelOfferId(pid.CheckIn, pid.StayLength, pid.MarketId, pid.MetaCode);

        var packageHotelOffers = await packageHotelOfferRepository.ListByIds([phoId], cancellationToken);
        var packageHotelOffer = packageHotelOffers.SingleOrDefault();

        if (packageHotelOffer == null)
        {
            return null;
        }

        var packageFlights = await GetPackageFlightsForPackageHotelOffer(packageHotelOffer, cancellationToken);
        if (packageFlights.Count == 0)
        {
            return null;
        }

        var packageDto = CreatePackageDto(packageHotelOffer, packageFlights);

        // TOOD: Move to dto?
        if (packageDto.FlightOffers.Length == 0 || packageDto.FlightOffers.All(f => f.Prices.Length == 0) ||
            packageDto.HotelOffers.Length == 0 || packageDto.HotelOffers.All(h => h.Prices.Count == 0))
        {
            // Skip packages with no prices
            return null;
        }

        return packageDto;
    }

    public async Task<PackageMetaSearchDto[]> GetByIds(string[] packageIds, Occupancy[] occupancies,
        CancellationToken cancellationToken)
    {
        var ids = packageIds
            .Select(id => new PackageId(id))
            .Select(pid => new PackageHotelOfferId(pid.CheckIn, pid.StayLength, pid.MarketId, pid.MetaCode))
            .ToList();
        
        var uniqueMarketIds = ids
            .Select(id => id.MarketId)
            .Distinct()
            .ToArray();

        if (uniqueMarketIds.Length > 1)
        {
            throw new ArgumentException($"All package IDs must belong to the same market. Found markets: {string.Join(", ", uniqueMarketIds)}");
        }

        var result = new List<PackageMetaSearchDto>();

        var packageHotelOffers = await packageHotelOfferRepository.ListByIds(ids, cancellationToken);

        var market = await marketRepository.GetById(uniqueMarketIds[0], cancellationToken);

        var allPackageFlightIds = packageHotelOffers
            .SelectMany(pho => pho.GetPackageFlightIds())
            .Distinct()
            .ToList();

        if (allPackageFlightIds.Count == 0) return result.ToArray();

        var allPackageFlights = await packageFlightRepository.ListByIds(allPackageFlightIds, cancellationToken);
        var packageFlightsByIdDictionary = allPackageFlights.ToDictionary(pf => pf.Id, pf => pf);

        var allFlightOfferKeys = allPackageFlights
            .SelectMany(pf => pf.FlightOffers)
            .Select(fo => fo.Id)
            .Distinct()
            .ToArray();

        var allFlightOffers =
            (await flightGateway.GetFlightsByOfferKeyAsync(
                new FlightByOfferKeySearchCriteria(
                    FlightOfferKeys: allFlightOfferKeys,
                    PartnerCode: market!.PartnerCode,
                    Occupancy: occupancies.Merge(),
                    IncludeFlightDetails: true,
                    IncludeLegLocators: false),
                cancellationToken))
            .ToArray();
        
        if (allFlightOffers.Length == 0) return result.ToArray();

        var flightOffersByIdDictionary = allFlightOffers.ToDictionary(fo => fo.Id, fo => fo);

        foreach (var packageHotelOffer in packageHotelOffers)
        {
            var packageFlightIds = packageHotelOffer.GetPackageFlightIds();
            var packageFlights = packageFlightIds
                .Where(id => packageFlightsByIdDictionary.ContainsKey(id))
                .Select(id => packageFlightsByIdDictionary[id])
                .ToList();

            if (packageFlights.Count == 0)
            {
                continue;
            }

            var packageDto = CreatePackageDtoWithPreloadedData(packageHotelOffer, packageFlights, flightOffersByIdDictionary, occupancies, market!);

            if (packageDto.FlightOffers.Length == 0 || packageDto.HotelOffers.Count == 0)
            {
                continue;
            }

            result.Add(packageDto);
        }

        return result.ToArray();
    }

    private static PackageDto CreatePackageDto(
        PackageHotelOffer packageHotelOffer,
        List<PackageFlight> packageFlights)
    {
        var flightOfferPrices = packageFlights
            .Select(packageFlight => new FlightOfferDto(
                ArrivalAirport: packageFlight.Id.ArrivalAirport,
                DepartureAirport: packageFlight.Id.DepartureAirport,
                Prices: packageFlight
                    .GetLowestPricesByOccupancy()
                    .Select(p => new FlightOccupancyPriceEntryDto(
                        Occupancy: p.Key.ToOccupancy().ToDto(),
                        Price: new FlightPriceEntryDto(
                            Id: p.Value.Id,
                            FlightIds: p.Value.FlightIds,
                            Price: p.Value.Price,
                            DepartureDate: p.Value.DepartureDate,
                            ReturnArrivalDate: p.Value.ReturnArrivalDate)))
                    .ToArray()
            ))
            .ToArray();

        var hotelOfferPrices = packageHotelOffer
            .GetPrices()
            .Select(x => new HotelOfferDto(
                Occupancy: x.Key.ToOccupancy().ToDto(),
                Prices: x.Value.ToDictionary(f => f.Key.ToString(), f => f.Value)))
            .ToArray();

        return new PackageDto(
            Id: packageHotelOffer.Id.ToString(),
            CheckIn: packageHotelOffer.Id.CheckIn,
            StayLength: packageHotelOffer.Id.StayLength,
            MetaCode: packageHotelOffer.Id.MetaCode,
            MarketId: packageHotelOffer.Id.MarketId,
            Currency: packageHotelOffer.Currency,
            FlightOffers: flightOfferPrices,
            HotelOffers: hotelOfferPrices);
    }

    private PackageMetaSearchDto CreatePackageDtoWithPreloadedData(
        PackageHotelOffer packageHotelOffer,
        List<PackageFlight> packageFlights,
        Dictionary<string, FlightOffer> flightOffersByIdDictionary,
        Occupancy[] requestedOccupancies,
        Market market)
    {
        var combinedOccupancy = requestedOccupancies.Merge();

        var flightOfferKeys = packageFlights
            .SelectMany(f => f.FlightOffers)
            .Select(p => p.Id)
            .Distinct()
            .ToArray();

        var flightOffers = flightOfferKeys
            .Where(flightOffersByIdDictionary.ContainsKey)
            .Select(key => flightOffersByIdDictionary[key])
            .ToArray();

        var flightOfferPrices = packageFlightFactory
            .CreateMany(flightOffers, market.Id, $"{market.Id}-dynamic", market.Currency, [PackageOccupancy.FromOccupancy(combinedOccupancy)])
            .Select(packageFlight =>
            {
                var pf = packageFlight.GetLowestPricesByOccupancy().First();

                return new FlightOfferMetaSearchDto(
                    ArrivalAirport: packageFlight.Id.ArrivalAirport,
                    DepartureAirport: packageFlight.Id.DepartureAirport,
                    Id: pf.Value.Id,
                    FlightIds: pf.Value.FlightIds,
                    Price: pf.Value.Price,
                    DepartureDate: pf.Value.DepartureDate,
                    ReturnArrivalDate: pf.Value.ReturnArrivalDate);
            })
            .ToArray();

        return new PackageMetaSearchDto(
            Id: packageHotelOffer.Id.ToString(),
            CheckIn: packageHotelOffer.Id.CheckIn,
            StayLength: packageHotelOffer.Id.StayLength,
            MetaCode: packageHotelOffer.Id.MetaCode,
            MarketId: packageHotelOffer.Id.MarketId,
            Currency: packageHotelOffer.Currency,
            FlightOffers: flightOfferPrices,
            HotelOffers: packageHotelOffer
                .GetPrices(requestedOccupancies.Select(PackageOccupancy.FromOccupancy).ToArray())
                .ToDictionary(x => x.Key.ToString(), x => x.Value.CompensatedPrice));
    }

    public async Task<PackageLiveDto> GetLive(PackageLiveParameters parameters, CancellationToken cancellationToken)
    {
        if (PackageId.IsOldPackageId(parameters.PackageId.ToString()))
        {
            throw new PackageNotFoundException("Old package ids are not supported. Please use the new format.");
        }

        var packageId = parameters.PackageId;

        var market = await marketRepository.GetById(packageId.MarketId, cancellationToken);
        if (market is null)
        {
            throw new MarketNotFoundException(packageId.MarketId);
        }

        var packageHotelAirports = await packageHotelAirportsRepository.GetByMarketIdAndMetaCode(market.Id,
            packageId.MetaCode, cancellationToken);
        if (packageHotelAirports == null)
        {
            throw new PackageNotFoundException("No arrival airports found for the package.");
        }

        var packageDefinition = await packageDefinitionRepository.GetById(packageHotelAirports.DefinitionId, 
            cancellationToken);
        if (packageDefinition == null)
        {
            throw new PackageDefinitionNotFoundException(packageHotelAirports.DefinitionId);
        }

        var definitionDepartureAirports = packageDefinition.Parameters.FlightCriteria.DepartureAirportCodes
            .Select(a => new Airport(a))
            .ToArray();
        var arrivalAirports = packageHotelAirports.Airports.ToArray();

        var combinedOccupancy = parameters.Occupancies.Merge();

        var flightTask = GetLiveFlightOffer(packageId, market, definitionDepartureAirports, arrivalAirports, 
            combinedOccupancy, parameters.FlightOptionId, parameters.PreferredDepartureAirport, 
            parameters.SelectedDepartureAirports, parameters.FallbackDepartureAirports, parameters.InboundDepartures,
            parameters.OutboundDepartures, packageDefinition.Parameters.FlightCriteria, cancellationToken);

        var hotelTask = GetLiveHotelOffers(packageId, market, parameters.Occupancies, parameters.PreferredMealPlans, cancellationToken);

        await Task.WhenAll(flightTask, hotelTask);

        var selectedFlightVariant = await flightTask;
        var hotelOfferVariants = await hotelTask;
        
        if (market.EmitPriceHistoryEvents)
        {
            var packageVariants = CreatePackageVariants(selectedFlightVariant, hotelOfferVariants.HotelOffers, 
                packageHotelAirports, packageId, combinedOccupancy);
            
            await priceTrackerService.EmitPriceHistoryEvents(packageVariants, market, cancellationToken);
        }

        return new PackageLiveDto(
            Id: packageId.ToString(),
            Occupancies: parameters.Occupancies.Select(o => o.ToDto()).ToArray(),
            MetaCode: packageId.MetaCode,
            MarketId: packageId.MarketId,
            Currency: market.Currency,
            FlightOffer: CreateFlightOfferLiveDto(selectedFlightVariant),
            HotelOffers: hotelOfferVariants.HotelOffers.Select(CreateHotelOfferLiveDto).ToArray(),
            Prices: CreatePackageLiveVariantPriceDtos([selectedFlightVariant], hotelOfferVariants.HotelOffers.ToArray(), 
                packageId, combinedOccupancy));
    }

    private static IEnumerable<PackageVariant> CreatePackageVariants(FlightLiveVariant flightLiveVariant,
        IEnumerable<HotelOfferLiveVariant> hotelOfferVariants, PackageHotelAirports packageHotelAirports,
        PackageId packageId, Occupancy combinedOccupancy)
    {
        var packageOccupancy = PackageOccupancy.FromOccupancy(combinedOccupancy);

        List<PackageFlightVariant> packageFlightVariants =
        [
            flightLiveVariant.ToPackageFlightVariant(packageId.MarketId, packageId.MetaCode, packageOccupancy,
                packageId.CheckIn, packageId.StayLength)
        ];
        var packageHotelOfferVariants = hotelOfferVariants.Select(h =>
            h.ToPackageHotelOfferVariant(packageId.MarketId, packageId.MetaCode, packageOccupancy)).ToList();
        List<PackageHotelAirports> packageHotelAirportsList = [packageHotelAirports];

        return PackageVariant.CreateMany(packageFlightVariants, packageHotelOfferVariants, packageHotelAirportsList);
    }

    public async Task<PackageLiveAlternativesDto> GetLiveAlternatives(PackageLiveAlternativesParameters parameters,
        CancellationToken cancellationToken)
    {
        if (PackageId.IsOldPackageId(parameters.PackageId.ToString()))
        {
            throw new PackageNotFoundException("Old package ids are not supported. Please use the new format.");
        }

        var packageId = parameters.PackageId;

        var market = await marketRepository.GetById(packageId.MarketId, cancellationToken);
        if (market is null)
        {
            throw new MarketNotFoundException(packageId.MarketId);
        }

        var packageHotelAirports = await packageHotelAirportsRepository.GetByMarketIdAndMetaCode(market.Id,
            packageId.MetaCode, cancellationToken);
        if (packageHotelAirports == null)
        {
            throw new PackageNotFoundException("No arrival airports found for the package.");
        }

        var packageDefinition = await packageDefinitionRepository.GetById(packageHotelAirports.DefinitionId, 
            cancellationToken);
        if (packageDefinition == null)
        {
            throw new PackageDefinitionNotFoundException(packageHotelAirports.DefinitionId);
        }

        var departureAirports = packageDefinition.Parameters.FlightCriteria.DepartureAirportCodes
            .Select(a => new Airport(a))
            .ToArray();
        var arrivalAirports = packageHotelAirports.Airports.ToArray();

        var combinedOccupancy = parameters.Occupancies.Merge();

        var flightTask = GetLiveAlternativeFlightOffers(packageId, market, departureAirports, arrivalAirports,
            combinedOccupancy, packageDefinition.Parameters.FlightCriteria, cancellationToken);

        var hotelTask = GetLiveHotelOffer(packageId, market, parameters.HotelOfferId, parameters.Occupancies, 
            parameters.PreferredMealPlans, cancellationToken);

        await Task.WhenAll(flightTask, hotelTask);

        var alternativeFlightVariants = await flightTask;
        var hotelOfferVariant = await hotelTask;

        if (alternativeFlightVariants.Length == 0)
        {
            throw new PackageFlightNotFoundException("No alternative flight variants found for the package.");
        }

        var flightOfferLiveDtos = alternativeFlightVariants.Select(CreateFlightOfferLiveDto).ToArray();
        var hotelOfferLiveDto = CreateHotelOfferLiveDto(hotelOfferVariant);
        var packageLiveVariantPriceDtos = CreatePackageLiveVariantPriceDtos(alternativeFlightVariants, 
            [hotelOfferVariant], packageId, combinedOccupancy);

        return new PackageLiveAlternativesDto(
            Id: packageId.ToString(),
            Occupancies: parameters.Occupancies.Select(o => o.ToDto()).ToArray(),
            MetaCode: packageId.MetaCode,
            MarketId: packageId.MarketId,
            Currency: market.Currency,
            FlightOffers: flightOfferLiveDtos,
            HotelOffer: hotelOfferLiveDto,
            Prices: packageLiveVariantPriceDtos);
    }

    private async Task<List<PackageFlight>> GetPackageFlightsForPackageHotelOffer(PackageHotelOffer packageHotelOffer,
        CancellationToken cancellationToken)
    {
        var packageFlightsIds = packageHotelOffer.GetPackageFlightIds();

        return await packageFlightRepository
            .ListByIds(packageFlightsIds, cancellationToken);
    }

    private async Task<FlightLiveVariant?> GetSelectedFlightVariant(PackageId packageId, Market market,
        Airport[] definitionDepartureAirports, Airport[] arrivalAirports, Occupancy combinedOccupancy,
        string? flightOptionId, Airport? preferredDepartureAirport, Airport[]? selectedDepartureAirports,
        Airport[]? fallbackDepartureAirports, TimeOfDay[]? inboundDepartures, TimeOfDay[]? outboundDepartures,
        FlightCriteria flightCriteria, CancellationToken cancellationToken)
    {
        // TODO: Do I need to get packageFlight from database first?

        if (!string.IsNullOrEmpty(flightOptionId))
        {
            var specificFlightOffers = await flightGateway.GetFlightsByOfferKeyAsync(
                new FlightByOfferKeySearchCriteria(
                    FlightOfferKeys: [flightOptionId],
                    PartnerCode: market.PartnerCode,
                    Occupancy: combinedOccupancy,
                    IncludeFlightDetails: true,
                    IncludeLegLocators: true),
                cancellationToken);

            var unavailableOffers = await offerAccuracyGateway.GetUnavailableFlightOffers(
                [flightOptionId], combinedOccupancy, cancellationToken);

            var availableSpecificOffers = specificFlightOffers
                .Where(x => !unavailableOffers.Contains(x.Id))
                .Select(MapToFlightLiveVariant)
                .Where(f => FilterByDepartureTime(f, inboundDepartures, outboundDepartures))
                .ToArray();

            if (availableSpecificOffers.Length > 0)
            {
                return availableSpecificOffers.First();
            }
        }

        var alternativeFlightOffers = (await flightGateway.GetAlternativeFlightOffers(
            new AlternativeFlightOffersCriteria(
                PartnerCode: market.PartnerCode,
                CheckIn: packageId.CheckIn,
                StayLength: packageId.StayLength,
                DepartureAirports: definitionDepartureAirports.Select(a => a.ToString()).ToArray(),
                ArrivalAirports: arrivalAirports.Select(a => a.ToString()).ToArray(),
                Occupancy: combinedOccupancy,
                MaxStops: flightCriteria.MaxStop,
                MaxLegDuration: flightCriteria.MaxLegDuration,
                IncludeLegLocators: true,
                Limit: 5),
            cancellationToken)).ToList();

        var unavailableAlternativeOffers = await offerAccuracyGateway.GetUnavailableFlightOffers(
            alternativeFlightOffers.Select(x => x.Id).ToArray(), combinedOccupancy, cancellationToken);

        var available = alternativeFlightOffers
            .Where(x => !unavailableAlternativeOffers.Contains(x.Id))
            .Select(MapToFlightLiveVariant)
            .OrderBy(x => x.Price)
            .ToList();

        Airport[] preferredDepartureAirports =
            preferredDepartureAirport != null ? [(Airport)preferredDepartureAirport] : [];

        var selected = Pick(preferredDepartureAirports,
            available.Where(f => FilterByDepartureTime(f, inboundDepartures, outboundDepartures)));
        selected ??= Pick(preferredDepartureAirports, available);
        selected ??= Pick(selectedDepartureAirports,
            available.Where(f => FilterByDepartureTime(f, inboundDepartures, outboundDepartures)));
        selected ??= Pick(selectedDepartureAirports, available);
        selected ??= Pick(fallbackDepartureAirports,
            available.Where(f => FilterByDepartureTime(f, inboundDepartures, outboundDepartures)));
        selected ??= Pick(fallbackDepartureAirports, available);
        selected ??= Pick(definitionDepartureAirports,
            available.Where(f => FilterByDepartureTime(f, inboundDepartures, outboundDepartures)));
        selected ??= Pick(definitionDepartureAirports, available);

        return selected;
    }

    private static FlightLiveVariant? Pick(Airport[]? airports, IEnumerable<FlightLiveVariant> flights)
    {
        return airports == null || airports.Length == 0
            ? null
            : flights.FirstOrDefault(v => airports.Contains(v.DepartureAirport));
    }

    private FlightLiveVariant MapToFlightLiveVariant(FlightOffer flightOffer)
    {
        var firstPrice = flightOffer.Prices.Values.First();
        var prices = flightOffer.Prices.Values
            .SelectMany(p => p.Prices.Values.Select(price => new Money(price, p.Currency)))
            .ToArray();

        return flightVariantFactory.Create(
            key: flightOffer.Id,
            departureAirport: flightOffer.DepartureAirport,
            arrivalAirport: flightOffer.ArrivalAirport,
            departureDate: flightOffer.DepartureDate,
            arrivalDate: flightOffer.ArrivalDate,
            returnDepartureDate: flightOffer.ReturnDepartureDate,
            returnArrivalDate: flightOffer.ReturnArrivalDate,
            flightNumbers: flightOffer.FlightNumbers,
            returnFlightNumbers: flightOffer.ReturnFlightNumbers,
            stops: flightOffer.Stops,
            providerCode: flightOffer.ProviderCode,
            airlineCodes: flightOffer.AirlineCodes,
            flightIds: flightOffer.FlightIds,
            legLocators: flightOffer.LegLocators ?? [],
            currency: firstPrice.Currency,
            prices: prices,
            registeredBaggageIncluded: flightOffer.BaggageIncluded);
    }

    private Task<PackageHotelOffer?> GetPackageHotelOffer(PackageId packageId, CancellationToken cancellationToken)
    {
        var packageHotelOfferId = new PackageHotelOfferId(
            packageId.CheckIn,
            packageId.StayLength,
            packageId.MarketId,
            packageId.MetaCode);

        return packageHotelOfferRepository.GetById(packageHotelOfferId, cancellationToken);
    }

    private async Task<FlightLiveVariant> GetLiveFlightOffer(
        PackageId packageId,
        Market market,
        Airport[] definitionDepartureAirports,
        Airport[] arrivalAirports,
        Occupancy combinedOccupancy,
        string? flightOptionId,
        Airport? preferredDepartureAirport,
        Airport[]? selectedDepartureAirports,
        Airport[]? fallbackDepartureAirports,
        TimeOfDay[]? inboundDepartures,
        TimeOfDay[]? outboundDepartures,
        FlightCriteria flightCriteria,
        CancellationToken cancellationToken)
    {
        var selectedFlightVariant = await GetSelectedFlightVariant(
            packageId,
            market,
            definitionDepartureAirports,
            arrivalAirports,
            combinedOccupancy,
            flightOptionId,
            preferredDepartureAirport,
            selectedDepartureAirports,
            fallbackDepartureAirports,
            inboundDepartures,
            outboundDepartures,
            flightCriteria,
            cancellationToken);

        if (selectedFlightVariant == null)
        {
            throw new PackageFlightNotFoundException("No flight variants found for the package.");
        }

        var liveCheck = await flightLiveGateway.LiveCheck(
            new LiveCheckCriteria(
                FlightOfferKey: selectedFlightVariant.Key,
                PartnerCode: market.PartnerCode,
                CurrencyCode: market.Currency,
                Occupancy: combinedOccupancy),
            cancellationToken);

        if (liveCheck is null ||
            liveCheck.Status is not FlightLiveCheckStatusDto.Available and not FlightLiveCheckStatusDto.Timeout)
        {
            throw new PackageFlightNotFoundException("Flight is not available.");
        }

        if (liveCheck.Price is not null)
        {
            selectedFlightVariant.UpdateLivePrice(
                price: new Money(liveCheck.Price.Value, liveCheck.Price.Currency),
                legLocators: liveCheck.LegLocators);
        }

        return selectedFlightVariant;
    }

    private async Task<HotelOfferLiveVariants> GetLiveHotelOffers(PackageId packageId, Market market,
        Occupancy[] occupancies, MealPlan[]? preferredMealPlans, CancellationToken cancellationToken)
    {
        var packageHotelOffer = await GetPackageHotelOffer(packageId, cancellationToken);

        var onlyRefundable = packageHotelOffer?.OnlyRefundable ??
                             PackageHotelOffer.ShouldBeOnlyRefundable(packageId.CheckIn);

        var hotelOfferVariants = await GetHotelOfferVariants(packageId, market.Currency, onlyRefundable,
            market.PartnerCode, occupancies, preferredMealPlans, cancellationToken);

        if (hotelOfferVariants.HotelOffers.Count == 0)
        {
            throw new PackageHotelOfferNotFoundException("No hotel offers found for the package.");
        }
        
        var packageOccupancies = new[] { PackageOccupancy.FromOccupancy(occupancies.Merge()) };
        var pricesByMealPlan = packageHotelOffer?.GetPrices(packageOccupancies);

        foreach (var (mealPlan, hotelOffersForMealPlan) in hotelOfferVariants.GetHotelOffersByMealPlan())
        {
            var cheapestPackageHotelOfferForMealPlan = pricesByMealPlan?.TryGetValue(mealPlan, out var price) == true
                ? (decimal?)price.CompensatedPrice
                : null;

            for (var i = 0; i < hotelOffersForMealPlan.Count; i++)
            {
                var hotelOffer = hotelOffersForMealPlan[i];

                var compensatedHotelPrice = (cheapestPackageHotelOfferForMealPlan, i) switch
                {
                    (null or 0m, _) => hotelOffer.Compensate(hotelOffer.Price),
                    (_, 0) => hotelOffer.Compensate(cheapestPackageHotelOfferForMealPlan),
                    _ => hotelOffer.Compensate(cheapestPackageHotelOfferForMealPlan, onlyUp: true)
                };
                
                hotelOffer.SetPriceCompensated(compensatedHotelPrice);
            }
        }
        
        return hotelOfferVariants;
    }

    private async Task<HotelOfferLiveVariant> GetLiveHotelOffer(PackageId packageId, Market market,
        string? hotelOfferId, Occupancy[] occupancies, MealPlan[]? preferredMealPlans, CancellationToken cancellationToken)
    {
        var hotelOfferVariants = await GetLiveHotelOffers(packageId, market, occupancies, preferredMealPlans, cancellationToken);

        if (hotelOfferVariants.HotelOffers.Count == 0)
        {
            throw new PackageHotelOfferNotFoundException("No hotel offers found for the package.");
        }
        
        var hotelOfferVariant = hotelOfferVariants.HotelOffers.FirstOrDefault(h => h.OfferId == hotelOfferId);
        
        return hotelOfferVariant ?? hotelOfferVariants.HotelOffers.MinBy(h => h.PriceCompensated)!;
    }

    private async Task<FlightLiveVariant[]> GetLiveAlternativeFlightOffers(PackageId packageId, Market market,
        Airport[] departureAirports, Airport[] arrivalAirports, Occupancy combinedOccupancy,
        FlightCriteria flightCriteria, CancellationToken cancellationToken)
    {
        var alternativeFlightOffers = (await flightGateway.GetAlternativeFlightOffers(
            new AlternativeFlightOffersCriteria(
                PartnerCode: market.PartnerCode,
                CheckIn: packageId.CheckIn,
                StayLength: packageId.StayLength,
                DepartureAirports: departureAirports.Select(a => a.ToString()).ToArray(),
                ArrivalAirports: arrivalAirports.Select(a => a.ToString()).ToArray(),
                Occupancy: combinedOccupancy,
                MaxStops: flightCriteria.MaxStop,
                MaxLegDuration: flightCriteria.MaxLegDuration,
                IncludeLegLocators: true,
                Limit: null),
            cancellationToken)).ToList();

        var unavailableAlternativeOffers = await offerAccuracyGateway.GetUnavailableFlightOffers(
            alternativeFlightOffers.Select(x => x.Id).ToArray(), combinedOccupancy, cancellationToken);

        var availableAlternativeOffers = alternativeFlightOffers
            .Where(x => !unavailableAlternativeOffers.Contains(x.Id))
            .Select(MapToFlightLiveVariant)
            .OrderBy(x => x.Price)
            .ToArray();

        return availableAlternativeOffers;
    }

    private static bool FilterByDepartureTime(FlightLiveVariant flightVariant, TimeOfDay[]? inboundDepartures,
        TimeOfDay[]? outboundDepartures)
    {
        var inboundMatch = inboundDepartures == null || inboundDepartures.Length == 0 ||
                           inboundDepartures.Contains(flightVariant.InboundDeparture);

        var outboundMatch = outboundDepartures == null || outboundDepartures.Length == 0 ||
                            outboundDepartures.Contains(flightVariant.OutboundDeparture);

        return inboundMatch && outboundMatch;
    }

    private async Task<HotelOfferLiveVariants> GetHotelOfferVariants(
        PackageId packageId,
        Currency currency,
        bool onlyRefundable,
        string partnerCode,
        Occupancy[] occupancies,
        MealPlan[]? preferredMealPlans,
        CancellationToken cancellationToken)
    {
        var variantsGroupedByRoomsDtos = await hotelTransactionGateway.GetVariantsGroupedByRooms(
            new HotelOfferVariantsCritieria(
                CheckIn: packageId.CheckIn,
                CheckOut: packageId.CheckIn.AddDays(packageId.StayLength),
                PartnerCode: partnerCode,
                MetaCode: packageId.MetaCode,
                Occupancies: occupancies
            ),
            cancellationToken);

        var hotelOfferIds = variantsGroupedByRoomsDtos
            .SelectMany(x => x.Values)
            .SelectMany(x => x)
            .Select(z => z.OfferId)
            .Distinct()
            .ToArray();

        var excludedHotelOfferIds = await offerAccuracyGateway.GetUnavailableHotelOfferIds(
            packageId.MetaCode,
            hotelOfferIds,
            packageId.CheckIn,
            packageId.CheckIn.AddDays(packageId.StayLength),
            cancellationToken);

        var hotelOfferVariantsGroupedByRooms = variantsGroupedByRoomsDtos
            .Select(x => x
                .Select(p =>
                    (Key: p.Key,
                        Value: p.Value
                            .Where(o => !excludedHotelOfferIds.Contains(o.OfferId))
                            .Select(o => hotelOfferVariantFactory
                                .Create(
                                    checkIn: packageId.CheckIn,
                                    stayLength: packageId.StayLength,
                                    offerId: o.OfferId,
                                    mealPlan: o.MealPlan.ToDomain(),
                                    refundability: o.Refundability.ToDomain(),
                                    freeRefundUntil: o.FreeRefundUntil,
                                    roomIds: o.RoomIds,
                                    currency: currency,
                                    price: new Money(o.Price.Value, o.Price.Currency),
                                    priceAtHotel: new Money(o.PriceAtHotel.Value, o.PriceAtHotel.Currency),
                                    availability: o.Availability))
                            .ToArray()))
                .Where(p => p.Value.Length > 0)
                .ToDictionary(p => p.Key, p => p.Value.AsEnumerable()))
            .ToArray();

        return HotelOfferLiveVariants.CreateCheapestHotelOfferVariantsByRooms(
            hotelOfferVariantsGroupedByRooms: hotelOfferVariantsGroupedByRooms,
            onlyRefundable: onlyRefundable,
            preferredMealPlans: preferredMealPlans);
    }

    private static FlightOfferLiveDto CreateFlightOfferLiveDto(FlightLiveVariant flightVariant)
    {
        return new FlightOfferLiveDto(
            OfferId: flightVariant.Key,
            DepartureAirport: flightVariant.DepartureAirport,
            ArrivalAirport: flightVariant.ArrivalAirport,
            DepartureDate: DateOnly.FromDateTime(flightVariant.DepartureDate),
            ArrivalDate: DateOnly.FromDateTime(flightVariant.ArrivalDate),
            ReturnDepartureDate: DateOnly.FromDateTime(flightVariant.ReturnDepartureDate),
            ReturnArrivalDate: DateOnly.FromDateTime(flightVariant.ReturnArrivalDate),
            FlightNumbers: flightVariant.FlightNumbers,
            ReturnFlightNumbers: flightVariant.ReturnFlightNumbers,
            Stops: flightVariant.Stops,
            FlightIds: flightVariant.FlightIds,
            LegLocators: flightVariant.LegLocators,
            RegisteredBaggageIncluded: flightVariant.RegisteredBaggageIncluded);
    }

    private static HotelOfferLiveDto CreateHotelOfferLiveDto(HotelOfferLiveVariant hotelOfferVariant)
    {
        return new HotelOfferLiveDto(
                OfferId: hotelOfferVariant.OfferId,
                CheckIn: hotelOfferVariant.CheckIn,
                StayLength: hotelOfferVariant.StayLength,
                MealPlan: hotelOfferVariant.MealPlan.ToDto(),
                FreeRefund: hotelOfferVariant.Refundability.IsRefundable,
                FreeRefundUntil: hotelOfferVariant.FreeRefundUntil,
                RoomIds: hotelOfferVariant.RoomIds,
                PriceAtHotel: new MoneyDto(
                    Value: hotelOfferVariant.PriceAtHotel.Value,
                    Currency: hotelOfferVariant.PriceAtHotel.Currency));
    }

    private static PackageLiveVariantPriceDto[] CreatePackageLiveVariantPriceDtos(FlightLiveVariant[] flightVariants,
        HotelOfferLiveVariant[] hotelOfferVariants, PackageId packageId, Occupancy combinedOccupancy)
    {
        var result = new List<PackageLiveVariantPriceDto>();
        
        foreach (var flightVariant in flightVariants)
        {
            foreach (var hotelOffer in hotelOfferVariants)
            {
                var occupancy = PackageOccupancy.FromOccupancy(combinedOccupancy);

                var packageVariantId = new PackageVariantId(
                    checkIn: packageId.CheckIn,
                    stayLength: packageId.StayLength,
                    marketId: packageId.MarketId,
                    metaCode: packageId.MetaCode,
                    occupancy: occupancy,
                    mealPlan: hotelOffer.MealPlan,
                    refundability: hotelOffer.Refundability,
                    roomIds: hotelOffer.RoomIds,
                    arrivalAirport: flightVariant.ArrivalAirport,
                    departureAirport: flightVariant.DepartureAirport,
                    departureDate: DateOnly.FromDateTime(flightVariant.DepartureDate),
                    returnDepartureDate: DateOnly.FromDateTime(flightVariant.ReturnDepartureDate),
                    flightNumbers: flightVariant.FlightNumbers,
                    returnFlightNumbers: flightVariant.ReturnFlightNumbers,
                    baggageIncluded: flightVariant.RegisteredBaggageIncluded);

                var flightPriceCompensated = flightVariant.Price;
                var total = hotelOffer.Price +flightVariant.Price;
                var totalCompensated = hotelOffer.PriceCompensated + flightPriceCompensated;

                result.Add(new PackageLiveVariantPriceDto(
                    PackageVariantId: packageVariantId.ToString(),
                    HotelOfferId: hotelOffer.OfferId,
                    FlightOfferId: flightVariant.Key,
                    HotelOfferPrice: hotelOffer.Price,
                    HotelOfferPriceCompensated: hotelOffer.PriceCompensated,
                    FlightOfferPrice: flightVariant.Price,
                    FlightOfferPriceCompensated: flightPriceCompensated,
                    Price: total,
                    PriceCompensated: totalCompensated));
            }
        }

        return result.ToArray();
    }
}