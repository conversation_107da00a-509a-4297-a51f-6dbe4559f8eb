using System.Collections.Concurrent;
using Esky.Packages.Application.Abstractions.Gateways.FlightGateway;
using Esky.Packages.Application.Abstractions.Gateways.HotelOfferGateway;
using Esky.Packages.Application.Abstractions.Gateways.HotelsApiGateway;
using Esky.Packages.Application.Dtos.Common;
using Esky.Packages.Application.Dtos.HotelOffers;
using Esky.Packages.Application.Exceptions;
using Esky.Packages.Application.Observability;
using Esky.Packages.Contract.Search;
using Esky.Packages.Domain.Factories;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.Markets;
using Esky.Packages.Domain.Model.PackageFlightVariants;
using Esky.Packages.Domain.Model.PackageHotelAirports;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Model.PackageHotelOfferVariants;
using Esky.Packages.Domain.Model.Packages;
using Esky.Packages.Domain.Model.PackageVariants;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;
using Esky.Packages.Domain.Types.Extensions;

namespace Esky.Packages.Application.Services;

public record SearchParameters(
    string MarketId,
    int[] MetaCodes,
    int[] StayLengths,
    DateOnly DepartureDateFrom,
    DateOnly DepartureDateTo,
    Airport[] DepartureAirports,
    MealPlan[] MealPlans,
    Occupancy[] Occupancies,
    TimeOfDay[] InboundDepartures,
    TimeOfDay[] OutboundDepartures,
    int? MaxPrice,
    bool UseDynamicSearchFallback);

public interface ISearchService
{
    Task<SearchDto> Search(SearchParameters parameters, CancellationToken cancellationToken = default);
}

internal class SearchService(
    IMarketRepository marketRepository,
    IPackageFlightRepository packageFlightRepository,
    IPackageFlightVariantRepository packageFlightVariantRepository,
    IPackageHotelAirportsRepository packageHotelAirportsRepository,
    IPackageHotelOfferFactory packageHotelOfferFactory,
    IPackageFlightFactory packageFlightFactory,
    IPackageHotelOfferService packageHotelOfferService,
    IFlightGateway flightGateway,
    IHotelOfferGateway hotelOfferGateway,
    IHotelOfferLiveGateway hotelOfferLiveGateway,
    IPriceTrackerService priceTrackerService)
    : ISearchService
{
    public async Task<SearchDto> Search(SearchParameters parameters, CancellationToken cancellationToken = default)
    {
        var isDynamic = false;
        var isCached = false;

        var market = await marketRepository.GetById(parameters.MarketId, cancellationToken);
        if (market == null)
        {
            throw new MarketNotFoundException(parameters.MarketId);
        }

        var packageHotelAirports = (await packageHotelAirportsRepository.ListByMarketIdAndMetaCodes(market.Id,
            parameters.MetaCodes, cancellationToken)).ToArray();
        var arrivalAirports = packageHotelAirports.SelectMany(m => m.Airports).Distinct().ToArray();

        if (arrivalAirports.Length == 0)
        {
            return new SearchDto(Variants: []);
        }
        
        var mergedOccupancy = GetMergedOccupancy(parameters.Occupancies);
        var flightOccupancy = GetFlightSeatsOccupancy(parameters.Occupancies);

        var packageFlightVariants = await packageFlightVariantRepository.GetCheapestsPerAirports(
            marketId: parameters.MarketId,
            stayLengths: parameters.StayLengths,
            departureAirports: parameters.DepartureAirports,
            arrivalAirports: arrivalAirports,
            departureDateFrom: parameters.DepartureDateFrom,
            departureDateTo: parameters.DepartureDateTo,
            occupancy: flightOccupancy,
            inboundDepartures: parameters.InboundDepartures,
            outboundDepartures: parameters.OutboundDepartures,
            cancellationToken: cancellationToken);

        if (packageFlightVariants.Length == 0)
        {
            return new SearchDto(Variants: []);
        }

        packageFlightVariants = await UpdateFlightVariantPrices(market, packageFlightVariants, mergedOccupancy,
            cancellationToken);

        var packageHotelOfferVariants = await packageHotelOfferService.GetPackageHotelOfferVariants(
            packageFlightVariants, packageHotelAirports, parameters.Occupancies, parameters.MealPlans, 
            cancellationToken);

        if (packageHotelOfferVariants.Length == 0 && PackageHotelOffer.AvailableInHotelsCache(parameters.Occupancies))
        {
            isCached = true;

            var hcacheOccupancies = parameters.Occupancies
                .Select(PackageOccupancy.FromOccupancy)
                .Select(o => o.ToOccupancy())
                .ToArray();

            packageHotelOfferVariants = await GetCachedPackageHotelOfferVariants(market, packageFlightVariants,
                packageHotelAirports, hcacheOccupancies, parameters.MealPlans, cancellationToken);
        }

        if (packageHotelOfferVariants.Length == 0 && parameters.UseDynamicSearchFallback)
        {
            isDynamic = true;
            packageHotelOfferVariants = await GetDynamicPackageHotelOfferVariants(market,
                packageFlightVariants, packageHotelAirports, parameters.Occupancies, parameters.MealPlans,
                cancellationToken);
        }

        var packageVariants = PackageVariant.CreateManyPerMetaCode(packageFlightVariants, packageHotelOfferVariants,
            packageHotelAirports);

        if (isDynamic)
        {
            ApplicationMetrics.RegisterPackageDynamicSearchOffers(packageVariants.Count);
        }
        else if (isCached)
        {
            ApplicationMetrics.RegisterPackageCachedSearchOffers(packageVariants.Count);
        }
        else
        {
            ApplicationMetrics.RegisterPackageFactorsSearchOffers(packageVariants.Count);
        }

        if (parameters.MaxPrice != null)
        {
            packageVariants = packageVariants
                .Where(v => v.Price <= parameters.MaxPrice)
                .ToList();
        }

        if (market.EmitPriceHistoryEvents)
        {
            await priceTrackerService.EmitPriceHistoryEvents(packageVariants, market, cancellationToken);
        }

        return new SearchDto(Variants: packageVariants
            .Select(v => Map(v,
                isDynamic || isCached ? PackageVariantSourceDto.Dynamic : PackageVariantSourceDto.Factors))
            .ToList());
    }

    private async Task<PackageHotelOfferVariant[]> GetCachedPackageHotelOfferVariants(Market market,
        PackageFlightVariant[] packageFlightVariants, PackageHotelAirports[] packageHotelAirports,
        Occupancy[] occupancies, MealPlan[] mealPlans, CancellationToken cancellationToken)
    {
        const int maxBatchSize = 3000;

        var providerConfigurationIds = market.HotelOfferProviderConfigurationIds.Select(p => p.ToString()).ToArray();

        var airportsToMetaCodes = packageHotelAirports
            .SelectMany(m => m.Airports.Select(a => (ArrivalAirport: a, m.Id.MetaCode)))
            .GroupBy(g => g.ArrivalAirport)
            .ToDictionary(g => g.Key, g => g.Select(x => x.MetaCode).Distinct().ToList());

        var stayKeyChunks = packageFlightVariants
            .SelectMany(f => airportsToMetaCodes.GetValueOrDefault(f.Id.ArrivalAirport, [])
                .Select(metaCode => new HotelOfferSearchManyStayKey(
                    MetaCode: metaCode,
                    CheckIn: f.Id.CheckIn,
                    StayLength: f.Id.StayLength)))
            .OrderBy(s => s.CheckIn)
            .ThenBy(s => s.StayLength)
            .ThenBy(s => s.MetaCode)
            .Chunk(maxBatchSize)
            .ToList();

        var hotelOffers = new ConcurrentBag<HotelOffer>();

        await Parallel.ForEachAsync(stayKeyChunks, new ParallelOptions
        {
            CancellationToken = cancellationToken,
        }, async (stayKeys, token) =>
        {
            var hotelOfferOccupancies = occupancies
                .Select(o => new HotelOfferOccupancy(adults: o.Adults, children: o.ChildrenAges));

            var searchManyCriteria = new HotelOfferSearchManyCriteria(
                ProviderConfigurationIds: providerConfigurationIds,
                Occupancies: hotelOfferOccupancies.ToArray(),
                StayKeys: stayKeys.ToArray());

            var localHotelOffers = await hotelOfferGateway.SearchMany(searchManyCriteria, token);

            foreach (var localHotelOffer in localHotelOffers)
            {
                hotelOffers.Add(localHotelOffer);
            }
        });

        var packageOccupancies = occupancies.Select(PackageOccupancy.FromOccupancy).ToArray();
        var packageHotelOffers = packageHotelOfferFactory.CreateMany(
            hotelOffers: hotelOffers,
            marketId: market.Id,
            definitionId: $"{market.Id}-cached",
            currency: market.Currency,
            occupancies: packageOccupancies,
            providerConfigurationIds: market.HotelOfferProviderConfigurationIds,
            metaCodeByArrivalAirportByDepartureAirport: []);

        return packageHotelOffers
            .SelectMany(o => PackageHotelOfferVariant.CreateMany(o, packageOccupancies, mealPlans))
            .ToArray();
    }

    private async Task<PackageHotelOfferVariant[]> GetDynamicPackageHotelOfferVariants(Market market,
        PackageFlightVariant[] packageFlightVariants, PackageHotelAirports[] packageHotelAirports,
        Occupancy[] occupancies, MealPlan[] mealPlans, CancellationToken cancellationToken)
    {
        var arrivalAirports = packageFlightVariants.Select(f => f.Id.ArrivalAirport).ToHashSet();

        // take only the cheapest flight variant per arrival airport with the shortest stay length
        packageFlightVariants = packageFlightVariants
            .GroupBy(f => f.Id.ArrivalAirport)
            .ToDictionary(
                g => g.Key,
                g => g
                    .OrderBy(f => f.Id.StayLength)
                    .ThenBy(f => f.Price)
                    .Take(arrivalAirports.Count >= 5 ? 1 : 3) // TODO: Adjust this
            )
            .SelectMany(p => p.Value)
            .ToArray();

        var mergedOccupancy = occupancies.Merge();
        var packageOccupancy = PackageOccupancy.FromOccupancy(mergedOccupancy);

        var airportsToMetaCodes = packageHotelAirports
            .SelectMany(m => m.Airports.Select(a => (ArrivalAirport: a, m.Id.MetaCode)))
            .GroupBy(g => g.ArrivalAirport)
            .ToDictionary(g => g.Key, g => g.Select(x => x.MetaCode).Distinct().ToList());

        var cachedPackageHotelOfferVariants = await GetCachedPackageHotelOfferVariants(market, packageFlightVariants,
            packageHotelAirports, occupancies, mealPlans, cancellationToken);

        var searchCriteriaList = packageFlightVariants
            .Select(f => new HotelSearchCriteria(
                MetaCodes: airportsToMetaCodes
                    .GetValueOrDefault(f.Id.ArrivalAirport, [])
                    .Where(m => cachedPackageHotelOfferVariants.FirstOrDefault(h =>
                        h.Id.MetaCode == m && h.Id.CheckIn == f.Id.CheckIn &&
                        h.Id.StayLength == f.Id.StayLength) == null)
                    .ToList(),
                CheckIn: f.Id.CheckIn,
                CheckOut: f.Id.CheckIn.AddDays(f.Id.StayLength),
                PartnerCode: market.PartnerCode,
                Occupancies: occupancies.ToList()))
            .Where(c => c.MetaCodes.Count > 0)
            .ToList();

        var hotelOffers = new List<HotelOffer>();
        await Parallel.ForEachAsync(searchCriteriaList, new ParallelOptions
        {
            CancellationToken = cancellationToken
        }, async (criteria, token) =>
        {
            var liveHotelOffers = (await hotelOfferLiveGateway.Search(criteria, token)).ToList();
            lock (hotelOffers)
            {
                hotelOffers.AddRange(liveHotelOffers);
            }
        });

        var providerConfigurationIds = hotelOffers
            .Select(x => new ProviderConfigurationId(x.ProviderConfigurationId))
            .Distinct()
            .ToArray();

        var packageHotelOffers = packageHotelOfferFactory.CreateMany(
            hotelOffers: hotelOffers,
            marketId: market.Id,
            definitionId: $"{market.Id}-dynamic",
            currency: market.Currency,
            occupancies: [packageOccupancy],
            providerConfigurationIds: providerConfigurationIds, []);

        return packageHotelOffers
            .SelectMany(o => PackageHotelOfferVariant.CreateMany(o, [packageOccupancy], mealPlans))
            .Union(cachedPackageHotelOfferVariants)
            .ToArray();
    }

    // TODO: Move this method from here and CalendarService to some common place
    private async Task<PackageFlightVariant[]> UpdateFlightVariantPrices(Market market,
        PackageFlightVariant[] packageFlightVariants, PackageOccupancy packageOccupancy, 
        CancellationToken cancellationToken)
    {
        var flightOfferIds = packageFlightVariants
            .Select(f => f.OfferId)
            .ToHashSet();

        var flightOffers = await flightGateway.GetFlightsByOfferKeyAsync(
            new FlightByOfferKeySearchCriteria(flightOfferIds, packageOccupancy.ToOccupancy(), market.PartnerCode,
                true, false), cancellationToken);

        var updatedPackageFlights = packageFlightFactory.CreateMany(flightOffers, market.Id, $"{market.Id}-dynamic",
            market.Currency, [packageOccupancy]);

        return updatedPackageFlights.SelectMany(f => f.GetVariants(packageOccupancy)).ToArray();
    }

    private static PackageVariantDto Map(PackageVariant packageVariant, PackageVariantSourceDto source)
    {
        return new PackageVariantDto(
            PackageId: new PackageId(packageVariant.Id.CheckIn, packageVariant.Id.StayLength,
                packageVariant.Id.MarketId, packageVariant.Id.MetaCode),
            PackageVariantId: packageVariant.Id,
            FlightOfferId: packageVariant.FlightOfferId,
            MetaCode: packageVariant.Id.MetaCode,
            StayLength: packageVariant.Id.StayLength,
            CheckIn: packageVariant.Id.CheckIn,
            DepartureAirport: packageVariant.Id.DepartureAirport,
            DepartureDate: packageVariant.Id.DepartureDate,
            ReturnArrivalDate: packageVariant.ReturnArrivalDate,
            MealPlan: packageVariant.Id.MealPlan.ToDto(),
            Price: packageVariant.Price,
            Source: source
        );
    }

    private static PackageOccupancy GetMergedOccupancy(Occupancy[] occupancies)
    {
        var mergedOccupancy = occupancies.Merge();
        var packageOccupancy = PackageOccupancy.FromOccupancy(mergedOccupancy);

        return packageOccupancy;
    }

    private static PackageOccupancy GetFlightSeatsOccupancy(Occupancy[] occupancies)
    {
        var packageOccupancy = GetMergedOccupancy(occupancies);

        return packageOccupancy.ToFlightSeatsOccupancy();
    }
}