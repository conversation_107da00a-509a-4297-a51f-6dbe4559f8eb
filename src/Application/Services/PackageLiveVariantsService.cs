using Esky.Packages.Application.Abstractions.Gateways.FlightGateway;
using Esky.Packages.Application.Abstractions.Gateways.FlightLiveGateway;
using Esky.Packages.Application.Abstractions.Gateways.HotelTransactionGateway;
using Esky.Packages.Application.Dtos.Common;
using Esky.Packages.Application.Dtos.HotelOfferVariants;
using Esky.Packages.Application.Exceptions;
using Esky.Packages.Contract.Common;
using Esky.Packages.Contract.Packages;
using Esky.Packages.Contract.PackageVariants;
using Esky.Packages.Domain.Factories;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.LiveVariants;
using Esky.Packages.Domain.Model.Markets;
using Esky.Packages.Domain.Model.PackageDefinitions;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Model.Packages;
using Esky.Packages.Domain.Model.PackageVariants;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;
using Esky.Packages.Domain.Types.Extensions;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.Services;

public record PackageLiveVariantsParameters(
    string PackageId,
    Occupancy[] Occupancies,
    Airport[]? DepartureAirports,
    string? FlightOptionId,
    Airport? PreferredDepartureAirport,
    TimeOfDay[]? InboundDepartures,
    TimeOfDay[]? OutboundDepartures,
    MealPlan[]? PreferredMealPlans,
    bool PreferSelectedFlight);

public interface IPackageLiveVariantsService
{
    Task<PackageLiveVariantsDto> GetPackageLiveVariants(
        PackageLiveVariantsParameters parameters,
        CancellationToken cancellationToken);
}

internal class PackageLiveVariantsService(
    IPriceTrackerService priceTrackerService,
    IPackageHotelOfferRepository packageHotelOfferRepository,
    IPackageFlightRepository packageFlightRepository,
    IPackageDefinitionRepository packageDefinitionRepository,
    IPackageHotelAirportsRepository packageHotelAirportsRepository,
    IMarketRepository marketRepository,
    IFlightGateway flightGateway,
    IFlightLiveGateway flightLiveGateway,
    IPackageFlightFactory packageFlightFactory,
    IFlightVariantFactory flightVariantFactory,
    IHotelTransactionGateway hotelTransactionGateway,
    IHotelOfferVariantFactory hotelOfferVariantFactory,
    IOfferAccuracyGateway offerAccuracyGateway,
    ILogger<PackageLiveVariantsService> logger)
    : IPackageLiveVariantsService
{
    public async Task<PackageLiveVariantsDto> GetPackageLiveVariants(
        PackageLiveVariantsParameters parameters,
        CancellationToken cancellationToken)
    {
        if (PackageId.IsOldPackageId(parameters.PackageId))
        {
            throw new PackageNotFoundException("Old package ids are not supported. Please use the new format.");
        }

        var packageId = new PackageId(parameters.PackageId);

        var market = await marketRepository.GetById(packageId.MarketId, cancellationToken);
        if (market is null)
        {
            throw new MarketNotFoundException(packageId.MarketId);
        }

        var packageHotelAirports = await packageHotelAirportsRepository.GetByMarketIdAndMetaCode(market.Id,
            packageId.MetaCode, cancellationToken);
        if (packageHotelAirports == null)
        {
            throw new PackageNotFoundException("No arrival airports found for the package.");
        }

        var definition =
            await packageDefinitionRepository.GetById(packageHotelAirports.DefinitionId, cancellationToken);
        if (definition == null)
        {
            throw new PackageDefinitionNotFoundException(packageHotelAirports.DefinitionId);
        }

        var departureAirports = definition.Parameters.FlightCriteria.DepartureAirportCodes
            .Select(a => new Airport(a))
            .ToArray();

        var packageFlightsTask = GetPackageFlights(
            departureAirports: departureAirports,
            arrivalAirports: packageHotelAirports.Airports,
            checkIn: packageId.CheckIn,
            stayLength: packageId.StayLength,
            marketId: market.Id,
            cancellationToken: cancellationToken);

        var packageHotelOfferTask = GetPackageHotelOffer(packageId, cancellationToken);
        var definitionTask = packageDefinitionRepository.GetById(packageHotelAirports.DefinitionId, cancellationToken);

        await Task.WhenAll(packageFlightsTask, packageHotelOfferTask);

        var packageFlights = packageFlightsTask.Result;
        var packageHotelOffer = packageHotelOfferTask.Result;
        var packageDefinition = definitionTask.Result;

        if (packageFlights.Count == 0)
        {
            throw new PackageFlightNotFoundException($"No package flights found for package {packageId}.");
        }

        if (packageDefinition == null)
        {
            throw new PackageDefinitionNotFoundException(packageHotelAirports.DefinitionId);
        }

        packageFlights = await UpdatePackageFlightsPrices(market, packageFlights, parameters.Occupancies,
            cancellationToken);

        var packageLiveVariants = await GetPackageLiveVariants(
            packageId,
            market,
            packageFlights,
            packageHotelOffer,
            packageDefinition,
            parameters,
            cancellationToken);

        if (market.EmitPriceHistoryEvents)
        {
            var combinedOccupancy = PackageOccupancy.FromOccupancy(CombineOccupancies(parameters.Occupancies));
            var packageVariants = packageLiveVariants.HotelOffers.Join(packageLiveVariants.FlightOffers, _ => true,
                _ => true, (h, f) => PackageVariant.Create(
                    checkIn: packageId.CheckIn,
                    stayLength: packageId.StayLength,
                    marketId: packageId.MarketId,
                    metaCode: packageId.MetaCode,
                    departureAirport: f.DepartureAirport,
                    arrivalAirport: f.ArrivalAirport,
                    occupancy: combinedOccupancy,
                    mealPlan: h.MealPlan.ToDomain(),
                    departureDate: f.DepartureDate,
                    returnDepartureDate: f.ReturnDepartureDate,
                    returnArrivalDate: f.ReturnArrivalDate,
                    refundability: h.FreeRefund,
                    roomIds: h.RoomIds,
                    flightNumbers: f.FlightNumbers,
                    returnFlightNumbers: f.ReturnFlightNumbers,
                    baggageIncluded: f.RegisteredBaggageIncluded,
                    flightOfferId: f.Id,
                    flightVariantPrice: (int)f.PriceCompensated,
                    hotelOfferVariantPrice: (int)h.PriceCompensated
                ));
            await priceTrackerService.EmitPriceHistoryEvents(packageVariants, market, cancellationToken);
        }

        return packageLiveVariants;
    }

    private async Task<PackageLiveVariantsDto> GetPackageLiveVariants(
        PackageId packageId,
        Market market,
        ICollection<PackageFlight> packageFlights,
        PackageHotelOffer? packageHotelOffer,
        PackageDefinition packageDefinition,
        PackageLiveVariantsParameters parameters,
        CancellationToken cancellationToken)
    {
        var currency = market.Currency;

        var liveFlightsDtosTask = GetLiveFlightOffersDtos(
            packageId,
            market,
            packageFlights,
            packageDefinition,
            parameters,
            cancellationToken);

        var liveHotelOffersDtosTask = GetLiveHotelOfferDtos(
            packageId,
            market,
            packageHotelOffer,
            packageDefinition,
            parameters,
            cancellationToken);

        await Task.WhenAll(liveFlightsDtosTask, liveHotelOffersDtosTask);

        var liveFlightsDtosArray = liveFlightsDtosTask.Result.ToArray();
        var liveHotelOffersDtosArray = liveHotelOffersDtosTask.Result.ToArray();

        var occupancy = GetMergedOccupancy(parameters.Occupancies);

        var prices = liveFlightsDtosArray.Join(liveHotelOffersDtosArray, _ => true, _ => true, (f, h) =>
            new PackageLiveVariantPriceDto(
                PackageVariantId: new PackageVariantId(
                    checkIn: packageId.CheckIn,
                    stayLength: packageId.StayLength,
                    marketId: packageId.MarketId,
                    metaCode: packageId.MetaCode,
                    occupancy: occupancy,
                    mealPlan: h.MealPlan.ToDomain(),
                    refundability: new Refundability(h.FreeRefund),
                    roomIds: h.RoomIds,
                    arrivalAirport: f.ArrivalAirport,
                    departureAirport: f.DepartureAirport,
                    departureDate: f.DepartureDate,
                    returnDepartureDate: f.ReturnDepartureDate,
                    flightNumbers: f.FlightNumbers,
                    returnFlightNumbers: f.ReturnFlightNumbers,
                    baggageIncluded: f.RegisteredBaggageIncluded),
                HotelOfferId: h.OfferId,
                FlightOfferId: f.Id,
                HotelOfferPrice: h.Price,
                HotelOfferPriceCompensated: h.PriceCompensated,
                FlightOfferPrice: f.Price,
                FlightOfferPriceCompensated: f.PriceCompensated,
                Price: h.Price + f.Price,
                PriceCompensated: h.PriceCompensated + f.PriceCompensated
            )).ToArray();

        return new PackageLiveVariantsDto(
            Id: packageId.ToString(),
            Occupancy: parameters.Occupancies[0].ToDto(),
            Occupancies: parameters.Occupancies.Select(o => o.ToDto()).ToArray(),
            CheckIn: packageId.CheckIn,
            StayLength: packageId.StayLength,
            MetaCode: packageId.MetaCode,
            DefinitionId: packageDefinition.Id,
            MarketId: packageId.MarketId,
            PartnerCode: packageDefinition.Parameters.PartnerCode,
            Currency: currency,
            FlightOffers: liveFlightsDtosArray,
            HotelOffers: liveHotelOffersDtosArray,
            Prices: prices);
    }

    private async Task<IEnumerable<PackageFlightOfferDto>> GetLiveFlightOffersDtos(
        PackageId packageId,
        Market market,
        ICollection<PackageFlight> packageFlights,
        PackageDefinition packageDefinition,
        PackageLiveVariantsParameters parameters,
        CancellationToken cancellationToken)
    {
        var combinedOccupancy = CombineOccupancies(parameters.Occupancies);

        var departureAirports = packageFlights.Select(f => f.Id.DepartureAirport).Distinct().ToArray();
        var arrivalAirports = packageFlights.Select(f => f.Id.ArrivalAirport).Distinct().ToArray();

        var flightVariants = await GetFlightLiveVariants(packageId,
            parameters: parameters,
            combinedOccupancy: combinedOccupancy,
            partnerCode: packageDefinition.Parameters.PartnerCode,
            currency: market.Currency,
            departureAirports: departureAirports,
            arrivalAirports: arrivalAirports,
            maxStops: packageDefinition.Parameters.FlightCriteria.MaxStop,
            maxLegDuration: packageDefinition.Parameters.FlightCriteria.MaxLegDuration,
            flightOfferIds: packageFlights.SelectMany(f => f.GetFlightOfferIds()).ToHashSet(),
            cancellationToken: cancellationToken);

        var flightOccupancy = GetMergedOccupancy(parameters.Occupancies);

        var cheapestPackageFlightsByDepartureAirportByInboundByOutbound = packageFlights
            .Select(f => (f.Id.DepartureAirport, Prices: f.GetLowestPricesByOccupancyInboundOutbound(flightOccupancy)))
            .SelectMany(x => x.Prices
                .Select(p => (x.DepartureAirport, p.Key.InboundDeparture, p.Key.OutboundDeparture, Price: p.Value)))
            .GroupBy(x => (x.DepartureAirport, x.InboundDeparture, x.OutboundDeparture))
            .ToDictionary(
                g => (g.Key.DepartureAirport, g.Key.InboundDeparture, g.Key.OutboundDeparture),
                g => g.MinBy(f => f.Price.Price).Price.Price);

        return GetLiveFlightOffersDtosFromVariants(flightVariants,
            cheapestPackageFlightsByDepartureAirportByInboundByOutbound);
    }

    private async Task<List<PackageHotelOfferDto>> GetLiveHotelOfferDtos(
        PackageId packageId,
        Market market,
        PackageHotelOffer? packageHotelOffer,
        PackageDefinition packageDefinition,
        PackageLiveVariantsParameters parameters,
        CancellationToken cancellationToken)
    {
        var hotelOfferVariants = await GetHotelOfferVariants(
            packageId,
            market.Currency,
            packageHotelOffer?.OnlyRefundable ?? PackageHotelOffer.ShouldBeOnlyRefundable(packageId.CheckIn),
            packageDefinition.Parameters.PartnerCode,
            parameters.Occupancies,
            parameters.PreferredMealPlans,
            cancellationToken);

        var packageOccupancies = parameters.Occupancies
            .Select(PackageOccupancy.FromOccupancy)
            .ToArray();

        var pricesByMealPlan = packageOccupancies.Length == parameters.Occupancies.Length && packageHotelOffer != null
            ? packageHotelOffer.GetPrices(packageOccupancies)
            : null;

        return GetLiveHotelOfferDtosFromVariants(hotelOfferVariants, pricesByMealPlan);
    }

    private async Task<List<PackageFlight>> UpdatePackageFlightsPrices(Market market,
        List<PackageFlight> packageFlights, Occupancy[] occupancies, CancellationToken cancellationToken)
    {
        var flightOccupancy = GetMergedOccupancy(occupancies);

        var flightOfferIds = packageFlights.SelectMany(f => f.GetFlightOfferIds()).ToArray();
        var flightOffers = await flightGateway.GetFlightsByOfferKeyAsync(
            new FlightByOfferKeySearchCriteria(flightOfferIds, flightOccupancy.ToOccupancy(), market.PartnerCode,
                true, false), cancellationToken);

        var updatedPackageFlights = packageFlightFactory.CreateMany(flightOffers, market.Id, $"{market.Id}-dynamic",
            market.Currency, [flightOccupancy]);

        return updatedPackageFlights.ToList();
    }

    private List<PackageHotelOfferDto> GetLiveHotelOfferDtosFromVariants(
        HotelOfferLiveVariants hotelOfferVariants,
        Dictionary<MealPlan, PackageRoomOffer>? cheapestPackageHotelOffersPerMealPlan)
    {
        var liveHotelOffers = new List<PackageHotelOfferDto>();

        foreach (var (mealPlan, hotelVariantsForMealPlan) in hotelOfferVariants.GetHotelOffersByMealPlan())
        {
            var cheapestPackageHotelOfferForMealPlan =
                cheapestPackageHotelOffersPerMealPlan?.TryGetValue(mealPlan, out var price) == true
                    ? (decimal?)price.CompensatedPrice
                    : null;

            liveHotelOffers.AddRange(CreateMealPlanHotelOfferDtos(hotelVariantsForMealPlan,
                cheapestPackageHotelOfferForMealPlan));
        }

        return liveHotelOffers;
    }

    private static IEnumerable<PackageFlightOfferDto> GetLiveFlightOffersDtosFromVariants(
        FlightLiveVariants flightVariants,
        Dictionary<(Airport DepartureAirport, TimeOfDay InboundDeparture, TimeOfDay OutboundDeparture), decimal>
            cheapestPackageFlightsForAirports)
    {
        var liveFlightOffersDtos = new List<PackageFlightOfferDto>();

        foreach (var (key, alternativeFlightsForAirport) in flightVariants
                     .GetFlightsByDepartureAirportByInboundDepartureByOutboundDeparture())
        {
            var cheapestPackageFlightForAirport =
                cheapestPackageFlightsForAirports?.TryGetValue(
                    (key.DepartureAirport, key.InboundDeparture, key.OutboundDeparture), out var price) == true
                    ? (decimal?)price
                    : null;

            liveFlightOffersDtos.AddRange(CreateAirportFlightOfferDtos(alternativeFlightsForAirport,
                cheapestPackageFlightForAirport));
        }

        return liveFlightOffersDtos;
    }

    private async Task<FlightLiveVariant?> GetSelectedFlightLiveVariant(
        string partnerCode,
        Currency currency,
        Occupancy combinedOccupancy,
        string flightOptionId,
        CancellationToken cancellationToken)
    {
        FlightOffer? flightOffer;
        try
        {
            flightOffer = (await flightGateway.GetFlightsByOfferKeyAsync(new FlightByOfferKeySearchCriteria
                    (
                        FlightOfferKeys: [flightOptionId],
                        PartnerCode: partnerCode,
                        Occupancy: combinedOccupancy,
                        IncludeFlightDetails: true,
                        IncludeLegLocators: true
                    ), cancellationToken)
                ).FirstOrDefault();
        }
        catch (Exception e)
        {
            logger.LogWarning(e, "Error while getting flight by offer key '{FlightOptionId}'", flightOptionId);
            return null;
        }

        if (flightOffer is null) return null;

        var selectedFlightVariant = flightVariantFactory.Create(
            key: flightOffer.Id,
            departureAirport: flightOffer.DepartureAirport,
            arrivalAirport: flightOffer.ArrivalAirport,
            departureDate: flightOffer.DepartureDate,
            arrivalDate: flightOffer.ArrivalDate,
            returnDepartureDate: flightOffer.ReturnDepartureDate,
            returnArrivalDate: flightOffer.ReturnArrivalDate,
            flightNumbers: flightOffer.FlightNumbers,
            returnFlightNumbers: flightOffer.ReturnFlightNumbers,
            providerCode: flightOffer.ProviderCode,
            stops: flightOffer.Stops,
            airlineCodes: flightOffer.AirlineCodes,
            flightIds: flightOffer.FlightIds,
            legLocators: flightOffer.LegLocators ?? [],
            currency: currency,
            prices: flightOffer.Prices.Select(p =>
                new Money(p.Value.Prices.First().Value, p.Value.Currency)).ToArray(),
            registeredBaggageIncluded: flightOffer.BaggageIncluded);

        var liveCheck = await flightLiveGateway.LiveCheck(
            new LiveCheckCriteria(
                FlightOfferKey: selectedFlightVariant.Key,
                PartnerCode: partnerCode,
                CurrencyCode: currency,
                Occupancy: combinedOccupancy),
            cancellationToken);

        if (liveCheck is null ||
            liveCheck.Status is not FlightLiveCheckStatusDto.Available and not FlightLiveCheckStatusDto.Timeout)
            return null;

        selectedFlightVariant.MarkAsSelected();

        if (liveCheck.Price is not null)
        {
            selectedFlightVariant.UpdateLivePrice(
                price: new Money(liveCheck.Price.Value, liveCheck.Price.Currency),
                legLocators: liveCheck.LegLocators);
        }

        return selectedFlightVariant;
    }

    private async Task LiveCheckFlightVariant(
        string partnerCode,
        Currency currency,
        PackageLiveVariantsParameters parameters,
        Occupancy combinedOccupancy,
        FlightLiveVariants flightLiveVariants,
        CancellationToken cancellationToken)
    {
        var selectedFlightVariant = flightLiveVariants.SelectFlight(
            selectedDepartureAirports: parameters.DepartureAirports,
            selectedFlightOptionId: parameters.FlightOptionId,
            preferredDepartureAirport: parameters.PreferredDepartureAirport,
            inboundDepartures: parameters.InboundDepartures,
            outboundDepartures: parameters.OutboundDepartures);

        if (selectedFlightVariant is not null)
        {
            selectedFlightVariant.MarkAsSelected();

            var liveCheck = await flightLiveGateway.LiveCheck(
                new LiveCheckCriteria(
                    FlightOfferKey: selectedFlightVariant.Key,
                    PartnerCode: partnerCode,
                    CurrencyCode: currency,
                    Occupancy: combinedOccupancy),
                cancellationToken);

            if (liveCheck is not null)
            {
                if (liveCheck.Status is FlightLiveCheckStatusDto.NotAvailable)
                {
                    flightLiveVariants.RemoveSoldOutFlight(selectedFlightVariant);
                }
                else if (liveCheck.Status is FlightLiveCheckStatusDto.Available && liveCheck.Price is not null)
                {
                    selectedFlightVariant.UpdateLivePrice(
                        price: new Money(liveCheck.Price.Value, liveCheck.Price.Currency),
                        legLocators: liveCheck.LegLocators);
                }
                else
                {
                    logger.LogWarning("Live check failed for flight {flightOfferKey} with status {status}",
                        selectedFlightVariant.Key,
                        liveCheck.Status);
                }
            }
        }
    }

    private async Task<PackageHotelOffer?> GetPackageHotelOffer(PackageId packageId,
        CancellationToken cancellationToken)
    {
        var packageHotelOfferId = new PackageHotelOfferId(
            checkIn: packageId.CheckIn,
            stayLength: packageId.StayLength,
            marketId: packageId.MarketId,
            metaCode: packageId.MetaCode);

        return await packageHotelOfferRepository.GetById(packageHotelOfferId, cancellationToken);
    }

    private async Task<List<PackageFlight>> GetPackageFlights(IEnumerable<Airport> departureAirports,
        IEnumerable<Airport> arrivalAirports, DateOnly checkIn, int stayLength, string marketId,
        CancellationToken cancellationToken)
    {
        var packageFlightOfferIds = departureAirports.Join(arrivalAirports, _ => true, _ => true,
            (departureAirport, arrivalAirport) =>
                new PackageFlightId(checkIn: checkIn, stayLength: stayLength, marketId: marketId,
                    arrivalAirport: arrivalAirport, departureAirport: departureAirport)).ToArray();

        return await packageFlightRepository.ListByIds(packageFlightOfferIds, cancellationToken);
    }

    private static IEnumerable<PackageFlightOfferDto> CreateAirportFlightOfferDtos(
        List<FlightLiveVariant> alternativeFlightsByDepartureAirportByInboundDepartureByOutboundDeparture,
        decimal? cheapestPackagePriceByDepartureAirportByInboundDepartureByOutboundDeparture)
    {
        for (var i = 0; i < alternativeFlightsByDepartureAirportByInboundDepartureByOutboundDeparture.Count; i++)
        {
            var alternativeFlight = alternativeFlightsByDepartureAirportByInboundDepartureByOutboundDeparture[i];
            var priceCompensated = (
                    cheapestPackagePriceForAirport:
                    cheapestPackagePriceByDepartureAirportByInboundDepartureByOutboundDeparture, i) switch
                {
                    (null or 0m, _) => alternativeFlight.Compensate(alternativeFlight.Price),
                    (_, 0) => alternativeFlight.Compensate(
                        cheapestPackagePriceByDepartureAirportByInboundDepartureByOutboundDeparture),
                    _ => alternativeFlight.Compensate(
                        cheapestPackagePriceByDepartureAirportByInboundDepartureByOutboundDeparture, onlyUp: true)
                };

            yield return new PackageFlightOfferDto(
                Id: alternativeFlight.Key,
                Price: alternativeFlight.Price,
                PriceCompensated: priceCompensated,
                DepartureAirport: alternativeFlight.DepartureAirport,
                ArrivalAirport: alternativeFlight.ArrivalAirport,
                DepartureDate: alternativeFlight.DepartureDate.ToDateOnly(),
                ArrivalDate: alternativeFlight.ArrivalDate.ToDateOnly(),
                ReturnDepartureDate: alternativeFlight.ReturnDepartureDate.ToDateOnly(),
                ReturnArrivalDate: alternativeFlight.ReturnArrivalDate.ToDateOnly(),
                FlightNumbers: alternativeFlight.FlightNumbers,
                ReturnFlightNumbers: alternativeFlight.ReturnFlightNumbers,
                Stops: alternativeFlight.Stops,
                FlightIds: alternativeFlight.FlightIds,
                LegLocators: alternativeFlight.LegLocators,
                RegisteredBaggageIncluded: alternativeFlight.RegisteredBaggageIncluded,
                IsSelected: alternativeFlight.IsSelected);
        }
    }

    private async Task<HotelOfferLiveVariants> GetHotelOfferVariants(
        PackageId packageId,
        Currency currency,
        bool onlyRefundable,
        string partnerCode,
        Occupancy[] occupancies,
        MealPlan[]? preferredMealPlans,
        CancellationToken cancellationToken)
    {
        var variantsGroupedByRoomsDtos = await hotelTransactionGateway.GetVariantsGroupedByRooms(
            new HotelOfferVariantsCritieria(
                CheckIn: packageId.CheckIn,
                CheckOut: packageId.CheckIn.AddDays(packageId.StayLength),
                PartnerCode: partnerCode,
                MetaCode: packageId.MetaCode,
                Occupancies: occupancies
            ),
            cancellationToken);

        var hotelOfferIds = variantsGroupedByRoomsDtos
            .SelectMany(x => x.Values)
            .SelectMany(x => x)
            .Select(z => z.OfferId)
            .Distinct()
            .ToArray();
        var excludedHotelOfferIds = await offerAccuracyGateway.GetUnavailableHotelOfferIds(
            packageId.MetaCode,
            hotelOfferIds,
            packageId.CheckIn,
            packageId.CheckIn.AddDays(packageId.StayLength),
            cancellationToken);

        var hotelOfferVariantsGroupedByRooms = variantsGroupedByRoomsDtos
            .Select(x => x
                .Select(p =>
                    (Key: p.Key,
                        Value: p.Value
                            .Where(o => !excludedHotelOfferIds.Contains(o.OfferId))
                            .Select(o => hotelOfferVariantFactory
                                .Create(
                                    offerId: o.OfferId,
                                    checkIn: packageId.CheckIn,
                                    stayLength: packageId.StayLength,
                                    mealPlan: o.MealPlan.ToDomain(),
                                    refundability: o.Refundability.ToDomain(),
                                    freeRefundUntil: o.FreeRefundUntil,
                                    roomIds: o.RoomIds,
                                    currency: currency,
                                    price: new Money(o.Price.Value, o.Price.Currency),
                                    priceAtHotel: new Money(o.PriceAtHotel.Value, o.PriceAtHotel.Currency),
                                    availability: o.Availability))
                            .ToArray()))
                .Where(p => p.Value.Length > 0)
                .ToDictionary(p => p.Key, p => p.Value.AsEnumerable()))
            .ToArray();

        return HotelOfferLiveVariants.CreateCheapestHotelOfferVariantsByRooms(
            hotelOfferVariantsGroupedByRooms: hotelOfferVariantsGroupedByRooms,
            onlyRefundable: onlyRefundable,
            preferredMealPlans: preferredMealPlans);
    }

    private static IEnumerable<PackageHotelOfferDto> CreateMealPlanHotelOfferDtos(
        List<HotelOfferLiveVariant> hotelOfferVariantsForMealPlan,
        decimal? cheapestPackageHotelOfferForMealPlan)
    {
        for (var i = 0; i < hotelOfferVariantsForMealPlan.Count; i++)
        {
            var hotelOfferVariant = hotelOfferVariantsForMealPlan[i];
            var priceCompensated = (cheapestPackageHotelOfferForMealPlan, i) switch
            {
                (null or 0m, _) => hotelOfferVariant.Compensate(hotelOfferVariant.Price),
                (_, 0) => hotelOfferVariant.Compensate(cheapestPackageHotelOfferForMealPlan),
                _ => hotelOfferVariant.Compensate(cheapestPackageHotelOfferForMealPlan, onlyUp: true)
            };

            yield return new PackageHotelOfferDto(
                OfferId: hotelOfferVariant.OfferId,
                MealPlan: hotelOfferVariant.MealPlan.ToDto(),
                Availability: hotelOfferVariant.Availability,
                Price: hotelOfferVariant.Price,
                PriceCompensated: priceCompensated,
                PriceAtHotel: new MoneyDto(
                    Value: hotelOfferVariant.PriceAtHotel.Value,
                    Currency: hotelOfferVariant.PriceAtHotel.Currency),
                FreeRefund: hotelOfferVariant.Refundability.IsRefundable,
                FreeRefundUntil: hotelOfferVariant.FreeRefundUntil,
                RoomIds: hotelOfferVariant.RoomIds
            );
        }
    }

    private async Task<FlightLiveVariants> GetFlightLiveVariants(
        PackageId packageId,
        PackageLiveVariantsParameters parameters,
        Occupancy combinedOccupancy,
        string partnerCode,
        string currency,
        Airport[] departureAirports,
        Airport[] arrivalAirports,
        int maxStops,
        TimeSpan? maxLegDuration,
        HashSet<string> flightOfferIds,
        CancellationToken cancellationToken)
    {
        if (parameters is { PreferSelectedFlight: true, FlightOptionId: not null })
        {
            var selectedFlightVariantTask = GetSelectedFlightLiveVariant(
                partnerCode,
                currency,
                combinedOccupancy,
                parameters.FlightOptionId,
                cancellationToken);
            var offerAvailabilityTask = offerAccuracyGateway.GetUnavailableFlightOffers([parameters.FlightOptionId],
                combinedOccupancy, cancellationToken);
            await Task.WhenAll(selectedFlightVariantTask, offerAvailabilityTask);

            if (selectedFlightVariantTask.Result is not null &&
                (offerAvailabilityTask.Result.Count == 0 ||
                 !offerAvailabilityTask.Result.Contains(parameters.FlightOptionId)))
            {
                return FlightLiveVariants.Create([selectedFlightVariantTask.Result]);
            }
        }

        FlightOffer[] flightOffers = [];

        if (parameters.PreferSelectedFlight && flightOfferIds.Count > 0)
        {
            var flightOffersTask = flightGateway.GetFlightsByOfferKeyAsync(new FlightByOfferKeySearchCriteria
            (
                FlightOfferKeys: flightOfferIds,
                PartnerCode: partnerCode,
                Occupancy: combinedOccupancy,
                IncludeFlightDetails: true,
                IncludeLegLocators: true
            ), cancellationToken);

            var offerAvailabilityTask = offerAccuracyGateway.GetUnavailableFlightOffers(flightOfferIds.ToArray(),
                combinedOccupancy, cancellationToken);
            await Task.WhenAll(flightOffersTask, offerAvailabilityTask);

            flightOffers = flightOffersTask.Result
                .Where(x => !offerAvailabilityTask.Result.Contains(x.Id))
                .ToArray();
        }

        if (flightOffers.Length == 0)
        {
            var alternativeFlightOffersTask = GetAlternativeFlightOffers();
            var offerAvailabilityTask = offerAccuracyGateway.GetUnavailableFlightOffers(
                alternativeFlightOffersTask.Result.Select(x => x.Id).ToArray(), combinedOccupancy, cancellationToken);
            await Task.WhenAll(alternativeFlightOffersTask, offerAvailabilityTask);

            flightOffers = alternativeFlightOffersTask.Result
                .Where(x => !offerAvailabilityTask.Result.Contains(x.Id))
                .ToArray();
        }

        var flightVariants = FlightLiveVariants.Create(flightOffers.Select(MapToFlightLiveVariantFromOffer));

        await LiveCheckFlightVariant(
            partnerCode,
            currency,
            parameters,
            combinedOccupancy,
            flightVariants,
            cancellationToken);

        return flightVariants;

        async Task<List<FlightOffer>> GetAlternativeFlightOffers()
        {
            return (await flightGateway.GetAlternativeFlightOffers(
                new AlternativeFlightOffersCriteria(
                    PartnerCode: partnerCode,
                    CheckIn: packageId.CheckIn,
                    StayLength: packageId.StayLength,
                    DepartureAirports: departureAirports.Select(a => a.ToString()).ToArray(),
                    ArrivalAirports: arrivalAirports.Select(a => a.ToString()).ToArray(),
                    Occupancy: combinedOccupancy,
                    MaxStops: maxStops,
                    MaxLegDuration: maxLegDuration,
                    IncludeLegLocators: true,
                    Limit: null),
                cancellationToken)).ToList();
        }

        FlightLiveVariant MapToFlightLiveVariantFromOffer(FlightOffer x)
        {
            return flightVariantFactory.Create(
                key: x.Id,
                departureAirport: x.DepartureAirport,
                arrivalAirport: x.ArrivalAirport,
                departureDate: x.DepartureDate,
                arrivalDate: x.ArrivalDate,
                returnDepartureDate: x.ReturnDepartureDate,
                returnArrivalDate: x.ReturnArrivalDate,
                flightNumbers: x.FlightNumbers,
                returnFlightNumbers: x.ReturnFlightNumbers,
                providerCode: x.ProviderCode,
                stops: x.Stops,
                airlineCodes: x.AirlineCodes,
                flightIds: x.FlightIds,
                legLocators: x.LegLocators ?? [],
                currency: currency,
                prices: x.Prices.Select(p =>
                    new Money(p.Value.Prices.First().Value, p.Value.Currency)).ToArray(),
                registeredBaggageIncluded: x.BaggageIncluded);
        }
    }

    private static Occupancy CombineOccupancies(Occupancy[] occupancies)
    {
        return new Occupancy(occupancies.Sum(o => o.Adults), occupancies.SelectMany(o => o.ChildrenAges).ToArray());
    }

    private static PackageOccupancy GetMergedOccupancy(Occupancy[] occupancies)
    {
        var mergedOccupancy = occupancies.Merge();
        var packageOccupancy = PackageOccupancy.FromOccupancy(mergedOccupancy)!;

        return packageOccupancy;
    }

    private static PackageOccupancy GetFlightSeatsOccupancy(Occupancy[] occupancies)
    {
        var packageOccupancy = GetMergedOccupancy(occupancies);

        return packageOccupancy.ToFlightSeatsOccupancy();
    }
}