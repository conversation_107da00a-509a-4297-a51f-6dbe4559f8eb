using System.Collections.Concurrent;

namespace Esky.Packages.Application.Observability;

public static class OfferAccuracyMetrics
{
    private static readonly ConcurrentDictionary<string, long> _offerAccuracyRequests = new();
    private static readonly ConcurrentDictionary<string, long> _offerAccuracyTimeouts = new();
    private static readonly ConcurrentDictionary<string, long> _offerAccuracyErrors = new();
    private static readonly ConcurrentDictionary<string, long> _offersAccuracyExclusions = new();
    
    public static IDictionary<string, long> OfferAccuracyRequests => _offerAccuracyRequests;
    public static IDictionary<string, long> OfferAccuracyTimeouts => _offerAccuracyTimeouts;
    public static IDictionary<string, long> OfferAccuracyErrors => _offerAccuracyErrors;
    public static IDictionary<string, long> OffersAccuracyExclusions => _offersAccuracyExclusions;
    
    public static void RegisterOfferAccuracyRequest(string offerType)
    {
        _offerAccuracyRequests.AddOrUpdate(offerType, 1, (_, oldValue) => oldValue + 1);
    }
    
    public static void RegisterOfferAccuracyTimeout(string offerType)
    {
        _offerAccuracyTimeouts.AddOrUpdate(offerType, 1, (_, oldValue) => oldValue + 1);
    }
    
    public static void RegisterOfferAccuracyError(string offerType)
    {
        _offerAccuracyErrors.AddOrUpdate(offerType, 1, (_, oldValue) => oldValue + 1);
    }
    
    public static void RegisterOfferAccuracyExclusions(string offerType, int count)
    {
        _offersAccuracyExclusions.AddOrUpdate(offerType, count, (_, oldValue) => oldValue + count);
    }
}