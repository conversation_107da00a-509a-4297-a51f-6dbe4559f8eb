namespace Esky.Packages.Application.Abstractions.Pipelines;

public interface IPipelineRepository
{
    Task UpsertWithoutConcurrency(List<Pipeline> pipelines, CancellationToken cancellationToken = default);
    Task<Pipeline?> GetByIdAndDefinitionId(string id, string definitionId, CancellationToken cancellationToken = default);
    Task MarkForRetry(string id, string definitionId, string startedByUserId, CancellationToken cancellationToken = default);
    Task MarkAsRunning(string id, string definitionId, CancellationToken cancellationToken = default);
    Task MarkAsCompleted(string id, string definitionId, CancellationToken cancellationToken = default);
    Task MarkAsFailed(string id, string definitionId, string errorMessage, CancellationToken cancellationToken = default);
    Task RemoveOutdatedPipelines(string definitionId, CancellationToken cancellationToken = default);
    Task<List<GenerationAggregatedStatistics>> GenerationsAggregatedStatistics(CancellationToken cancellationToken = default);
    Task<GenerationDetailedStatistics?> GenerationDetailedStatistics(string definitionId, CancellationToken cancellationToken = default);
    Task<List<DefinitionFailedPipelines>> GetAllFailedPipelinesByDefinitionId(CancellationToken cancellationToken = default);
}