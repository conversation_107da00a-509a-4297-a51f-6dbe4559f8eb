namespace Esky.Packages.Application.Abstractions.Pipelines;

public class SequenceStage<TContext>(params PipelineStageBase<TContext>[] stages) : PipelineStageBase<TContext> 
    where TContext : PipelineContext
{
    protected override async Task RunImpl(TContext context, CancellationToken cancellationToken)
    {
        foreach (var stage in stages)
        {
            cancellationToken.ThrowIfCancellationRequested();

            await stage.Run(context, cancellationToken);
        }
    }
}