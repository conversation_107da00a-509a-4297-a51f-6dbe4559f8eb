namespace Esky.Packages.Application.Abstractions.Pipelines;

public class Pipeline
{
    public required string Id { get; init; } = null!;
    public required string GroupId { get; init; } = null!;
    public required string DefinitionId { get; init; } = null!;
    public required PipelineStatus Status { get; init; } = PipelineStatus.Running;
    public string? ErrorMessage { get; init; }
    public required string StartedByUserId { get; init; } = null!;
    public required DateTime CreatedAt { get; init; }
    public required int TotalPartitions { get; init; }
    public required int Partition { get; init; }

    public DateTime? FinishedAt { get; set; }
}