namespace Esky.Packages.Application.Abstractions.Pipelines;

public class GenerationAggregatedStatistics
{
    public required string DefinitionId { get; init; } = null!;
    public required string PipelineGroupId { get; init; } = null!;
    public DateTime? LatestFinishedAt { get; init; }
    public int Completed { get; init; }
    public int Running { get; init; }
    public int Waiting { get; init; }
    public int Failed { get; init; }
    public int Total { get; init; }

    public GenerationAggregatedStatisticsStatus GetStatus()
    {
        if (Waiting == Total)
        {
            return GenerationAggregatedStatisticsStatus.Waiting;
        }

        if (Running > 0)
        {
            if (Failed > 0)
            {
                return GenerationAggregatedStatisticsStatus.RunningWithFailures;
            }

            return GenerationAggregatedStatisticsStatus.Running;
        }

        if (Completed == Total)
        {
            return GenerationAggregatedStatisticsStatus.CompletedWithSuccess;
        }

        if (Completed + Failed == Total)
        {
            return GenerationAggregatedStatisticsStatus.CompletedWithFailures;
        }

        return GenerationAggregatedStatisticsStatus.Running;
    }

    public int? GetCompletedSuccessRate()
    {
        var totalCompleted = Completed + Failed;

        if (Waiting == Total || totalCompleted == 0)
        {
            return null;
        }

        return (int)Math.Round((double)Completed / totalCompleted * 100);
    }
}
