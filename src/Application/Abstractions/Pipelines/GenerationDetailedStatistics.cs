namespace Esky.Packages.Application.Abstractions.Pipelines;

public class GenerationDetailedStatistics
{
    public required string DefinitionId { get; init; } = null!;
    public required string PipelineGroupId { get; init; } = null!;
    public DateTime? LatestFinishedAt { get; init; }
    public FailedPipeline[] FailedPipelines { get; init; } = [];
    public int Completed { get; init; }
    public int Running { get; init; }
    public int Waiting { get; init; }
    public int Failed { get; init; }
    public int Total { get; init; }

    public class FailedPipeline
    {
        public required string PipelineId { get; init; } = null!;
        public string? ErrorMessage { get; init; }
    }
}