using System.Diagnostics;
using Esky.Packages.Application.Observability;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.Abstractions.Pipelines;

public abstract class PipelineStageBase<TContext> where
    TContext : PipelineContext
{
    protected PipelineStageBase()
    {
        StageName = GetStageFriendlyName();
    }

    private string GetStageFriendlyName()
    {
        var fullStageName = GetType().Name.Split(['`'], StringSplitOptions.RemoveEmptyEntries)[0];
        if (fullStageName.EndsWith("Stage"))
        {
            return fullStageName.Substring(0, fullStageName.Length - "Stage".Length);
        }

        return fullStageName;
    }

    protected string StageName { get; init; }

    protected abstract Task RunImpl(TContext context, CancellationToken cancellationToken);

    public virtual async Task Run(TContext context, CancellationToken cancellationToken)
    {
        context.Logger.LogInformation("Stage {stage} starting", StageName);

        var stopwatch = Stopwatch.StartNew();
        var scope = context.Logger.BeginScope("Stage: {stage}", StageName);

        try
        {
            await RunImpl(context, cancellationToken);
            scope?.Dispose();
        }
        catch (Exception ex)
        {
            ApplicationMetrics.RegisterGenerationStepError(StageName);
            context.Logger.LogError(ex, "Unhandled exception during {stageName} stage", StageName);
            scope?.Dispose();

            throw;
        }
        finally
        {
            ApplicationMetrics.RegisterGenerationStepError(StageName, errorCount: 0);
            stopwatch.Stop();
        }

        context.Logger.LogInformation("{stageName} completed in {timeInMs:N0}ms", StageName, stopwatch.ElapsedMilliseconds);
        ApplicationMetrics.RegisterGenerationStepDuration(StageName, stopwatch.ElapsedMilliseconds);
    }
}