using Esky.Packages.Domain.Events;
using Esky.Packages.Domain.Events.PackageFlightVariants;

namespace Esky.Packages.Application.Abstractions.Gateways.PriceTrackerGateway;

public interface IPriceTrackerGateway
{
    Task PublishEvents(ICollection<PackageFlightVariantPriceEvent> events,
        CancellationToken cancellationToken);

    Task PublishEvents(ICollection<PackageHotelOfferVariantPriceEvent> events,
        CancellationToken cancellationToken);
}
