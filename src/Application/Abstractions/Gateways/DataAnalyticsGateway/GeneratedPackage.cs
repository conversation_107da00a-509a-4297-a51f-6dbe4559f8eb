namespace Esky.Packages.Application.Abstractions.Gateways.DataAnalyticsGateway;

public class GeneratedPackage
{
    public DateTime Timestamp { get; set; }
    public required string Id { get; set; }
    public required string DefinitionId { get; set; }
    public int MetaCode { get; set; }
    public DateOnly CheckIn { get; set; }
    public int StayLength { get; set; }
    public required string Currency { get; set; }
    public List<HotelOffer> HotelOffers { get; set; } = [];
    public List<FlightOffer> FlightOffers { get; set; } = [];

    public class HotelOffer
    {
        public required string Occupancy { get; set; }
        public required string MealPlan { get; set; }
        public decimal Price { get; set; }
    }

    public class FlightOffer
    {
        public List<string> Ids { get; set; } = [];
        public required string DepartureAirport { get; set; }
        public required string ArrivalAirport { get; set; }
        public DateOnly DepartureDate { get; set; }
        public DateOnly ReturnArrivalDate { get; set; }
        public required string Occupancy { get; set; }
        public decimal Price { get; set; }
    }
}
