using Esky.Packages.Contract.Packages;
using Esky.Packages.Domain.Model.PackageHotelOffers;

namespace Esky.Packages.Application.Abstractions.Gateways.HotelOfferGateway;

public interface IHotelOfferGateway
{
    Task<IEnumerable<HotelOffer>> Search(HotelOfferSearchCriteria criteria, 
        CancellationToken cancellationToken = default);
    Task<IEnumerable<HotelOffer>> SearchMany(HotelOfferSearchManyCriteria criteria, 
        CancellationToken cancellationToken = default);
}
