using Esky.Packages.Domain.Model.PackageDefinitions.HotelSelectors;

namespace Esky.Packages.Application.Abstractions.Gateways.HotelGateway;

public interface IHotelGateway
{
    Task<List<Hotel>> SearchHotels(
        HotelSelector selector, 
        int? top = null,
        CancellationToken cancellationToken = default);

    Task<List<HotelAirports>> GetAirportsForMetaCodes(
        IReadOnlyCollection<int> metaCodes,
        CancellationToken cancellationToken = default);
}