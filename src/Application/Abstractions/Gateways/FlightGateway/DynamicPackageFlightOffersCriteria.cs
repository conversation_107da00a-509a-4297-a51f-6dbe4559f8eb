using Esky.Packages.Domain.Types;

namespace Esky.Packages.Application.Abstractions.Gateways.FlightGateway;

public class DynamicPackageFlightOffersCriteria
{
    public readonly record struct Route
    {
        public string Departure { get; init; }
        public string Arrival { get; init; }
    }

    public required string PartnerCode { get; init; }
    public required DateOnly Min<PERSON>heckIn { get; init; }
    public required DateOnly Max<PERSON>heckIn { get; init; }
    public required Route[] Routes { get; init; }
    public required int[] StayLengths { get; init; }
    public required int MaxStops { get; init; }
    public required int LimitPerArrivalAirport { get; init; }
    public required Occupancy Occupancy { get; init; }
}