using Esky.Packages.Application.Dtos.FlightOffers;
using Esky.Packages.Domain.Model.PackageFlights;

namespace Esky.Packages.Application.Abstractions.Gateways.FlightGateway;

public interface IFlightGateway
{
    Task<IEnumerable<FlightOffer>> GetFlightOffers(
        FlightSearchCriteria criteria,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<FlightOffer>> GetAlternativeFlightOffers(
        AlternativeFlightOffersCriteria criteria,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<FlightOffer>> GetFlightsByOfferKeyAsync(
        FlightByOfferKeySearchCriteria criteria,
        CancellationToken cancellationToken);
}