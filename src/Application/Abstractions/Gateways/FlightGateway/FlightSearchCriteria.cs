namespace Esky.Packages.Application.Abstractions.Gateways.FlightGateway;

public class FlightSearchCriteria
{
    public readonly struct Destination
    {
        public string DepartureAirport { get; init; }
        public string ArrivalAirport { get; init; }
    }

    public required string PartnerCode { get; init; }
    public required string BaseCurrency { get; init; }
    public required Destination[] Destinations { get; init; }
    public required int MaxStop { get; init; }
    public required TimeSpan? MaxLegDuration { get; init; }
    public required DateOnly CheckIn { get; init; }
    public required int[] StayLengths { get; init; }
    public required bool EnableInboundOutboundDepartureHours { get; init; } = false;
}