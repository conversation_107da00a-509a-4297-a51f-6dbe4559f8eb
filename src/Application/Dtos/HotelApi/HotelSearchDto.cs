namespace Esky.Packages.Application.Dtos.HotelApi;

public record HotelSearchResultDto(
    string Currency,
    ICollection<HotelDto> Hotels,
    ContextDto Context);

public record HotelDto(
    int MetaCode,
    int QualifiedCode,
    ICollection<RoomPackageDto> RoomPackages);

public record RoomPackageDto(
    string OfferId,
    string ProviderOfferGroup,
    ICollection<RoomsDto> Rooms,
    PriceDto Price,
    PriceDetailsDto PriceDetails,
    HotelSearchPaymentTypeDto PaymentType,
    HotelSearchMealPlanDto MealPlan,
    CancellationInfoDto CancellationInfo,
    DateTime ReceivedAt);

public record RoomsDto(
    string Name,
    string Code);

public record PriceDto(
    decimal Amount,
    string Currency);

public record PriceDetailsDto(
    string Currency,
    decimal NetPrice,
    decimal TaxPrice,
    decimal ExtraCharges,
    PriceDto ProviderNetPrice);

public record CancellationInfoDto(
    ICollection<CancellationInfoDetailsDto> CancellationDetails);

public record CancellationInfoDetailsDto(
    DateTime Until,
    PriceDto Price);

public record ContextDto(
    string PartnerCode,
    ICollection<string> ContextsApplied);

public enum HotelSearchPaymentTypeDto
{
    Unknown = 0,
    OnSite = 1,
    AtCheckout = 2,
    Installments = 3
}

public enum HotelSearchMealPlanDto
{
    Unknown = 0,
    None = 1,
    Breakfast = 2,
    HalfBoard = 3,
    FullBoard = 4,
    AllInclusive = 5
}