using Esky.Packages.Contract.Common;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Application.Dtos.Common;

public static class TimeOfDayDtoExtension
{
    public static TimeOfDayDto ToDto(this TimeOfDay timeOfDay)
    {
        return timeOfDay switch
        {
            var tod when tod.Equals(TimeOfDay.Morning) => TimeOfDayDto.Morning,
            var tod when tod.Equals(TimeOfDay.Afternoon) => TimeOfDayDto.Afternoon,
            var tod when tod.Equals(TimeOfDay.Evening) => TimeOfDayDto.Evening,
            _ => throw new ArgumentOutOfRangeException(nameof(timeOfDay), timeOfDay, null)
        };
    }
    
    public static TimeOfDay ToDomain(this TimeOfDayDto timeOfDayDto)
    {
        return timeOfDayDto switch
        {
            TimeOfDayDto.Morning => TimeOfDay.Morning,
            TimeOfDayDto.Afternoon => TimeOfDay.Afternoon,
            TimeOfDayDto.Evening => TimeOfDay.Evening,
            _ => throw new ArgumentOutOfRangeException(nameof(timeOfDayDto), timeOfDayDto, null)
        };
    }
}