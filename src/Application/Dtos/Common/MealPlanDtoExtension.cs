using Esky.Packages.Contract.Common;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Application.Dtos.Common;

public static class MealPlanDtoExtension
{
    public static MealPlanDto ToDto(this MealPlan mealPlan)
    {
        return mealPlan switch
        {
            var mp when mp.Equals(MealPlan.None) => MealPlanDto.None,
            var mp when mp.Equals(MealPlan.Breakfast) => MealPlanDto.Breakfast,
            var mp when mp.Equals(MealPlan.HalfBoard) => MealPlanDto.HalfBoard,
            var mp when mp.Equals(MealPlan.FullBoard) => MealPlanDto.FullBoard,
            var mp when mp.Equals(MealPlan.AllInclusive) => MealPlanDto.AllInclusive,
            _ => throw new ArgumentOutOfRangeException(nameof(mealPlan), mealPlan, null)
        };
    }
    
    public static MealPlan ToDomain(this MealPlanDto mealPlanDto)
    {
        return mealPlanDto switch
        {
            MealPlanDto.None => MealPlan.None,
            MealPlanDto.Breakfast => MealPlan.Breakfast,
            MealPlanDto.HalfBoard => MealPlan.HalfBoard,
            MealPlanDto.FullBoard => MealPlan.FullBoard,
            MealPlanDto.AllInclusive => MealPlan.AllInclusive,
            _ => throw new ArgumentOutOfRangeException(nameof(mealPlanDto), mealPlanDto, null)
        };
    }
}
