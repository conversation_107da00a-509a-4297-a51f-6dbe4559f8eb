using System.Text.Json.Serialization;

namespace Esky.Packages.Application.Dtos.BloomFilterNotifications;

[JsonDerivedType(typeof(BloomFilterFlightUpsertedNotificationDto), "flightUpserted")]
[JsonDerivedType(typeof(BloomFilterHotelOfferUpsertedNotificationDto), "hotelOfferUpserted")]
[JsonDerivedType(typeof(BloomFilterFlightFalsePositiveNotificationDto), "flightFalsePositive")]
[JsonDerivedType(typeof(BloomFilterHotelOfferFalsePositiveNotificationDto), "hotelOfferFalsePositive")]
[JsonPolymorphic(TypeDiscriminatorPropertyName = "_t", UnknownDerivedTypeHandling = JsonUnknownDerivedTypeHandling.FailSerialization)]
public abstract class BloomFilterNotificationDto;