using Esky.Packages.Application.PackagesGeneration;
using Esky.Packages.Contract.PackagesGeneration;

namespace Esky.Packages.Application.Dtos.PackageGenerations;

public static class Extensions
{
    public static GenerationMode ToApplication(this GenerationModeDto dto)
    {
        return dto switch
        {
            GenerationModeDto.Full => GenerationMode.Full,
            GenerationModeDto.FlightsOnly => GenerationMode.FlightsOnly,
            _ => throw new ArgumentOutOfRangeException(nameof(dto), dto, message: null)
        };
    }
}
