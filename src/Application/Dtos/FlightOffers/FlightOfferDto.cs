namespace Esky.Packages.Application.Dtos.FlightOffers;

public record FlightOfferDto(
    string Key,
    string DepartureAirport,
    string ArrivalAirport,
    DateTime DepartureDate,
    DateTime ArrivalDate,
    DateTime ReturnDepartureDate,
    DateTime ReturnArrivalDate,
    int ProviderCode,
    int Stops,
    string[] AirlineCodes,
    string[] FlightIds,
    string[]? LegLocators,
    IReadOnlyDictionary<string, FlightPriceDto> Prices,
    bool BaggageIncluded);
