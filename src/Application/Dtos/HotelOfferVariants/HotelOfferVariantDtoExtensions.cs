using Esky.Packages.Domain.Types;

namespace Esky.Packages.Application.Dtos.HotelOfferVariants;

public static class HotelOfferVariantDtoExtensions
{
    public static MealPlan ToDomain(this HotelVariantMealPlanDto mealPlanDto)
    {
        return mealPlanDto switch
        {
            HotelVariantMealPlanDto.None => MealPlan.None,
            HotelVariantMealPlanDto.Breakfast => MealPlan.Breakfast,
            HotelVariantMealPlanDto.HalfBoard => MealPlan.HalfBoard,
            HotelVariantMealPlanDto.FullBoard => MealPlan.FullBoard,
            HotelVariantMealPlanDto.AllInclusive => MealPlan.AllInclusive,
            _ => throw new ArgumentOutOfRangeException(nameof(mealPlanDto), mealPlanDto, null)
        };
    }

    public static Refundability ToDomain(this HotelVariantRefundabilityDto refundabilityDto)
    {
        return refundabilityDto switch
        {
            HotelVariantRefundabilityDto.Refundable => Refundability.Refundable,
            HotelVariantRefundabilityDto.NonRefundable => Refundability.NonRefundable,
            _ => throw new ArgumentOutOfRangeException(nameof(refundabilityDto), refundabilityDto, null)
        };
    }
}