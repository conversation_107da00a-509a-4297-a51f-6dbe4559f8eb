using System.Diagnostics.CodeAnalysis;

namespace Esky.Packages.Application.Dtos.HotelOffers;

// TODO: Don't parse id
public readonly struct HotelOfferIdDto : IParsable<HotelOfferIdDto>, IEquatable<HotelOfferIdDto>
{
    private const char KeySeparator = ':';

    public DateOnly CheckIn { get; private init; }
    public int StayLength { get; private init; }
    public int MetaCode { get; private init; }
    public string ProviderConfigurationId { get; private init; }
    public HotelOfferOccupancy Occupancy { get; private init; }

    public HotelOfferIdDto(string id)
    {
        (CheckIn, StayLength, MetaCode, ProviderConfigurationId, Occupancy) =
            Parse(id, shouldThrow: true) ?? throw new FormatException("Invalid hotel offer id");
    }

    public HotelOfferIdDto(DateOnly checkIn, int stayLength, int metaCode, string providerConfigurationId,
        HotelOfferOccupancy occupancy)
    {
        CheckIn = checkIn;
        StayLength = stayLength;
        MetaCode = metaCode;
        ProviderConfigurationId = providerConfigurationId;
        Occupancy = occupancy;
    }

    private static (DateOnly CheckIn, int StayLength, int MetaCode, string ProviderConfigurationId,
        HotelOfferOccupancy Occupancy)? Parse(string id, bool shouldThrow)
    {
        var t = id.AsSpan();

        var separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("CheckIn/StayLength date separator not found");
            return null;
        }

        if (!DateOnly.TryParseExact(t.Slice(0, separatorPosition), "yyMMdd", out var checkIn))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(checkIn)} date");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("StayLength/MetaCode separator not found");
            return null;
        }

        if (!int.TryParse(t.Slice(0, separatorPosition), out var stayLength))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(stayLength)} value");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("MetaCode/ProviderConfigurationId separator not found");
            return null;
        }

        if (!int.TryParse(t.Slice(0, separatorPosition), out var metaCode))
        {
            if (shouldThrow)
                throw new FormatException($"Invalid {nameof(metaCode)} value");
            return null;
        }

        t = t.Slice(separatorPosition + 1);

        separatorPosition = t.IndexOf(KeySeparator);
        if (separatorPosition < 0)
        {
            if (shouldThrow)
                throw new FormatException("ProviderConfigurationId/Occupancy separator not found");
            return null;
        }

        var providerConfigurationId = t.Slice(0, separatorPosition).ToString();

        t = t.Slice(separatorPosition + 1);

        if (!HotelOfferOccupancy.TryParse(t.ToString(), null, out var occupancy))
        {
            if (shouldThrow)
                throw new FormatException("Invalid occupancy format");
            return null;
        }

        return (checkIn, stayLength, metaCode, providerConfigurationId, occupancy);
    }

    public static implicit operator HotelOfferIdDto(string id)
        => new(id);

    public static implicit operator string(HotelOfferIdDto hotelOfferId)
        => hotelOfferId.ToString();

    public static bool operator ==(HotelOfferIdDto hotelOfferId, HotelOfferIdDto hotelOfferId2)
        => hotelOfferId.ToString() == hotelOfferId2.ToString();

    public static bool operator !=(HotelOfferIdDto hotelOfferId, HotelOfferIdDto hotelOfferId2)
        => !(hotelOfferId == hotelOfferId2);

    public override bool Equals([NotNullWhen(true)] object? obj)
        => obj is HotelOfferIdDto && Equals(obj);

    public override int GetHashCode()
        => ToString().GetHashCode();

    public override string ToString()
        => $"{CheckIn.ToString("yyMMdd")}{KeySeparator}{StayLength}{KeySeparator}{MetaCode}{KeySeparator}" +
           $"{ProviderConfigurationId}{KeySeparator}{Occupancy.ToString()}";

    public static HotelOfferIdDto Parse(string s, IFormatProvider? provider)
        => (HotelOfferIdDto)s;

    public static bool TryParse([NotNullWhen(true)] string? s, IFormatProvider? provider, out HotelOfferIdDto result)
    {
        if (s != null && Parse(s, false) != null)
        {
            result = new HotelOfferIdDto(s);
            return true;
        }

        result = default;
        return false;
    }

    public bool Equals(HotelOfferIdDto other)
    {
        return CheckIn.Equals(other.CheckIn) && StayLength == other.StayLength && MetaCode == other.MetaCode &&
               ProviderConfigurationId == other.ProviderConfigurationId && Occupancy == other.Occupancy;
    }
}