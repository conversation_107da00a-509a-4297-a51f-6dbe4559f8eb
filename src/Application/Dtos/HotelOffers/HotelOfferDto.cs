namespace Esky.Packages.Application.Dtos.HotelOffers;

public class HotelOfferDto
{
    public required string Id { get; set; }
    public required DateOnly CheckIn { get; set; }
    public required int StayLength { get; set; }
    public required int MetaCode { get; set; }
    public required string ProviderConfigurationId { get; set; }
    public required HotelOfferOccupancy Occupancy { get; set; }
    public required Dictionary<HotelOfferMealPlanDto, Dictionary<HotelOfferRefundabilityDto, List<HotelOfferRoomOfferDto>>> RoomOffersByMealPlanByRefundability { get; set; } = [];
    
    public enum HotelOfferMealPlanDto
    {
        None,
        Breakfast,
        HalfBoard,
        FullBoard,
        AllInclusive
    }

    public enum HotelOfferRefundabilityDto
    {
        Refundable,
        NonRefundable
    }

    public class HotelOfferRoomOfferDto
    {
        public required int Availability { get; init; }
        public required HotelOfferMoneyDto Price { get; init; }
        public string[] MetaRoomIds { get; init; } = [];
    }

    public class HotelOfferMoneyDto
    {
        public required decimal Value { get; init; }
        public required string Currency { get; init; }
    }
}