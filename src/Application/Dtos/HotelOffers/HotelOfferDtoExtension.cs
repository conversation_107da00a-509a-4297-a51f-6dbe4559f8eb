using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Application.Dtos.HotelOffers;

public static class HotelOfferDtoExtension
{
    public static bool HasSupportedOccupancy(this HotelOfferDto dto)
    {
        return HasSupportedOccupancy(dto.Occupancy);
    }

    public static bool HasSupportedOccupancy(this HotelOfferIdDto dto)
    {
        return HasSupportedOccupancy(dto.Occupancy);
    }

    private static bool HasSupportedOccupancy(HotelOfferOccupancy hotelOfferOccupancy)
    {
        // TODO: Refactor this - move this logic somewhere else
        var occupancy = new Occupancy(hotelOfferOccupancy.Adults, hotelOfferOccupancy.Children);
        
        return PackageOccupancy.AreExactChildBuckets(occupancy);
    }

    public static HotelOffer ToDomain(this HotelOfferDto dto)
    {
        return new HotelOffer
        {
            MetaCode = dto.MetaCode,
            CheckIn = dto.CheckIn,
            StayLength = dto.StayLength,
            ProviderConfigurationId = new ProviderConfigurationId(dto.ProviderConfigurationId),
            Occupancy =  new Occupancy(dto.Occupancy.Adults, dto.Occupancy.Children),
            RoomOffersByMealPlanByRefundability = ToDomain(dto.RoomOffersByMealPlanByRefundability),
            UpdatedAt = DateTime.UtcNow
        };
    }

    private static MealPlan ToDomain(HotelOfferDto.HotelOfferMealPlanDto dto)
    {
        return dto switch
        {
            HotelOfferDto.HotelOfferMealPlanDto.None => MealPlan.None,
            HotelOfferDto.HotelOfferMealPlanDto.Breakfast => MealPlan.Breakfast,
            HotelOfferDto.HotelOfferMealPlanDto.HalfBoard => MealPlan.HalfBoard,
            HotelOfferDto.HotelOfferMealPlanDto.FullBoard => MealPlan.FullBoard,
            HotelOfferDto.HotelOfferMealPlanDto.AllInclusive => MealPlan.AllInclusive,
            _ => throw new ArgumentOutOfRangeException(nameof(dto), dto, message: null)
        };
    }

    private static Refundability ToDomain(HotelOfferDto.HotelOfferRefundabilityDto dto)
    {
        return dto switch
        {
            HotelOfferDto.HotelOfferRefundabilityDto.Refundable => Refundability.Refundable,
            HotelOfferDto.HotelOfferRefundabilityDto.NonRefundable => Refundability.NonRefundable,
            _ => throw new ArgumentOutOfRangeException(nameof(dto), dto, message: null)
        };
    }

    private static Money ToDomain(HotelOfferDto.HotelOfferMoneyDto dto)
    {
        return new Money(dto.Value, dto.Currency);
    }

    private static Dictionary<MealPlan, Dictionary<Refundability, RoomOffer[]>> ToDomain(
        Dictionary<HotelOfferDto.HotelOfferMealPlanDto, Dictionary<HotelOfferDto.HotelOfferRefundabilityDto,
            List<HotelOfferDto.HotelOfferRoomOfferDto>>> roomOffersByMealPlanByRefundability)
    {
        return roomOffersByMealPlanByRefundability.SelectMany(mealPlanEntry => mealPlanEntry.Value.Select(refundabilityEntry => (
                MealPlan: ToDomain(mealPlanEntry.Key),
                Refundability: ToDomain(refundabilityEntry.Key),
                RoomOffersByMealPlanByRefundability: refundabilityEntry.Value
                    .Select(roomOffer => new RoomOffer
                    {
                        Availability = roomOffer.Availability,
                        Price = ToDomain(roomOffer.Price),
                        RoomIds = new RoomIds(roomOffer.MetaRoomIds)
                    }).ToArray())))
            .GroupBy(x => x.MealPlan)
            .ToDictionary(g => g.Key, g => g
                .GroupBy(x => x.Refundability)
                .ToDictionary(g2 => g2.Key, g2 => g2.First().RoomOffersByMealPlanByRefundability));
    }
}