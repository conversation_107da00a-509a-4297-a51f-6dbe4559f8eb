using System.Diagnostics.CodeAnalysis;
using System.Text;

namespace Esky.Packages.Application.Dtos.HotelOffers;

public readonly struct HotelOfferOccupancy : IEquatable<HotelOfferOccupancy>, IParsable<HotelOfferOccupancy>
{
    public static readonly HotelOfferOccupancy A1 = new(1);
    public static readonly HotelOfferOccupancy A2 = new(2);
    public static readonly HotelOfferOccupancy A3 = new(3);
    public static readonly HotelOfferOccupancy A2C12 = new(2, [12]);
    public static readonly HotelOfferOccupancy A2C12C12 = new(2, [12, 12]);

    private readonly string? _key;
    private readonly int[]? _children;

    public HotelOfferOccupancy(int adults, params int[] children)
    {
        Adults = adults;
        _children = children;

        _key = (adults, children) switch
        {
            (0, []) => "",
            (1, []) => "A1",
            (1, [5]) => "A1C5",
            (1, [12]) => "A1C12",
            (1, [15]) => "A1C15",
            (2, []) => "A2",
            (2, [5]) => "A2C5",
            (2, [12]) => "A2C12",
            (2, [15]) => "A2C15",
            (2, [12, 12]) => "A2C12C12",
            // ReSharper disable once RedundantToStringCallForValueType
            (1, [_]) => "A1C" + children[0].ToString(),
            // ReSharper disable once RedundantToStringCallForValueType
            (2, [_]) => "A2C" + children[0].ToString(),
            // ReSharper disable once RedundantToStringCallForValueType
            (_, []) => "A" + adults.ToString(),
            _ => GenKey(adults, children)
        };
    }

    private string GenKey(int adults, int[] children)
    {
        StringBuilder b = new();

        b.Append('A');
        b.Append(adults);

        foreach (var child in children.OrderBy(c => c))
        {
            b.Append('C');
            b.Append(child);
        }

        return b.ToString();
    }

    public int Adults { get; }
    public int[] Children => _children ?? [];

    public string Key => _key ?? "";

    public override string ToString()
        => Key;

    public override int GetHashCode()
        => Key.GetHashCode();

    public bool Equals(HotelOfferOccupancy occupancy)
        => Key == occupancy.Key;

    public override bool Equals(object? obj)
    {
        if (obj is HotelOfferOccupancy o)
            return Equals(o);

        return base.Equals(obj);
    }

    public static bool operator ==(HotelOfferOccupancy l, HotelOfferOccupancy r)
        => l.Key == r.Key;

    public static bool operator !=(HotelOfferOccupancy l, HotelOfferOccupancy r)
        => l.Key != r.Key;

    public static HotelOfferOccupancy Parse(string key, IFormatProvider? provider)
    {
        if (TryParse(key, null, out var occupancy))
            return occupancy;

        throw new FormatException("Invalid occupancy format");
    }

    public static bool TryParse([NotNullWhen(true)] string? key, IFormatProvider? provider, out HotelOfferOccupancy occupancy)
    {
        occupancy = new HotelOfferOccupancy();

        key ??= "";
        var k = key.AsSpan();

        if (k.Length > 0 && k[0] != 'A')
        {
            return false;
        }

        var pos = k.IndexOf('C');
        if (pos == -1)
            pos = k.Length;

        if (!int.TryParse(k.Slice(1, pos - 1), out var adult))
        {
            return false;
        }

        k = k[pos..];

        int i = 0;
        Span<int> numbers = stackalloc int[10];
        for (; i < numbers.Length && k.Length != 0; i++)
        {
            if (k[0] != 'C')
            {
                return false;
            }

            k = k[1..];

            pos = k.IndexOf('C');
            if (pos == -1)
                pos = k.Length;

            if (!int.TryParse(k.Slice(0, pos), out numbers[i]))
            {
                return false;
            }

            k = k[pos..];
        }

        var children = i == 0 ? [] : new int [i];
        for (var j = 0; j < i; j++)
            children[j] = numbers[j];

        if (k.Length != 0)
        {
            return false;
        }

        occupancy = new HotelOfferOccupancy(adult, children);
        return true;
    }

    public static implicit operator HotelOfferOccupancy(string occupancyKey)
        => Parse(occupancyKey, null);
}