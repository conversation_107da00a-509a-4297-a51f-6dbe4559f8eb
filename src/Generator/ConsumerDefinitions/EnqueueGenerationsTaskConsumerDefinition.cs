using Esky.Packages.Generator.Consumers;
using MassTransit;

namespace Esky.Packages.Generator.ConsumerDefinitions;

internal class EnqueueGenerationsTaskConsumerDefinition : ConsumerDefinition<EnqueueGenerationsTaskConsumer>
{
    public EnqueueGenerationsTaskConsumerDefinition()
    {
        ConcurrentMessageLimit = 4;
    }

    protected override void ConfigureConsumer(IReceiveEndpointConfigurator endpointConfigurator, 
        IConsumerConfigurator<EnqueueGenerationsTaskConsumer> consumerConfigurator, IRegistrationContext context)
    {
        endpointConfigurator.PrefetchCount = 4;
        
        endpointConfigurator.UseDelayedRedelivery(r => 
            r.Exponential(10, TimeSpan.FromSeconds(15), TimeSpan.FromSeconds(600), TimeSpan.FromSeconds(15)));
        
        endpointConfigurator.UseKillSwitch(options => options
            .SetActivationThreshold(4)
            .SetTripThreshold(70) // % of failed messages in the activation threshold
            .SetRestartTimeout(TimeSpan.FromMinutes(5)));
    }
}