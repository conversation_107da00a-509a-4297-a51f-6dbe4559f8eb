using Esky.Packages.Generator.Consumers;
using MassTransit;

namespace Esky.Packages.Generator.ConsumerDefinitions;

internal class RetryAllFailedPipelinesTaskConsumerDefinition : ConsumerDefinition<RetryAllFailedPipelinesTaskConsumer>
{
    public RetryAllFailedPipelinesTaskConsumerDefinition()
    {
        ConcurrentMessageLimit = 1;
    }

    protected override void ConfigureConsumer(IReceiveEndpointConfigurator endpointConfigurator, 
        IConsumerConfigurator<RetryAllFailedPipelinesTaskConsumer> consumerConfigurator, IRegistrationContext context)
    {
        endpointConfigurator.PrefetchCount = 1;
        
        endpointConfigurator.UseDelayedRedelivery(r => 
            r.Exponential(10, TimeSpan.FromSeconds(15), TimeSpan.FromSeconds(600), TimeSpan.FromSeconds(15)));
    }
}