using Esky.Packages.Generator.Consumers;
using MassTransit;

namespace Esky.Packages.Generator.ConsumerDefinitions;

internal class GeneratePackagesTaskConsumerDefinition : ConsumerDefinition<GeneratePackagesTaskConsumer>
{
    public GeneratePackagesTaskConsumerDefinition()
    {
        ConcurrentMessageLimit = 5;
    }

    protected override void ConfigureConsumer(IReceiveEndpointConfigurator endpointConfigurator, 
        IConsumerConfigurator<GeneratePackagesTaskConsumer> consumerConfigurator, IRegistrationContext context)
    {
        endpointConfigurator.PrefetchCount = 5;
        
        endpointConfigurator.UseDelayedRedelivery(r => 
            r.Exponential(5, TimeSpan.FromSeconds(15), TimeSpan.FromSeconds(180), TimeSpan.FromSeconds(15)));
        
        endpointConfigurator.UseKillSwitch(options => options
            .SetActivationThreshold(4)
            .SetTripThreshold(70) // % of failed messages in the activation threshold
            .SetRestartTimeout(TimeSpan.FromMinutes(2)));
    }
}