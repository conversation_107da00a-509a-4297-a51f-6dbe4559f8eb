using System.Diagnostics.Metrics;
using Esky.Packages.Application.Observability;

namespace Esky.Packages.Generator.Observability;

public static class Metrics
{
    public const string MeterName = "packages-generator";
    private static readonly Meter Meter = new(MeterName);

    public static void Initialize()
    {
        var name = Meter.Name;
    }

    internal static readonly ObservableCounter<long> GeneratedPackageFlights =
        Meter.CreateObservableCounter($"{MeterName}_generated_package_flights",
            () => ApplicationMetrics.GeneratedPackageFlights);

    internal static readonly ObservableCounter<long> UpsertedPackageFlights =
        Meter.CreateObservableCounter($"{MeterName}_upserted_package_flights",
            () => ApplicationMetrics.UpsertedPackageFlights);

    internal static readonly ObservableCounter<long> GeneratedPackageHotelOffers =
        Meter.CreateObservableCounter($"{MeterName}_generated_package_hotel_offers",
            () => ApplicationMetrics.GeneratedPackageHotelOffers);

    internal static readonly ObservableCounter<long> UpsertedPackageHotelOffers =
        Meter.CreateObservableCounter($"{MeterName}_upserted_package_hotel_offers",
            () => ApplicationMetrics.UpsertedPackageHotelOffers);

    internal static readonly ObservableCounter<long> GenerationStepDuration =
        Meter.CreateObservableCounter($"{MeterName}_generation_step_duration",
            () => ApplicationMetrics.GenerationStepDuration
                .Select(kvp =>
                    new Measurement<long>(kvp.Value, new[] { new KeyValuePair<string, object?>("step", kvp.Key) })));
    
    internal static readonly ObservableCounter<long> GenerationStepErrors = 
        Meter.CreateObservableCounter($"{MeterName}_generation_step_errors",
            () => ApplicationMetrics.GenerationStepErrors
                .Select(kvp =>
                    new Measurement<long>(kvp.Value, new[] { new KeyValuePair<string, object?>("step", kvp.Key) })));
}