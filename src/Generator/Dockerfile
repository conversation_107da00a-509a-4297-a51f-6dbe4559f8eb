FROM europe-docker.pkg.dev/esky-common/esky-docker-virtual/dotnet/aspnet:9.0 AS base
USER $APP_UID
WORKDIR /app
EXPOSE 8080

FROM europe-docker.pkg.dev/esky-common/esky-docker-virtual/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["NuGet.Config", "NuGet.Config"]
COPY ["Directory.Packages.props", "Directory.Packages.props"]
COPY ["src/Generator/Generator.csproj", "src/Generator/"]
COPY ["src/Domain/Domain.csproj", "src/Domain/"]
COPY ["src/Application/Application.csproj", "src/Application/"]
COPY ["src/Infrastructure/Infrastructure.csproj", "src/Infrastructure/"]
COPY ["src/Contract/Contract.csproj", "src/Contract/"]
RUN dotnet restore "src/Generator/Generator.csproj"
COPY . .
WORKDIR "/src/src/Generator"
RUN dotnet build "Generator.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "Generator.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENV DOTNET_gcServer=1
ENV DOTNET_GDynamicAdaptationMode=1
ENTRYPOINT ["dotnet", "Generator.dll"]
