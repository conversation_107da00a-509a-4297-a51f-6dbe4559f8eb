using Esky.Packages.Application.DependencyInjections;
using Esky.Packages.Generator;
using Esky.Packages.Infrastructure.DependencyInjections;
using NLog.Web;

var builder = WebApplication.CreateBuilder(args);
var configuration = builder.Configuration;
var services = builder.Services;
var environment = builder.Environment;

if (!builder.Environment.IsDevelopment())
{
    builder.Logging.ClearProviders();
    builder.Host.UseNLog();
}

services
    .AddMessageBus(configuration);

services
    .AddApplication(o =>
    {
        o.AddGenerationPipeline();
        o.AddCurrencyConverter();
    })
    .AddInfrastructure(configuration, o =>
    {
        o.AddTaskScheduler(skipMessageBusConfiguration: true);
        o.AddDataAnalyticsGateway();
        o.AddBloomFilterNotificationGateway();
    });
    
services
    .AddObservability(configuration)
    .AddHealthChecks();

var app = builder.Build();

app.MapHealthChecks("/healthz");
app.MapPrometheusScrapingEndpoint();

app.Run();

namespace Esky.Packages.Generator
{
    // Required for WebApplicationFactory in tests
    public partial class Program { }
}