{"HotelGateway": {"ConnectionString": "SECRET"}, "Mongo": {"ConnectionString": "SECRET"}, "BloomFilterNotificationProducer": {"BootstrapServers": "esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-0.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-1.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-2.service.gcp-staging.consul:9092", "Topic": "bloomFilterNotifications", "CompressionType": "Lz4"}, "BigQuery": {"ProjectId": "esky-ets-logs-ci", "Dataset": "Packages"}}