<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <RootNamespace>Esky.Packages.Generator</RootNamespace>
        <UserSecretsId>642dfe01-8c09-433e-8d89-6ec470d515bd</UserSecretsId>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\Infrastructure\Infrastructure.csproj" />
    </ItemGroup>

	<ItemGroup>
		<Content Update="appsettings.Development.json">
			<DependentUpon>appsettings.json</DependentUpon>
		</Content>
		<Content Update="appsettings.Staging.json">
			<DependentUpon>appsettings.json</DependentUpon>
		</Content>
		<Content Update="appsettings.Production.json">
			<DependentUpon>appsettings.json</DependentUpon>
		</Content>
		<Content Update="nlog.config">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="MassTransit" />
		<PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" />
		<PackageReference Include="OpenTelemetry.Exporter.Prometheus.AspNetCore"/>
		<PackageReference Include="OpenTelemetry.Extensions.Hosting"/>
		<PackageReference Include="OpenTelemetry.Extensions.Propagators" />
		<PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore"/>
		<PackageReference Include="OpenTelemetry.Instrumentation.Http"/>
		<PackageReference Include="OpenTelemetry.Instrumentation.Process"/>
		<PackageReference Include="OpenTelemetry.Instrumentation.Runtime"/>
	</ItemGroup>

</Project>
