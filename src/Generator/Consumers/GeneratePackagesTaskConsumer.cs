using Esky.Packages.Application.Abstractions.Tasks;
using Esky.Packages.Application.Services;
using Esky.Packages.Application.Tasks;
using MassTransit;

namespace Esky.Packages.Generator.Consumers;

internal class GeneratePackagesTaskConsumer(
    IPackageGenerationService service)
    : IConsumer<GeneratePackagesTask>
{
    public async Task Consume(ConsumeContext<GeneratePackagesTask> context)
    {
        await service.GeneratePackages(
            context.Message.DefinitionId, 
            context.Message.Options, 
            context.Message.Parameters, 
            context.CancellationToken);
    }
}