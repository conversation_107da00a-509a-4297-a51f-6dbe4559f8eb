using Esky.Packages.Application.Abstractions.Tasks;
using Esky.Packages.Application.Services;
using MassTransit;

namespace Esky.Packages.Generator.Consumers;

internal class RetryAllFailedPipelinesTaskConsumer(
    IPackageGenerationService service)
    : IConsumer<RetryAllFailedPipelinesTask>
{
    public async Task Consume(ConsumeContext<RetryAllFailedPipelinesTask> context)
    {
        await service.RetryAllFailedPipelines(
            context.Message,
            context.CancellationToken);
    }
}