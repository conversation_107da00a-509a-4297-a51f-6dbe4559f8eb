using Esky.Packages.Application.Abstractions.Tasks;
using Esky.Packages.Application.Services;
using Esky.Packages.Application.Tasks;
using MassTransit;

namespace Esky.Packages.Generator.Consumers;

internal class RetryPipelinesTaskConsumer(
    IPackageGenerationService service)
    : IConsumer<RetryPipelinesTask>
{
    public async Task Consume(ConsumeContext<RetryPipelinesTask> context)
    {
        await service.RetryPipelines(
            context.Message,
            context.CancellationToken);
    }
}