using Esky.Packages.Application.Abstractions.Tasks;
using Esky.Packages.Application.Services;
using Esky.Packages.Application.Tasks;
using MassTransit;

namespace Esky.Packages.Generator.Consumers;

internal class EnqueueGenerationsTaskConsumer(
    IPackageGenerationService service)
    : IConsumer<EnqueueGenerationsTask>
{
    public async Task Consume(ConsumeContext<EnqueueGenerationsTask> context)
    {
        await service.EnqueueGenerations(
            context.Message, 
            context.CancellationToken);
    }
}
