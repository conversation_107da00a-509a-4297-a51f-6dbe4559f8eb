using Esky.Hotels.Infrastructure.Kafka;
using Esky.Packages.FlightVariantor.Observability;
using Esky.Packages.Infrastructure.Configuration;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;

namespace Esky.Packages.FlightVariantor;

public static class DependencyInjection
{
    internal static IServiceCollection AddKafkaConsumers(this IServiceCollection services, IConfiguration configuration)
    {
        services.RegisterOptions<FlightVariantConsumerOptions>(configuration,
            FlightVariantConsumerOptions.ConfigurationSection);

        services.AddKafka(configuration);

        services.AddHostedService<FlightVariantConsumer>();

        return services;
    }

    internal static IServiceCollection AddObservability(this IServiceCollection services)
    {
        var appName = "esky-packages-flightvariantor";

        services.AddOpenTelemetry()
            .WithMetrics(options =>
                options.SetResourceBuilder(ResourceBuilder.CreateDefault().AddService(appName))
                    .AddMeter(Metrics.MeterName)
                    .AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation()
                    .AddRuntimeInstrumentation()
                    .AddProcessInstrumentation()
                    .AddPrometheusExporter()
            );

        return services;
    }
}