using System.Diagnostics;
using System.Text.Json;
using Esky.Hotels.Infrastructure.Kafka.Consumer;
using Esky.Packages.Application.Services;
using Esky.Packages.Domain.Events.PackageFlightVariants;
using Esky.Packages.FlightVariantor.Observability;
using Esky.Packages.Infrastructure.Serialization.Contexts;

namespace Esky.Packages.FlightVariantor;

public class FlightVariantConsumer(
    KafkaConsumerFactory kafkaConsumerFactory,
    FlightVariantConsumerOptions options,
    IPackageFlightVariantService packageFlightVariantService,
    ILogger<FlightVariantConsumer> logger) : BackgroundService
{
    private KafkaConsumer<string, byte[]> _flightQuoteConsumer = null!;

    private void InitializeKafka()
    {
        _flightQuoteConsumer = kafkaConsumerFactory.Create<string, byte[]>(o => o
            .WithBootstrapServers(options.BootstrapServers)
            .WithDefaultTopics(options.Topic)
            .WithConsumerGroupId(options.ConsumerGroup)
            .WithAutoOffsetReset(options.AutoOffsetReset)
            .WithAutoCommitIntervalMs(200)
            .WithLogPartitionRebalance()
        );

        _flightQuoteConsumer.Subscribe();
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        InitializeKafka();

        while (!stoppingToken.IsCancellationRequested)
        {
            var batch = await _flightQuoteConsumer.ConsumeBatchAsync(new KafkaBatchOptions<string, byte[]>
            {
                MaxBatchSize = options.MaxBatchSize,
                MaxProcessedMessages = options.MaxProcessedMessagesPerBatch,
                StoppingToken = stoppingToken
            });

            logger.LogDebug("Received batch with {receivedMessageCount} messages.", batch.InterestingMessageCount);

            Metrics.FlightVariantBatches.Add(1);
            Metrics.FlightVariantMessages.Add(batch.InterestingResults.Count);

            await ProcessBatch(batch, stoppingToken);
        }
    }

    private async Task ProcessBatch(KafkaBatch<string, byte[]> batch, CancellationToken cancellationToken)
    {
        var watch = Stopwatch.StartNew();

        if (batch.InterestingResults.Count > 0)
        {
            var events = DeserializeEvents(batch);
            await packageFlightVariantService.UpdateVariants(events, cancellationToken);
            batch.StoreOffsets();
        }

        watch.Stop();
        Metrics.FlightVariantBatchProcessDuration.Record(watch.ElapsedMilliseconds);
    }

    private List<PackageFlightVariantEvent> DeserializeEvents(KafkaBatch<string, byte[]> batch)
    {
        var events = new List<PackageFlightVariantEvent>();
        var watch = Stopwatch.StartNew();

        foreach (var result in batch.InterestingResults)
        {
            if (result.Message.Value == null)
            {
                logger.LogWarning("Received unexpected tombstone message, ignoring");
                Metrics.FlightVariantTombstoneMessages.Add(1);
                continue;
            }

            try
            {
                var ev = JsonSerializer.Deserialize<PackageFlightVariantEvent>(result.Message.Value,
                    PackageFlightVariantEventJsonContext.Default.PackageFlightVariantEvent)!;

                events.Add(ev);
            }
            catch (Exception e)
            {
                logger.LogError(e, "Failed to deserialize message");
                Metrics.FlightVariantInvalidMessages.Add(1);

                throw;
            }
        }

        watch.Stop();
        Metrics.FlightVariantDeserializationDuration.Record(watch.ElapsedMilliseconds);

        return events;
    }

}