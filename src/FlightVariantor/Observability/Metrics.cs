using System.Diagnostics.Metrics;

namespace Esky.Packages.FlightVariantor.Observability;

public static class Metrics
{
    public const string MeterName = "packages-flightvariantor";
    private static readonly Meter Meter = new(MeterName);

    static Metrics()
    {
        FlightVariantBatches.Add(0);
        FlightVariantMessages.Add(0);
        FlightVariantInvalidMessages.Add(0);
        FlightVariantTombstoneMessages.Add(0);
    }

    // Flight metrics
    internal static readonly Counter<long> FlightVariantBatches = 
        Meter.CreateCounter<long>($"{MeterName}_flight_variant_batches", "count");
    
    internal static readonly Counter<long> FlightVariantMessages = 
        Meter.CreateCounter<long>($"{MeterName}_flight_variant_messages", "count");

    internal static readonly Counter<long> FlightVariantInvalidMessages =
        Meter.CreateCounter<long>($"{MeterName}_flight_variant_invalid_messages", "count");

    internal static readonly Counter<long> FlightVariantTombstoneMessages =
        Meter.CreateCounter<long>($"{MeterName}_flight_variant_tombstone_messages", "msg");

    internal static readonly Histogram<double> FlightVariantBatchProcessDuration =
        Meter.CreateHistogram<double>($"{MeterName}_flight_variant_batch_process", "milliseconds");

    internal static readonly Histogram<double> FlightVariantDeserializationDuration =
        Meter.CreateHistogram<double>($"{MeterName}_flight_variant_batch_deserialization", "milliseconds");
}