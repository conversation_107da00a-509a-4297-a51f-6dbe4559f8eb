using Confluent.Kafka;

namespace Esky.Packages.FlightVariantor;

public class FlightVariantConsumerOptions
{
    public const string ConfigurationSection = "FlightVariantConsumer";
    
    public string BootstrapServers { get; set; } = default!;
    public string ConsumerGroup { get; set; } = default!;
    public AutoOffsetReset AutoOffsetReset { get; set; } = AutoOffsetReset.Latest;
    public string Topic { get; set; } = default!;

    public int MaxBatchSize { get; set; } = 10000;
    public int MaxProcessedMessagesPerBatch { get; set; } = 100_000;
}