using Esky.Hotels.Infrastructure.Kafka;
using Esky.Packages.Infrastructure.Configuration;
using Esky.Packages.Repricer.Consumers;
using Esky.Packages.Repricer.Observability;
using Esky.Packages.Repricer.Options;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using FlightQuoteConsumer = Esky.Packages.Repricer.Consumers.FlightQuoteConsumer;

namespace Esky.Packages.Repricer;

public static class DependencyInjection
{
    internal static IServiceCollection AddKafkaConsumers(this IServiceCollection services, IConfiguration configuration)
    {
        services.RegisterOptions<HotelOfferQuoteConsumerOptions>(configuration,
            HotelOfferQuoteConsumerOptions.ConfigurationSection);
        services.RegisterOptions<FlightQuoteConsumerOptions>(configuration,
            FlightQuoteConsumerOptions.ConfigurationSection);

        services.AddKafka(configuration);

        services.AddHostedService<HotelOfferQuoteConsumer>();
        services.AddHostedService<FlightQuoteConsumer>();

        return services;
    }
    
    internal static IServiceCollection AddFalsePositiveMeter(this IServiceCollection services)
    {
        services.AddSingleton<BloomFilterFalsePositiveMeter>();
        services.AddSingleton<IBloomFilterFalsePositiveMeter>(
            p => p.GetRequiredService<BloomFilterFalsePositiveMeter>());
        services.AddHostedService(p => p.GetRequiredService<BloomFilterFalsePositiveMeter>());
        
        return services;
    }

    internal static IServiceCollection AddObservability(this IServiceCollection services)
    {
        var appName = "esky-packages-repricer";

        services.AddOpenTelemetry()
            .WithMetrics(options =>
                options.SetResourceBuilder(ResourceBuilder.CreateDefault().AddService(appName))
                    .AddMeter(Metrics.MeterName)
                    .AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation()
                    .AddRuntimeInstrumentation()
                    .AddProcessInstrumentation()
                    .AddPrometheusExporter()
            );

        return services;
    }
}