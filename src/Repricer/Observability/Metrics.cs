using System.Diagnostics.Metrics;

namespace Esky.Packages.Repricer.Observability;

public static class Metrics
{
    public const string MeterName = "packages-repricer";
    private static readonly Meter Meter = new(MeterName);

    static Metrics()
    {
        FlightBatches.Add(0);
        FlightMessages.Add(0);
        FlightInvalidMessages.Add(0);
        FlightTombstoneMessages.Add(0);
        
        HotelOfferBatches.Add(0);
        HotelOfferMessages.Add(0);
        HotelOfferInvalidMessages.Add(0);
        HotelOfferTombstoneMessages.Add(0);
    }

    // Flight metrics
    internal static readonly Counter<long> FlightBatches = 
        Meter.CreateCounter<long>($"{MeterName}_flight_batches", "count");
    
    internal static readonly Counter<long> FlightMessages = 
        Meter.CreateCounter<long>($"{MeterName}_flight_messages", "count");

    internal static readonly Counter<long> FlightInvalidMessages =
        Meter.CreateCounter<long>($"{MeterName}_flight_invalid_messages", "count");

    internal static readonly Counter<long> FlightTombstoneMessages =
        Meter.CreateCounter<long>($"{MeterName}_flight_tombstone_messages", "msg");

    internal static readonly Histogram<double> FlightBatchProcessDuration =
        Meter.CreateHistogram<double>($"{MeterName}_flight_batch_process", "milliseconds");

    internal static readonly Histogram<double> FlightDeserializationDuration =
        Meter.CreateHistogram<double>($"{MeterName}_flight_batch_deserialization", "milliseconds");
    
    internal static readonly Histogram<double> FlightMessageSize =
        Meter.CreateHistogram<double>($"{MeterName}_flight_message_size", "bytes");
    
    internal static readonly Counter<long> FlightFalsePositives =
        Meter.CreateCounter<long>($"{MeterName}_flight_false_positives", "count");
    
    internal static readonly Counter<long> FlightTruePositives =
        Meter.CreateCounter<long>($"{MeterName}_flight_true_positives", "count");
    
    internal static readonly Counter<long> FlightUpdatedQuotes =
        Meter.CreateCounter<long>($"{MeterName}_flight_updated_quotes", "count");
    
    public static void RecordFlightQuoteStatistics(int truePositives, int falsePositives, int updated)
    {
        FlightFalsePositives.Add(falsePositives);
        FlightTruePositives.Add(truePositives);
        FlightUpdatedQuotes.Add(updated);
    }
    
    // Hotel offer metrics
    internal static readonly Counter<long> HotelOfferBatches = 
        Meter.CreateCounter<long>($"{MeterName}_hotel_offer_batches", "count");
    
    internal static readonly Counter<long> HotelOfferMessages = 
        Meter.CreateCounter<long>($"{MeterName}_hotel_offer_messages", "count");

    internal static readonly Counter<long> HotelOfferInvalidMessages =
        Meter.CreateCounter<long>($"{MeterName}_hotel_offer_invalid_messages", "count");

    internal static readonly Counter<long> HotelOfferTombstoneMessages =
        Meter.CreateCounter<long>($"{MeterName}_hotel_offer_tombstone_messages", "msg");

    internal static readonly Histogram<double> HotelOfferBatchProcessDuration =
        Meter.CreateHistogram<double>($"{MeterName}_hotel_offer_batch_process", "milliseconds");

    internal static readonly Histogram<double> HotelOfferDeserializationDuration =
        Meter.CreateHistogram<double>($"{MeterName}_hotel_offer_batch_deserialization", "milliseconds");
    
    internal static readonly Histogram<double> HotelOfferMessageSize =
        Meter.CreateHistogram<double>($"{MeterName}_hotel_offer_message_size", "bytes");
    
    internal static readonly Counter<long> HotelOfferFalsePositives =
        Meter.CreateCounter<long>($"{MeterName}_hotel_offer_false_positives", "count");
    
    internal static readonly Counter<long> HotelOfferTruePositives =
        Meter.CreateCounter<long>($"{MeterName}_hotel_offer_true_positives", "count");
    
    internal static readonly Counter<long> HotelOfferUpdatedQuotes =
        Meter.CreateCounter<long>($"{MeterName}_hotel_offer_updated_quotes", "count");

    public static void RecordHotelOfferQuoteStatistics(int truePositives, int falsePositives, int updated)
    {
        HotelOfferFalsePositives.Add(falsePositives);
        HotelOfferTruePositives.Add(truePositives);
        HotelOfferUpdatedQuotes.Add(updated);
    }
}