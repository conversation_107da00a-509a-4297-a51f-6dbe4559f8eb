using Esky.Packages.Application.Abstractions.Gateways.BloomFilterNotificationGateway;
using Esky.Packages.Application.Dtos.BloomFilterNotifications;
using Esky.Packages.Infrastructure.Observability;

namespace Esky.Packages.Repricer.Observability;

public interface IBloomFilterFalsePositiveMeter
{
    void RegisterFlightFalsePositive(int count);
    void RegisterHotelOfferFalsePositive(int count);
}

public class BloomFilterFalsePositiveMeter(IBloomFilterNotificationGateway bloomFilterNotificationGateway) 
    : BackgroundService, IBloomFilterFalsePositiveMeter
{
    private static readonly TimeSpan NotificationInterval = TimeSpan.FromSeconds(30);

    private readonly BucketMeter _hotelOfferFalsePositiveMeter = new(300);
    private readonly BucketMeter _flightFalsePositiveMeter = new(300);
    
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await Task.Delay(NotificationInterval, stoppingToken);
            }
            catch (TaskCanceledException)
            {
                return;
            }
            
            await PublishNotifications(stoppingToken);
        }
    }

    public void RegisterFlightFalsePositive(int count)
    {
        _flightFalsePositiveMeter.Add(count);
    }

    public void RegisterHotelOfferFalsePositive(int count)
    {
        _hotelOfferFalsePositiveMeter.Add(count);
    }

    private async Task PublishNotifications(CancellationToken cancellationToken)
    {
        var hotelOfferNotification = new BloomFilterHotelOfferFalsePositiveNotificationDto
        {
            Rate = _hotelOfferFalsePositiveMeter.GetRatePerSecond(60)
        };
        
        var flightNotification = new BloomFilterFlightFalsePositiveNotificationDto
        {
            Rate = _flightFalsePositiveMeter.GetRatePerSecond(60)
        };

        await bloomFilterNotificationGateway.PublishNotifications([hotelOfferNotification, flightNotification],
            cancellationToken);
    }
}