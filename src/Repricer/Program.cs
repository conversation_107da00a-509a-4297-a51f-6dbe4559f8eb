using Esky.Packages.Application.DependencyInjections;
using Esky.Packages.Infrastructure.DependencyInjections;
using Esky.Packages.Repricer;
using NLog.Web;

var builder = WebApplication.CreateBuilder(args);

var services = builder.Services;
var configuration = builder.Configuration;

if (!builder.Environment.IsDevelopment())
{
    builder.Logging.ClearProviders();
    builder.Host.UseNLog();
}

services
    .AddApplication(o =>
    {
        o.AddCurrencyConverter();
    })
    .AddInfrastructure(configuration, o =>
    {
        o.AddBloomFilterNotificationGateway();
    })
    .AddHealthChecks();

services
    .AddKafkaConsumers(configuration)
    .AddFalsePositiveMeter()
    .AddObservability();

var app = builder.Build();

app.MapHealthChecks("/healthz");
app.UseOpenTelemetryPrometheusScrapingEndpoint();

app.Run();

namespace Esky.Packages.Repricer
{
    // Required for WebApplicationFactory in tests
    public partial class Program { }
}