using System.Text.Json.Serialization;

namespace Esky.Packages.Repricer.Events.Flights;

public class FlightQuoteEvent
{
    [JsonPropertyName("RefreshDate")]
    public required DateTime RefreshDate { get; init; }

    [JsonPropertyName("Currency")]
    public string? Currency { get; init; }

    [JsonPropertyName("PaxConfigurations")]
    public Dictionary<string, PaxPrice?>? PaxConfigurations { get; init; }
}