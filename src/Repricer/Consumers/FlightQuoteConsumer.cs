using System.Diagnostics;
using System.Text.Json;
using System.Text.RegularExpressions;
using Esky.Hotels.Infrastructure.Kafka.Consumer;
using Esky.Packages.Application.Services;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Types;
using Esky.Packages.Repricer.Events.Flights;
using Esky.Packages.Repricer.Observability;
using Esky.Packages.Repricer.Options;

namespace Esky.Packages.Repricer.Consumers;

public partial class FlightQuoteConsumer(
    KafkaConsumerFactory kafkaConsumerFactory,
    FlightQuoteConsumerOptions options,
    IPackageFlightService packageFlightService,
    IBloomFilterFalsePositiveMeter falsePositiveMeter,
    ILogger<FlightQuoteConsumer> logger) : BackgroundService
{
    private KafkaConsumer<string, byte[]> _flightQuoteConsumer = null!;

    private static readonly Regex PaxConfigRegex = PaxConfigGeneratedRegex();

    private void InitializeKafka()
    {
        _flightQuoteConsumer = kafkaConsumerFactory.Create<string, byte[]>(o => o
            .WithBootstrapServers(options.BootstrapServers)
            .WithDefaultTopics(options.Topic)
            .WithConsumerGroupId(options.ConsumerGroup)
            .WithAutoOffsetReset(options.AutoOffsetReset)
            .WithAutoCommitIntervalMs(200)
            .WithLogPartitionRebalance()
        );

        _flightQuoteConsumer.Subscribe();
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        InitializeKafka();

        while (!stoppingToken.IsCancellationRequested)
        {
            var batch = await _flightQuoteConsumer.ConsumeBatchAsync(new KafkaBatchOptions<string, byte[]>
            {
                MaxBatchSize = options.MaxBatchSize,
                MaxProcessedMessages = options.MaxProcessedMessagesPerBatch,
                StoppingToken = stoppingToken
            });
            
            logger.LogDebug("Received batch with {receivedMessageCount} messages.", batch.InterestingMessageCount);
            
            Metrics.FlightBatches.Add(1);
            Metrics.FlightMessages.Add(batch.InterestingResults.Count);

            await ProcessBatch(batch, stoppingToken);
        }
    }

    private async Task ProcessBatch(KafkaBatch<string, byte[]> batch, CancellationToken cancellationToken)
    {
        var watch = Stopwatch.StartNew();

        if (batch.InterestingResults.Count > 0)
        {
            var flightQuotes = MapEventsToFlightQuotes(DeserializeFlightQuotes(batch));
            var (truePositives, falsePositives, updated) = await packageFlightService.ApplyQuotes(flightQuotes, 
                cancellationToken);
            falsePositiveMeter.RegisterFlightFalsePositive(falsePositives);
            Metrics.RecordFlightQuoteStatistics(truePositives, falsePositives, updated);
            
            logger.LogDebug("Updated {updated} flight quotes", updated);
            batch.StoreOffsets();
        }
        
        watch.Stop();
        Metrics.FlightBatchProcessDuration.Record(watch.ElapsedMilliseconds);
    }

    private List<(string FlightId, FlightQuoteEvent QuoteEvent)> DeserializeFlightQuotes(
        KafkaBatch<string, byte[]> batch)
    {
        var events = new List<(string FlightId, FlightQuoteEvent QuoteEvent)>();
        var watch = Stopwatch.StartNew();
        
        foreach (var result in batch.InterestingResults)
        {
            if (result.Message.Value == null)
            {
                logger.LogWarning("Received unexpected tombstone message, ignoring");
                Metrics.FlightTombstoneMessages.Add(1);
                continue;
            }

            Metrics.FlightMessageSize.Record(result.Message.Value.Length);
            
            try
            {
                var ev = JsonSerializer.Deserialize<FlightQuoteEvent>(result.Message.Value)!;
                events.Add((result.Message.Key, ev));
            } 
            catch (JsonException e)
            {
                logger.LogError(e, "Failed to deserialize message");
                Metrics.FlightInvalidMessages.Add(1);
            }
        }
        
        watch.Stop();
        Metrics.FlightDeserializationDuration.Record(watch.ElapsedMilliseconds);
        
        return events;
    }
    
    private static List<FlightQuote> MapEventsToFlightQuotes(List<(string FlightId, FlightQuoteEvent QuoteEvent)> events)
    {
        var flightQuotes = new List<FlightQuote>();
        
        foreach (var (flightId, quoteEvent) in events)
        {
            var prices = new Dictionary<PackageOccupancy, decimal>();
            
            if (quoteEvent.PaxConfigurations != null)
            {
                foreach (var (paxConfig, paxPrice) in quoteEvent.PaxConfigurations)
                {
                    if (paxPrice == null)
                    {
                        continue;
                    }
                    
                    var match = PaxConfigRegex.Match(paxConfig);
                    
                    if (match.Success)
                    {
                        var adults = int.Parse(match.Groups["adults"].Value);
                        var youths = int.Parse(match.Groups["youths"].Value);
                        var children = int.Parse(match.Groups["children"].Value);
                        var infants = int.Parse(match.Groups["infants"].Value);
                        
                        var occupancy = new PackageOccupancy(adults, youths, children, infants);
                        prices.Add(occupancy, paxPrice.TotalPrice);
                    }
                }
            }

            var flightQuote = new FlightQuote
            {
                FlightId = flightId,
                UpdateTime = quoteEvent.RefreshDate,
                Currency = quoteEvent.Currency != null ? new Currency(quoteEvent.Currency) : Currency.EUR,
                Prices = prices
            };
            
            flightQuotes.Add(flightQuote);
        }

        return flightQuotes;
    }

    [GeneratedRegex(@"^(?<adults>\d+)\.(?<youths>\d+)\.(?<children>\d+)\.(?<infants>\d+)$")]
    private static partial Regex PaxConfigGeneratedRegex();
}