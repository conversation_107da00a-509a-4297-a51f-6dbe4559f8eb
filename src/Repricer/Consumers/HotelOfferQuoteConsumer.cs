using System.Diagnostics;
using System.Text.Json;
using Esky.Hotels.Infrastructure.Kafka.Consumer;
using Esky.Packages.Application.Dtos.HotelOffers;
using Esky.Packages.Application.Services;
using Esky.Packages.Infrastructure.Serialization.Contexts;
using Esky.Packages.Repricer.Observability;
using Esky.Packages.Repricer.Options;

namespace Esky.Packages.Repricer.Consumers;

public class HotelOfferQuoteConsumer(
    KafkaConsumerFactory kafkaConsumerFactory,
    HotelOfferQuoteConsumerOptions options,
    IPackageHotelOfferService packageHotelOfferService,
    IBloomFilterFalsePositiveMeter falsePositiveMeter,
    ILogger<HotelOfferQuoteConsumer> logger) : BackgroundService
{
    private KafkaConsumer<string, byte[]> _hotelOfferQuoteConsumer = null!;

    private void InitializeKafka()
    {
        _hotelOfferQuoteConsumer = kafkaConsumerFactory.Create<string, byte[]>(o => o
            .WithBootstrapServers(options.BootstrapServers)
            .WithDefaultTopics(options.Topic)
            .WithConsumerGroupId(options.ConsumerGroup)
            .WithAutoOffsetReset(options.AutoOffsetReset)
            .WithAutoCommitIntervalMs(200)
            .WithLogPartitionRebalance()
        );

        _hotelOfferQuoteConsumer.Subscribe();
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        InitializeKafka();

        while (!stoppingToken.IsCancellationRequested)
        {
            var batch = await _hotelOfferQuoteConsumer.ConsumeBatchAsync(new KafkaBatchOptions<string, byte[]>
            {
                MaxBatchSize = options.MaxBatchSize,
                MaxProcessedMessages = options.MaxProcessedMessagesPerBatch,
                StoppingToken = stoppingToken
            });

            await ProcessBatch(batch, stoppingToken);
        }
    }

    private async Task ProcessBatch(KafkaBatch<string, byte[]> batch, CancellationToken cancellationToken)
    {
        Metrics.HotelOfferBatches.Add(1);
        Metrics.HotelOfferMessages.Add(batch.InterestingResults.Count);
        
        var watch = Stopwatch.StartNew();

        if (batch.InterestingResults.Count > 0)
        {
            var hotelOfferQuoteEvents = DeserializeHotelOffers(batch);
            var hotelQuotes = hotelOfferQuoteEvents
                .Where(x => x.HasSupportedOccupancy())
                .Select(x => x.ToDomain()).ToList();
            
            var (truePositives, falsePositives, updated) = await packageHotelOfferService.ApplyQuotes(hotelQuotes, 
                cancellationToken);
            
            falsePositiveMeter.RegisterHotelOfferFalsePositive(falsePositives);
            Metrics.RecordHotelOfferQuoteStatistics(truePositives, falsePositives, updated);
            
            batch.StoreOffsets();
        }
        
        watch.Stop();
        Metrics.HotelOfferBatchProcessDuration.Record(watch.ElapsedMilliseconds);
    }

    private List<HotelOfferDto> DeserializeHotelOffers(KafkaBatch<string, byte[]> batch)
    {
        var events = new List<HotelOfferDto>();
        var watch = Stopwatch.StartNew();
        
        foreach (var result in batch.InterestingResults)
        {
            if (result.Message.Value == null)
            {
                logger.LogWarning("Received unexpected tombstone message, ignoring");
                continue;
            }
            
            Metrics.HotelOfferMessageSize.Record(result.Message.Value.Length);

            try
            {
                var ev = JsonSerializer.Deserialize(result.Message.Value, HotelOfferDtoJsonContext.Default.HotelOfferDto)!;
                events.Add(ev);
            } 
            catch (JsonException e)
            {
                logger.LogError(e, "Failed to deserialize message");
            }
        }
        
        watch.Stop();
        Metrics.HotelOfferDeserializationDuration.Record(watch.ElapsedMilliseconds);
        
        return events;
    }
}