{"Mongo": {"ConnectionString": "SECRET"}, "FlightQuoteConsumer": {"ConsumerGroup": "packageRepricer-staging", "BootstrapServers": "esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-0.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-1.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-2.service.gcp-staging.consul:9092", "Topic": "repartitionedFlightQuotes", "AutoOffsetReset": "Latest"}, "HotelOfferQuoteConsumer": {"ConsumerGroup": "packageRepricer-staging", "BootstrapServers": "esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-0.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-1.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-2.service.gcp-staging.consul:9092", "Topic": "repartitionedHotelOfferQuotes", "AutoOffsetReset": "Latest"}, "BloomFilterNotificationProducer": {"BootstrapServers": "esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-0.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-1.service.gcp-staging.consul:9092,esky-ets-hotels-ci.kafka-hotels-ci-kafka-node-2.service.gcp-staging.consul:9092", "Topic": "bloomFilterNotifications", "CompressionType": "Lz4"}}