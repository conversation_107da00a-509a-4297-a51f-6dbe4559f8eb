using System.Diagnostics;
using System.Text.Json;
using Esky.Hotels.Infrastructure.Kafka.Consumer;
using Esky.Packages.Application.Services;
using Esky.Packages.Domain.Events;
using Esky.Packages.Domain.Model.PackageHotelOfferVariantPrices;
using Esky.Packages.Infrastructure.Serialization.Contexts;
using Esky.Packages.PriceTracker.Observability;
using Esky.Packages.PriceTracker.Options;

namespace Esky.Packages.PriceTracker.Consumers;

public class HotelOfferPricesConsumer(
    KafkaConsumerFactory kafkaConsumerFactory,
    IPackageHotelOfferVariantPriceService packageHotelOfferVariantPriceService,
    HotelOfferPricesConsumerOptions options,
    ILogger<HotelOfferPricesConsumer> logger) : BackgroundService
{
    private KafkaConsumer<string, byte[]> _hotelOfferPricesConsumer = null!;
    
    private void InitializeKafka()
    {
        _hotelOfferPricesConsumer = kafkaConsumerFactory.Create<string, byte[]>(o => o
            .WithBootstrapServers(options.BootstrapServers)
            .WithDefaultTopics(options.Topic)
            .WithConsumerGroupId(options.ConsumerGroup)
            .WithAutoOffsetReset(options.AutoOffsetReset)
            .WithAutoCommitIntervalMs(200)
            .WithLogPartitionRebalance()
        );

        _hotelOfferPricesConsumer.Subscribe();
    }
    
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        InitializeKafka();
        
        while (!stoppingToken.IsCancellationRequested)
        {
            var batch = await _hotelOfferPricesConsumer.ConsumeBatchAsync(new KafkaBatchOptions<string, byte[]>
            {
                MaxBatchSize = options.MaxBatchSize,
                MaxProcessedMessages = options.MaxProcessedMessagesPerBatch,
                StoppingToken = stoppingToken
            });
            
            logger.LogDebug("Received batch with {receivedMessageCount} messages.", batch.InterestingMessageCount);

            Metrics.HotelOfferBatches.Add(1);
            Metrics.HotelOfferMessages.Add(batch.InterestingResults.Count);
            
            await ProcessBatch(batch, stoppingToken);
        }
    }
    
    private async Task ProcessBatch(KafkaBatch<string, byte[]> batch, CancellationToken cancellationToken)
    {
        var watch = Stopwatch.StartNew();
        
        if (batch.InterestingMessageCount > 0)
        {
            var hotelOfferPrices = MapEvents(DeserializeHotelOfferPrices(batch));
            var upserts = await packageHotelOfferVariantPriceService.ApplyPrices(hotelOfferPrices, cancellationToken);
            Metrics.HotelOfferMongoUpserts.Add(upserts);
            batch.StoreOffsets();
        }
        
        watch.Stop();
        Metrics.HotelOfferBatchProcessDuration.Record(watch.ElapsedMilliseconds);
    }
    
    private List<PackageHotelOfferVariantPriceEvent> DeserializeHotelOfferPrices(KafkaBatch<string, byte[]> batch)
    {
        var events = new List<PackageHotelOfferVariantPriceEvent>();
        var watch = Stopwatch.StartNew();
        
        foreach (var result in batch.InterestingResults)
        {
            if (result.Message.Value == null)
            {
                logger.LogWarning("Received null hotel offer price event. Skipping.");
                Metrics.HotelOfferTombstoneMessages.Add(1);
                continue;
            }
            
            Metrics.HotelOfferMessageSize.Record(result.Message.Value.Length);
            
            try
            {
                var hotelOfferPrice = JsonSerializer.Deserialize<PackageHotelOfferVariantPriceEvent>(result.Message.Value, PackageHotelOfferVariantPriceEventJsonContext.Default.PackageHotelOfferVariantPriceEvent)!;
                events.Add(hotelOfferPrice);
            }
            catch (JsonException ex)
            {
                logger.LogError(ex, "Failed to deserialize hotel offer price event.");
                Metrics.HotelOfferInvalidMessages.Add(1);
            }
        }
        
        watch.Stop();
        Metrics.HotelOfferDeserializationDuration.Record(watch.ElapsedMilliseconds);

        return events;
    }

    private static List<PackageHotelOfferVariantPrice> MapEvents(List<PackageHotelOfferVariantPriceEvent> events)
    {
        return events.Select(ev => PackageHotelOfferVariantPrice.Create(
                PackageHotelOfferVariantPriceId.Create(
                    ev.CheckIn,
                    ev.StayLength,
                    ev.MarketId,
                    ev.MetaCode,
                    ev.MealPlan,
                    ev.Occupancy,
                    ev.PriceDate
                ),
                new Dictionary<PackageHotelOfferVariantPriceKey, int>
                {
                    [new PackageHotelOfferVariantPriceKey { Refundability = ev.Refundability, RoomIds = ev.RoomIds }] = ev.Price
                }))
            .ToList();
    }
}