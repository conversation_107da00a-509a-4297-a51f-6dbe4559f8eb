using System.Diagnostics;
using System.Text.Json;
using Esky.Hotels.Infrastructure.Kafka.Consumer;
using Esky.Packages.Application.Services;
using Esky.Packages.Domain.Events.PackageFlightVariants;
using Esky.Packages.Domain.Model.PackageFlightVariantPrices;
using Esky.Packages.Infrastructure.Serialization.Contexts;
using Esky.Packages.PriceTracker.Observability;
using Esky.Packages.PriceTracker.Options;

namespace Esky.Packages.PriceTracker.Consumers;

public class FlightPricesConsumer(
    KafkaConsumerFactory kafkaConsumerFactory,
    IPackageFlightVariantPriceService packageFlightVariantPriceService,
    FlightPricesConsumerOptions options,
    ILogger<FlightPricesConsumer> logger) : BackgroundService
{
    private KafkaConsumer<string, byte[]> _flightPricesConsumer = null!;
    
    private void InitializeKafka()
    {
        _flightPricesConsumer = kafkaConsumerFactory.Create<string, byte[]>(o => o
            .WithBootstrapServers(options.BootstrapServers)
            .WithDefaultTopics(options.Topic)
            .WithConsumerGroupId(options.ConsumerGroup)
            .WithAutoOffsetReset(options.AutoOffsetReset)
            .WithAutoCommitIntervalMs(200)
            .WithLogPartitionRebalance()
        );

        _flightPricesConsumer.Subscribe();
    }
    
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        InitializeKafka();
        
        while (!stoppingToken.IsCancellationRequested)
        {
            var batch = await _flightPricesConsumer.ConsumeBatchAsync(new KafkaBatchOptions<string, byte[]>
            {
                MaxBatchSize = options.MaxBatchSize,
                MaxProcessedMessages = options.MaxProcessedMessagesPerBatch,
                StoppingToken = stoppingToken
            });
            
            logger.LogDebug("Received batch with {receivedMessageCount} messages.", batch.InterestingMessageCount);
            
            Metrics.FlightBatches.Add(1);
            Metrics.FlightMessages.Add(batch.InterestingResults.Count);
            
            await ProcessBatch(batch, stoppingToken);
        }
    }

    private async Task ProcessBatch(KafkaBatch<string, byte[]> batch, CancellationToken cancellationToken)
    {
        var watch = Stopwatch.StartNew();
        
        if (batch.InterestingMessageCount > 0)
        {
            var flightPrices = MapEvents(DeserializeFlightPrices(batch));
            var upserts = await packageFlightVariantPriceService.ApplyPrices(flightPrices, cancellationToken);
            Metrics.FlightMongoUpserts.Add(upserts);
            batch.StoreOffsets();
        }
        
        watch.Stop();
        Metrics.FlightBatchProcessDuration.Record(watch.ElapsedMilliseconds);
    }
    
    private List<PackageFlightVariantPriceEvent> DeserializeFlightPrices(KafkaBatch<string, byte[]> batch)
    {
        var events = new List<PackageFlightVariantPriceEvent>();
        var watch = Stopwatch.StartNew();
        
        foreach (var result in batch.InterestingResults)
        {
            if (result.Message.Value == null)
            {
                logger.LogWarning("Received null flight price event. Skipping.");
                Metrics.FlightTombstoneMessages.Add(1);
                continue;
            }
            
            Metrics.FlightMessageSize.Record(result.Message.Value.Length);
            
            try
            {
                var flightPrice = JsonSerializer.Deserialize<PackageFlightVariantPriceEvent>(result.Message.Value, PackageFlightVariantPriceEventJsonContext.Default.PackageFlightVariantPriceEvent)!;
                events.Add(flightPrice);
            }
            catch (JsonException ex)
            {
                logger.LogError(ex, "Failed to deserialize flight price event.");
                Metrics.FlightInvalidMessages.Add(1);
            }
        }
        
        watch.Stop();
        Metrics.FlightDeserializationDuration.Record(watch.ElapsedMilliseconds);

        return events;
    }

    private static List<PackageFlightVariantPrice> MapEvents(List<PackageFlightVariantPriceEvent> events)
    {
        return events.Select(ev => PackageFlightVariantPrice.Create(
                PackageFlightVariantPriceId.Create(
                    ev.DepartureDate,
                    ev.ReturnDepartureDate,
                    ev.MarketId,
                    ev.ArrivalAirport,
                    ev.DepartureAirport,
                    ev.FlightNumbers,
                    ev.ReturnFlightNumbers,
                    ev.Occupancy,
                    ev.PriceDate
                ),
                new Dictionary<PackageFlightVariantPriceKey, int>
                {
                    [new PackageFlightVariantPriceKey { BaggageIncluded = ev.BaggageIncluded }] = ev.Price
                }))
            .ToList();
    }
}