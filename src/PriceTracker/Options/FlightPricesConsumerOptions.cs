using Confluent.Kafka;

namespace Esky.Packages.PriceTracker.Options;

public class FlightPricesConsumerOptions
{
    public const string ConfigurationSection = "FlightPricesConsumer";

    public string BootstrapServers { get; set; } = default!;
    public string ConsumerGroup { get; set; } = default!;
    public AutoOffsetReset AutoOffsetReset { get; set; } = AutoOffsetReset.Latest;
    public string Topic { get; set; } = default!;

    public int MaxBatchSize { get; set; } = 1000;
    public int MaxProcessedMessagesPerBatch { get; set; } = 100_000;
}