using System.Diagnostics.Metrics;

namespace Esky.Packages.PriceTracker.Observability;

public static class Metrics
{
    public const string MeterName = "packages-pricetracker";
    private static readonly Meter Meter = new(MeterName);
    
    static Metrics()
    {
        FlightBatches.Add(0);
        FlightMessages.Add(0);
        FlightInvalidMessages.Add(0);
        FlightTombstoneMessages.Add(0);
        FlightMongoUpserts.Add(0);
        
        HotelOfferBatches.Add(0);
        HotelOfferMessages.Add(0);
        HotelOfferInvalidMessages.Add(0);
        HotelOfferTombstoneMessages.Add(0);
        HotelOfferMongoUpserts.Add(0);
    }
    
    // Flight metrics
    internal static readonly Counter<long> FlightBatches = 
        Meter.CreateCounter<long>($"{MeterName}_flight_batches", "count");
    
    internal static readonly Counter<long> FlightMessages = 
        Meter.CreateCounter<long>($"{MeterName}_flight_messages", "count");
    
    internal static readonly Counter<long> FlightInvalidMessages =
        Meter.CreateCounter<long>($"{MeterName}_flight_invalid_messages", "count");
    
    internal static readonly Counter<long> FlightTombstoneMessages =
        Meter.CreateCounter<long>($"{MeterName}_flight_tombstone_messages", "msg");
    
    internal static readonly Counter<long> FlightMongoUpserts =
        Meter.CreateCounter<long>($"{MeterName}_flight_upserts", "msg");
    
    internal static readonly Histogram<double> FlightBatchProcessDuration =
        Meter.CreateHistogram<double>($"{MeterName}_flight_batch_process", "milliseconds");
    
    internal static readonly Histogram<double> FlightDeserializationDuration =
        Meter.CreateHistogram<double>($"{MeterName}_flight_batch_deserialization", "milliseconds");
    
    internal static readonly Histogram<double> FlightMessageSize =
        Meter.CreateHistogram<double>($"{MeterName}_flight_message_size", "bytes");
    
    // Hotel offer metrics
    internal static readonly Counter<long> HotelOfferBatches = 
        Meter.CreateCounter<long>($"{MeterName}_hotel_offer_batches", "count");
    
    internal static readonly Counter<long> HotelOfferMessages = 
        Meter.CreateCounter<long>($"{MeterName}_hotel_offer_messages", "count");
    
    internal static readonly Counter<long> HotelOfferInvalidMessages =
        Meter.CreateCounter<long>($"{MeterName}_hotel_offer_invalid_messages", "count");
    
    internal static readonly Counter<long> HotelOfferTombstoneMessages =
        Meter.CreateCounter<long>($"{MeterName}_hotel_offer_tombstone_messages", "msg");
    
    internal static readonly Counter<long> HotelOfferMongoUpserts =
        Meter.CreateCounter<long>($"{MeterName}_hotel_offer_upserts", "msg");
    
    internal static readonly Histogram<double> HotelOfferBatchProcessDuration =
        Meter.CreateHistogram<double>($"{MeterName}_hotel_offer_batch_process", "milliseconds");
    
    internal static readonly Histogram<double> HotelOfferDeserializationDuration =
        Meter.CreateHistogram<double>($"{MeterName}_hotel_offer_batch_deserialization", "milliseconds");
    
    internal static readonly Histogram<double> HotelOfferMessageSize =
        Meter.CreateHistogram<double>($"{MeterName}_hotel_offer_message_size", "bytes");
}