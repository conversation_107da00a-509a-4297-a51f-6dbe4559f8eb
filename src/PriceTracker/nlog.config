<?xml version="1.0" encoding="utf-8"?>

<nlog	
	xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	throwExceptions="false" 
	throwConfigExceptions="true" 
	internalLogLevel="Error" 
	internalLogToConsoleError="true" 
	internalLogFile="/app/logs.log" 
	autoReload="true">

	<include file="${basedir}/nlog-common.config"/>

	<rules>
		<logger name="Polly" maxLevel="Info" writeTo="BlackHole" final="true" />
		<logger name="System.Net.Http.HttpClient.*" maxLevel="Info" writeTo="BlackHole" final="true" />
		<logger name="PipelineLogger" maxLevel="Info" writeTo="BlackHole" final="true" />
		<logger name="Microsoft.*" maxLevel="Info" writeTo="BlackHole" final="true" />
		<logger name="*" minlevel="Info" writeTo="Rabbit_BQ_Elastic,JsonConsole" />
	</rules>
</nlog>