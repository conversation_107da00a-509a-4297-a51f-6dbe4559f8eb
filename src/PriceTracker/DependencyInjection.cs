using Esky.Hotels.Infrastructure.Kafka;
using Esky.Packages.Infrastructure.Configuration;
using Esky.Packages.PriceTracker.Consumers;
using Esky.Packages.PriceTracker.Observability;
using Esky.Packages.PriceTracker.Options;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;

namespace Esky.Packages.PriceTracker;

public static class DependencyInjection
{
    internal static IServiceCollection AddKafkaConsumers(this IServiceCollection services, IConfiguration configuration)
    {
        services.RegisterOptions<FlightPricesConsumerOptions>(configuration, FlightPricesConsumerOptions.ConfigurationSection);
        services.RegisterOptions<HotelOfferPricesConsumerOptions>(configuration, HotelOfferPricesConsumerOptions.ConfigurationSection);

        services.AddKafka(configuration);

        services.AddHostedService<FlightPricesConsumer>();
        services.AddHostedService<HotelOfferPricesConsumer>();

        return services;
    }
    
    internal static IServiceCollection AddObservability(this IServiceCollection services)
    {
        const string appName = "esky-packages-pricetracker";

        services.AddOpenTelemetry()
            .WithMetrics(options =>
                options.SetResourceBuilder(ResourceBuilder.CreateDefault().AddService(appName))
                    .AddMeter(Metrics.MeterName)
                    .AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation()
                    .AddRuntimeInstrumentation()
                    .AddProcessInstrumentation()
                    .AddPrometheusExporter()
            );

        return services;
    }
}