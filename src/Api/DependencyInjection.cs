using System.Text.Json.Serialization;
using Esky.Packages.Api.Observability;
using Esky.Packages.Api.Options;
using Esky.Packages.Application.Observability;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Types;
using Esky.Packages.Infrastructure.OpenApi;
using Esky.Packages.Infrastructure.OpenApi.Filters;
using Microsoft.OpenApi.Models;
using MongoDB.Driver.Core.Extensions.DiagnosticSources;
using OpenTelemetry;
using OpenTelemetry.Context.Propagation;
using OpenTelemetry.Extensions.Propagators;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using MvcJsonOptions = Microsoft.AspNetCore.Mvc.JsonOptions;

namespace Esky.Packages.Api;

internal static class DependencyInjection
{
    public static WebApplication UseOpenApi(this WebApplication app)
    {
        app.UseSwagger();
        app.UseSwaggerUI(c => c.DisplayRequestDuration());

        return app;
    }

    public static IServiceCollection AddOpenApi(this IServiceCollection services)
    {
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo
            {
                Title = "eSky Packages Api",
                Version = "1.0"
            });

            c.MapWellKnownTypes();
            c.MapTypeAsString<PackageOccupancy>();
            c.MapTypeAsString<Currency>();

            c.SchemaFilter<PolymorphicSchemaFilter>();
            c.SchemaFilter<RequiredSchemaFilter>();
        });

        return services;
    }
    
    internal static IServiceCollection AddObservability(this IServiceCollection services, IConfiguration configuration)
    {
        var tracingOptions = new TracingOptions();
        configuration.GetSection(TracingOptions.ConfigurationSection).Bind(tracingOptions);

        var appName = "esky-packages-api";

        services.AddOpenTelemetry()
            .WithMetrics(options =>
                options.SetResourceBuilder(ResourceBuilder.CreateDefault().AddService(appName))
                    .AddMeter(Metrics.MeterName)
                    .AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation()
                    .AddRuntimeInstrumentation()
                    .AddProcessInstrumentation()
                    .AddPrometheusExporter()
            )            
            .WithTracing(builder =>
            {
                builder
                    .SetResourceBuilder(ResourceBuilder.CreateDefault().AddService(appName))
                    .AddSource(Activities.Source.Name, DiagnosticsActivityEventSubscriber.ActivitySourceName)
                    .AddAspNetCoreInstrumentation(b =>
                    {
                        b.Filter = httpContext =>
                            !httpContext.Request.Path.StartsWithSegments("/healthz") &&
                            !httpContext.Request.Path.StartsWithSegments("/metrics");
                    })
                    .AddHttpClientInstrumentation()
                    .AddOtlpExporter(o => o.Endpoint = new Uri($"http://{tracingOptions.AgentHost}:{tracingOptions.AgentPort}"))
                    .SetSampler(new ParentBasedSampler(new AlwaysOffSampler()));
            });
        
        // for some reason the Metrics class is not being initialized so we need to do it manually
        Metrics.Initialize();
        
        Sdk.SetDefaultTextMapPropagator(new CompositeTextMapPropagator([new TraceContextPropagator(), new JaegerPropagator(), new BaggagePropagator()]));

        return services;
    }
    
    public static IServiceCollection ConfigureJson(this IServiceCollection services)
    {
        return services
            .Configure<MvcJsonOptions>(o => o.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter()));
    }
}