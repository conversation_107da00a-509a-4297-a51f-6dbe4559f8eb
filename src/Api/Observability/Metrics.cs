using System.Diagnostics.Metrics;
using Esky.Packages.Application.Observability;

namespace Esky.Packages.Api.Observability;

public class Metrics
{
    public const string MeterName = "packages-api";
    private static readonly Meter Meter = new(MeterName);
    
    public static void Initialize()
    {
        var name = Meter.Name;
    }
    
    internal static readonly ObservableCounter<long> PackageFactorsSearchOffers =
        Meter.CreateObservableCounter($"{MeterName}_package_factors_search_offers",
            () => ApplicationMetrics.PackageFactorsSearchOffers);
    
    internal static readonly ObservableCounter<long> PackageDynamicSearchOffers = 
        Meter.CreateObservableCounter($"{MeterName}_package_dynamic_search_offers",
            () => ApplicationMetrics.PackageDynamicSearchOffers);
    
    internal static readonly ObservableCounter<long> PackageCachedSearchOffers =
        Meter.CreateObservableCounter($"{MeterName}_package_cached_search_offers",
            () => ApplicationMetrics.PackageCachedSearchOffers);

    internal static readonly ObservableCounter<long> OfferAccuracyRequests =
        Meter.CreateObservableCounter($"{MeterName}_offer_accuracy_requests",
            () => OfferAccuracyMetrics.OfferAccuracyRequests
                .Select(kvp =>
                    new Measurement<long>(kvp.Value, tags: new KeyValuePair<string, object?>("type", kvp.Key))));
    
    internal static readonly ObservableCounter<long> OfferAccuracyTimeouts =
        Meter.CreateObservableCounter($"{MeterName}_offer_accuracy_timeouts",
            () => OfferAccuracyMetrics.OfferAccuracyTimeouts
                .Select(kvp =>
                    new Measurement<long>(kvp.Value, tags: new KeyValuePair<string, object?>("type", kvp.Key))));
    
    internal static readonly ObservableCounter<long> OfferAccuracyErrors =
        Meter.CreateObservableCounter($"{MeterName}_offer_accuracy_errors",
            () => OfferAccuracyMetrics.OfferAccuracyErrors
                .Select(kvp =>
                    new Measurement<long>(kvp.Value, tags: new KeyValuePair<string, object?>("type", kvp.Key))));
    
    internal static readonly ObservableCounter<long> OfferAccuracyExclusions =
        Meter.CreateObservableCounter($"{MeterName}_offer_accuracy_exclusions",
            () => OfferAccuracyMetrics.OffersAccuracyExclusions
                .Select(kvp =>
                    new Measurement<long>(kvp.Value, tags: new KeyValuePair<string, object?>("type", kvp.Key))));
}