using System.Diagnostics;

namespace Esky.Packages.Api.Observability;

internal sealed class TraceRequestBodyMiddleware
{
    private readonly RequestDelegate _next;

    public TraceRequestBodyMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var activity = Activity.Current;

        if (activity is { Recorded: true, IsAllDataRequested: true })
        {
            context.Request.EnableBuffering();
            var reader = new StreamReader(context.Request.Body);
            context.Response.RegisterForDispose(reader);
            var json = await reader.ReadToEndAsync();
            context.Request.Body.Position = 0;
            activity.SetTag("http.request.body", json);
        }

        await _next.Invoke(context);
    }
}

internal static class TraceRequestBodyMiddlewareExtensions
{
    public static IApplicationBuilder UseTraceRequestBody(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<TraceRequestBodyMiddleware>();
    }
}