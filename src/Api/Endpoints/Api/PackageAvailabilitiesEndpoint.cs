using Esky.Packages.Application.Services;
using Esky.Packages.Contract.PackageAvailabilities;
using Microsoft.AspNetCore.Http.HttpResults;

namespace Esky.Packages.Api.Endpoints.Api;

internal static class PackageAvailabilitiesEndpoint
{
    private const string Prefix = "packageAvailabilities";

    public static IEndpointRouteBuilder MapPackageAvailabilities(this IEndpointRouteBuilder builder)
    {
        var group = builder
            .MapGroup(Prefix)
            .WithTags(Prefix);

        group
            .MapGet("{definitionId}", async Task<Results<Ok<PackageAvailabilitiesDto>, NotFound>> (
                string definitionId, CancellationToken cancellationToken, IPackageAvailabilityService service) =>
            {
                var availabilities = await service.GetByDefinitionId(definitionId, cancellationToken);

                if (availabilities.Availabilities.Length == 0)
                {
                    return TypedResults.NotFound();
                }

                return TypedResults.Ok(availabilities);
            });

        return builder;
    }
}