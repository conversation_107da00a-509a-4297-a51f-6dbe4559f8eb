using Esky.Packages.Application.Dtos.Common;
using Esky.Packages.Application.Exceptions;
using Esky.Packages.Application.Services;
using Esky.Packages.Contract.Common;
using Esky.Packages.Contract.Packages;
using Esky.Packages.Contract.PackageVariants;
using Esky.Packages.Domain.Model.Packages;
using Esky.Packages.Domain.Model.PackageVariants;
using Esky.Packages.Domain.Types;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;

namespace Esky.Packages.Api.Endpoints.Api;

internal static class PackagesEndpoint
{
    private const string Prefix = "packages";

    public static IEndpointRouteBuilder MapPackages(this IEndpointRouteBuilder builder)
    {
        var group = builder
            .MapGroup(Prefix)
            .WithTags(Prefix);

        group
            .MapPost("metasearch", async Task<Results<Ok<PackageMetaSearchDto[]>, BadRequest<ErrorDto>>> (
                [FromBody] GetMetaSearchPackagesQuery query, CancellationToken cancellationToken,
                IPackageService service) =>
            {
                try
                {
                    var result = await service.GetByIds(query.PackageIds, query.Occupancies.Select(Occupancy.FromDto).ToArray(), cancellationToken);
                    return TypedResults.Ok(result);
                }
                catch (ArgumentException ex)
                {
                    return TypedResults.BadRequest(new ErrorDto(ex.Message));
                }
            });

        group
            .MapGet("{packageId}", async (string packageId, IPackageService service, CancellationToken cancellationToken)
                => await service.GetById(packageId, cancellationToken));

        group
            .MapPost("{packageId}/live-variants", async Task<Results<Ok<PackageLiveVariantsDto>, NotFound>> (
                    string packageId, [FromBody] GetPackageLiveVariantsQuery query, 
                    CancellationToken cancellationToken, IPackageLiveVariantsService service)
                =>
            {
                try
                {
                    var variants = await service.GetPackageLiveVariants(
                        new PackageLiveVariantsParameters(
                            PackageId: packageId,
                            Occupancies: query.Occupancies.Select(Occupancy.FromDto).ToArray(),
                            DepartureAirports: query.DepartureAirports?.Select(a => new Airport(a)).ToArray(),
                            FlightOptionId: query.FlightOptionId,
                            PreferredDepartureAirport: query.PreferredDepartureAirport != null
                                ? new Airport(query.PreferredDepartureAirport)
                                : (Airport?)null,
                            InboundDepartures: query.InboundDepartures?.Select(td => td.ToDomain()).ToArray() ?? [],
                            OutboundDepartures: query.OutboundDepartures?.Select(td => td.ToDomain()).ToArray() ?? [],
                            PreferredMealPlans: query.PreferredMealPlans?.Select(td => td.ToDomain()).ToArray() ?? [],
                            PreferSelectedFlight: query.PreferSelectedFlight),
                        cancellationToken);

                    return TypedResults.Ok(variants);
                }
                catch (NotFoundException)
                {
                    return TypedResults.NotFound();
                }
            });

        group
            .MapPost("{packageId}/live", async Task<Results<Ok<PackageLiveDto>, NotFound>> (
                    string packageId, [FromBody] GetPackageLiveQuery query,
                    CancellationToken cancellationToken, IPackageService service)
                =>
            {
                var parameters = new PackageLiveParameters(
                    PackageId: new PackageId(packageId),
                    Occupancies: query.Occupancies.Select(Occupancy.FromDto).ToArray(),
                    PreferredDepartureAirport: query.PreferredDepartureAirport != null
                        ? new Airport(query.PreferredDepartureAirport)
                        : (Airport?)null,
                    SelectedDepartureAirports: query.SelectedDepartureAirports?.Select(a => new Airport(a)).ToArray() ?? [],
                    FallbackDepartureAirports: query.FallbackDepartureAirports?.Select(a => new Airport(a)).ToArray() ?? [],
                    FlightOptionId: query.FlightOptionId,
                    InboundDepartures: query.InboundDepartures?.Select(td => td.ToDomain()).ToArray() ?? [],
                    OutboundDepartures: query.OutboundDepartures?.Select(td => td.ToDomain()).ToArray() ?? [],
                    PreferredMealPlans: query.PreferredMealPlans?.Select(td => td.ToDomain()).ToArray() ?? []);

                try
                {
                    var live = await service.GetLive(parameters, cancellationToken);
                    return TypedResults.Ok(live);
                } catch (NotFoundException)
                {
                    return TypedResults.NotFound();
                }
            });

        group
            .MapPost("{packageId}/live-alternatives", async Task<Results<Ok<PackageLiveAlternativesDto>, NotFound>> (
                    string packageId, [FromBody] GetPackageLiveAlternativesQuery query,
                    CancellationToken cancellationToken, IPackageService service)
                =>
            {
                var parameters = new PackageLiveAlternativesParameters(
                    PackageId: new PackageId(packageId),
                    HotelOfferId: query.HotelOfferId,
                    Occupancies: query.Occupancies.Select(Occupancy.FromDto).ToArray(),
                    PreferredMealPlans: query.PreferredMealPlans?.Select(td => td.ToDomain()).ToArray() ?? []);

                try
                {
                    var alternatives = await service.GetLiveAlternatives(parameters, cancellationToken);
                    return TypedResults.Ok(alternatives);
                }
                catch (NotFoundException)
                {
                    return TypedResults.NotFound();
                }
            });

        group
            .MapPost("price-history", async Task<Results<Ok<PackageVariantsPriceHistoryResponseDto>, BadRequest<ErrorDto>>> (
                [FromBody] GetPackageVariantsPriceHistoryQuery query,
                IPriceHistoryService service,
                CancellationToken cancellationToken)
                =>
            {
                if (query.PackageVariantDateRanges.Length == 0)
                {
                    return TypedResults.BadRequest(new ErrorDto("At least one package variant date range is required."));
                }

                var packageVariantDateRanges = query
                    .PackageVariantDateRanges
                    .Select(x => (Id: new PackageVariantId(x.PackageVariantId), StartDate: x.StartDate, EndDate: x.EndDate))
                    .ToArray();

                var result = await service.GetHistoryAsync(packageVariantDateRanges, cancellationToken);

                return TypedResults.Ok(result);
            });

        return builder;
    }
}
