using Esky.Packages.Application.Services;
using Esky.Packages.Domain.Model.PackageDefinitions;
using Esky.Packages.Domain.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace Esky.Packages.Api.Endpoints.Api;

internal static class PackageDefinitionsEndpoint
{
    private const string Prefix = "packageDefinitions";

    public static IEndpointRouteBuilder MapPackageDefinitions(this IEndpointRouteBuilder builder)
    {
        var group = builder
            .MapGroup(Prefix)
            .WithTags(Prefix);

        group
            .MapGet("{id}", async (string id, CancellationToken cancellationToken, IPackageDefinitionRepository repository)
                => await repository.GetById(id, cancellationToken));

        group
            .MapGet("", async (CancellationToken cancellationToken, IPackageDefinitionRepository repository)
                => await repository.GetAll(cancellationToken));

        group
            .MapPost("", async ([FromBody] PackageDefinition definition, CancellationToken cancellationToken, IPackageDefinitionRepository repository)
                => await repository.Add(definition, cancellationToken));

        group
            .MapPut("{id}", async (string id, [FromBody] PackageDefinition definition, CancellationToken cancellationToken, IPackageDefinitionService service)
                => await service.Update(id, definition, cancellationToken));

        group
            .MapDelete("{id}", async (string id, CancellationToken cancellationToken, IPackageDefinitionRepository repository) 
                => await repository.Remove(id, cancellationToken)); // TODO: Remove all related package hotel offers and package flights

        return builder;
    }
}