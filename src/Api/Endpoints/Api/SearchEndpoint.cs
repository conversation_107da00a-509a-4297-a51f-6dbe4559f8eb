using Esky.Packages.Application.Dtos.Common;
using Esky.Packages.Application.Services;
using Esky.Packages.Contract.Common;
using Esky.Packages.Contract.Search;
using Esky.Packages.Domain.Types;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;

namespace Esky.Packages.Api.Endpoints.Api;

public static class SearchEndpoint
{
    private const string Prefix = "search";

    public static IEndpointRouteBuilder MapSearch(this IEndpointRouteBuilder builder)
    {
        var group = builder.MapGroup(Prefix)
            .WithTags(Prefix);

        group.MapPost("", async Task<Results<Ok<SearchDto>, NotFound, BadRequest<ErrorDto>>> (
            [FromBody] GetSearchQueryDto query, ISearchService service, CancellationToken cancellationToken) =>
        {
            if (query.DepartureDateFrom > query.DepartureDateTo)
            {
                return TypedResults.BadRequest(new ErrorDto("DepartureDateFrom cannot be after DepartureDateTo."));
            }

            var search = await service.Search(new SearchParameters(
                MarketId: query.MarketId,
                MetaCodes: query.MetaCodes,
                // TODO: It's a temporary fix to limit the number of stay lengths which causes database exceptions. Remove it when the PSE team add validation for it.
                StayLengths: query.StayLengths.Order().Take(4).ToArray(),
                DepartureDateFrom: query.DepartureDateFrom,
                DepartureDateTo: query.DepartureDateTo,
                DepartureAirports: query.DepartureAirports?.Select(d => new Airport(d)).ToArray() ?? [],
                MealPlans: query.MealPlans?.Select(m => m.ToDomain()).ToArray() ?? [],
                Occupancies: query.Occupancies.Select(Occupancy.FromDto).ToArray(),
                InboundDepartures: query.InboundDepartures?.Select(td => td.ToDomain()).ToArray() ?? [],
                OutboundDepartures: query.OutboundDepartures?.Select(td => td.ToDomain()).ToArray() ?? [],
                MaxPrice: query.MaxPrice,
                UseDynamicSearchFallback: query.UseDynamicSearchFallback == true), cancellationToken);

            if (search.Variants.Count == 0)
                return TypedResults.NotFound();

            return TypedResults.Ok(search);
        });

        return builder;
    }
}