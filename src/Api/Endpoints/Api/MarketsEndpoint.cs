using Esky.Packages.Application.Services;
using Esky.Packages.Contract.Markets;
using Microsoft.AspNetCore.Mvc;

namespace Esky.Packages.Api.Endpoints.Api;

internal static class MarketsEndpoint
{
    private const string Prefix = "markets";

    public static IEndpointRouteBuilder MapMarkets(this IEndpointRouteBuilder builder)
    {
        var group = builder
            .MapGroup(Prefix)
            .WithTags(Prefix);
        
        group.MapGet("{id}", async (string id, CancellationToken cancellationToken, IMarketsService service)
            => await service.GetById(id, cancellationToken));
        
        group.MapGet("", async (CancellationToken cancellationToken, IMarketsService service)
            => await service.GetAll(cancellationToken));
        
        group.MapPost("", async ([FromBody] MarketDto market, CancellationToken cancellationToken, IMarketsService service)
            => await service.Create(market, cancellationToken));
        
        group.MapPut("{id}", async (string id, [FromBody] MarketDto market, CancellationToken cancellationToken, IMarketsService service) 
            => await service.Upsert(id, market, cancellationToken));
        
        group.MapDelete("{id}", async (string id, CancellationToken cancellationToken, IMarketsService service)
            => await service.Delete(id, cancellationToken));
        
        return builder;
    }
}