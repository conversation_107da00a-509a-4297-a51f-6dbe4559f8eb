using Esky.Packages.Application.Services;
using Esky.Packages.Contract.Availabilities;
using Esky.Packages.Domain.Types;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;

namespace Esky.Packages.Api.Endpoints.Api;

public static class AvailabilitiesEndpoint
{
    private const string Prefix = "availabilities";

    public static IEndpointRouteBuilder MapAvailabilities(this IEndpointRouteBuilder builder)
    {
        var group = builder
            .MapGroup(Prefix)
            .WithTags(Prefix);

        group
            .MapPost("departure-dates", async Task<Results<Ok<DepartureDatesDto>, NotFound>> (
                [FromBody] GetDepartureDatesQuery query, CancellationToken cancellationToken,
                IAvailabilityService service) =>
            {
                var departureDates = await service.GetDepartureDates(
                    marketId: query.MarketId,
                    stayLength: query.StayLength,
                    departureAirports: query.DepartureAirports.Select(d => new Airport(d)).ToArray(),
                    arrivalAirports: query.ArrivalAirports.Select(a => new Airport(a)).ToArray(),
                    occupancies: query.Occupancies.Select(Occupancy.FromDto).ToArray(),
                    cancellationToken: cancellationToken);

                if (departureDates.DepartureDates.Length == 0)
                {
                    return TypedResults.NotFound();
                }

                return TypedResults.Ok(departureDates);
            });

        return builder;
    }
}