<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
    <!-- .net -->
    <PackageVersion Include="Esky.Hotels.HApi.Protos" Version="3.0.0" />
    <PackageVersion Include="Esky.NLog.BigQueryTarget" Version="2.0.0" />
    <PackageVersion Include="Esky.NLog.RabbitMQ.Target" Version="1.2.0" />
    <PackageVersion Include="Google.Cloud.BigQuery.V2" Version="3.11.0" />
    <PackageVersion Include="Grpc.Net.ClientFactory" Version="2.67.0" />
    <PackageVersion Include="MassTransit" Version="8.4.0" />
    <PackageVersion Include="MassTransit.RabbitMQ" Version="8.4.0" />
    <PackageVersion Include="Microsoft.Data.SqlClient" Version="6.0.1" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Http" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Http.Resilience" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.TimeProvider.Testing" Version="9.8.0" />
    <PackageVersion Include="MongoDB.Driver" Version="3.1.0" />
    <PackageVersion Include="MongoDB.Driver.Core.Extensions.DiagnosticSources" Version="2.1.0" />
    <PackageVersion Include="NLog.Appsettings.Standard" Version="2.1.0" />
    <PackageVersion Include="NLog.Targets.ElasticSearch" Version="7.7.0" />
    <PackageVersion Include="NLog.Web.AspNetCore" Version="5.4.0" />
    <PackageVersion Include="SqlKata.Execution" Version="2.4.0" />
    <!-- Api -->
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="7.2.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore.SwaggerGen" Version="7.2.0" />
    <!-- OpenTelemetry -->
    <PackageVersion Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" Version="1.6.0-rc.1" />
    <PackageVersion Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Extensions.Propagators" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Http" Version="1.9.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Process" Version="0.5.0-beta.3" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Runtime" Version="1.12.0" />
    <!-- Esky -->
    <PackageVersion Include="Esky.Hotels.Infrastructure.Kafka" Version="2.1.0" />
    <PackageVersion Include="Esky.Hotels.Infrastructure.Observability" Version="2.0.0" />
    <PackageVersion Include="Esky.Hotels.Logs" Version="2.0.0" />
    <!-- Infrastructure -->
    <PackageVersion Include="BloomFilter.NetCore" Version="2.4.0" />
    <!-- Kafka -->
    <PackageVersion Include="Confluent.Kafka" Version="2.6.1" />
    <!-- Tests -->
    <PackageVersion Include="NSubstitute" Version="5.3.0" />
    <PackageVersion Include="Shouldly" Version="4.3.0" />
    <PackageVersion Include="Testcontainers.Kafka" Version="4.3.0" />
    <PackageVersion Include="Testcontainers.MongoDb" Version="4.3.0" />
    <PackageVersion Include="Testcontainers.MsSql" Version="4.4.0" />
    <PackageVersion Include="Testcontainers.RabbitMq" Version="4.3.0" />
    <PackageVersion Include="xunit" Version="2.9.3" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="3.0.2" />
    <PackageVersion Include="coverlet.collector" Version="6.0.4" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.3" />
  </ItemGroup>
</Project>