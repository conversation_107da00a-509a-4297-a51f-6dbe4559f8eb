name: Repartitioner Deploy
run-name: Repartitioner Deploy by @${{ github.actor }}

on:
  workflow_dispatch:
  push:
    branches:
      - master
    paths:
      - ".github/workflows/repartitioner_deploy.yaml"
      - "helm/esky-packages-repartitioner/**"
      - "Directory.Packages.props"
      - "src/Repartitioner/**"
      - "src/Application/**"
      - "src/Domain/**"
      - "src/Infrastructure/**"
      - "src/Contract/**"

permissions:
  checks: write
  contents: write
  pull-requests: write

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: false

jobs:
  publish_container_image:
    secrets: inherit
    uses: eskygroup/actions-docker/.github/workflows/docker.yaml@v0.0.1
    with:
      file: src/Repartitioner/Dockerfile
      registry: europe-docker.pkg.dev/esky-ets-hotels-pro/hotels-docker-release
      repositories: esky-packages-repartitioner
      tags: gh-b${{ github.run_number }}

  staging-deployment-marker:
    runs-on:
      group: small
    needs: publish_container_image
    environment: staging
    steps: # these steps are meaningless but required by the workflow
      - name: deploying
        run: echo "Deploying to STAGING"

  deploy-staging:
    needs: staging-deployment-marker
    secrets: inherit
    uses: eskygroup/actions-deploy-with-argocd/.github/workflows/deploy_with_argocd.yaml@v1
    with:
      argocd_project: png
      app: esky-packages-repartitioner
      environment: staging
      tag: gh-b${{ github.run_number }}

  # production deployment requires manual approval
  production-deployment-marker:
    needs: deploy-staging
    runs-on:
      group: small
    environment: production
    steps: # these steps are meaningless but required by the workflow
      - name: deploying
        run: echo "Deploying to PRODUCTION"

  deploy-production:
    needs: production-deployment-marker
    secrets: inherit
    uses: eskygroup/actions-deploy-with-argocd/.github/workflows/deploy_with_argocd.yaml@v1
    with:
      argocd_project: png
      app: esky-packages-repartitioner
      environment: pro
      tag: gh-b${{ github.run_number }}