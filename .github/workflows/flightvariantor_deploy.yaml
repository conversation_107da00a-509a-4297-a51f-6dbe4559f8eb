name: Flight Variantor Deploy
run-name: Flight Variantor Deploy by @${{ github.actor }}

on:
  workflow_dispatch:
  push:
    branches:
      - master
    paths:
      - ".github/workflows/flightvariantor_deploy.yaml"
      - "helm/esky-packages-flightvariantor/**"
      - "Directory.Packages.props"
      - "src/FlightVariantor/**"
      - "src/Application/**"
      - "src/Domain/**"
      - "src/Infrastructure/**"
      - "src/Contract/**"

permissions:
  checks: write
  contents: write
  pull-requests: write

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: false

jobs:
  publish_container_image:
    secrets: inherit
    uses: eskygroup/actions-docker/.github/workflows/docker.yaml@v0.0.1
    with:
      file: src/FlightVariantor/Dockerfile
      registry: europe-docker.pkg.dev/esky-ets-hotels-pro/hotels-docker-release
      repositories: esky-packages-flightvariantor
      tags: gh-b${{ github.run_number }}

  staging-deployment-marker:
    runs-on:
      group: small
    needs: publish_container_image
    environment: staging
    steps: # these steps are meaningless but required by the workflow
      - name: deploying
        run: echo "Deploying to STAGING"

  deploy-staging:
    needs: staging-deployment-marker
    secrets: inherit
    uses: eskygroup/actions-deploy-with-argocd/.github/workflows/deploy_with_argocd.yaml@v1
    with:
      argocd_project: png
      app: esky-packages-flightvariantor
      environment: staging
      tag: gh-b${{ github.run_number }}

  # production deployment requires manual approval
  production-deployment-marker:
    needs: deploy-staging
    runs-on:
      group: small
    environment: production
    steps: # these steps are meaningless but required by the workflow
      - name: deploying
        run: echo "Deploying to PRODUCTION"

  deploy-production:
    needs: production-deployment-marker
    secrets: inherit
    uses: eskygroup/actions-deploy-with-argocd/.github/workflows/deploy_with_argocd.yaml@v1
    with:
      argocd_project: png
      app: esky-packages-flightvariantor
      environment: pro
      tag: gh-b${{ github.run_number }}