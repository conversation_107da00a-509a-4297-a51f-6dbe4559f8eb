name: Pull Request Build
run-name: Pull Request Build by @${{ github.actor }}

on:
  pull_request:
  workflow_dispatch:

permissions:
  checks: write
  contents: write
  pull-requests: write
  
jobs:
  link_jira:
    name: Link Jira Task
    if: github.event_name == 'pull_request' && github.event.action == 'opened'
    runs-on: k8s-runner
    steps:
      - uses: eskygroup/github-actions/.github/actions/jira-link-insert@master
        with:
          github_token: "${{ secrets.GITHUB_TOKEN }}" 

  test:
    runs-on: k8s-runner
    env:
      DOTNET_INSTALL_DIR: ./.dotnet
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
          
      - name: Rebase to master
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "GitHub Actions"
          git fetch origin master
          git rebase origin/master
        
      - name: Setup dotnet
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: 9.0.x
          cache: false
          
      - name: Run tests
        run: dotnet test Esky.Packages.sln --verbosity normal --logger "trx;LogFileName=test-results.trx"

      - name: Generate test report
        if: always()
        uses: dorny/test-reporter@v1
        with:
          name: test report
          path: "**/test-results.trx"
          reporter: dotnet-trx
          fail-on-error: true

  build:
    runs-on: k8s-runner
    needs: test
    env:
      DOTNET_INSTALL_DIR: ./.dotnet
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
          
      - name: Rebase to master
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "GitHub Actions"
          git fetch origin master
          git rebase origin/master || exit 0
        continue-on-error: true

      - name: Setup dotnet
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: 9.0.x
          cache: false

      - name: Restore
        run: dotnet restore Esky.Packages.sln

      - name: Build
        run: dotnet build Esky.Packages.sln 