# Esky Packages

## Team
This project is maintained by the INV (Inventory) Team.

### Contact & Communication
- Slack team alias: @inventory_team
- Slack team support alias: @inventory_support
- Slack support channel: #inventory-support
- Slack alerts channel: #inventory-alerts
- [Jira Board](https://eskygroup.atlassian.net/jira/software/c/projects/INV/boards/289)
- Mail alias: <EMAIL>

## Table of Contents
- [Quick Start](#quick-start)
  - [Local Infrastructure](#local-infrastructure)
  - [First Run](#first-run)
- [Infrastructure Components](#infrastructure-components)
  - [RabbitMQ](#rabbitmq)
  - [Kafka](#kafka)
  - [MongoDB](#mongodb)
- [Configuration](#configuration)
  - [Secrets Management](#secrets-management)
  - [Environment Variables](#environment-variables)
- [API Clients](#api-clients)
- [Maintenance](#maintenance)
  - [MongoDB Shard Servers](#modifying-mongodb-shard-servers)
  - [Oplogreader Replicas](#modifying-number-of-oplogreader-replicas)
- [Architecture & Resources](#architecture--resources)
  - [Architecture Diagram](#architecture-diagram)
  - [Repositories](#repositories)
  - [CI/CD](#cicd)
  - [API Documentation](#api-documentation)
  - [Databases](#databases)
  - [Monitoring](#monitoring)

## Quick Start

### Local Infrastructure

```bash
make infrastructure
```
or
```bash
docker compose -f docker/docker-compose.yaml up 
```

### First Run

1. Run local infrastructure
2. Setup mongodb replica set
```bash
make setup-mongodb
```
3. Setup kafka topics
```bash
make setup-kafka
```
4. Add entries to /etc/hosts
```
127.0.0.1       packages-mongo1
127.0.0.1       packages-mongo2
127.0.0.1       packages-mongo3
```

## Infrastructure Components

### RabbitMQ
Default development credentials for localhost RabbitMQ management UI:
- Username: guest
- Password: guest
- URL: http://localhost:15672/

### Kafka
Information about managing Kafka topics can be found in `/scripts/kafka` and dedicated environment.

### MongoDB
`packages-pro` cluster in Atlas uses sharding mechanism, so we can take advantage of that and shard collections as well.

Collection sharding commands:
```javascript
sh.shardCollection('esky-packages.packageHotelOffers', {'_id.m': 'hashed'} )
sh.shardCollection('esky-packages.packageFlights', {'partitionKey': 'hashed'} )
sh.shardCollection('esky-packages.packageFlightVariants', {'_id.a': 'hashed'} )
sh.shardCollection('esky-packages.packageAvailabilities', {'_id.d': 'hashed'} )
sh.shardCollection('esky-packages.packageDefinitions', {'_id': 'hashed'} )
sh.shardCollection('esky-packages.pipelines', {'definitionId': 'hashed'} )
sh.shardCollection('esky-packages.packageFlightVariantPrices', {'pk': 'hashed'} )
sh.shardCollection('esky-packages.packageHotelOfferVariantPrices', {'_id.m': 'hashed'} )
```

Verify if everything is set up correctly using:
```javascript
sh.status()
```

## Configuration

### Secrets Management
Request secrets from developer in INV team. You have two options:
1. Add secrets manually in Visual Studio: right click on Generator/Api projects and click "Manage User Secrets"
2. Add secrets via CLI:
```bash
src/Generator $ dotnet user-secrets set "HotelGateway:ConnectionString" "**********"
src/Api $ dotnet user-secrets set "HotelGateway:ConnectionString" "**********"
```

### Environment Variables
Required environment variables:
- Logging to RabbitMQ needs two keys for each application (nlog.config is not able to resolve it via User Secrets):
  - `Logging__ServiceBus__UserName`
  - `Logging__ServiceBus__Password`
- BigQuery integration for CI:
  - `GOOGLE_APPLICATION_CREDENTIALS`

## API Clients

### FlightSearch
```bash
$ ./scripts/api-clients/flights-cache.sh
```

## Maintenance

### Modifying MongoDB Shard Servers

We noticed that after adding or removing shard servers, oplog resume tokens can be deleted, which can cause issues 
with oplog readers - they will not be able to resume from the last position and will restart forever.

To add new shard server to the cluster, you need to:

1. Scale to zero all consumers and services that can write to the database:
    - `packages-generator`
    - `packages-repricer`
2. Wait until all oplog readers consume all oplog entries (lag is 0 and no events are being published)
3. Scale to zero all oplog readers:
   - `packages-flightoplogreader`
4. Remove from database all oplog resume tokens from collections:
   - `oplogPackageFlightResumeTokens`
5. Add new shard server to the cluster or remove one
6. Wait until all changes in cluster are deployed
7. Scale up all oplog readers
8. Scale up all consumers and services
9. Check if oplog readers are working correctly

### Modifying Number of Oplogreader Replicas

Each oplogreader listens to its own part of data so every change in number of replicas can cause issues with oplog 
resume tokens.

To modify number of oplogreader replicas, you need to:

1. Prepare changes in appsettings and deployment files
2. Scale to zero all consumers and services that can write to the collection that oplogreader listens to
3. Wait until all oplog readers consume all oplog entries (lag is 0 and no events are being published)
4. Scale to zero all oplog readers
5. Remove from database all oplog resume tokens from collection that oplogreader listens to
6. Apply changes in number of replicas
7. Wait until all changes in cluster are deployed
8. Scale up all oplog readers (unless they are scaled up automatically)
9. Scale up all consumers and services
10. Check if oplog readers are working correctly

## Architecture & Resources

### Architecture Diagram
[View Architecture Diagram](https://miro.com/app/board/uXjVKzPceuA=/)

### Repositories
- [packages](https://github.com/eskygroup/esky-packages)
- [packages-search](https://github.com/eskygroup/esky-packages-search)
- [hotels-cache](https://github.com/eskygroup/esky-hotels-cache)

### CI/CD
#### GitHub Actions
- [packages](https://github.com/eskygroup/esky-packages/actions)
- [packages-search](https://github.com/eskygroup/esky-packages-search/actions/)
- [hotels-cache](https://github.com/eskygroup/esky-hotels-cache/actions/)

#### ArgoCD
- [Staging](https://argocd.eskyspace.com/applications?showFavorites=false&proj=png&sync=&autoSync=&health=&namespace=&cluster=&labels=env%253Dstaging)
- [Production](https://argocd.eskyspace.com/applications?showFavorites=false&proj=png&sync=&autoSync=&health=&namespace=&cluster=&labels=env%253Dpro)

### API Documentation
#### esky-packages-api
- [Staging](http://default.esky-packages-api.service.gcp-staging.consul/swagger/index.html)
- [Production](http://default.esky-packages-api.service.gcp-pro.consul/swagger/index.html)

#### esky-packages-search-api
- [Staging](http://default.esky-packages-search-api.service.gcp-staging.consul/swagger/index.html)
- [Production](http://default.esky-packages-search-api.service.gcp-pro.consul/swagger/index.html)

#### esky-hotels-cache-api
- [Staging](http://default.esky-hotels-cache-api.service.gcp-staging.consul/swagger/index.html)
- [Production](http://default.esky-hotels-cache-api.service.gcp-pro.consul/swagger/index.html)

### Databases
#### Staging
- [packages-ci](https://cloud.mongodb.com/v2/65a13de0bfa61b50ebdd97bb#/clusters/detail/packages-ci)
- [packages-search-ci](https://cloud.mongodb.com/v2/65a13de0bfa61b50ebdd97bb#/clusters/detail/packages-search-ci)
- [hotels-cache](https://cloud.mongodb.com/v2/65f002553d7aff5962f71632#/overview)

#### Production
- [packages-pro](https://cloud.mongodb.com/v2/65a13de0bfa61b50ebdd97bb#/clusters/detail/packages-pro)
- [packages-search-pro](https://cloud.mongodb.com/v2/65a13de0bfa61b50ebdd97bb#/clusters/detail/packages-search-pro)
- [hotels-cache](https://cloud.mongodb.com/v2/65f002553d7aff5962f71632#/overview)

### Monitoring
#### Grafana Dashboards
- [hotels-cache](https://grafanasre.eskyspace.com/d/fecxyu18dl69sf/hotels-cache?orgId=1&var-hotels=wRDxvguMz&var-env=PRO)
- [packages](https://grafanasre.eskyspace.com/d/bedmkdc6y0zy8e/packages-precomputed-new?orgId=1)
- [packages-slo](https://grafanasre.eskyspace.com/d/aew9ytjsxt1xcb/packages-summary?orgId=1)

#### Kibana Logs
##### Staging
- [Applications logs](https://kibana-ci.eskyspace.com/goto/8e348bf0-2fe5-11f0-a189-fd03f72a7dba)
- [Pipeline logs](https://kibana-ci.eskyspace.com/goto/95e4e3f0-e543-11ef-b807-153464e3aacc)

##### Production
- [Applications logs](https://kibana.eskyspace.com/goto/5a09bee0-2f2c-11f0-85d4-bd3393a55383)
- [Pipeline logs](https://kibana.eskyspace.com/goto/a0a4eca0-e542-11ef-924b-33dff754efc1)

#### RabbitMQ Logs
- [Staging](http://esky-ets-hotels-ci.rabbitmq-logs-k8s.service.gcp-staging.consul:15672/#/)
- [Production](http://esky-ets-hotels-pro.rabbitmq-logs-k8s.service.gcp-pro.consul:15672/#/)
