
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.12.35527.113
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Services", "Services", "{7CB85811-0BE8-4306-864D-3659E2B307A3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Layers", "Layers", "{43F15C17-9132-479E-86A0-59D3860BFDE2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Application", "src\Application\Application.csproj", "{349126D1-22FA-4720-97B9-90A486CFAE07}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Domain", "src\Domain\Domain.csproj", "{E9AAE1A7-8FCB-41E8-81C4-951E84633267}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Infrastructure", "src\Infrastructure\Infrastructure.csproj", "{19E3CB5E-CDDE-4A14-9A51-28BA57DA6C71}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Api", "src\Api\Api.csproj", "{B9E8C2FC-3AE3-44F1-8338-A4A5BB029E27}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Generator", "src\Generator\Generator.csproj", "{93C85028-46E9-43F0-A3CE-ABA3AE2503C0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Repricer", "src\Repricer\Repricer.csproj", "{79EF6B6F-E6E2-4958-B54D-94426A921A43}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Contract", "src\Contract\Contract.csproj", "{81CF045C-0219-4746-B644-7C4C428F76FF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Repartitioner", "src\Repartitioner\Repartitioner.csproj", "{851F2C5A-E89E-4F0B-BC9E-3E22E0E2A221}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FlightOplogReader", "src\FlightOplogReader\FlightOplogReader.csproj", "{14B7B01E-070C-40E0-B780-B5F189B9F040}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{6E26FF3C-FBCC-4D06-9F66-94A43DE0CD6C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Domain.Tests", "tests\Domain.Tests\Domain.Tests.csproj", "{88548C05-798C-4A8D-B1F5-51FC7F740D13}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Application.Tests", "tests\Application.Tests\Application.Tests.csproj", "{80758D64-3D6F-47CE-A8C6-960A24420571}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Common.Tests", "tests\Common.Tests\Common.Tests.csproj", "{7CCC5467-13E9-4295-810B-535691B6D965}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FlightVariantor", "src\FlightVariantor\FlightVariantor.csproj", "{A79EFA22-9355-4578-9D54-8A5C919DAE80}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Integration.Tests", "tests\Integration.Tests\Integration.Tests.csproj", "{F8DFC5A9-3043-4E9F-8B67-DD0CCE06E93A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PriceTracker", "src\PriceTracker\PriceTracker.csproj", "{CA42A705-C8A2-4C3A-A2CA-BC676FEB170B}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{349126D1-22FA-4720-97B9-90A486CFAE07}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{349126D1-22FA-4720-97B9-90A486CFAE07}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{349126D1-22FA-4720-97B9-90A486CFAE07}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{349126D1-22FA-4720-97B9-90A486CFAE07}.Release|Any CPU.Build.0 = Release|Any CPU
		{E9AAE1A7-8FCB-41E8-81C4-951E84633267}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E9AAE1A7-8FCB-41E8-81C4-951E84633267}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E9AAE1A7-8FCB-41E8-81C4-951E84633267}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E9AAE1A7-8FCB-41E8-81C4-951E84633267}.Release|Any CPU.Build.0 = Release|Any CPU
		{19E3CB5E-CDDE-4A14-9A51-28BA57DA6C71}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{19E3CB5E-CDDE-4A14-9A51-28BA57DA6C71}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{19E3CB5E-CDDE-4A14-9A51-28BA57DA6C71}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{19E3CB5E-CDDE-4A14-9A51-28BA57DA6C71}.Release|Any CPU.Build.0 = Release|Any CPU
		{B9E8C2FC-3AE3-44F1-8338-A4A5BB029E27}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B9E8C2FC-3AE3-44F1-8338-A4A5BB029E27}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B9E8C2FC-3AE3-44F1-8338-A4A5BB029E27}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B9E8C2FC-3AE3-44F1-8338-A4A5BB029E27}.Release|Any CPU.Build.0 = Release|Any CPU
		{93C85028-46E9-43F0-A3CE-ABA3AE2503C0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{93C85028-46E9-43F0-A3CE-ABA3AE2503C0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{93C85028-46E9-43F0-A3CE-ABA3AE2503C0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{93C85028-46E9-43F0-A3CE-ABA3AE2503C0}.Release|Any CPU.Build.0 = Release|Any CPU
		{79EF6B6F-E6E2-4958-B54D-94426A921A43}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{79EF6B6F-E6E2-4958-B54D-94426A921A43}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{79EF6B6F-E6E2-4958-B54D-94426A921A43}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{79EF6B6F-E6E2-4958-B54D-94426A921A43}.Release|Any CPU.Build.0 = Release|Any CPU
		{81CF045C-0219-4746-B644-7C4C428F76FF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{81CF045C-0219-4746-B644-7C4C428F76FF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{81CF045C-0219-4746-B644-7C4C428F76FF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{81CF045C-0219-4746-B644-7C4C428F76FF}.Release|Any CPU.Build.0 = Release|Any CPU
		{851F2C5A-E89E-4F0B-BC9E-3E22E0E2A221}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{851F2C5A-E89E-4F0B-BC9E-3E22E0E2A221}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{851F2C5A-E89E-4F0B-BC9E-3E22E0E2A221}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{851F2C5A-E89E-4F0B-BC9E-3E22E0E2A221}.Release|Any CPU.Build.0 = Release|Any CPU
		{14B7B01E-070C-40E0-B780-B5F189B9F040}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{14B7B01E-070C-40E0-B780-B5F189B9F040}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{14B7B01E-070C-40E0-B780-B5F189B9F040}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{14B7B01E-070C-40E0-B780-B5F189B9F040}.Release|Any CPU.Build.0 = Release|Any CPU
		{88548C05-798C-4A8D-B1F5-51FC7F740D13}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{88548C05-798C-4A8D-B1F5-51FC7F740D13}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{88548C05-798C-4A8D-B1F5-51FC7F740D13}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{88548C05-798C-4A8D-B1F5-51FC7F740D13}.Release|Any CPU.Build.0 = Release|Any CPU
		{80758D64-3D6F-47CE-A8C6-960A24420571}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{80758D64-3D6F-47CE-A8C6-960A24420571}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{80758D64-3D6F-47CE-A8C6-960A24420571}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{80758D64-3D6F-47CE-A8C6-960A24420571}.Release|Any CPU.Build.0 = Release|Any CPU
		{7CCC5467-13E9-4295-810B-535691B6D965}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7CCC5467-13E9-4295-810B-535691B6D965}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7CCC5467-13E9-4295-810B-535691B6D965}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7CCC5467-13E9-4295-810B-535691B6D965}.Release|Any CPU.Build.0 = Release|Any CPU
		{A79EFA22-9355-4578-9D54-8A5C919DAE80}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A79EFA22-9355-4578-9D54-8A5C919DAE80}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A79EFA22-9355-4578-9D54-8A5C919DAE80}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A79EFA22-9355-4578-9D54-8A5C919DAE80}.Release|Any CPU.Build.0 = Release|Any CPU
		{F8DFC5A9-3043-4E9F-8B67-DD0CCE06E93A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F8DFC5A9-3043-4E9F-8B67-DD0CCE06E93A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F8DFC5A9-3043-4E9F-8B67-DD0CCE06E93A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F8DFC5A9-3043-4E9F-8B67-DD0CCE06E93A}.Release|Any CPU.Build.0 = Release|Any CPU
		{CA42A705-C8A2-4C3A-A2CA-BC676FEB170B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CA42A705-C8A2-4C3A-A2CA-BC676FEB170B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CA42A705-C8A2-4C3A-A2CA-BC676FEB170B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CA42A705-C8A2-4C3A-A2CA-BC676FEB170B}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{349126D1-22FA-4720-97B9-90A486CFAE07} = {43F15C17-9132-479E-86A0-59D3860BFDE2}
		{E9AAE1A7-8FCB-41E8-81C4-951E84633267} = {43F15C17-9132-479E-86A0-59D3860BFDE2}
		{19E3CB5E-CDDE-4A14-9A51-28BA57DA6C71} = {43F15C17-9132-479E-86A0-59D3860BFDE2}
		{B9E8C2FC-3AE3-44F1-8338-A4A5BB029E27} = {7CB85811-0BE8-4306-864D-3659E2B307A3}
		{93C85028-46E9-43F0-A3CE-ABA3AE2503C0} = {7CB85811-0BE8-4306-864D-3659E2B307A3}
		{79EF6B6F-E6E2-4958-B54D-94426A921A43} = {7CB85811-0BE8-4306-864D-3659E2B307A3}
		{81CF045C-0219-4746-B644-7C4C428F76FF} = {43F15C17-9132-479E-86A0-59D3860BFDE2}
		{851F2C5A-E89E-4F0B-BC9E-3E22E0E2A221} = {7CB85811-0BE8-4306-864D-3659E2B307A3}
		{14B7B01E-070C-40E0-B780-B5F189B9F040} = {7CB85811-0BE8-4306-864D-3659E2B307A3}
		{88548C05-798C-4A8D-B1F5-51FC7F740D13} = {6E26FF3C-FBCC-4D06-9F66-94A43DE0CD6C}
		{80758D64-3D6F-47CE-A8C6-960A24420571} = {6E26FF3C-FBCC-4D06-9F66-94A43DE0CD6C}
		{7CCC5467-13E9-4295-810B-535691B6D965} = {6E26FF3C-FBCC-4D06-9F66-94A43DE0CD6C}
		{A79EFA22-9355-4578-9D54-8A5C919DAE80} = {7CB85811-0BE8-4306-864D-3659E2B307A3}
		{F8DFC5A9-3043-4E9F-8B67-DD0CCE06E93A} = {6E26FF3C-FBCC-4D06-9F66-94A43DE0CD6C}
		{CA42A705-C8A2-4C3A-A2CA-BC676FEB170B} = {7CB85811-0BE8-4306-864D-3659E2B307A3}
	EndGlobalSection
EndGlobal
